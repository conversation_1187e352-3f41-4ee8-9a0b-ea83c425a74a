<?php

namespace App\Http\Controllers\Admin;
use Mail;

use Illuminate\Http\Request;
use App\Models\User;
use Helper;
use Response;
use AUTH;
use DB;
use Storage;
use ArPHP\I18N\Arabic;
use App;
use Session;
use App\Jobs\SendAssetsReportJob;
use PDF;
use PDF2;
use DateTime;
use stdClass;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use FilesystemIterator;
use App\Http\Controllers\Controller;
use App\Models\Contracts;
use Log;
use Mccarlosen\LaravelMpdf\Facades\LaravelMpdf;
use App\Http\Helpers\ReportQueryHelper;
use App\Http\Helpers\ImagesUploadHelper;


class AssetsManagementExportController extends Controller
{
    private $userType;

    public function __construct(){
        $this->userType = \AUTH::user()?->user_type;
    }

    public function export_assets(Request $request)
    {
       
        $language=$request->language;
        $report=$request->choose_type ?? '';
        $report_type=$request->file_type ?? 'csv';
        $logedin_user = auth()->user();
        $user_id = $logedin_user->id;
        $project_user_id = Auth::user()->project_user_id;
        $lang = App::getLocale();
        $user = Auth::user();
        $link_start = asset('uploads/');
        $report_number = 'RPT' . rand(1000, 9999);
        $filename = rand(10000000, 99999999) . '.xlsx';
        $project_image = Helper::getProjectImage($user->project_id);
        $report_details = ['user_id' => $user_id, 'filename' => $filename, 'report_no' => $report_number, 'report_type' => $report_type, 'requested_at' => date('Y-m-d H:i:s'), 'status' => 'pending','project_user_id' => $project_user_id];
        $report_id = ReportQueryHelper::createReport($report_details);
        if ($report_id) {
            $project_image_link = asset('img/OSOOL_logo_svg.svg'); 
            if (!empty($project_image) || $project_image != null) {
                $project_image_link = ImagesUploadHelper::displayImage($project_image, 'uploads/project_images', 1);
                if (empty($project_image_link)) { 
                $project_image_link = asset('img/OSOOL_logo_svg.svg'); 
                }
            }
           SendAssetsReportJob::dispatch($request->all(), Auth::user(), $report_number, $filename, $lang, $_SERVER["DOCUMENT_ROOT"], $project_image_link, $link_start, $report_id,$project_user_id,$report);
        }
        App::setLocale($lang);
        $request->session()->put('generated', ['report_no' => $report_number]);
        return json_encode(['msg'=>'Success']);
    }


   
}
