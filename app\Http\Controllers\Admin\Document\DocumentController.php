<?php

namespace App\Http\Controllers\Admin\Document;

use App\Http\Controllers\Controller;
use App\Services\CRM\Sales\CallService;
use App\Services\ManageDocument\DocumentService;
use Illuminate\Http\Request;
use Barryvdh\Snappy\Facades\SnappyPdf as PDF;

class DocumentController extends Controller
{
    public function index()
    {
        return view('applications.admin.document.index');
    }

    public function details($id)
    {
        $service =  app(DocumentService::class);
        $data = $service->view($id);
        
        $response = [];
        if (@$data['status'] == "success") {
            $response = $data['data'] ?? [];
        }

        return view('applications.admin.document.details', compact('response', 'id'));
    }

    public function pdf($id)
    {
        $service =  app(DocumentService::class);
        $data = $service->view($id);
        $response = [];
        if (@$data['status'] == "success") {
            $response = $data['data'] ?? [];
        }

        $document_id = $response ? $response['info']['document_id'] : null;
        $type = $response ? $response['info']['type'] : null;
        $description = $response ? $response['info']['description'] : null;

        return view('applications.admin.document.pdf', compact('document_id', 'description', 'type', 'id'));
    }

    public function downloadPdf($id)
    {
        $service =  app(DocumentService::class);
        $data = $service->view($id);
        $response = [];
        if (@$data['status'] == "success") {
            $response = $data['data'] ?? [];
        }

        $document_id = $response ? $response['info']['document_id'] : null;
        $type = $response ? $response['info']['type'] : null;
        $description = $response ? $response['info']['description'] : null;

        $html = view('applications.admin.document.pdf', compact('document_id', 'description', 'type'))->render();

        $pdf = PDF::loadHTML($html)->setPaper('a4', 'landscape');

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->output(); // use inline to output PDF content
        }, "document_details_$document_id.pdf");
    }
}
