<?php

namespace App\Http\Controllers\Admin\Document;

use App\Http\Controllers\Controller;
use App\Services\CRM\Sales\CallService;
use App\Services\ManageDocument\DocumentService;
use App\Services\ManageDocument\TemplateService;
use Illuminate\Http\Request;
use Barryvdh\Snappy\Facades\SnappyPdf as PDF;

class TemplateController extends Controller
{
    public function index()
    {
        return view('applications.admin.document-template.index');
    }

    public function details($id)
    {
        $service =  app(TemplateService::class);
        $data = $service->view($id);

        $response = [];
        if (@$data['status'] == "success") {
            $response = $data['data'] ?? [];
        }

        return view('applications.admin.document-template.details', compact('response', 'id'));
    }

    public function pdf($id)
    {
        $service =  app(TemplateService::class);
        $data = $service->view($id);
        $response = [];
        if (@$data['status'] == "success") {
            $response = $data['data'] ?? [];
        }

        $document_id = $response ? $response['info']['document_id'] : null;
        $subject = $response ? $response['info']['subject'] : null;
        $type = $response ? $response['info']['type'] : null;
        $description = $response ? $response['info']['description'] : null;
        $additional_description = $response ? $response['info']['additional_description'] : null;

        return view('applications.admin.document-template.pdf', compact('document_id', 'description', 'type', 'id', 'subject', 'additional_description'));
    }

    public function downloadPdf($id)
    {
        $service =  app(TemplateService::class);
        $data = $service->view($id);
        $response = [];
        if (@$data['status'] == "success") {
            $response = $data['data'] ?? [];
        }

        $document_id = $response ? $response['info']['document_id'] : null;
        $subject = $response ? $response['info']['subject'] : null;
        $type = $response ? $response['info']['type'] : null;
        $description = $response ? $response['info']['description'] : null;
        $additional_description = $response ? $response['info']['additional_description'] : null;

        $html = view('applications.admin.document-template.pdf', compact('document_id', 'description', 'type', 'subject', 'additional_description'))->render();

        $pdf = PDF::loadHTML($html)->setPaper('a4', 'landscape');

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->output(); // use inline to output PDF content
        }, "template_details_$document_id.pdf");
    }
}
