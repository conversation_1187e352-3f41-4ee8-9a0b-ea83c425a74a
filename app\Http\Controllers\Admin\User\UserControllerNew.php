<?php
namespace App\Http\Controllers\Admin\User;

use Akaunting\Api\Data\WarehouseData;
use App\Events\VarificationEmail;
use App\Http\Controllers\Controller;
use App\Http\Helpers\Helper;
use App\Http\Helpers\ChecklistHelper;
use App\Http\Helpers\WorkorderHelper;
use App\Jobs\DashSync\VendorAccounts;
use App\Mail\OsoolMail;
use App\Models\{Contracts, User, City, Country, ServiceProvider, ProjectsDetails, WorkerProfession, UserSubPrivileges};
use App\Models\Checklists;
use App\Models\ChecklistTasks;
use App\Models\ServiceProviderProjectMapping;
use App\Models\WorkerAttendances;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use App\Notifications\VendorAssigned;
use Illuminate\Support\Facades\Notification;
use Illuminate\Validation\Rule;
use Yajra\Datatables\Datatables;
use App\Models\WorkerAvailabilityRequestReasonType;
use App\Http\Requests\Admin\Users\CreateAvailabilityStatusRequest;
use App\Models\ManageWorkerAvailabilityStatus;
use App\Http\Requests\Admin\Users\RejectAvailabilityStatusRequest;
use App\Http\Requests\Admin\Users\AcceptAvailabilityStatusRequest;
use App\Http\Helpers\ImagesUploadHelper;
use App\Http\Controllers\Api\V1\Tenant\ApiHelper;
use App\Events\VarificationEmailSuccess;
use App\Models\PropertyBuildings;
use App\Http\Helpers\ReportQueryHelper;
use App\Jobs\SendAttendanceReportJob;
use Illuminate\Support\Facades\Config;
use App\Models\Auth\Role;
use App\Models\PermissionPAAccess;
use App\Models\Auth\Permission;
use App\Services\AkauntingService;
use App\Http\Traits\PurchaseSalesTraits\PurchaseUserManagement;
use App\Http\Traits\RatingWorkerTrait;
use App\Http\Traits\FunctionsTrait;
use App\Services\CRM\CRMCompany;
use App\Services\CRM\CRMWorkspace;
use App\Models\CrmUser;
use App\Http\Traits\UserTrait;
use App\Enums\Role as EnumRole;
use App\Enums\Proficiency;

class UserControllerNew extends Controller
{
    use RatingWorkerTrait, FunctionsTrait, UserTrait;
    private $view_path='applications.admin.user';
    private $data=[];
    protected $crmCompany;
    protected $crmWorkspace;

    use PurchaseUserManagement;


    public function __construct(CRMCompany $crmCompany, CRMWorkspace $crmWorkspace){
      $this->middleware(function ($request, $next) {
		// Skip middleware for 'verify-email' route
        if ($request->routeIs('email.verify')) {
            return $next($request);
        }
        if(!$request->routeIs('update_reset_password')){
            $redirectinactiveuser = \Helper::redirectinactiveuser();
           if($redirectinactiveuser != "active user")
           {
               return $redirectinactiveuser;
           }
        }
         $route = Request()->route()->getPrefix();
         if ($route == '/user' || $route == '/supervisor') {
          Config::set('livewire.back_button_cache', true);
           return $next($request);
         }
         elseif(isset(auth()->user()->user_type) && auth()->user()->user_type == 'super_admin')  //for super admin
         {
            if(empty(auth()->user()->project_id)){ //check project id
              return redirect()->route('workspace.home')->withFlashMessage('You are not authorized to access that page.')->withFlashType('warning');
            }
          }
          return $next($request);
      });
        $this->crmCompany = $crmCompany;
        $this->crmWorkspace = $crmWorkspace;
    }

    /**
     * GET users/users-list
     *
     * Return view a display user list.
     * Return a view with list of users respective of the logged in user type with filters such as user-type/role and search query and total count.
     *
     * @authenticated
     * @group Users
     *
     * @responseField data array
     * @response 200 {data:[]}
     */
    public function list(Request $request, $availability_request_id = null, $notification_read = null, $notification_id = null)
    {

      if (!empty(Auth::user()->user_type == 'procurement_admin')){
        return redirect()->route('admin.dashboard');
       }

      Session::forget('bma_link_workdo');
      Session::forget('user_info');
      Session::forget('asset_categories_contacts');
      Session::forget('service_provider_id');
      Session::forget('edit_spa_user');
      Session::forget('worker_privilege');

      $selected_role = ''; //set default as null
      if(isset($request->user_role)) // Filter Users Role-Wise
      {
        $selected_role=$request->user_role;
        $request->session()->put('selected_role', $selected_role); //Add requested role to session data
      } else {
        Session::forget('selected_role'); //Unset session variable
      }


      $logedin_user=auth()->user();
      $isHasAdmin=0; //Set default value
      $this->data['pageTitle']='User Management';

       // Check if the 'notification_read' parameter is provided and equal to 'notification-read'
       if (!empty($notification_read) && $notification_read == 'notification-read') {
          // When user reads a notification, fetch details of the maintenance request
          $read_availability_notification = WorkorderHelper::readAvailabilityRequestNotification($notification_read, $notification_id, $availability_request_id, $logedin_user);
          $this->data['notification_request_status'] = $read_availability_notification['notification_request_status'];
          $this->data['notification_request_id'] = $read_availability_notification['notification_request_id'];
      }
      else
      {
        $this->data['notification_request_status'] = "-";
        $this->data['notification_request_id'] = "-";
      }

      // PROJECT USER ID CONDITION FOR VARIOUS USER TYPES
      if($logedin_user->user_type!='super_admin'&& $logedin_user->user_type!='osool_admin') //When not logged in as Super admin or admin
      {
        if(Session::has('entered_project_id')) //check sesssion data
        {
          $project_id = Session::get('entered_project_id');
        }
        elseif($logedin_user->user_type == 'admin' || $logedin_user->user_type == 'admin_employee')//when logged in as admin and admin employee
        {
          $project_id = $logedin_user->project_id;
          $isHasAdmin = User::select('service_providers.global_sp','users.*')->leftJoin('service_providers','service_providers.id','=','users.service_provider');
          $isHasAdmin = $isHasAdmin->whereRaw("( `service_providers`.`global_sp` = '0' or `service_providers`.`global_sp` IS NULL ) ");
          $isHasAdmin = $isHasAdmin->where('user_type', '!=', 'osool_admin')
          ->where('user_type', '!=', 'tenant')
          ->where('user_type', '!=', 'admin')
          ->where('user_type', '!=', 'super_admin');
          if($logedin_user->user_type == 'admin_employee') //for admin employee
          {
            $isHasAdmin = $isHasAdmin->where('user_type', '!=', 'admin_employee');
          }
          $isHasAdmin=$isHasAdmin->where([['users.deleted_at',NULL],['users.is_deleted', 'no']]);

          $isHasAdmin = $isHasAdmin->where('users.project_user_id', $logedin_user->project_user_id)->count();
          $this->data['total_user'] = $isHasAdmin;
        }
        elseif($logedin_user->user_type == 'sp_admin') //for SP admin user type
        {
          $user_id = $logedin_user->id;
          $service_provider = $logedin_user->service_provider; //get service providers

          $supervisor_ids = User::select('id')->where('user_type','supervisor')
          //->where('sp_admin_id',$user_id)
          ->where('service_provider', $service_provider)
          ->where(['is_deleted' => 'no', 'status' => 1])
          ->get()->pluck('id')->implode(',');//get supervisors
          if($supervisor_ids == "") //if has no supervisors
          {
            $where = "((user_type = 'supervisor' and service_provider = '$service_provider') OR (user_type = 'store_keeper' and service_provider = '$service_provider' and created_by = '$user_id'))";
          }
          else{ //If has supervisors
            $where = "((user_type = 'supervisor' and service_provider = '$service_provider') OR (user_type = 'store_keeper' and service_provider = '$service_provider' and created_by = '$user_id') OR (user_type = 'sp_worker' and supervisor_id in ( $supervisor_ids ) ))";
          }
          $isHasAdmin = User::whereRaw($where)->where([['deleted_at',NULL],['users.is_deleted', 'no']])->count();
          $this->data['total_user'] = $isHasAdmin;
        }
        elseif($logedin_user->user_type == 'supervisor')//when logged in as Supervisor
        {
          $user_id = $logedin_user->id;
          $isHasAdmin = User::select('id')->where('user_type','sp_worker')->whereRaw("find_in_set($logedin_user->id, users.supervisor_id)")->where([['deleted_at',NULL],['users.is_deleted', 'no']])->count();
          $this->data['total_user'] = $isHasAdmin;
        }
        elseif($logedin_user->user_type == 'building_manager') //when logged in as Building Manager
        {
          $user_id = $logedin_user->id;
          $isHasAdmin = User::whereRaw("(users.sp_admin_id = '$user_id' OR users.created_by = '$user_id')");
          $isHasAdmin=$isHasAdmin->where([['deleted_at',NULL],['users.is_deleted', 'no']]);

          $isHasAdmin = $isHasAdmin->where('user_type', 'building_manager_employee');
          $isHasAdmin = $isHasAdmin->count();
          $this->data['total_user'] = $isHasAdmin;
        }
      }
      else //When logged in as Super admin and osool admin
      {
        if(empty(Session::get('entered_project_id'))){return redirect()->route('admin.return_user');}
        $project_id = Session::get('entered_project_id');
        $isHasAdmin = User::where('user_type','admin')->where('project_id',$project_id)->count();
        if($isHasAdmin == 0) { //Check if project has admin
          $this->data['total_user'] = 0; //Set default as 0 ueser is there's no admin for the entered project
        } else {  //else returm the data of users
          $data = User::select('service_providers.global_sp','users.*')->leftJoin('service_providers','service_providers.id','=','users.service_provider');
          $data = $data->whereRaw("( `service_providers`.`global_sp` = '0' or `service_providers`.`global_sp` IS NULL ) ");
          $data = $data->whereRaw(" ( users.user_type = 'admin' OR
                users.user_type = 'admin_employee'  OR
                ( users.user_type = 'sp_admin' AND service_providers.global_sp = 0 )   OR
                  users.user_type = 'supervisor' OR
                  users.user_type = 'store_keeper' OR
                users.user_type = 'building_manager' OR
                  users.user_type = 'building_manager_employee' OR
                  users.user_type = 'sp_worker'  ) ");

          //dd($sselected_role);
          if(!empty($selected_role)) { //check for user type filter and add condtion
            $data = $data->whereIn('users.user_type', $selected_role)->where('users.project_user_id', $logedin_user->project_user_id);
            $data=$data->where([['users.deleted_at',NULL],['users.is_deleted', 'no']]);

            $this->data['total_user'] = $data->count();
          } else {
            $data = $data->where('users.project_user_id', $logedin_user->project_user_id);
            $data=$data->where([['users.deleted_at',NULL],['users.is_deleted', 'no']]);

            $this->data['total_user'] = $data->count();
          }
        }
      }
      $this->data['request_status_type'] = WorkerAvailabilityRequestReasonType::get()->toArray();
      return view($this->view_path.'.list-row-view',['data'=>$this->data]);
    }

    /**
     * GET users/userListAjax/{id?}
     *
     * Return JSON collection of user list ajax.
     * Return a JSON collection of list of users respective of the logged in user type with filters such as user-type/role and search query and total count.
     *
     * @authenticated
     * @group Users
     *
     * @responseField data array
     * @response 200 {data:[]}
     */
    public function userListAjax(Request $request)
    {
      Session::forget('user_info');
      Session::forget('asset_categories_contacts');
      Session::forget('service_provider_id');
      Session::forget('edit_spa_user');
      Session::forget('worker_privilege');


      $selected_role = ''; //Set default filter as null
      // if(isset($request->user_role)) //if filter is requested
      // {
      //   $selected_role=$request->user_role;
      //   $request->session()->put('selected_role', $selected_role);
      // } else {
      //     Session::forget('selected_role');
      // }
      $selected_role = $request->session()->get('selected_role');

      $page_length = $request->page_length;

      //dd($request->all());
      $logedin_user=auth()->user();

      if($request->ajax()) //check for ajax request
      {
        $searchValue=$request->search_text;
        $data = User::select('service_providers.global_sp','users.*','cities.name_en as city_name_en','cities.name_ar  as city_name_ar', 'worker_professions.profession_en', 'worker_professions.profession_ar');
        $data =  $data->leftJoin('worker_professions','worker_professions.id','=','users.profession_id');
        $data =  $data->leftJoin('service_providers','service_providers.id','=','users.service_provider');
        $data =  $data->where('users.id','!=',$logedin_user->id);
        //Filter Data as per User-type
        if($logedin_user->user_type == "admin" || $logedin_user->user_type == "admin_employee" || $logedin_user->user_type == "osool_admin" || $logedin_user->user_type == "super_admin")
        {
          if($logedin_user->user_type == "osool_admin" || $logedin_user->user_type == "super_admin") //Not for admin and admin Employee
          {
            $data = $data->whereRaw("( `service_providers`.`global_sp` = '0' or `service_providers`.`global_sp` IS NULL ) ");
            //dd($selected_role);
            if(!empty($selected_role)) { //if usertype filter applied
              $data = $data->whereRaw(" ( users.user_type = 'admin' OR
                  users.user_type = 'admin_employee'  OR
                  ( users.user_type = 'sp_admin' AND service_providers.global_sp = 0 )   OR
                    users.user_type = 'supervisor' OR
                  users.user_type = 'building_manager' OR
                    users.user_type = 'building_manager_employee' OR
                    users.user_type = 'store_keeper' OR
                    users.user_type = 'sp_worker' OR users.user_type = 'team_leader'  ) ");
              $data = $data->whereIn('user_type', $selected_role)->where('project_user_id', $logedin_user->project_user_id);
            } else { // if user type filter is not applied
              $data = $data->whereRaw(" ( users.user_type = 'admin' OR
                  users.user_type = 'admin_employee'  OR
                  ( users.user_type = 'sp_admin' AND service_providers.global_sp = 0 )   OR
                    users.user_type = 'supervisor' OR
                  users.user_type = 'building_manager' OR
                    users.user_type = 'building_manager_employee' OR
                    users.user_type = 'store_keeper' OR
                    users.user_type = 'sp_worker' OR users.user_type = 'team_leader'  ) ");
              $data = $data->where('project_user_id', $logedin_user->project_user_id);
            }
          }
          else{ //For admin and admin employee
            
            $project_id = $logedin_user->project_id;
            $data = $data->whereRaw("( `service_providers`.`global_sp` = '0' or `service_providers`.`global_sp` IS NULL ) ");
            $data = $data->where('user_type', '!=', 'osool_admin')
            ->where('user_type', '!=', 'tenant')
            ->where('user_type', '!=', 'admin')
            ->where('user_type', '!=', 'super_admin');
            if($logedin_user->user_type == 'admin_employee') //If the logged in user type is Admin Employee
            {
              $data = $data->where('user_type', '!=', 'admin_employee')->where('user_type', '!=', 'team_leader');
            }
            $data = $data->where('project_user_id', $logedin_user->project_user_id);
          }
        }
        /*****************for not = Super Admin and Osool Admin *******************/
        elseif($logedin_user->user_type!='super_admin' && $logedin_user->user_type!='osool_admin') //For rest of the user types filtering data set
        {
          if($logedin_user->user_type=='supervisor') // For Supervisor
          {
            //$data = $data->where('users.supervisor_id',$logedin_user->id)->where('user_type','sp_worker');
            $data = $data->whereRaw("find_in_set($logedin_user->id, users.supervisor_id)")->where('user_type','sp_worker');
          }
          elseif($logedin_user->user_type=='sp_admin') //For SP Admin
          {
            $projectIds = ServiceProviderProjectMapping::where('service_provider_id', Auth::user()->service_provider)
                ->pluck('project_id')->implode(',');

            $user_id = Auth::user()->id;
            $service_provider = $logedin_user->service_provider;
            $supervisor_ids = User::select('id')
            ->where('user_type','supervisor')
            ->where('service_provider',$service_provider)
            ->get()->pluck('id')->implode(',');
            if($supervisor_ids == "")
            {
              if ($projectIds== "") {
                $where = "((users.user_type = 'supervisor' and users.service_provider = '$service_provider') OR (users.user_type = 'store_keeper' and users.service_provider = '$service_provider' and created_by = '$user_id'))";
              }
              else
              {
                $where = "((user_type = 'sp_worker' and users.service_provider = '$service_provider' and users.project_id in ( $projectIds )) OR (users.user_type = 'supervisor' and users.service_provider = '$service_provider') OR (users.user_type = 'store_keeper' and users.service_provider = '$service_provider' and created_by = '$user_id'))";
              }
            }
            else{
              if ($projectIds== "") {
                $where = "((users.user_type = 'supervisor' and users.service_provider = '$service_provider') OR (users.user_type = 'store_keeper' and users.service_provider = '$service_provider' and created_by = '$user_id') OR (users.user_type = 'sp_worker' and users.supervisor_id in ( $supervisor_ids ) ))";
              }
              else
              {
                $where = "((user_type = 'sp_worker' and users.service_provider = '$service_provider' and users.project_id in ( $projectIds )) OR (users.user_type = 'supervisor' and users.service_provider = '$service_provider') OR (users.user_type = 'store_keeper' and users.service_provider = '$service_provider' and created_by = '$user_id') OR (users.user_type = 'sp_worker' and users.supervisor_id in ( $supervisor_ids ) ) OR (users.user_type = 'team_leader' and users.supervisor_id in ( $supervisor_ids ) ))";
              }

            }
            $data = $data->whereRaw($where);
          }
          elseif($logedin_user->user_type=='admin') // For Admin
          {
              $project_id = $logedin_user->project_id;
              $isHasAdmin = User::where('user_type','admin_employee')->where('project_user_id',$project_id)->count();
              if($isHasAdmin == 0) {
                $data = $data->where('users.user_type', '!=', 'tenant')->where('users.user_type', '!=', 'admin')->where('users.user_type', '!=', 'osool_admin')->where('users.user_type', '!=', 'super_admin')->where('users.project_user_id', $logedin_user->project_user_id);
              } else {
                $data = $data->where('users.user_type', '!=', 'tenant')->where('users.user_type', '!=', 'admin')->where('users.user_type', '!=', 'osool_admin')->where('users.user_type', '!=', 'super_admin')->where('users.project_user_id', $logedin_user->project_user_id);
              }
          }
          elseif($logedin_user->user_type=='building_manager') // For Building Manager
          {
            $data = $data->where('users.sp_admin_id',$logedin_user->id);
            $data = $data->where('users.user_type','building_manager_employee');
            // $data = $data->orwhereRaw(" (  `users`.`created_by` = '".$logedin_user->id."' and `users`.`user_type` != 'tenant'  ) ");
          }
          elseif($logedin_user->user_type == 'admin_employee') //For Admin Employee
          {
            $data = $data->where('users.created_by',$logedin_user->id);
          }
          else
          {
            $data = $data->where('users.created_by',$logedin_user->id);
          }
        }
        /*********************************************************/
        /*************for osool admin & SUper Admin if user type selected*******************/
        if($logedin_user->user_type == 'super_admin' || $logedin_user->user_type == 'osool_admin')
        {
          if($request->session()->has('selected_role')) //If the session Selected Role
          {
            $selected_role=$request->session()->get('selected_role');
            $project_id = Session::get('entered_project_id');
            $isHasAdmin = User::where('user_type','admin')->where('project_id',$project_id)->count();
            if($isHasAdmin == 0) { //If the user type is admin with Project id
              $data = $data->where('user_type', '!=', 'osool_admin')->where('user_type', '!=', 'super_admin')->whereIn('user_type',$selected_role)->where('project_user_id', $logedin_user->project_user_id);
            } else {
              $data = $data->whereRaw(" ( users.user_type = 'admin' OR
              users.user_type = 'admin_employee'  OR
              ( users.user_type = 'sp_admin' AND service_providers.global_sp = 0 )   OR
                users.user_type = 'supervisor' OR
              users.user_type = 'building_manager' OR
                users.user_type = 'building_manager_employee' OR
                users.user_type = 'store_keeper' OR
                users.user_type = 'sp_worker' OR users.user_type = 'team_leader'  ) ")->where('project_user_id', $logedin_user->project_user_id)->whereIn('user_type',$selected_role);
            }
          } else {
            $project_id = Session::get('entered_project_id');
            $isHasAdmin = User::where('user_type','admin')->where('project_id',$project_id)->count();
            if($isHasAdmin == 0) //if project does not have admin
            {
              $data = $data->where('user_type', '!=', 'osool_admin')->where('user_type', '!=', 'super_admin')->where('project_user_id', $logedin_user->project_user_id);
            } else {
              $data = $data->whereRaw(" ( users.user_type = 'admin' OR
              users.user_type = 'admin_employee'  OR
              ( users.user_type = 'sp_admin' AND service_providers.global_sp = 0 )   OR
                users.user_type = 'supervisor' OR
              users.user_type = 'building_manager' OR
                users.user_type = 'building_manager_employee' OR
                users.user_type = 'store_keeper' OR
                users.user_type = 'sp_worker'  OR users.user_type = 'team_leader' ) ")->where('project_user_id', $logedin_user->project_user_id);
            }
          }
        }
        if(!empty($searchValue)) // If requested with a search filter
        {
            $data = $data->where(function ($query) use ($searchValue) {
            $query->where('users.name','LIKE', "%{$searchValue}%");
            });

        }

        $data=$data->where([['users.deleted_at',NULL],['users.is_deleted', 'no']]);

        $data=$data->leftJoin("cities",function($join){
        $join->on("cities.id","=","users.city_id");
        })
        ->orderBy('users.id','DESC')
        ->groupBy('users.id')
        ->paginate($page_length);

        $total_user_count=$data->total();

        //dd($data->items());
        if($total_user_count > 0) {
          foreach($data->items() as $key=>$row) {
            $data->items()[$key]->encrypted_id = Crypt::encryptString($data->items()[$key]->id);
            $data->items()[$key]->logged_in_user = $logedin_user->user_type;
            $data->items()[$key]->profile_img = ImagesUploadHelper::displayImage($data->items()[$key]->profile_img, 'uploads/profile_images', 0);
          }

        }

        return json_encode(array('data'=>$data,'total_user_count'=>$total_user_count,'user_type'=>$logedin_user->user_type));
      }
    }



    /**
     * GET users/workerListAjax/{id?}
     *
     * Return JSON collection of user list ajax.
     * Return a JSON collection of list of workers with filters such search query and total count.
     *
     * @authenticated
     * @group Users
     *
     * @responseField data array
     * @response 200 {data:[]}
     */
    public function workerListAjax(Request $request)
    {
      Session::forget('user_info');
      Session::forget('asset_categories_contacts');
      Session::forget('service_provider_id');
      Session::forget('edit_spa_user');
      Session::forget('worker_privilege');


      $selected_role = ''; //Set default filter as null
      $page_length = $request->page_length ?? 10;
      $logedin_user=auth()->user();
      if($request->ajax()) //check for ajax request
      {
        $searchValue=$request->search_text;
        $data = User::select('service_providers.global_sp','users.*','cities.name_en as city_name_en','cities.name_ar  as city_name_ar', 'worker_professions.profession_en', 'worker_professions.profession_ar', 'countries.name_en as country_name_en','countries.name_ar  as country_name_ar');
        $data =  $data->leftJoin('worker_professions','worker_professions.id','=','users.profession_id');
        $data =  $data->leftJoin('countries','countries.id','=','users.country_id');
        $data =  $data->leftJoin('service_providers','service_providers.id','=','users.service_provider');
        $data =  $data->where('users.id','!=',$logedin_user->id);
        //Filter Data as per User-type
        if($logedin_user->user_type == "sp_admin")
        {
          $projectIds = ServiceProviderProjectMapping::where('service_provider_id', Auth::user()->service_provider)
          ->pluck('project_id')->implode(',');

            $user_id = Auth::user()->id;
            $service_provider = $logedin_user->service_provider;
            $supervisor_ids = User::select('id')
            ->where('user_type','supervisor')
            ->where('service_provider',$service_provider)
            ->get()->pluck('id')->implode(',');

            if($supervisor_ids == "")
            {
                $where = "((users.user_type = 'sp_worker' and users.supervisor_id in ( 0 )))";
            }
            else{
              $where = "((users.user_type = 'sp_worker' and users.supervisor_id in ( $supervisor_ids )))";
            }

            $data = $data->whereRaw($where);

        }
        elseif($logedin_user->user_type == "supervisor")
        {
          $data->whereRaw("find_in_set($logedin_user->id, users.supervisor_id) AND users.user_type = 'sp_worker'");
          //$data = $data->whereRaw("( `service_providers`.`global_sp` = '0' or `service_providers`.`global_sp` IS NULL ) ");

          //$data = $data->whereRaw("(users.user_type = 'sp_worker')")->where('project_user_id', $logedin_user->project_user_id);
        }
        else {

          $data =  $data->where('users.id','=','0');
        }

        if(!empty($searchValue)) // If requested with a search filter
        {
            $data = $data->where(function ($query) use ($searchValue) {
            $query->where('users.name','LIKE', "%{$searchValue}%");
            });

        }

        $data=$data->where([['users.deleted_at',NULL],['users.is_deleted', 'no']]);

        $data=$data->leftJoin("cities",function($join){
        $join->on("cities.id","=","users.city_id");
        })
        ->orderBy('users.id','DESC')
        ->groupBy('users.id')
        ->paginate($page_length);

        $total_user_count=$data->total();

        //dd($data->items());
        if($total_user_count > 0) {
          foreach($data->items() as $key=>$row) {
            $availability_record = Helper::checkActiveApprovedOfflineRequest($data->items()[$key]->id);
            $data->items()[$key]->encrypted_id = Crypt::encryptString($data->items()[$key]->id);
            $data->items()[$key]->logged_in_user = $logedin_user->user_type;
            $data->items()[$key]->profile_img = ImagesUploadHelper::displayImage($data->items()[$key]->profile_img, 'uploads/profile_images', 0);

            $data->items()[$key]->leave_request_id = "";
            $data->items()[$key]->request_id = "";

            if($availability_record)
            {
              $data->items()[$key]->leave_request_id = $availability_record->leave_request_id;
              $data->items()[$key]->request_id = $availability_record->id;
            }

            $ratingData = $this->getRatingWorkerInformationByValues('worker_id', $data->items()[$key]->id);
            $ratingField = isset($ratingData) ? $ratingData->rating_avg : 0;
            $data->items()[$key]->rating = $this->roundToIntNumber($ratingField);
          }
        }

        /*if(isset($data)){
          $collection = $data->map(function ($item) {
            $ratingData = $this->getRatingWorkerInformationByValues('worker_id', $item['id']);
            $ratingField = isset($ratingData) ? $ratingData->rating_avg : 0;
            $item['rating'] = $this->roundToIntNumber($ratingField);
            return $item;
          });

        }*/
        return json_encode(array('data'=>$data,'total_user_count'=>$total_user_count,'user_type'=>$logedin_user->user_type));
      }
    }

    /**
     * GET users/pspa-list
     *
     * Return JSON response public Service Providers listing.
     * Return a view of list of public service providers respective of the logged in user type with total count and also filter results by user-type/role, search query.
     *
     * @authenticated
     * @group Users
     *
     * @responseField data array
     * @response 200 {data:[]}
     */
    public function pspa(Request $request)
    {
      Session::forget('user_info');
      Session::forget('asset_categories_contacts');
      Session::forget('service_provider_id');
      Session::forget('edit_spa_user');
      Session::forget('worker_privilege');

      $logedin_user=auth()->user();
      $this->data['pageTitle']='User Management';
      //dd($request->search_text);
      $searchValue=$request->search_text;
      $this->data['total_user'] = 0;
      $data = User::select('users.*')->leftJoin('service_providers','service_providers.id','=','users.service_provider');
      // USER TYPE CONDITIONS
      if($logedin_user->user_type == 'super_admin') // For Super Admin
      {
        $data = $data->where('service_providers.global_sp',1);
        if($request->session()->has('selected_role'))
        {
          $selected_role=$request->session()->get('selected_role');
          $data = $data->whereIn('user_type',$selected_role)->where('project_user_id', $logedin_user->project_user_id);
        } else { // project_id, user_type = admin
          $data = $data->where('user_type', 'sp_admin');
        }
        if(!empty($searchValue)) //Check if search filter is requested
        {
            $data = $data->where(function ($query) use ($searchValue) {
            $query->where('users.name','LIKE', "%{$searchValue}%");
            });
        }
        $data = $data->get();
        $this->data['total_user'] = $data->count();
      }
      if($logedin_user->user_type == 'osool_admin') //For Osool Admin
      {
          $projects = Helper::getOsoolAdminProjects($logedin_user->id);
          $data = $data->leftJoin('service_providers_project_mapping','service_providers_project_mapping.service_provider_id','=','service_providers.id');
          $data = $data->where('service_providers.global_sp',1);
          if(count($projects)>0) {
            $data = $data->whereIn('service_providers_project_mapping.project_id',$projects);
          }
          if($request->session()->has('selected_role'))
          {
            $selected_role=$request->session()->get('selected_role');
            $data = $data->whereIn('user_type',$selected_role)->where('project_user_id', $logedin_user->project_user_id);
          } else { // project_id, user_type = admin
            $data = $data->where('user_type', 'sp_admin');
          }
          $data = $data->groupBy('service_providers.id');
            $data = $data->get();
          $this->data['total_user'] = $data->count();
      }
      return view($this->view_path.'.pspalist',['data'=>$this->data]);
    }

    /**
     * GET users/userPSPAListAjax/{id?}
     *
     * Return JSON response public service provider Users listing - Ajax.
     * Return a JSON collection of list of users belonging to the public service provider respective of the logged in user type with total count and also filter results by user-type/role, search query.
     *
     * @authenticated
     * @group Users
     *
     * @responseField data array
     * @response 200 {data:[]}
     */
    public function userPSPAListAjax(Request $request)
    {
      Session::forget('user_info');
      Session::forget('asset_categories_contacts');
      Session::forget('service_provider_id');
      Session::forget('edit_spa_user');
      Session::forget('worker_privilege');

      $logedin_user=auth()->user();
      if($request->ajax()) //Check if ajax request is received
      {
        $searchValue=$request->search_text;
        $data = User::select('users.*','cities.name_en as city_name_en','cities.name_ar  as city_name_ar')->leftJoin('service_providers','service_providers.id','=','users.service_provider');
        /*************for osool admin if user type selected*******************/
        if($logedin_user->user_type == 'super_admin')
        {
          $data = $data->where('service_providers.global_sp',1);
          if($request->session()->has('selected_role'))
          {
            $selected_role=$request->session()->get('selected_role');
            $data = $data->whereIn('user_type',$selected_role)->where('project_user_id', $logedin_user->project_user_id);
          } else { // project_id, user_type = admin
            $data = $data->where('user_type', 'sp_admin');
          }
        }
        if($logedin_user->user_type == 'osool_admin') // For Osool admin
        {
            $projects = Helper::getOsoolAdminProjects($logedin_user->id);
            $data = $data->leftJoin('service_providers_project_mapping','service_providers_project_mapping.service_provider_id','=','service_providers.id');
            if(count($projects)>0) {
              $data = $data->whereIn('service_providers_project_mapping.project_id',$projects);
            }
            $data = $data->where('service_providers.global_sp',1);
            if($request->session()->has('selected_role'))
            {
                $selected_role=$request->session()->get('selected_role');
                $data = $data->whereIn('user_type',$selected_role)->where('project_user_id', $logedin_user->project_user_id);
            } else { // project_id, user_type = admin
              $data = $data->where('user_type', 'sp_admin');
            }
            $data = $data->groupBy('service_providers.id');
        }
        if(!empty($searchValue)) //Check if search filter is requested
        {
            $data = $data->where(function ($query) use ($searchValue) {
            $query->where('users.name','LIKE', "%{$searchValue}%");
            });
        }
        $data=$data->leftJoin("cities",function($join){
          $join->on("cities.id","=","users.city_id");
        })
        ->orderBy('users.id','DESC')
        ->groupBy('users.id')
        ->paginate(10);
        $total_user_count=$data->total();

        if($total_user_count > 0) {
          foreach($data->items() as $key=>$row) {
            $data->items()[$key]->encrypted_id = Crypt::encryptString($data->items()[$key]->id);
          }
        }
        return json_encode(array('data'=>$data,'total_user_count'=>$total_user_count));
      }
    }

    /**
     * GET users/pspa-users-list/{id}
     *
     * Return view public service provider Users listing - view.
     * Return a view with list of users belonging to the public service provider respective of the logged in user type with total count and also filter results by search query.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function pspa_users_list(Request $request,$sp_id)
    {
      Session::forget('user_info');
      Session::forget('asset_categories_contacts');
      Session::forget('service_provider_id');
      Session::forget('edit_spa_user');
      Session::forget('worker_privilege');

      //dd($sp_id);
      $logedin_user=auth()->user();
      $this->data['pageTitle']='User Management';
      //dd($request->search_text);
      $searchValue=$request->search_text;
      $this->data['total_user'] = 0;
      $data = User::select('users.*')->leftJoin('service_providers','service_providers.id','=','users.service_provider');
      // USER TYPE CONDITIONS
      if($logedin_user->user_type == 'super_admin') // For Super Admin
      {
        //$data = $data->where('service_providers.global_sp',1);
        if(Session::has('entered_project_id'))
        {
          $data = $data->whereIn('user_type', ['sp_admin','supervisor','sp_worker'])
                          ->where('service_provider', $sp_id);
        } else { // project_id, user_type = admin
          $data = $data->where('service_providers.global_sp',1);
          $data = $data->whereIn('user_type', ['sp_admin','supervisor','sp_worker'])
                          ->where('service_provider', $sp_id);
        }
        if(!empty($searchValue)) //Check if search filter is requested
        {
            $data = $data->where(function ($query) use ($searchValue) {
            $query->where('users.name','LIKE', "%{$searchValue}%");
            });
        }
        $data = $data->get();
        $this->data['total_user'] = $data->count();
      }
      if($logedin_user->user_type == 'osool_admin') //For Osool Admin
      {
          // $projects = Helper::getOsoolAdminProjects($logedin_user->id);
          // $data = $data->leftJoin('service_providers_project_mapping','service_providers_project_mapping.service_provider_id','=','service_providers.id');

          // if(count($projects)>0) {
          //   $data = $data->whereIn('service_providers_project_mapping.project_id',$projects);
          //   $data = $data->whereIn('users.project_id',$projects);
          // }
          if(Session::has('entered_project_id'))
          {
            $data = $data->whereIn('user_type', ['sp_admin','supervisor','sp_worker'])
                            ->where('service_provider', $sp_id);
          } else { // project_id, user_type = admin
            $data = $data->where('service_providers.global_sp',1);
            $data = $data->whereIn('user_type', ['sp_admin','supervisor','sp_worker'])
                            ->where('service_provider', $sp_id);
          }
          $data = $data->groupBy('service_providers.id');
            $data = $data->get();
          $this->data['total_user'] = $data->count();

      }
      $this->data['service_provider_id'] = $sp_id;
      return view($this->view_path.'.pspa_user_list',['data'=>$this->data]);
    }

    /**
     * GET users/pspa-users-list-ajax/{id?}
     *
     * Return view public service provider Users listing - view ajax.
     * Return a view with list of users in ajax belonging to the public service provider respective of the logged in user type with total count and also filter results by search query.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function pspa_user_list_ajax(Request $request,$sp_id)
    {
      Session::forget('user_info');
      Session::forget('asset_categories_contacts');
      Session::forget('service_provider_id');
      Session::forget('edit_spa_user');
      Session::forget('worker_privilege');

      //dd($sp_id);
      $logedin_user=auth()->user();
      if($request->ajax()) //Check if ajax request is received
      {
        $searchValue=$request->search_text;
        $data = User::select('users.*','cities.name_en as city_name_en','cities.name_ar  as city_name_ar')
                      ->leftJoin('service_providers','service_providers.id','=','users.service_provider');
        /*************for osool admin if user type selected*******************/
        if($logedin_user->user_type == 'super_admin')
        {
          //$data = $data->where('service_providers.global_sp',1);
          if(Session::has('entered_project_id'))
          {
            $data = $data->whereIn('user_type', ['sp_admin','supervisor','sp_worker'])
                          ->where('service_provider', $sp_id);
          } else { // project_id, user_type = admin // main workspace
            $data = $data->where('service_providers.global_sp',1);
            $data = $data->whereIn('user_type', ['sp_admin','supervisor','sp_worker'])
                          ->where('service_provider', $sp_id);
          }
        }
        if($logedin_user->user_type == 'osool_admin') // For Osool admin
        {
            // $projects = Helper::getOsoolAdminProjects($logedin_user->id);
            // $data = $data->leftJoin('service_providers_project_mapping','service_providers_project_mapping.service_provider_id','=','service_providers.id');
            // if(count($projects)>0) {
            //   $data = $data->whereIn('service_providers_project_mapping.project_id',$projects);
            //   $data = $data->whereIn('users.project_id',$projects);
            // }

            if(Session::has('entered_project_id'))
            {
              $data = $data->whereIn('user_type', ['sp_admin','supervisor','sp_worker'])
              ->where('service_provider', $sp_id);
            } else { // project_id, user_type = admin
              $data = $data->where('service_providers.global_sp',1);
              $data = $data->whereIn('user_type', ['sp_admin','supervisor','sp_worker'])
                          ->where('service_provider', $sp_id);
            }
            $data = $data->groupBy('service_providers.id');
        }

        if(!empty($searchValue)) //Check if search filter is requested
        {
            $data = $data->where(function ($query) use ($searchValue) {
            $query->where('users.name','LIKE', "%{$searchValue}%");
            });
        }

        $data=$data->leftJoin("cities",function($join){
          $join->on("cities.id","=","users.city_id");
        });

        /*
        $query = str_replace(array('?'), array('\'%s\''), $data->toSql());
        $query = vsprintf($query, $data->getBindings());
        dump($query);
        die;
        */

        $data=$data->orderBy('users.id','DESC')
        ->groupBy('users.id')
        ->paginate(10);
        $total_user_count=$data->total();

        if($total_user_count > 0) {
          foreach($data->items() as $key=>$row) {
            $data->items()[$key]->encrypted_id = Crypt::encryptString($data->items()[$key]->id);
            $data->items()[$key]->service_provider_id = $sp_id;
          }
        }
        return json_encode(array('data'=>$data,'total_user_count'=>$total_user_count));
      }
    }

    /**
     * GET users/add-user-info
     *
     * Return view to create user step one.
     * Return Step-1 user create form view with data such User Types respective of the logged in user type.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function createInfo($supervisor_id = null)
    {
      Session::forget('user_info');
      Session::forget('asset_categories_contacts');
      Session::forget('service_provider_id');
      Session::forget('edit_spa_user');
      Session::forget('worker_privilege');

      $this->data['pageTitle']='Add new user | user information';

      //Get the country list
      $countryList=Country::where('status',1)->get();
      $this->data['countryList']=$countryList;

      //Get the city list
      $cityList=City::where('status',1)->get();
      $this->data['cityList']=$cityList;
      $this->data['roleOptions']= EnumRole::labels();
      $this->data['proficiencyOptions'] = Proficiency::limitedLabels();
      
      //By default make the values false
      $has_spa = false;
      $has_supervisor = false;

      //Get the authenticated user values
      $users = Auth::user();
      $this->data['user_type'] = $users->user_type;

      //Get the user types according to the logged in usertypes
      $user_types = Helper::getUserTypes($users);
      $this->data['usertypeList'] = $user_types['usertypeList'];
      $this->data['project_name'] = $user_types['project_name'];
      $this->data['project_id'] = $user_types['project_id'];

      //Get existing company array
      $existingcompanyArr = User::existingcompanyList($users);
      $this->data['existingcompanyArr']=$existingcompanyArr;

      //Get company list and supervisors exists or not
      $companyList = User::getcompanyList($users);
      $is_supervisors = $companyList['is_supervisors'];
      $companyList = $companyList['company_list'];
      $this->data['is_supervisors']=$is_supervisors;
      $this->data['companyList']=$companyList;

      //Get the company lisr for SP Admin. I am not sure why its required.
      $companyListSPAmin = ServiceProvider::companyListSPAdmin($users);
      $this->data['companyListSPAmin']=$companyListSPAmin;

      //Get the Project details where user id is 0
      $projectList = ProjectsDetails::getprojectList();
      $this->data['projectList']=$projectList;

      //Get the service providers excluding the array passed
      $companyListforSpadmin = ServiceProvider::companyListForSPAdmin($existingcompanyArr);
      $this->data['companyListforSpadmin']=$companyListforSpadmin;

      //Get user list according to the user type building manager and project user id
      $building_manager_list = User::getUsersWithPUIUserType('building_manager', $users->project_user_id);
      $this->data['building_manager_list']=$building_manager_list;

      //Get user list according to the user type admin and project user id
      $spga_admin_list = User::getUsersWithPUIUserType('admin', $users->project_user_id);
      $this->data['spga_admin_list']=$spga_admin_list;

      //Get SP supervisors list
      $sp_supervisor_list = User::getSPSupervisors($users);
      $this->data['sp_supervisor_list'] = $sp_supervisor_list;

      // If Public service provider is adding his users
      $usertypes_with_user_roles = Helper::getUserTypeWithUserRoles($users, $supervisor_id);
      if(!empty($supervisor_id)){
        $this->data['usertypeList'] = $usertypes_with_user_roles['usertypeList'];
        $this->data['companyList'] = $usertypes_with_user_roles['companyList'];
        $this->data['sp_admin'] = $usertypes_with_user_roles['sp_admin'];
        $this->data['supervisors_list'] = $usertypes_with_user_roles['supervisors_list'];
        $this->data['admin_count'] = $usertypes_with_user_roles['admin_count'];
      }

      //Adding disabled status for usertype list
      $user_type_list = Helper::addDisabledStatusUserTypeList($this->data['usertypeList'], $users, $supervisor_id);
      $this->data['usertypeList'] = $user_type_list['usertypeList'];

      //      dd($this->data['usertypeList']);
      // Get the professions of the Worker
      //$workerProfessions = WorkerProfession::all();
      $workerProfessions = WorkerProfession::where('id', '!=', 10)->orderBy('id')->latest()->get();//10 is id of other profession
      $workerProfessions->push(WorkerProfession::find(10)); //10 is id of other profession
      $this->data['workerProfessions'] = $workerProfessions;

      // Get the Nationalities
      $nationalities = Country::all();
      $this->data['nationalities'] = $nationalities;

      //Redirecting to the respective view according to the user types
      if($users->user_type=='supervisor') //For supervisor
      {
        return view($this->view_path.'.create.create-supervisor',['data'=>$this->data]);
      }
      if($users->user_type=='building_manager')//For building manager
      {
        return view($this->view_path.'.create.create-info',['data'=>$this->data]);
      }
      if($users->user_type=='sp_admin') //For sp admin
      {
        return view($this->view_path.'.create.create-info_sp_admin',['data'=>$this->data]);
      }
      //added for osool admin
      if($users->user_type=='super_admin' || $users->user_type=='osool_admin') //For super admin and osool admin
      {
        //if(empty(Session::get('entered_project_id'))){return redirect()->route('admin.return_user');}
        $project_id = Session::get('entered_project_id');
        if(isset($project_id)) {
          return view($this->view_path.'.create.osool_admin.create-info',['data'=>$this->data]);
        } else {
          if(!Session::has('service_provider_id')) {
            Session::put('service_provider_id', $supervisor_id);
          } else{
            Session::put('service_provider_id', $supervisor_id);
          }
          return view($this->view_path.'.create.public_sp.create-info',['data'=>$this->data]);
        }
      }
      if($users->user_type=='admin' || $users->user_type=='admin_employee') //For admin and admin Employee
      {
        return view($this->view_path.'.create.osool_admin.create-info',['data'=>$this->data]);
      }
    }

    /**
     * GET/POST users/add-user-role
     *
     * Return view to create user step two.
     * Return Step-2 user create form view after storing data from step-1 form in sessions.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */


    public function createRole(Request $request)
    {

      $sp_admin_service_provider = 0;
      // IF THERE ARE NO PARAMETERS PRESENT IN THE REQUEST - REDIRECT USER TO OTHER
      if(count($request->all())==0){
        $session_project_id = Session::get('entered_project_id');
        if(isset($session_project_id)) {
          return redirect()->route('users.create.info');
        } else {
          if(Session::has('service_provider_id')) {
            $session_service_provider_id = Session::get('service_provider_id');
            return redirect(url('/').'/supervisor/create-user/'.$session_service_provider_id);
          } else{
            return redirect()->route('users.create.info');
          }
        }

      }
        $logedin_user = auth()->user();
        $created_user_type="";

        //        dd($request->all());
        if($logedin_user->user_type=='supervisor')
        {
           if(trim($request->phone) != "")
           {
                $validatedData = $request->validate([
                  'worker_name' => 'required',
                //   'phone'=> [
                //   // 'required',
                //     Rule::unique('users')->ignore($request->phone)->whereNull('deleted_at')
                // ],
                  'phone'=> 'nullable',
                  'worker_id'=>'required',
                  'profession'=>'nullable'
              ]);
           }
           else
           {
                  $validatedData = $request->validate([
                    'worker_name' => 'required',
                  //   'phone'=> [
                  //   // 'required',
                  //     Rule::unique('users')->ignore($request->phone)->whereNull('deleted_at')
                  // ],
                    'worker_id'=>'required',
                    'profession'=>'nullable'
                ]);
           }

            $users=Auth::user();
            $data =array(
                'name'=>$request->worker_name,
                'emp_id'=>$request->worker_id,
                'emp_dept'=> $request->profession,
                'project_id'=> $request->project_id,

            );
            if($request->worker_id != null){// @flip1@ if worker id is not null
              $data['worker_id'] = $request->worker_id;
            }
            if($request->phone != null){// @flip1@ if phone is not null
              $data['phone'] = $request->phone;
            }
            if($request->profession_id != null){// @flip1@ if phone is not null
              $data['profession_id'] = $request->profession_id;
            }

            if($request->nationality_id != null){
              $data['country_id'] = $request->nationality_id;
            }

            if($request->favorite_language != null){
              $data['favorite_language'] = $request->favorite_language;
            }
            else
            {
              $data['favorite_language'] = '-';
            }

            /***********************for uploading logo ***********************/
            if($request->hasFile('profile_img'))
            {
                $image = $request->file('profile_img');
                $data['profile_img'] = ImagesUploadHelper::compressedImage($image, 'profile_images');

            }
            else
            {
              $data['profile_img'] = NULL;
            }
            /*******************************************************************/
        
            if ($logedin_user->user_type=='supervisor' || (isset($data['user_type']) && $data['user_type'] == 'sp_worker')) {
                $data['user_type'] = 'sp_worker';
                $data['salary'] = $request->input('salary');
                $data['attendance_target'] = $request->input('attendance_target');
                $data['role'] = $request->input('role');
                $data['admin_level'] = $request->input('admin_level');
                $data['attendance_mandatory'] = $request->input('attendance_mandatory') ?? 0;
                $data['show_extra_info'] = $request->input('show_extra_info') ?? 0;
            }

            $User = new User();
            $User->fill($data);
            $request->session()->put('user_info', $data);

            $contractList=DB::table('contracts')->select('contracts.*', 'projects_details.project_name','projects_details.project_name_ar')
            ->leftjoin('users', 'users.id', '=', 'contracts.user_id')
            ->leftjoin('projects_details', 'projects_details.id', '=', 'users.project_id')
            ->where('contracts.end_date','>=',date('Y-m-d'))
            ->where('contracts.deleted_at',null)


            // $contractList=DB::table('contracts')
            // ->where('end_date','>=',date('Y-m-d'))
            // ->where('is_deleted','no')
            // ->where('deleted_at',null)

            //->where('user_id',$users->project_user_id)
            ->where('contracts.status',1);
            if($logedin_user->user_type != 'sp_admin' && $logedin_user->user_type != 'supervisor') //for users other than sp admin and supervisor
            {
              $contractList = $contractList->where('contracts.user_id',$logedin_user->project_user_id);
            }

            $contractList->whereIn('contracts.id',explode(',',$logedin_user->contract_ids));
            $contractList=$contractList->get();

            $this->data['contractList']=$contractList;


            $this->data['pageTitle']='Add new user | user Role';
            return view($this->view_path.'.create.create-supervisor-role',['data'=>$this->data]);
        }
          // USER TYPE WISE CONDTIONS AND PARAMETERS

        if($logedin_user->user_type=='super_admin' ||  $logedin_user->user_type=='osool_admin' ||$logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee' || $logedin_user->user_type=='building_manager' || $logedin_user->user_type=='building_manager_employee' || $logedin_user->user_type=='sp_admin' || $logedin_user->user_type=='supervisor')
        {
          $created_user_type=$request->user_type;

          if($logedin_user->user_type=='building_manager' || $logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee' || $logedin_user->user_type=='osool_admin')
          {

              if($created_user_type != 'sp_worker')
              {
                $validatedData = $request->validate([
                    'name' => 'required',
                    'email' => 'nullable',
                  //   'phone'=> [
                  //     // 'required',// @flip1@ remove from require
                  //     Rule::unique('users')->ignore($request->phone)->whereNull('deleted_at')
                  // ],
                  'phone'=> 'nullable',
                    'country_id'=>'required',
                    'emp_id'=>'nullable',
                    'emp_dept'=>'nullable',
                    'city_id'=>'required'
                ]);
              }
              else
              {
                //dd('g');
                if(trim($request->phone) != "")
                {
                  $validatedData = $request->validate([
                    'name' => 'required',
                    'email' => 'nullable',
                    // 'phone'=> [
                    // //  'required',
                    //   Rule::unique('users')->ignore($request->phone)->whereNull('deleted_at')
                    // ],
                    'phone'=> 'nullable',
                    'emp_id'=>'nullable',

                  ]);
                }
                else
                {
                  $validatedData = $request->validate([
                    'name' => 'required',
                    'email' => 'nullable',
                    // 'phone'=> [
                    // //  'required',
                    //   Rule::unique('users')->ignore($request->phone)->whereNull('deleted_at')
                    // ],
                    'emp_id'=>'nullable',

                  ]);
                }

              }
          }
          elseif($created_user_type != 'sp_worker')
          {
                  $validatedData = $request->validate([
                  'name' => 'required',
                  'email' => 'nullable',
                //   'phone'=> [
                //     // 'required',
                //     Rule::unique('users')->ignore($request->phone)->whereNull('deleted_at')
                // ],
                'phone'=> 'nullable',
                  'country_id'=>'required',
                  'emp_id'=>'nullable',
                  'emp_dept'=>'nullable',
                  'city_id'=>'required',
                  'user_type'=>'required'
              ]);
          } else {
            if(trim($request->phone) != "")
            {
              $validatedData = $request->validate([
                'name' => 'required',
                'email' => 'nullable',
                // 'phone'=> [
                //    // 'required',
                //     Rule::unique('users')->ignore($request->phone)->whereNull('deleted_at')
                //   ],
                  'emp_id'=>'nullable',
                  'emp_dept'=>'nullable',
                  'user_type'=>'required'
              ]);
            }
            else
            {
              $validatedData = $request->validate([
                'name' => 'required',
                'email' => 'nullable',
                // 'phone'=> [
                //    // 'required',
                //     Rule::unique('users')->ignore($request->phone)->whereNull('deleted_at')
                //   ],
                  'emp_id'=>'nullable',
                  'emp_dept'=>'nullable',
                  'user_type'=>'required'
              ]);
            }
          }

        }

        if(isset($request->supervisor_id)) //if request has supervisor ID
        {
          $validatedData['supervisor_id']= $request->supervisor_id;
        }
        if(isset($request->sp_admin_id)) //if request has SP admin ID
        {
          $validatedData['sp_admin_id']= $request->sp_admin_id;
        }
        if(isset($request->service_provider)) //if request has SP ID
        {
          $validatedData['service_provider']= $request->service_provider;
        }
        else
        {
          $validatedData['service_provider']=$logedin_user->service_provider;
        }
        if(isset($request->user_type) && ($request->user_type == 'building_manager' || $request->user_type == 'building_manager_employee' || $request->user_type == 'sp_admin' || $request->user_type == 'supervisor' || $request->user_type == 'sp_worker'))
        {
          $validatedData['user_type'] =$request->user_type;
        }
          if($logedin_user->user_type=='osool_admin' || $logedin_user->user_type=='super_admin' ) //For osool admin and Super admin
          {
              $validatedData['user_type'] = $request->user_type;
              $validatedData['project_id'] = $request->project_id;
          }
          if($logedin_user->user_type=='building_manager') // for building manager
          {
              $validatedData['user_type'] ='building_manager_employee';
              $validatedData['sp_admin_id'] =$logedin_user->id;
          }
          if($logedin_user->user_type=='sp_admin') // for SP admin
          {
              $validatedData['user_type'] =$request->user_type;
          }
          if($logedin_user->user_type=='supervisor') // for Supervisor
          {
              $validatedData['user_type'] ='sp_worker';
          }
          if($logedin_user->user_type=='admin_employee') // for admin Employee
          {
            if($created_user_type != 'sp_worker')
            {
              $validatedData = $request->validate([
                'name' => 'required',
                'email' => 'nullable',
                // 'phone'=> [
                //   // 'required',
                //   Rule::unique('users')->ignore($request->phone)->whereNull('deleted_at')
                // ],
                'phone'=> 'nullable',
                'country_id'=>'required',
                // 'emp_id'=>'required',
                'emp_dept'=>'nullable',
                'city_id'=>'required',
                'user_type' => ''
              ]);
            }
          }

          $validatedData['user_type'] = $request->user_type;
          if($logedin_user->user_type=='building_manager') {
            $validatedData['user_type'] = 'building_manager_employee';
          }
          if(isset($request->service_provider))
          {
            $validatedData['service_provider'] = $request->service_provider;
          }
          if(isset($request->sp_admin_id))
          {
            $validatedData['sp_admin_id'] = $request->sp_admin_id;
          }
        $users=Auth::user();
        
        if($logedin_user->user_type=='super_admin' || $logedin_user->user_type=='osool_admin') //Super Admin or Osool admin
        {
          if($request->user_type=="supervisor") //If creating a Supervisor
          {
            $sp_admin_id=$request->sp_admin_id;

            $sp_admin_details=DB::table('users')->where('id', $sp_admin_id)->first();

            $sp_admin_service_provider=$sp_admin_details->service_provider;
            $contractsOsool=DB::table('contracts')
            ->where('end_date','>=',date('Y-m-d'))
            ->where('service_provider_id',$sp_admin_service_provider)->get();
            foreach($contractsOsool as $contractsdata)
            {
            //   $regionsArr[]=$contractsdata->region_id;
            //   $cityArr[]=$contractsdata->city_id;

                  if (!empty($contractsdata->region_id)) {
                      $regionsArr[] = explode(',', $contractsdata->region_id);
                  }
                  if (!empty($contractsdata->city_id)) {
                      $cityArr[] = explode(',', $contractsdata->city_id);
                  }
            }

            $regionsArr = !empty($regionsArr) ? array_unique(array_merge(...$regionsArr)) : [];
            $cityArr = !empty($regionsArr) ? array_unique(array_merge(...$cityArr)) : [];
          }
          if($request->user_type=="building_manager_employee") //If creating a building manager employee
          {

            $sp_admin_id=$request->employee_admin_id;
            if(isset($request->sp_admin_id))
            {
              $sp_admin_id=$request->sp_admin_id;
            }
            elseif(isset($request->building_admin))
            {
              $sp_admin_id=$request->building_admin;
            }
            $sp_admin_details=DB::table('users')->where('id', $sp_admin_id)->first();
            $regionsArr=explode(",",$sp_admin_details->role_regions);
            $cityArr=explode(",",$sp_admin_details->role_cities);

          }
        }
        else if($logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee') //Admin
        {
          $regionsArr=array();
          $cityArr=array();
          if($request->user_type=="building_manager_employee")  //If creating a building manager employee
          {
            if(isset($request->employee_admin_id))
            {
              $bm_details = User::where('id', $request->employee_admin_id)->first();
              if(isset($bm_details)) //check data
              {
                $regionsArr = explode(',', $bm_details->role_regions);
                $cityArr = explode(',', $bm_details->role_cities);
              }
            }
          }
          else
          {
            $properties=DB::table('properties')->select('region_id','city_id')
            ->where('user_id',$logedin_user->id)
            ->groupBy('region_id','city_id')->get();
            $regionsArr=array();
            $cityArr=array();
            foreach($properties as $propertiesdata)
            {
              $regionsArr[]=$propertiesdata->region_id;
              $cityArr[]=$propertiesdata->city_id;
            }
          }
        }
        else if($logedin_user->user_type=='sp_admin') // For SP ADMIN
        {
          if($request->user_type=="supervisor")//if creating supervisor
          {
            $logedin_service_provider=$logedin_user->service_provider;
            $contracts=DB::table('contracts')->where('end_date','>=',date('Y-m-d'))->where('service_provider_id',$logedin_service_provider)->get();
            foreach($contracts as $contractsdata)
            {
              $regionsArr[]=explode(',',$contractsdata->region_id);
            }
            $regionsArr = !empty($regionsArr) ? array_unique(array_merge(...$regionsArr)) : [];
            $validatedData = array_unique(array_merge($validatedData, $request->all()));
          }
          else{
            $validatedData = array_merge($validatedData, $request->all());
          }
        }
        else
        {
          $regions=json_decode($users->role_regions);
          if(isset($regions->id))
          {
              $regionsArr=$regions->id;
          }
          else
          {
            $regionsArr=explode(",",$users->role_regions);
          }
          $city=json_decode($users->role_cities);
          if(isset($city->id))//check for city id
          {
            $cityArr=$city->id;
          }
          else
          {
            $cityArr=explode(",",$users->role_cities);
          }
        }  //exit;

        if(empty($regionsArr)||$regionsArr[0]=='')
        {
            $all_region_data=DB::table('regions')->where('is_deleted', 'no')->get();
            foreach ($all_region_data as $key => $value) {
                $regionsArr[]=$value->id;
            }
        }
        if(empty($cityArr)||$cityArr[0]=='')//check empty data
        {
            $all_city_data=DB::table('cities')->where('is_deleted', 'no')->get();
            foreach ($all_city_data as $key => $value) {
                $cityArr[]=$value->id;
            }
        }

        $this->data['filter_regions']=Helper::getUsersRegions($regionsArr);
        $this->data['filter_city']=Helper::getUserCity($cityArr);
        //dd($this->data['filter_regions']);
        /***********************for uploading logo ***********************/
        if($request->hasFile('profile_img')) //Check for image
        {
          $image = $request->file('profile_img');
          $validatedData['profile_img'] = ImagesUploadHelper::compressedImage($image, 'profile_images');
        }
        else
        {
          $validatedData['profile_img'] = NULL;
        }
        /*******************************************************************/

        $User = new User();
        $validatedData['project_id'] = $request->project_id;

        if ($created_user_type == 'building_manager') {
            if (isset($request->bma_area_manager)) {
                $validatedData['is_bma_area_manager']=$request->bma_area_manager=='1' ? true:false;
            }
        }

        if(isset($request->building_admin))
        {
          $validatedData['building_admin'] = $request->building_admin;
        }
        if(isset($request->emp_dept)) //if request has SP ID
        {
          $validatedData['emp_dept']= $request->emp_dept;
        }

        if(isset($request->emp_id) && trim($request->emp_id) != null)
        {
          $validatedData['emp_id']= $request->emp_id;
        }
        if(isset($request->profession_id) && trim($request->profession_id) != null)
        {
          $validatedData['profession_id']= $request->profession_id;
        }
        if($request->nationality_id != null){
          $validatedData['country_id'] = $request->nationality_id;
        }
        if($request->favorite_language != null){
          $validatedData['favorite_language'] = $request->favorite_language;
        }
        else
        {
          $validatedData['favorite_language'] = '-';
        }

          $validatedData['isAkaunting_Vendor']=$request->input('isAkaunting_Vendor')=='1' ? true:false;

        //dd($validatedData);
        if(isset($validatedData['phone']) && $validatedData['phone'] == null){// @flip1@ if phone is comming null
          unset($validatedData['phone']);
        }
        if(isset($validatedData['emp_id']) && $validatedData['emp_id'] == null){// @flip1@ if emp_id is comming null
          unset($validatedData['emp_id']);
        }
        if ($validatedData['user_type'] === 'sp_worker') {
              $validatedData['salary'] = $request->input('salary');
              $validatedData['attendance_target'] = $request->input('attendance_target');
              $validatedData['role'] = $request->input('role');
              $validatedData['admin_level'] = $request->input('admin_level');
              $validatedData['attendance_mandatory'] = $request->input('attendance_mandatory') ?? 0;
              $validatedData['show_extra_info'] = $request->input('show_extra_info') ?? 0;
          }

        $User->fill($validatedData);
        $validatedData['user_type'] = $validatedData['user_type'] ?? $created_user_type ?? null;
          Log::info("1584",['data' => json_encode($validatedData)]);
        $request->session()->put('user_info', $validatedData);

        if($created_user_type=='osool_admin' || $created_user_type=='admin' || $created_user_type=='sp_admin' ||$created_user_type=='admin_employee') // If admins are being created
        {
          $data=$request->session()->get('user_info');
           $this->data['pageTitle']='Add new user | user confirmation';
            $randompw='123456';
            $hashedPassword = Hash::make($randompw);
            $data['password'] = $hashedPassword;
            $data['created_by'] = $logedin_user->id;
            if($created_user_type=='admin') {
              $data['project_id'] = $validatedData['project_id'];
            } elseif(($created_user_type=='admin_employee' || $created_user_type=='sp_admin') && Session::has('entered_project_id')) //For admin employee and sp admin
            {
              $data['project_id'] = $request->project_id;
              $data['project_user_id'] = $logedin_user->project_user_id;
            }

            elseif(($created_user_type=='sp_admin') && !Session::has('entered_project_id')) //For sp admin outside project
            {
              $project_user_id = '';
              $project_id = '';
              // Check is it created by super admin outside a project
              if($logedin_user->user_type=='super_admin') {
                if( !Session::has('entered_project_id') && $created_user_type=='sp_admin' ) {
                  $supervisor_id = Session::get('service_provider_id');
                  //dd($supervisor_id);
                  $is_admin_created = Helper::getServiceProviderAdminIds($supervisor_id);
                  //dd($is_admin_created);
                  if(count($is_admin_created)>0) {
                    $project_user_id = $is_admin_created[0];
                    $user_admin_data = DB::table('users')->select('project_id')->where('id',$project_user_id)->first();
                    if($user_admin_data) {
                      $project_id = $user_admin_data->project_id;
                    }
                  }
                }
              }
              $data['project_id'] = $project_id;
              $data['project_user_id'] = $project_user_id;
            }
            if($created_user_type=='admin_employee' && $logedin_user->user_type=='admin' ){
              $data['project_id'] = $users->project_id;
              $validatedData['project_id'] = $users->project_id;
            }
            // dd($data);
            $data['user_type'] = $validatedData['user_type'];
            if($users->user_type == 'admin') {
              $data['project_user_id'] = $logedin_user->id;
            }
            $existing_user_row=User::where('email',$data['email'])->first();
            if(!$existing_user_row) //Check if user already exists
            {
              if($created_user_type!='sp_admin'){
                $createCustomer = '';
                if($data['user_type']=='admin'){
                      // Create a company using the CRMCompany service
                      $createCustomer = $this->crmCompany->createCompnay([
                          'name'            => $data['name'],
                          'contact'         => $data['phone'],
                          'email'           => $data['email'],
                          'workSpace_name'  => Helper::getProjectName($data['project_id']),
                          'password_switch' => 'on',
                          'password'        => '123456',
                      ]);
                      Log::info([$createCustomer]);

                }
                Log::info("1653",['data' => json_encode($data)]);
                $created_user=User::create($data);
                if($createCustomer!= '' && isset($createCustomer['data']['id'])){
                  Log::info([$createCustomer]);
                    CrmUser::create([
                      'user_id' => $created_user->id, // user_id from created user
                      'crm_user_id' => $createCustomer['data']['id'] // Use the dynamic crm_user_id passed to the controller
                  ]);
                }

              }
            }
            else
            {
                $created_user=$existing_user_row;
            }
            if($created_user_type!='sp_admin'){

            $created_user_id=$created_user->id;
            if($created_user_type == 'admin') //for admin
            {
              User::where('id', $created_user_id)
                  ->update([
                    'project_id' => Session::get('entered_project_id'),
                    'project_user_id' => $created_user_id,
                  ]);
                  $user = Auth::user();
                  $user->project_user_id = $created_user_id;
                  $user->save();
              $user_projects_data = [
                'user_id' => $created_user_id,
                'project_id' => Session::get('entered_project_id'),
                'status' => 1
              ];
              DB::table('user_projects')->insert($user_projects_data);

              $wtf_data = array(
                'user_id'=>$created_user_id,
                'start_time'=>'08:00:00',
                'end_time'=>'20:00:00',
                'sunday'=>1,
                'monday'=>1,
                'tuesday'=>1,
                'wednesday'=>1,
                'thursday'=>1,
                'friday'=>0,
                'saturday'=>1,
                'created_at'=>date('Y-m-d H:i:s'),
                //'last_ip'=>$ipaddress
              );
              $id = DB::table('work_time_frame')->where('user_id',$created_user_id)->first();
              if(!empty($id))
              {
                $wtf_data['modified_at'] = date('Y-m-d H:i:s');
                DB::table('work_time_frame')->where('id',$id->id)->update($wtf_data);
              }
              else
              {
                $wtf_data['created_at'] = date('Y-m-d H:i:s');
                DB::table('work_time_frame')->insert($wtf_data);
              }

              // Add the default checklist if the the industyr type is Security and Services
              ChecklistHelper::createChecklistAndSubtasks($created_user_id);

            }
            $user_id_str=base64_encode($created_user_id);
              /******************************send email to user *******************************/
              $slug = 'registration-successfully';
              $variable_value=[
                  '##USERNAME##'=>"{$data['name']}",
                  '##EMAIL##'=>"{$data['email']}",
                  '##PASSWORD##'=>"123456",
                  '##SITELINK##'=>"<a href='".route('reset_password',$user_id_str)."' alt='Osool Link'>".url('/')."</a>",
              ];
              $mail_content=Helper::emailTemplateMail($slug,$variable_value);
              $sender_email=Helper::getAdminContactMail();
              $to_name= $data['name'];
              $to_email= $data['email'];
              $subject="Registration Successful";
              if($to_email!='') //check for null email id of receiver
              {
                try {
                  Mail::send('mail.send_email', ['mail_content' => $mail_content],function($message) use ($to_name, $to_email,$subject,$sender_email) {
                    $message->to($to_email, $to_name)
                    ->subject($subject);
                    $message->from($sender_email,'Osool Team');
                    });
                } catch (\Throwable $e) {
                  # code...
                }
              }
              event(new VarificationEmail($to_name, $to_email,$subject,$sender_email));
              //dd('ami');
              /*******************************************************************************/
              $request->session()->forget('user_info');
              $this->data['name']=$data['name'];
              $this->data['created_user_type']=$created_user_type;
              $created_user_id;
              Helper::updateWorkOrders($created_user_id);
              if(Session::has('service_provider_id') && !Session::has('entered_project_id')) {
                $this->data['service_provider'] = Session::get('service_provider_id');
                return view($this->view_path.'.create.public_sp.create-confirmation', ['data' => $this->data]);
              } else{
                // @flip1@ redirect to previleg page
                if($created_user_type=='sp_admin'){
                  $this->data['pageTitle']='Add new user | user privileges';
                  $this->data['project_id']=$request->project_id;
                  $this->data['created_user_type']='sp_admin';
                  // $this->data['sp_admin_id']=$created_user_id;
                    Log::info("1763",['data' => json_encode($data)]);
                  $request->session()->put('user_info', ['created_user_type_id'=>$created_user_id, 'user_type'=>'sp_admin', 'name'=>$to_name]);
                  return view($this->view_path.'.create.osool_admin.create-contract-previleges',['data'=>$this->data]);
                }
                return view($this->view_path.'.create.create-confirmation',['data' => $this->data]);
              }

            }
            else{

              $this->data['pageTitle']='Add new user | user privileges';
                  $this->data['project_id']=$request->project_id;
                  $this->data['created_user_type']='sp_admin';
                  // $this->data['sp_admin_id']=$created_user_id;
                   Log::info("1776",['data' => json_encode($data)]);
                  $request->session()->put('user_info', ['data'=>$data, 'user_type'=>'sp_admin' ]);
              return view($this->view_path.'.create.osool_admin.create-contract-previleges',['data'=>$this->data]);

            }


        }


        $categoryList = DB::table('asset_categories')
                            ->select('asset_categories.*')
                            //->join('priorities','priorities.id','=','asset_categories.priority_id')
                            ->where('asset_categories.is_deleted','=','no')
                            ->where('asset_categories.deleted_at','=',NULL)
                            //->where('priorities.is_deleted','=','no')
                            ->where(['asset_categories.user_id' => Auth::user()->project_user_id]);
                            //->where(['priorities.user_id' => Auth::user()->project_user_id]);

        //dd($categoryList->get());
        // FILTERING AS PER TYPE OF USER
        if($logedin_user->user_type=='building_manager') // FOr Building manager
        {
          $assigned_cat=$users->asset_categories;
          if(!empty($assigned_cat))
          {
          $assigned_cat_arr=explode(',', $assigned_cat);
          $categoryList->whereIn('asset_categories.id',$assigned_cat_arr);
          }
        }
        elseif($logedin_user->user_type=='admin') // FOr Building manager
        {
          if($request->user_type=="building_manager_employee") //if creating supervisor
          {
            $bm_details = User::select('asset_categories')->where('id', $request->building_admin)->first();
            $categoryList->whereIn('asset_categories.id',explode(',', $bm_details->asset_categories));
          }
        }
        elseif($logedin_user->user_type=='osool_admin') // FOr Building manager
        {
          if($request->user_type=="building_manager_employee") //if creating supervisor
          {
            $bm_details = User::select('asset_categories')->where('id', $request->building_admin)->first();
            $categoryList->whereIn('asset_categories.id',explode(',', $bm_details->asset_categories));
          }
        }
        else if($logedin_user->user_type=='super_admin') // FOr super admin
        {
          if($request->user_type=="building_manager_employee") //if creating supervisor
          {
            $bm_details = User::select('asset_categories')->where('id', $request->building_admin)->first();
            $categoryList->whereIn('asset_categories.id',explode(',', $bm_details->asset_categories));
          }
          if($request->user_type=="supervisor") //if creating supervisor
          {
            if(!empty($contractsOsool))
            {
              foreach($contractsOsool as $contractsdata)
              {
                $assigned_cat=$contractsdata->asset_categories;
              }
              if(!empty($assigned_cat))//check empty category data
              {
                $assigned_cat_arr=explode(',', $assigned_cat);
                $categoryList->whereIn('asset_categories.id',$assigned_cat_arr);
              }
            }
          }
          if($request->user_type=="sp_worker") //if creating a worker
          {
            $supervisor_id=$request->supervisor_id;
            $supervisor_details=DB::table('users')->where('id', $supervisor_id)->first();
            $assigned_cat=$supervisor_details->asset_categories;
            if(!empty($assigned_cat))
            {
              $assigned_cat_arr=explode(',', $assigned_cat);
              $categoryList->whereIn('asset_categories.id',$assigned_cat_arr);
            }
          }
        }
        $categoryList=$categoryList->get();
        $this->data['categoryList']=$categoryList;

        $buildingList=DB::table('property_buildings')
        ->where('is_deleted','no');
        if($logedin_user->user_type=='building_manager')  // FOr Building manager
        {
            $assigned_buildings=$users->building_ids;
            if(!empty($assigned_buildings))
            {
            $assigned_buildings_arr=explode(',', $assigned_buildings);
            $buildingList->whereIn('id',$assigned_buildings_arr);
            }
        }
        elseif($logedin_user->user_type=='admin') // FOr Admin
        {
          if($request->user_type == "sp_worker")
          {
            $supervisor_details = User::where('id', $request->supervisor_id)->first();
            $assigned_buildings=$supervisor_details->building_ids;
            if(!empty($assigned_buildings))
            {
              $assigned_buildings_arr=explode(',', $assigned_buildings);
              $buildingList->whereIn('id',$assigned_buildings_arr);
            }
          }
        }
        else if($logedin_user->user_type=='super_admin') // FOr Super admin
        {
          if($request->user_type=="supervisor") //if creating supervisor
          {
            $assigned_buildings=$sp_admin_details->building_ids;
            if(!empty($assigned_buildings))
            {
              $assigned_buildings_arr=explode(',', $assigned_buildings);
              $buildingList->whereIn('id',$assigned_buildings_arr);
            }
          }
          if($request->user_type=="sp_worker") //if creating worker
          {
            $assigned_buildings=$supervisor_details->building_ids;
            if(!empty($assigned_buildings))
            {
              $assigned_buildings_arr=explode(',', $assigned_buildings);
              $buildingList->whereIn('id',$assigned_buildings_arr);
            }
          }
        }
        else if($logedin_user->user_type=='sp_admin') //for sp admin
        {
          if($request->user_type=="supervisor")//if creating supervisor
          {
            $assigned_buildings=$logedin_user->building_ids;
            if(!empty($assigned_buildings))
            {
              $assigned_buildings_arr=explode(',', $assigned_buildings);
              $buildingList->whereIn('id',$assigned_buildings_arr);
            }
          }
          if($request->user_type=="sp_worker" || $request->user_type=="team_leader") //if creating worker
          {
            $supervisor_id=$request->supervisor_id;
            $supervisor_details=DB::table('users')->where('id', $supervisor_id)->first();
            
            $assigned_buildings=$supervisor_details->building_ids;
            if(!empty($assigned_buildings))
            {
              $assigned_buildings_arr=explode(',', $assigned_buildings);
              $buildingList->whereIn('id',$assigned_buildings_arr);
            }
          }
        }
        $buildingList=$buildingList->get();
        $this->data['buildingList']=$buildingList;
        //dd('yes');
        $contractList=DB::table('contracts')->select('contracts.*', 'projects_details.project_name', 'projects_details.project_name_ar')
        ->leftjoin('users', 'users.id', '=', 'contracts.user_id')
        ->leftjoin('projects_details', 'projects_details.id', '=', 'users.project_id')->where('contracts.end_date','>=',date('Y-m-d'));

        if($logedin_user->user_type != 'sp_admin' && $logedin_user->user_type != 'supervisor' && Session::has('entered_project_id')) //for users other than sp admin and supervisor
        {
          $contractList = $contractList->where('contracts.user_id',$logedin_user->project_user_id);
        }

        $contractList = $contractList->where('contracts.is_deleted','no')->where('contracts.status',1);

        if($logedin_user->user_type=='sp_admin') // for sp admin
        {
          $logedin_service_provider=$logedin_user->service_provider;
          $contractList = $contractList->where('service_provider_id', $logedin_service_provider);
          if($request->user_type=="sp_worker" || $request->user_type=="team_leader") //if creating worker
          {
            $supervisor_id=$request->supervisor_id;
            $supervisor_details_sp_admin=DB::table('users')->whereIn('id', $supervisor_id)->get();// @flip1@ get all selected sps data
            $assigned_contracts='';
            foreach ($supervisor_details_sp_admin as $sps) {// @flip1@ for multiple SPS concate all contract id.
              if($sps->contract_ids != '' && $sps->contract_ids != null){
                $assigned_contracts .=  ','.$sps->contract_ids;
              }
            }
            // $assigned_contracts=$supervisor_details_sp_admin->contract_ids;
            if(!empty($assigned_contracts))
            {
              $assigned_contracts_arr=explode(',', $assigned_contracts);
              $contractList->whereIn('contracts.id',$assigned_contracts_arr);
            }
          }
          else if($request->user_type=="supervisor") //if creating supervisor
          {
            $contractList->where('contracts.service_provider_id', $request->service_provider);
          }
          else
          {
            $assigned_contracts=$users->contract_ids;
            if(!empty($assigned_contracts))//check for empty contract data
            {
              $assigned_contracts_arr=explode(',', $assigned_contracts);
              $contractList->whereIn('contracts.id',$assigned_contracts_arr);
            }
          }
        }
        else if($logedin_user->user_type=='super_admin' || $logedin_user->user_type=='osool_admin' || $logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee') // For admins
        {
          if($request->user_type=="supervisor") //if creating supervisor
          {
            $contractList->where('contracts.service_provider_id', $request->service_provider);
          }
          if($request->user_type=="sp_worker" || $request->user_type=="team_leader") //if creating worker
          {
            //dd($request->supervisor_id);
            if(is_array($request->supervisor_id))
            {
              $supervisor_details=DB::table('users')->whereIn('id', $request->supervisor_id)->get();
            }
            else
            {
              $supervisor_details=DB::table('users')->where('id', $request->supervisor_id)->get();
            }
            $assigned_contracts='';
            foreach ($supervisor_details as $sps) {// @flip1@ for multiple SPS concate all contract id.
              if($sps->contract_ids != '' && $sps->contract_ids != null){
                $assigned_contracts .=  ','.$sps->contract_ids;
              }
            }
            if(!empty($assigned_contracts))
            {
                $assigned_contracts_arr=explode(',', $assigned_contracts);
                $contractList->whereIn('contracts.id',$assigned_contracts_arr);
            }
          }
        }
        else if ($logedin_user->user_type=='supervisor') //for supervisors
        {
          //dd('yes');
          if($data['user_type']=="sp_worker" || $data['user_type'] =="team_leader") //if creating worker
          {
            $contractList->whereIn('contracts.id',explode(',',$logedin_user->contract_ids));
          }
        }

        /*
          $query = str_replace(array('?'), array('\'%s\''), $contractList->toSql());
          $query = vsprintf($query, $contractList->getBindings());
          dump($query);
          die;
        */



        $contractList=$contractList->get();
      //        dd($contractList->last());
        $this->data['contractList']=$contractList;

        $subcontracts = Contracts::query()
            ->whereIn('parent_contract_id', $contractList->pluck('id')->toArray())
            ->get();

        $this->data['subContractIdList'] = array_keys(
            array_filter(
                $subcontracts->pluck('id', 'parent_contract_id')->toArray() ?? []
            )
        );

        $this->data['pageTitle']='Add new user | user Role';
        $this->data['project_id']=$request->project_id;
        if(isset($sp_admin_id)){
        $this->data['admin_id']=$sp_admin_id;

        }
        else{
          $this->data['admin_id']='';

        }
        $this->data['user_type'] = '';
        if(isset($request->user_type))
        {
          $this->data['user_type'] = $request->user_type;
        }

        if ($request->user_type == 'store_keeper'){
            $allWarehousesList= [];
            $companyID=Auth::user()->projectDetails->projectCompany->company_id;
            if ($request->has('service_provider')){
                $companyID= ServiceProvider::where('id','=',$request->service_provider)->first()->serviceProviderAdmin->userCompany->company_id;
            }
            $allWarehousesList = WorkorderHelper::getAllWarehousesByCompanyId($companyID);
            $allWarehousesList = WarehouseData::collection($allWarehousesList);
            $allWarehousesList = $allWarehousesList->all();
            $this->data['warehouses']= $allWarehousesList;
        }


        return view($this->view_path.'.create.create-role',['data'=>$this->data, 'sp_admin_service_provider' => $sp_admin_service_provider]);
    }


























































    /**
     * GET/POST users/create_worker_password
     *
     * Return view to create user step three.
     * Return Step-4  user create form view for storing the password after storing data recieved from step-3 form in sessions.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function create_worker_password(Request $request)
    {
       Log::info("2143",['data' => json_encode(Session::get('user_info'))]);
      $worker_privilege = array($request->work_qr, $request->report_maintenance_checkbox);
      if($request->session()->get('user_info')['user_type'] =='team_leader')// if creating supervisor
          {
              $worker_privilege =
              [
                $request->lead_action_on_behalf_of_workers,
                $request->lead_scan_on_behalf_of_workers,
                $request->lead_report_maintenance_issues_behalf_of_workers
              ];
          }
      $request->session()->put('worker_privilege',$worker_privilege);
      $this->data['project_id']=$request->project_id;
      return view($this->view_path.'.create.osool_admin.create-supervisor-password',['data'=>$this->data]);
    }

    /**
     * GET/POST users/add-user-privileges
     *
     * Return view to create user step three.
     * Return Step-4 user create form view for storing the password after storing data recieved from step-23 form in sessions.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function edit_worker_password(Request $request, $id)
    {
      $id = Crypt::decryptString($id);

      $this->data['u_data']=User::where('id', $id)->first();
      $worker_privilege = array($request->work_qr, $request->report_maintenance_checkbox);
      if($this->data['u_data']['user_type'] =='team_leader')// if creating supervisor
          {
              $worker_privilege =
              [
                $request->lead_action_on_behalf_of_workers,
                $request->lead_scan_on_behalf_of_workers,
                $request->lead_report_maintenance_issues_behalf_of_workers
              ];
          }
      $request->session()->put('worker_privilege',$worker_privilege);
      $this->data['project_id']=$request->project_id;
      return view($this->view_path.'.edit.osool_admin.create-supervisor-password',['data'=>$this->data]);
    }

    /**
     * GET/POST users/add-user-privileges
     *
     * Return view to create user step three.
     * Return Step-3  user create form view for storing the privileges after storing data recieved from step-2 form in sessions.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function createPrivileges(Request $request)
    {
    Log::info("2204",['data' => json_encode(Session::get('user_info'))]);
        if(count($request->all())==0)//check for request parameters
        {
              $session_project_id = Session::get('entered_project_id');
              if(isset($session_project_id)) {
                return redirect()->route('users.create.info');
              } else {
                if(Session::has('service_provider_id')) {
                  $session_service_provider_id = Session::get('service_provider_id');
                  return redirect(url('/').'/supervisor/create-user/'.$session_service_provider_id);
                } else{
                  return redirect()->route('users.create.info');
                }
              }
        }
         $logedin_user=auth()->user();
         $user_info=$request->session()->get('user_info');

        if($logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee' || $logedin_user->user_type=='sp_admin'){

          if (($user_info && (is_array($user_info) ? isset($user_info['user_type']) && $user_info['user_type'] == 'procurement_admin' : (is_object($user_info) && isset($user_info->user_type) && $user_info->user_type == 'procurement_admin')))) {
           
            session()->put('initiate_status', $request->input('initiate_status',1));
            session()->put('send_status', $request->input('send_status',1));
            session()->put('receive_status', $request->input('receive_status',1));
            session()->put('app_reject_status', $request->input('app_reject_status',1));
            session()->put('manage_status', 1);
            session()->put('createpo_status', $request->input('createpo_status',1));
            session()->put('auto_purchase', $request->input('auto_purchase',1));
            session()->put('max_ammount', $request->max_ammount);
          }

        }
       
        if($request->role_cities) //check parameter
        {
            $user_info['role_cities']= implode(",", $request->role_cities);
        }
        if($request->role_regions)//check parameter
        {
           $user_info['role_regions']= implode(",", $request->role_regions);
        }
        if($request->warehouse)//check parameter
        {
           $user_info['keeper_warehouses']= implode(",", $request->warehouse);
        }
        if($request->asset_categories)//check parameter
        {
           if($user_info['user_type'] == 'supervisor' || $user_info['user_type'] == 'sp_worker') //if creating worker or supervisor
           {
            $arr = [];
            foreach($request->asset_categories as $sel_cat) {
                 $arr[] = explode(',',$sel_cat)[1];
            }
            $user_info['asset_categories']= implode(",", array_unique($arr));
            $user_info['asset_categories_contacts']= $request->asset_categories;
           } else {
            $user_info['asset_categories']= implode(",", $request->asset_categories);
           }
        }
        //For Worker Set SP ADMIN
        if($user_info['user_type'] == 'sp_worker' || $user_info['user_type'] == 'team_leader') {

          if(isset($user_info['supervisor_id'])) {
            $sp_admin_detail = DB::table('users')->select('sp_admin_id')->where('user_type','supervisor')->where('id',$user_info['supervisor_id'])->first();
            $user_info['sp_admin_id'] =  $sp_admin_detail->sp_admin_id;
          }
          // for logged in supervisor

          if( $logedin_user->user_type == 'supervisor') {
            $user_info['sp_admin_id'] =  $logedin_user->sp_admin_id;
          }
        }
        if($request->building_ids)//check parameter
        {
            /************insert propery ids ******************/
            $propList=DB::table('property_buildings')->whereIn('id',$request->building_ids)->get();
            if(isset($propList))//check data
            {
                $prp_arr=array();
                foreach ($propList as $key => $value) {
                  $prop_arr[]= $value->property_id;
                }
                $user_info['properties']= implode(",", array_unique($prop_arr));
            }
            /**************************************************/
           $user_info['building_ids']= implode(",", $request->building_ids);
        }

        if($request->contract_ids)//check parameter
        {
           $user_info['contract_ids']= implode(",", $request->contract_ids);
        }

        if($user_info['user_type'] == 'team_leader')
        {
            $user_info['assigned_workers']= implode(",", $request->assigned_workers);
        }

        if ($request->has('is_subcontractors_worker')) {
            $user_info['is_subcontractors_worker'] = $request->boolean('is_subcontractors_worker');
        }

        $user_info['status']=$request->status;
        Log::info("2308",['data' => json_encode($user_info)]);
        $request->session()->put('user_info', $user_info);
        $this->data['pageTitle']='Add new user | user privileges';
        $this->data['project_id']=$request->project_id;
        $logedin_user=auth()->user();
        if(($logedin_user->user_type=='osool_admin' || $logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee') && ( $user_info['user_type'] != 'building_manager' && $user_info['user_type'] != 'building_manager_employee' ) && ($user_info['user_type'] != 'supervisor') && ($user_info['user_type'] != 'sp_worker') && ($user_info['user_type'] != 'team_leader'))//for osool admin,admin,admin employee,bm,bme,supervisor not creating worker
        {
          return view($this->view_path.'.create.create-previleges',['data'=>$this->data]);
        }


        if(($logedin_user->user_type=='osool_admin' || $logedin_user->user_type=='admin_employee') && $user_info['user_type'] == 'supervisor') //if creating supervisor
        {
          return view($this->view_path.'.create.osool_admin.create-contract-previleges',['data'=>$this->data]);
        }
        if($logedin_user->user_type=='osool_admin' && ($user_info['user_type'] == 'sp_worker' || $user_info['user_type'] == 'team_leader'))//if osool admin creating worker
         {
          return view($this->view_path.'.create.osool_admin.create-contract-previleges',['data'=>$this->data]);

         // return view($this->view_path.'.create.osool_admin.create-supervisor-password',['data'=>$this->data]);
        }
        if($logedin_user->user_type=='osool_admin' && $user_info['user_type'] == 'building_manager_employee')//if osool admin is creating bme
         {
          return view($this->view_path.'.create.create-building-previleges',['data'=>$this->data]);
        }

        if($user_info['user_type']=='building_manager') //if user is building manager
        {
       
          $this->data['user_type'] = $user_info['user_type'];
          return view($this->view_path.'.create.osool_admin.create-contract-previleges-bm',['data'=>$this->data]);
            $data=$request->session()->get('user_info');
            $created_user_type =  $logedin_user->user_type;
            $this->data['pageTitle']='Add new user | user confirmation';
            $randompw='123456';
            $hashedPassword = Hash::make($randompw);
            $data['password'] = $hashedPassword;
            $data['created_by'] = $logedin_user->id;
            $data['project_id'] = $request->project_id;
            $data['project_user_id'] = $logedin_user->project_user_id;
            $data['user_type'] = $user_info['user_type'];

            $existing_user_row=User::where('email',$data['email'])->first();
            if(!$existing_user_row)//check existing email
            {
               $created_user=User::create($data);
            }
            else
            {
                $created_user=$existing_user_row;
            }
            $created_user_id=$created_user->id;
            $user_id_str=base64_encode($created_user_id);
            /******************************send email to user *******************************/
            $slug = 'registration-successfully';
            $variable_value=[
                '##USERNAME##'=>"{$data['name']}",
                '##EMAIL##'=>"{$data['email']}",
                '##PASSWORD##'=>"123456",
                '##SITELINK##'=>"<a href='".route('reset_password',$user_id_str)."' alt='Osool Link'>".url('/')."</a>",
            ];
            $mail_content=Helper::emailTemplateMail($slug,$variable_value);
            $sender_email=Helper::getAdminContactMail();
            $to_name= $data['name'];
            $to_email= $data['email'];
            $subject="Registration Successful";
            if($to_email!='')//check empty email id
            {
              try{
                Mail::send('mail.send_email', ['mail_content' => $mail_content],function($message) use ($to_name, $to_email,$subject,$sender_email) {
                  $message->to($to_email, $to_name)
                  ->subject($subject);
                  $message->from($sender_email,'Osool Team');
                  });
                  event(new VarificationEmail($to_name, $to_email,$subject,$sender_email));

              }
            catch (\Exception $e) {
              $request->session()->put('emailNotSent', true);
              dd($e);
            }
          }

            /*******************************************************************************/

            $request->session()->forget('user_info');
            $this->data['name']=$data['name'];
            $this->data['created_user_type']=$created_user_type;
            return view($this->view_path.'.create.create-confirmation',['data'=>$this->data]);
        }
        if($logedin_user->user_type=='sp_admin') //for sp admin
        {
          if($user_info['user_type']=='supervisor') //if creating supervisor
          {
              return view($this->view_path.'.create.osool_admin.create-contract-previleges',['data'=>$this->data]);
          }
          elseif ($user_info['user_type'] == 'store_keeper') {
              return view($this->view_path . '.create.create-previleges', ['data' => $this->data]);
          }
          elseif($user_info['user_type']=='sp_worker' || $user_info['user_type']=='team_leader') //if creating worker
          {
            return view($this->view_path.'.create.osool_admin.create-contract-previleges',['data'=>$this->data]);

              return view($this->view_path.'.create.osool_admin.create-supervisor-password',['data'=>$this->data]);
          }
        }
        if($logedin_user->user_type=='supervisor') //for supervisor
        {
          return view($this->view_path.'.create.osool_admin.create-contract-previleges',['data'=>$this->data]);
            return view($this->view_path.'.create.create-supervisor-password',['data'=>$this->data]);
        }
        if($logedin_user->user_type=='super_admin' || $logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee') //for admins
        {
            if($user_info['user_type']=='admin'||$user_info['user_type']=='admin_employee') //for admin and admin employee
            {
                return view($this->view_path.'.create.create-previleges',['data'=>$this->data]);
            }
            elseif($user_info['user_type']=='supervisor') //if creating supervisor
            {
                return view($this->view_path.'.create.osool_admin.create-contract-previleges',['data'=>$this->data]);
            }
            elseif($user_info['user_type']=='sp_worker' || $user_info['user_type']=='team_leader') //if creating worker
            {

              return view($this->view_path.'.create.osool_admin.create-contract-previleges',['data'=>$this->data]);

               // return view($this->view_path.'.create.osool_admin.create-supervisor-password',['data'=>$this->data]);
            }
            elseif($user_info['user_type']=='building_manager_employee') //if creating building manager employee
            {
              if($logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee')
              {
                if(trim($user_info['building_admin']) != "")
                {
                  $get_ba_details = DB::table('users')->select('id','user_privileges')->where('id',$user_info['building_admin'])->first();
                  if(isset($get_ba_details) && !empty($get_ba_details))
                  {
                    $this->data['ba_id'] = $get_ba_details->id;
                        if(trim($get_ba_details->user_privileges) != "")
                        {
                          $this->data['ba_user_privileges'] = json_decode($get_ba_details->user_privileges,true);
                        }
                        else
                        {
                          $this->data['ba_user_privileges'] = array();
                        }
                      }
                      else
                      {
                        $this->data['ba_user_privileges'] = array();
                      }
                }
                else
                {
                  $this->data['ba_user_privileges'] = array();
                }
              }
              else
              {
                $this->data['ba_user_privileges'] = array();
              }

                return view($this->view_path.'.create.create-building-previleges',['data'=>$this->data]);
            }
            elseif($user_info['user_type']=='building_manager') //if creating building manager
            {
              return view($this->view_path.'.create.create-building-previleges',['data'=>$this->data]);
            }
            elseif($user_info['user_type']=='sp_admin') //if creating sp admin
            {
              return view($this->view_path.'.create.create-building-previleges',['data'=>$this->data]);
            }
        }
        if($logedin_user->user_type=='building_manager') //for building manager
        {
           return view($this->view_path.'.create.create-building-previleges',['data'=>$this->data]);
        }
    }

    /**
     * GET/POSt users/add-user-confirmation
     *
     * Return view to create user step four.
     * Return Step-4  user create form view for confirming user creating after storing data from sessions to database and sending the respectove email notification to the newly created user's email id.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function createConfirmation(Request $request)
    {
      // IF NO PARAMETERS IN THE REQUEST REDIRECT TO THE USERS PAGE
      if(count($request->all())==0){
        if(!empty(Auth::user()->user_type == 'super_admin') || !empty(Auth::user()->user_type == 'osool_admin')){
          return redirect()->route('serviceproviders.list');
        }else
        {
          return redirect()->route('users.create.info');
        }
      }
      $logedin_user=auth()->user();
      $data = $request->session()->get('user_info');
      if(!isset($data['sp_admin_id']))
      {
        if(isset($data['building_admin']))
        {
          $data['sp_admin_id'] = $data['building_admin'];
        }
      }
      //dd($data);
      if(empty($data))
      {
        if(!empty(Auth::user()->user_type == 'super_admin') || !empty(Auth::user()->user_type == 'osool_admin')){
          return redirect()->route('serviceproviders.list');
        }else
        {
          return redirect()->route('users.create.info');
        }
      }
      $created_user_type= $data['user_type'];

      if($created_user_type == 'supervisor' || $created_user_type == 'building_manager' || $created_user_type == 'building_manager_employee' || $created_user_type == 'sp_admin' ||  $created_user_type == 'sp_worker' ||  $created_user_type == 'team_leader') //check logged in user type
      {
        $data['project_user_id'] = $logedin_user->project_user_id;
        $data['project_id'] = $logedin_user->project_id;
      }
      $this->data['pageTitle']='Add new user | user confirmation';
      $user_previleges =[];
        if($logedin_user->user_type=='super_admin' || $logedin_user->user_type=='osool_admin' || $logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee') //for super admin and osool admin
      {
          if($data['user_type'] =='osool_admin' || $data['user_type'] =='admin'|| $data['user_type'] =='admin_employee')//for creating osool admin or admin employee
          {
              $user_previleges =
              [
                  'property'=>$request->property,
                  'contracts'=>$request->contracts,
                  'beneficiary'=>$request->beneficiary,
                  'service_provider'=>$request->service_provider,
                  'tenant'=>$request->tenant,
                  'warehouse'=>$request->warehouse,

              ];
              $data['project_user_id'] = $logedin_user->project_user_id;
          }
          elseif($data['user_type'] =='building_manager' || $data['user_type'] =='building_manager_employee') // if creating building manager employee
          {
              $user_previleges =
              [
                  'workorder'=>$request->workorder,
                  'assets'=>$request->assets,
                  'contracts'=>$request->contracts,
                  'tenant'=>$request->tenant,
                  'warehouse'=>$request->warehouse,

              ];
              if ($data['user_type'] == 'building_manager') {
                  $user_previleges['inventory'] = $request->inventory ?? [];

                  if (is_array($request->inventory) && in_array('view', $request->inventory)) {
                      $data['allow_akaunting'] = 1;
                  } else {
                      $data['allow_akaunting'] = 0;
                  }
              }
              if ($data['user_type'] == 'building_manager_employee') {
                  $buildingAdmin = User::where('id',$data['building_admin'])->value('allow_akaunting');
                  $data['allow_akaunting'] = $buildingAdmin ?? 0;
              }
          }
          elseif($data['user_type'] =='supervisor')// if creating supervisor
          {
              $user_previleges =
              [
                  'contracts'=>$request->contracts
              ];
          }
          elseif($data['user_type'] =='sp_worker')// if creating supervisor
          {
              $user_previleges =
              [
                'work_qr'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[0] : ['no'],
                'workorder'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[1] : ['no']
              ];
          }
          elseif($data['user_type'] =='team_leader')// if creating supervisor
          {
              $user_previleges =
              [
                'lead_action_on_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[0] : ['no'],
                'lead_scan_on_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[1] : ['no'],
                'lead_report_maintenance_issues_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[2] : ['no']
              ];
          }
          else
          {
              $user_previleges =[];
          }
      }
      if($logedin_user->user_type=='admin'|| $logedin_user->user_type=='admin_employee') //for admin
      {
          if($data['user_type'] =='building_manager_employee') // if creating building manager employee
          {
              $user_previleges =
              [
                  'workorder'=>$request->workorder,
                  'assets'=>$request->assets,
                  'contracts'=>$request->contracts,
                  'tenant'=>$request->tenant,
                  'warehouse'=>$request->warehouse,

              ];
          }
          elseif($data['user_type'] =='sp_worker')// if creating supervisor
          {
              $user_previleges =
              [
                'work_qr'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[0] : ['no'],
                'workorder'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[1] : ['no']
              ];
          }
          elseif($data['user_type'] =='team_leader')// if creating supervisor
          {
              $user_previleges =
              [
                'lead_action_on_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[0] : ['no'],
                'lead_scan_on_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[1] : ['no'],
                'lead_report_maintenance_issues_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[2] : ['no']
              ];
          }
           elseif ($data['user_type'] == 'store_keeper')// if creating supervisor
          {
              $user_previleges =
                  [
                    'warehouse'=>$request->warehouse,
                  ];
          }
          elseif ($data['user_type'] == 'procurement_admin') {
            Log::info('user privielege1');
            $user_previleges =
                [
                    'purchases'=>$request->purchases,
                    'warehouses' => $request->warehouses,
                    'inventory'=>$request->inventory,
                    'vendor'=>$request->vendor,
                    'workorder'=>$request->workorder,
                ];
        }
          else
          {
            $user_previleges =
            [
                'property'=>$request->property,
                'contracts'=>$request->contracts,
                'beneficiary'=>$request->beneficiary,
                'service_provider'=>$request->service_provider,
                'tenant'=>$request->tenant,
                'warehouse'=>$request->warehouse,
            ];
          }
          if($data['user_type'] =='admin_employee')//if creating admin employee
          {
              $data['project_user_id'] = $logedin_user->project_user_id;
          }
      }
      if($logedin_user->user_type=='building_manager' || $created_user_type == 'building_manager')//for building manager and building manger employee
      {
          $user_previleges =
          [
              'contracts'=>$request->contracts,
              'tenant'=>$request->tenant

          ];
      }
      if($logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee' || $logedin_user->user_type=='osool_admin' || $logedin_user->user_type=='building_manager') //for admin
      {
          if($data['user_type'] =='building_manager' || $data['user_type'] =='building_manager_employee') // if creating building manager employee
          {
              $user_previleges =
              [
                  'workorder'=>$request->workorder,
                  'assets'=>$request->assets,
                  'contracts'=>$request->contracts,
                  'tenant'=>$request->tenant,
                  'warehouse'=>$request->warehouse,
              ];
          }
          if ($data['user_type'] == 'building_manager') {
            $user_previleges['inventory'] = $request->inventory ?? [];

            if (is_array($request->inventory) && in_array('view', $request->inventory)) {
                $data['allow_akaunting'] = 1;
            } else {
                $data['allow_akaunting'] = 0;
            }
          }
          if (!empty($data['user_type']) && $data['user_type'] == 'building_manager_employee') {
            if (!empty($data['building_admin'])) { // Check if 'building_admin' exists
                $buildingAdmin = User::where('id', $data['building_admin'])->value('allow_akaunting');
                $data['allow_akaunting'] = $buildingAdmin !== null ? $buildingAdmin : 0;
            } else {
                $data['allow_akaunting'] = 0; // Default value if 'building_admin' is missing
            }
          }
          if($data['user_type'] =='admin_employee')//if creating admin employee
          {
              $data['project_user_id'] = $logedin_user->project_user_id;
          }
          if ($data['user_type'] == 'store_keeper')// if creating supervisor
          {
              $user_previleges =
                  [
                  'warehouse'=>$request->warehouse,
                  ];
          }
          if (($logedin_user->user_type=='admin'|| $logedin_user->user_type=='admin_employee') && $data['user_type'] == 'procurement_admin' ) {
            Log::info('user privielege1');
            $user_previleges =
                [
                    'purchases'=>$request->purchases,
                    'warehouses' => $request->warehouses,
                    'inventory'=>$request->inventory,
                    'vendor'=>$request->vendor,
                    'workorder'=>$request->workorder,
                ];
        }
      }
      if($logedin_user->user_type=='sp_admin') //for sp admin
      {
        if($data['user_type'] =='sp_worker')// if creating supervisor
        {
            $user_previleges =
            [
              'work_qr'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[0] : ['no'],
              'workorder'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[1] : ['no']
            ];
        }
        elseif($data['user_type'] =='team_leader')// if creating supervisor
          {
              $user_previleges =
              [
                'lead_action_on_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[0] : ['no'],
                'lead_scan_on_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[1] : ['no'],
                'lead_report_maintenance_issues_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[2] : ['no']
              ];
          }
        elseif ($data['user_type'] == 'store_keeper')// if creating supervisor
          {
              $user_previleges =
                  [
                    'warehouse'=>$request->warehouse
                  ];
          }
          elseif ($data['user_type'] == 'procurement_admin') {
            Log::info('user privielege1');
            $user_previleges =
                [
                    'purchases'=>$request->purchases,
                    'warehouses' => $request->warehouses,
                    'inventory'=>$request->inventory,
                    'vendor'=>$request->vendor,
                    'workorder'=>$request->workorder,
                ];
        }
        else
        {
          $user_previleges =
          [
            'contracts'=>$request->contracts
          ];
        }
      }
      if($logedin_user->user_type=='sp_admin' && $created_user_type=="supervisor") // for sp admin or supervisor
      {
        $data['sp_admin_id']=$logedin_user->id;
        $data['project_id']=$logedin_user->project_id;
      }
      if($logedin_user->user_type=='supervisor' && $created_user_type=="sp_worker") //for supervisor and worker
      {
        $data['supervisor_id'] = $logedin_user->id;
        $data['project_id']=$logedin_user->project_id;
        $data['service_provider']=$logedin_user->service_provider;

        $user_previleges =
              [
                'work_qr'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[0] : ['no'],
                'workorder'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[1] : ['no']
              ];
      }
// dd($request->all(),$user_previleges);
      // FORM SUBMISSION
      if($request->isMethod('POST')) // check for form submission
      {
        
//          dd($data);
          // @flip1@ update user data to view/no-view contract
          if($created_user_type == 'sp_admin') //if creating sp_admin
          {

            $data = session()->get('user_info')['data'];
            $data['project_user_id'] = $logedin_user->project_user_id;
            $data['project_id'] = $logedin_user->project_id;
            //dd($data);
            Log::info("2816",['data' => json_encode($data)]);
             $created_user=User::create($data);
             if (isset($data['isAkaunting_Vendor'])) {
              if ($data['isAkaunting_Vendor']) {
                $this->createSpAdminAndSyncWithAkauntingVendor($created_user->id);
               }
           }


            User::where('id', $created_user->id)
                ->update(['user_privileges'=>json_encode(['contracts'=>$request->contracts]),]);
            $this->data['name']=$data['name'];
            $this->data['created_user_type']=$created_user_type;
            $created_user_id=$created_user->id;

            $user_id_str=base64_encode($created_user_id);
              /******************************send email to user *******************************/
              $slug = 'registration-successfully';
              $variable_value=[
                  '##USERNAME##'=>"{$data['name']}",
                  '##EMAIL##'=>"{$data['email']}",
                  '##PASSWORD##'=>"123456",
                  '##SITELINK##'=>"<a href='".route('reset_password',$user_id_str)."' alt='Osool Link'>".url('/')."</a>",
              ];
              $mail_content=Helper::emailTemplateMail($slug,$variable_value);
              $sender_email=Helper::getAdminContactMail();
              $to_name= $data['name'];
              $to_email= $data['email'];
              $subject="Registration Successful";
              if($to_email!='') //check for null email id of receiver
              {
                try {
                  Mail::send('mail.send_email', ['mail_content' => $mail_content],function($message) use ($to_name, $to_email,$subject,$sender_email) {
                    $message->to($to_email, $to_name)
                    ->subject($subject);
                    $message->from($sender_email,'Osool Team');
                    });
                } catch (\Throwable $e) {
                  # code...
                }
              }
              event(new VarificationEmail($to_name, $to_email,$subject,$sender_email));
              //dd('ami');
              /*******************************************************************************/
              $request->session()->forget('user_info');
              $request->session()->forget('worker_privilege');


              $this->data['name']=$data['name'];
              $this->data['created_user_type']=$created_user_type;
              $created_user_id;
              Helper::updateWorkOrders($created_user_id);
              if(Session::has('service_provider_id') && !Session::has('entered_project_id')) {
                $this->data['service_provider'] = Session::get('service_provider_id');
                return view($this->view_path.'.create.public_sp.create-confirmation', ['data' => $this->data]);
              }

              //make sync with osool dash.
              $actedBy = auth()->user() ?? null;
              $projectID = Helper::getProjectID();
              $currentProject = ProjectsDetails::query()->where('id', '=', $projectID)->first();

              Log::info("Save tenant data", [
                  $currentProject?->use_crm_module, $actedBy?->crm_api_token
              ]);
              if ($currentProject && $currentProject->use_crm_module && !empty($actedBy->crm_api_token)) {
                  $sp_admin= User::query()->find($created_user_id);
                  $crmUserId = $sp_admin?->crmUser?->crm_user_id ?? null;
                  $vendorData= $created_user->toArray();
                  $vendorData['sp_id']= $created_user_id;
                  Log::info("Save vendor data", $vendorData);
                  dispatch(new VendorAccounts(actedBy: $actedBy, vendorData: $vendorData, dashVendorID: $crmUserId, enableAccountLogin: true));
              }

            return view($this->view_path.'.create.create-confirmation',['data' => $this->data]);
          }
          $data['user_privileges']=json_encode($user_previleges);
          $data['created_by'] = $logedin_user->id;
          if ($created_user_type == 'store_keeper'){
              $data['project_user_id'] = $logedin_user->project_user_id;
              $data['project_id'] = $logedin_user->project_id;
          }
//          dd($data);
          if(isset($request->new_password)) // if setting new password
          {
            $randompw=$request->new_password;
          }
          else
          {
            $randompw='123456';
          }
          $hashedPassword = Hash::make($randompw);
          $data['password'] = $hashedPassword;
          if(isset($data['email'])=='')//check for null email id
          {
              $data['email']=$data['emp_id'];
          }
          $existing_user_row=User::where('email',$data['email'])->first();
          if(!$existing_user_row)//check if email exist
          {
            if(isset($data['supervisor_id']) && !empty($data['supervisor_id']) && is_array($data['supervisor_id']))
            {
              $data['supervisor_id'] = implode(',', $data['supervisor_id']);
            }
            //dd($data);
            if(!isset($data['status']))
            {
              $data['status'] = 1;
            }
            Log::info("2907",['data' => json_encode($data)]);
            $created_user=User::create($data);
            if($created_user_type == 'sp_worker')
            {
              if(isset($data['contract_ids']))
              {
                $newContractIds=explode(',',$data['contract_ids']);
                //When create worker then add contract worker mapping
                $this->updateWorkerContracts($newContractIds,$created_user->id,'insert');
              }
            }
          }
          else
          {
              $created_user=$existing_user_row;
          }
          $created_user_id=$created_user->id;

          if(isset($request->work_order_approve)){
            $dataToInsert = [ 'privilage_id'=>Helper::getSubPrivilegeIdBySlug(Helper::getUserTypeIdBySlug($data['user_type']),'work_order_approve'),
            'value'=>$request->work_order_approve[0],
            'privilage_user_id'=>$created_user_id];
            $user_sub_previleges=UserSubPrivileges::create($dataToInsert);
          }
          // user types
          if($created_user_type == 'admin') //if creating admin
          {
            User::where('id', $created_user_id)
                ->update([
                  'project_id' => Session::get('entered_project_id'),
                  'project_user_id' => $created_user_id,
                ]);
                $user = Auth::user();
                $user->project_user_id = $created_user_id;
                $user->save();
            }
            if($logedin_user->user_type=='sp_admin' && $created_user_type=="vendor"){

                  User::updateUserProject($created_user_id, Session::get('entered_project_id'));
                  $created_user = User::findOrFail($created_user_id);
                  $token = Str::random(60);
                  $created_user->notify(new VendorAssigned($token));
                 /*  $created_user->assignRole('vendor'); */
                  $created_user->updateAuthUserProject();
                  Log::alert("success");
            }
            if($created_user_type == 'supervisor' || $created_user_type == 'sp_worker') //if creating worker or supervisor
            {

            if(isset($data['asset_categories_contacts'])) //check data
            {
              if(count($data['asset_categories_contacts'])>0) //check count
              {
                foreach($data['asset_categories_contacts'] as $rr) {
                  $arr = explode(',',$rr);

                  DB::table('user_assets_mapping')->insert([
                    'user_id' => $created_user_id,
                    'user_type' => $created_user_type,
                    'contract_id' =>$arr[0],
                    'asset_id' =>$arr[1]
                  ]);
                }

              }
            }

          }
          if($logedin_user->user_type=='admin' || $logedin_user->user_type=='sp_admin' || $logedin_user->user_type=='admin_employee'){
             $companyId = auth()->user()->crmUser?->crm_user_id;
              if($created_user_type == 'building_manager' && !empty( $companyId)) {
                   $WOTasksTrait = (new class { use \App\Http\Traits\CRMProjects\WOTasksTrait; });
                $bmaWorkdo_creat_Bma= $WOTasksTrait->createBmaAcccount($created_user->name,$created_user->email,$created_user->phone);
              
                if (isset($bmaWorkdo_creat_Bma['created']) && $bmaWorkdo_creat_Bma['created'] == 'yes') {
                    session()->put('bma_link_workdo','yes');
                    CrmUser::create([
                      'user_id' => $created_user->id,
                      'crm_user_id' =>  $companyId,
                  ]);

                  }


           }
          if ($created_user_type == 'procurement_admin') {
            $data = $request->session()->get('user_info');
            $data['user_type'] = 'procurement_admin';
            $randompw='123456';
            $hashedPassword = Hash::make($randompw);
            $data['password'] = $hashedPassword;
            $data['project_user_id'] = $logedin_user->project_user_id;
            $data['project_id'] = $logedin_user->project_id;
            $data['created_by'] = $logedin_user->id;
            $data['user_privileges']=json_encode($user_previleges);
            // $data['approved_max_amount'] = session('max_ammount', 0);

            $existing_user = User::where('email', $data['email'])->first();

            if (!$existing_user) {
                Log::info("3008",['data' => json_encode($data)]);
                $created_user = User::create($data);
            } else {
              $existing_user->update($data);
                $created_user = $existing_user;
            }
            $this->updatePrAdminAndSyncWithAkauntingCutomer($created_user->id);
            if ($created_user) {
              
           $permission_access=PermissionPAAccess::create([
            'user_id'=>$created_user->id,
            'initiate_pr'=>session::get('initiate_status',1),
            'send_pr_to_v'=>session::get('send_status',1),
            'view_v_q'=>session::get('receive_status',1),
            'require_app'=>session::get('app_reject_status',1),
            'manage_pay'=>session::get('manage_status',1),
            'request_bafo'=>session::get('createpo_status',1),
               'auto_purchase'=>session::get('auto_purchase') ?? 1,
               'amount_field'=>session::get('max_ammount') ?? 0,
             
           ]);
                $created_user->update([
                    'project_id' => $data['project_id'],
                    'project_user_id' => $data['project_user_id']
                ]);
            }


            $created_user_id=$created_user->id;
              $user_id_str=base64_encode($created_user_id);
              /******************************send email to user *******************************/
              $slug = 'registration-successfully';
              $variable_value=[
                  '##USERNAME##'=>"{$data['name']}",
                  '##EMAIL##'=>"{$data['email']}",
                  '##PASSWORD##'=>"123456",
                  '##SITELINK##'=>"<a href='".route('reset_password',$user_id_str)."' alt='Osool Link'>".url('/')."</a>",
              ];
                $mail_content=Helper::emailTemplateMail($slug,$variable_value);
                $sender_email=Helper::getAdminContactMail();
                $to_name= $data['name'];
                $to_email= $data['email'];
                $subject="Registration Successful";

                if ($to_email != '') {
                  try {
                      Mail::send('mail.send_email', ['mail_content' => $mail_content], function ($message) use ($to_name, $to_email, $subject, $sender_email) {
                          $message->to($to_email, $to_name)
                                  ->subject($subject);
                          $message->from($sender_email, 'Osool Team');
                      });
                  } catch (Exception $e) {
                      Log::error('Failed to send email:', [
                          'error' => $e->getMessage(),
                          'email' => $to_email
                      ]);
                  }
                }
                event(new VarificationEmail($to_name, $to_email,$subject,$sender_email));


            Log::info('Created/Updated User:', [
              'id' => $created_user->id,
              'name' => $created_user->name,
              'user_type' => $created_user->user_type
          ]);

         
            $request->session()->forget('user_info');
            $request->session()->forget('worker_privilege');
            /* Forget Proc Permession Session */
            $request->session()->forget('initiate_status');
            $request->session()->forget('send_status');
            $request->session()->forget('receive_status');
            $request->session()->forget('app_reject_status');
            $request->session()->forget('manage_status');
            $request->session()->forget('createpo_status');
              $request->session()->forget('auto_purchase') ;
           
            $request->session()->forget('max_ammount');
      
            return view($this->view_path . '.create.create-confirmation', ['data' => $this->data]);
        }}

          if ($created_user_type == 'store_keeper'){
              $created_user->syncWarehouses($data['keeper_warehouses']);
          }
          $user_id_str=base64_encode($created_user_id);
          /******************************send email to user *******************************/
          $slug = 'registration-successfully';
          $variable_value=[
              '##USERNAME##'=>"{$data['name']}",
              '##EMAIL##'=>"{$data['email']}",
              '##PASSWORD##'=>"123456",
              '##SITELINK##'=>"<a href='".route('reset_password',$user_id_str)."' alt='Osool Link'>".url('/')."</a>",
          ];
          $mail_content=Helper::emailTemplateMail($slug,$variable_value);
          $sender_email=Helper::getAdminContactMail();
          $to_name= $data['name'];
          $to_email= $data['email'];
          $subject="Registration Successful";
          // Mail::send('mail.send_email', ['mail_content' => $mail_content],function($message) use ($to_name, $to_email,$subject,$sender_email) {
          // $message->to($to_email, $to_name)
          // ->subject($subject);
          // $message->from($sender_email);
          // });
          //dd($sender_email.' -- '.$to_email);
          if($data['user_type']!='sp_worker' && $data['user_type']!='team_leader') //if creating worker
          {
          try{
            event(new VarificationEmail($to_name, $to_email,$subject,$sender_email));
          }
          catch (\Exception $e) {
            $request->session()->put('emailNotSent', true);
          }
          }

          /**mail implement */
          if($data['user_type']!='sp_worker' && $data['user_type']!='team_leader') //if creating worker
          {
            $delete_pass_request = DB::table('password_resets')->where('email', $data['email'])->delete();
            DB::table('password_resets')->insert(array('email'=>$data['email'],'token'=>Hash::make(date('Y-m-d H:i:s')),'created_at'=>date('Y-m-d H:i:s')));

              $email_url=route('reset_password',$user_id_str);
              $subject="Welcome to Osool!";
              $details = [
                  'name'=>$data['name'],
                  'title' => 'Welcome to Osool',
                  'email_id'=>$data['email'],
                  'emp_id'=>isset($data['emp_id'])?$data['emp_id']:null,
                  'body' => 'Your admin has created an account for you Click on the button to log-in and set your password',
                  'url_a'=>$email_url,
              ];
              try{
                Mail::to($to_email)->send(new OsoolMail($details));
              }
              catch (\Exception $e) {
                $request->session()->put('emailNotSent', true);
              }
          }
          if(session()->has('emailNotSent')) {
            //dd(session()->get('emailNotSent'));
          }

          /*******************************************************************************/
          $request->session()->forget('user_info');
          $request->session()->forget('worker_privilege');

          $this->data['name']=$data['name'];
          $this->data['created_user_type']=$created_user_type;
          // @flip1@ add credentials for worker
          if($data['user_type'] == 'sp_worker' || $data['user_type'] == 'team_leader') {
            $this->data['worker_id']= $data['email'];
            if(isset($request->new_password)){
              $this->data['password']= $request->new_password;
            }
          }

          if(Session::has('service_provider_id')) {
            $this->data['service_provider'] = Session::get('service_provider_id');
            return view($this->view_path.'.create.public_sp.create-confirmation',['data'=>$this->data]);
          } else{

            return view($this->view_path.'.create.create-confirmation', $this->data);
          }

      }else{
        return redirect()->route('users.list');
      }
    }

    /**
     * GET users/users-group
     *
     * Return view to display user group.
     * Return a view to display a display user group.
     *
     * @authenticated
     * @group Users
     *
     * @responseField success
     * @response 200
     */
    public function group()
    {
        $this->data['pageTitle']='User Profile';
        return view($this->view_path.'.user-profile',['data'=>$this->data]);
    }

    /**
     * GET users/
     *
     * Return view to display user profile.
     * Return a view with the user data of the logged in user.
     *
     * @authenticated
     * @group Users
     *
     * @responseField success
     * @response 200
     */
    public function profile()
    {
        $logedin_user=auth()->user();
        $this->data['row_data']=$logedin_user;
        $countryList=Country::where('status',1)->get();
        $this->data['countryList']=$countryList;
        $cityList=City::where('status',1)->get();
        $this->data['cityList']=$cityList;
        $this->data['pageTitle']='User Profile';
        $this->data['id']=$logedin_user->id;
        return view($this->view_path.'.user-profile',['data'=>$this->data]);
    }

    /**
     * POST users/update-profile
     *
     * Return redirect Edit user profile.
     * Edit active users's profile data in database and redirect to the view profile view.
     *
     * @authenticated
     * @group Users
     *
     * @responseField success
     * @response 200
     */
    public function update_profile(Request $request){
        $logedin_user=auth()->user();
        $id=$logedin_user->id;
        $data=array();
        $data['name']=$request->name;
        // if($request->phone != null){// @flip1@ phone is not null
          $data['phone']=$request->phone;
        // }
        $data['country_id']=$request->country_id;
        $data['city_id']=$request->city_id;
        $data['emp_dept']=$request->emp_dept;
        User::where('id', $id)->update($data);
        return redirect()->route('users.profile')->with('success','Profile successfully updated.');
    }

    /**
     * POST users/update-profile-img
     *
     * Return true after Edit user profile image.
     * Edit active users's profile image in database as well as uploading the image file to local directory and redirect to the view profile view.
     *
     * @authenticated
     * @group Users
     *
     * @responseField success
     * @response 200
     */
    public function update_img(Request $request){

        $logedin_user=auth()->user();
        $id=$logedin_user->id;
        $data=array();

        /***********************for uploading profile img ***********************/
        if($request->hasFile('profile_img')) //if has image uploaded
        {
            $image = $request->file('profile_img');
            //compress image
            $data['profile_img'] = ImagesUploadHelper::compressedImage($image, 'profile_images');
        }
        else
        {
          $data['profile_img']=$request->old_img;
        }
        /*******************************************************************/

        /*******************************************************************/
        User::where('id', $id)->update($data);
        return true;
    }

    /**
     * POST users/update-profile-contact
     *
     * Return redirect to edit user profile contact.
     * Edit active users's profile contact data in database redirect to the view profile view.
     *
     * @authenticated
     * @group Users
     *
     * @responseField success
     * @response 200
     */
    public function update_profile_contact(Request $request){
        $logedin_user=auth()->user();
        $id=$logedin_user->id;
        $data=array();
        $data['emp_id']=$request->emp_id;
        if($logedin_user->email != $request->email){
          $data['email']=$request->email;
          $data['email_verified']=0;
          $data['email_attempts']=0;
          $data['last_email_attempt_at']=null;
        }
        User::where('id', $id)->update($data);
        return redirect()->route('users.profile')->with('success','Profile successfully updated.');
    }

    /**
     * POST users/update-profile-password
     *
     * Return redirect to edit user profile password.
     * Edit active users's password in database redirect to the view profile view.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200
     */
    public function update_profile_password(Request $request){
        $logedin_user=auth()->user();
        $id=$logedin_user->id;
        $data=array();
        $pw=$request->password;
        $hashedPassword = Hash::make($pw);
        $data['password'] = $hashedPassword;
        User::where('id', $id)->update($data);
        return redirect()->route('users.profile')->with('success','Profile successfully updated.');
    }

    /**
     * POST users/ajax/ajax_check_useroldpw/{id?}
     *
     * Return true or false for checking old pw in ajax.
     * Check and inform user for old password alert for validation purposes.
     *
     * @authenticated
     * @group Users
     *
     * @responseField boolean
     * @response 200
     */
    public function ajax_check_useroldpw(Request $request,$id=null){
        $logedin_user=auth()->user();
        $id=$logedin_user->id;
        $row= User::where('id',$id)->first();
        $input_pw=$request->old_password;
        if (Hash::check($input_pw, $row->password)) {
            return "true";
        }
        else{
          return "false";
        }
    }

    /**
     * POST users/ajax/ajax_check_unique_useremail/{id?}
     *
     * Return true or false for for unique checking email id via ajax.
     * Check and inform user for existing emaild if alert for validation purposes.
     *
     * @authenticated
     * @group Users
     *
     * @responseField boolean
     * @response 200
     */
    public function ajax_check_unique_useremail(Request $request,$id=null){
        if($id)//check parameter
        {
             $row= User::where('id','!=',$id)
             ->where('email',$request->email)
             ->where('deleted_at',null)->first();
        }else{
             $row= User::where('email',$request->email)
             ->where('deleted_at',null)->first();
        }
        //dd($row);
        if($row){
            return "false";
        }else{
            return "true";
        }
    }

    /**
     * POST users/ajax/ajax_check_building_deleted/{id?}
     *
     * Return true or false for soft deleted user.
     * Check if user with entered email id is deleted or not via ajax.
     *
     * @authenticated
     * @group Users
     *
     * @responseField boolean
     * @response 200
     */
    public function ajax_check_building_deleted(Request $request,$id=null){
      //dd($request->all());
      if($id)//check parameter
      {
           $row= User::where('id','!=',$id)
           ->where('email',$request->email)
           ->where('deleted_at',null)
           ->first();
      }else{
           $row= User::where('email',$request->email)
           ->where('deleted_at',null)->first();
      }
      if($row){
          return "false";
      }else{
          return "true";
      }
    }

    /**
     * POST users/ajax/ajax_check_unique_usernumber/{id?}
     *
     * Return true or false for unique checking phone number via ajax.
     * Check if another user with entered contact/phone number already exist or not via ajax.
     *
     * @authenticated
     * @group Users
     *
     * @responseField boolean
     * @response 200
     */
    public function ajax_check_unique_usernumber(Request $request, $id = null)
    {
        $query = User::where('phone', $request->phone)
            ->where('deleted_at', null);
        $expetionUsers=['tenant','building_manager','building_manager_employee'];

        if($request->user_type == 'building_manager' || $request->user_type == 'building_manager_employee') {
          $query->where('user_type', '!=', 'tenant');
        }
        else if ($request->user_type === 'tenant') {
            $query->whereNotIn('user_type', $expetionUsers);
        }
        else {
            if ($id) {
                $query->where('id', '!=', $id);
            }
        }

        $row = $query->first();

        if ($row) {
            return "false";
        } else {
            return "true";
        }
    }


    /**
     * POST users/ajax/ajax_check_unique_emp_id/{id?}
     *
     * Return true or false for unique checking employee id via ajax.
     * Check if another user with entered employee id already exist or not via ajax for validation purpose.
     *
     * @authenticated
     * @group Users
     *
     * @responseField boolean
     * @response 200
     */
    public function ajaxCheckUniqueEmpId(Request $request, $id = null)
    {
      $id = $request->user_id;
      $empId = $request->emp_id;
      $userType = $request->user_type;
      $query = User::query();

      if ($userType == 'sp_worker') {
          $query->where('email', $empId);
      } else {
          $query->where('emp_id', $empId)
                ->where('project_id', Auth::user()->project_id)
                ->whereNull('deleted_at');
      }

      if ($id) {
          $query->where('id', '!=', $id);
      }

      $row = $query->first();

      return $row ? "false" : "true";
    }

    public function deprecated_ajax_check_unique_emp_id(Request $request,$id=null)
    {
      $id = $request->user_id;
      if($request->user_type == 'sp_worker') //for worker
      {
        if($id){
          $row= User::where('id','!=',$id)
          ->where('email', $request->emp_id)->first();
        }else{
          $row= User::where('email', $request->emp_id)->first();
        }
        if($row){
          return "false";
        }else{
          return "true";
        }
      }
      else{
        return "true";
      }
    }

    /**
     * GET users/ajax/ajax_check_unique_useremail_edit/{id?}
     *
     * Return true or false for unique checking user email id via ajax.
     * Check if another user with entered employee id already exist or not  while editing user via ajax for validation purpose.
     *
     * @authenticated
     * @group Users
     *
     * @responseField boolean
     * @response 200
     */
    public function ajax_check_unique_useremail_edit(Request $request,$id=null){
      $id = $request->user_id;
      if($id){//check parameter
             $row= User::where('id','!=',$id)
             ->where('email',$request->email)->first();
        }else{
             $row= User::where('email',$request->email)->first();
        }
        if($row){
            return "false";
        }else{
            return "true";
        }
    }

    /**
     * GET users/ajax/ajax_check_unique_usernumber_edit/{id?}
     *
     * Return true or false for unique checking user employee id via ajax.
     * Check if another user with entered employee id / phone number already exist or not  while editing user via ajax for validation purpose.
     *
     * @authenticated
     * @group Users
     *
     * @responseField boolean
     * @response 200
     */
    public function ajax_check_unique_usernumber_edit(Request $request,$id=null){
        if($id){//check parameter
             $row= User::where('id','!=',$id)
             ->where('emp_id',$request->emp_id)
             ->where('deleted_at',null)
             ->first();
        }else{
             $row= User::where('emp_id',$request->emp_id)
             ->where('id','!=',$request->user_id)
             ->where('deleted_at',null)
             ->first();
        }
        if($row){
            return "false";
        }else{
            return "true";
        }
    }

    /**
     * POST users/ajax/ajax_check_userphone_unique_edit/{id?}
     *
     * Return true or false according to the conditions.
     * Check if another user with entered contact number / phone number already exist or not  while editing user via ajax for validation purpose.
     *
     * @authenticated
     * @group Users
     *
     * @responseField boolean
     * @response 200
     */
    public function ajax_check_userphone_unique_edit(Request $request,$id=null){
      $id = $request->user_id;
      $expetionUsers=['tenant','building_manager','building_manager_employee'];
      if($id){ //check parameter
        if($request->user_type == 'building_manager' || $request->user_type == 'building_manager_employee') {
          $row= User::where('id','!=',$id)->where('phone',$request->phone)->where('user_type', '!=', 'tenant')->first();
        }
        else if ($request->user_type == 'tenant') {
          $row= User::where('id','!=',$id)->whereNotIn('user_type', $expetionUsers)
          ->where('phone',$request->phone)->first();
        }
        else{
          $row= User::where('id','!=',$id)
          ->where('phone',$request->phone)->first();
        }
      }else{
          $row= User::where('phone',$request->phone)->where('id','!=',$request->user_id)->first();
      }
      if($row){
          return "false";
      }else{
          return "true";
      }
    }

    /**
     * GET users/delete-user/{id}
     *
     * Return JSON with success message.
     * Soft deleted selected single user.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function deleteUser(Request $request, $id = null)
    {
        if ($id == null) { //check parameter
            return redirect()->route('users.list');
        }
        $details = User::where('id', $id)->first();
        //when deleting a sp admin all it's related supervisors and workers should be deleted as well
        if($details->user_type =='sp_admin'){
          $service_provider = $details->service_provider;

          User::where([['user_type', 'sp_worker'],['service_provider', $service_provider]])->update(['is_deleted' => 'yes']);
          User::where([['user_type', 'sp_worker'],['service_provider', $service_provider]])->delete();

          User::where([['user_type', 'supervisor'],['service_provider', $service_provider]])->update(['is_deleted' => 'yes']);
          User::where([['user_type', 'supervisor'],['service_provider', $service_provider]])->delete();

        }
        //when deleting a supervisor all it's related workers should be deleted as well

        if($details->user_type =='supervisor'){
          $supervisor_id = $details->id;
          User::where([['user_type', 'sp_worker'],['supervisor_id', $supervisor_id]])->update(['is_deleted' => 'yes']);
          User::where([['user_type', 'sp_worker'],['supervisor_id', $supervisor_id]])->delete();
        }

        if($details->user_type =='building_manager'){
          $building_manager_id = $details->id;
          User::where([['user_type', 'building_manager_employee'],['sp_admin_id',$building_manager_id]])->update(['is_deleted' => 'yes','deleted_at' => date('Y-m-d H:i:s')]);
        }
        // $delete = User::where('id', $id)->update(['is_deleted' => 'yes']);
        // $delete = $details->delete();
        $user = User::find($id);
        $user->update(['is_deleted' => 'yes']);
        $user->delete();

         if($details->user_type == 'sp_worker'){
          $delete_wo_token = DB::table('oauth_access_tokens')
              ->where('user_id', $id)
              ->delete();
         }

        if(!empty(Auth::user()->user_type == 'super_admin'))
        {
          DB::table('projects_details')->where('user_id', '=', $id)->update(['user_id' => 0]);
        }
        return response()->json(['message'=>__('general_sentence.s_global.delete_success')]);
    }
    public function deleteUserSpAdmin(Request $request)
    {
      $id=$request->id;
        $details = User::where('id', $id)->first();
        $newSpa = User::where('id', $request->new_spa)->first();
        //when deleting a sp admin all it's related supervisors and workers should be assigned new admin as well
        $service_provider = $details->service_provider;
        User::where('sp_admin_id', $id)->update(['sp_admin_id' => $request->new_spa]);
        User::where('sp_admin_id', $id)->update(['sp_admin_id' => $request->new_spa]);
        $user = User::find($id);
        $this->deleteVendorWithSpAssociation($user);
        $user->update(['is_deleted' => 'yes']);
        $user->delete();
        return response()->json(['message'=>__('general_sentence.s_global.delete_success')]);
    }
    /**
     * GET users/{id}/spedit
     *
     * Return view for Step -1 for eding users belonging to service provider side.
     * Return step-1 form view for editing users from service providers side with required data.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function editUsersSP($id)
    {
      Session::forget('user_info');
      Session::forget('asset_categories_contacts');
      Session::forget('service_provider_id');
      Session::forget('edit_spa_user');
      Session::forget('worker_privilege');

      $logedin_user=auth()->user();
      if($id==null){ //check parameter
          return redirect()->route('users.list');
      }
      else
      {
        $id = Crypt::decryptString($id);
        Session::put('edit_spa_user', 1);
        $countryList=Country::where('status',1)->get();
        $this->data['countryList']=$countryList;
        $cityList=City::where('status',1)->get();
        $this->data['cityList']=$cityList;
        $this->data['u_data']=User::where('id', $id)->first();
        /************user type dd *****************/
        $users = Auth::user();
        if($users->user_type == 'super_admin') //for sp admin
        {
          $usertypeList=DB::table('user_type')->where('slug', '!=', 'tenant')->where('status',1);
          $project_id = Session::get('entered_project_id');
          if(isset($project_id)) //check project id
          {
            $isHasAdmin = User::where('user_type','admin')->where('project_id',$project_id)->count();
            if($isHasAdmin == 0) {
              $usertypeList = $usertypeList->where('slug', 'admin')->get();
            }
            else{
              $usertypeList = $usertypeList->where('slug','!=','super_admin')->where('slug','!=','osool_admin')->where('slug','!=','admin')->get();
            }
          } else {
            $usertypeList = $usertypeList->where('slug', 'sp_admin')->get();
          }
          $this->data['usertypeList']=$usertypeList;
        } elseif($users->user_type == 'osool_admin') //for osool admin
        {
          $usertypeList=DB::table('user_type')->where('slug', '!=', 'tenant')->where('status',1);
          $project_id = Session::get('entered_project_id');
          if(isset($project_id)) {
            $isHasAdmin = User::where('user_type','admin')->where('project_id',$project_id)->count();
            if($isHasAdmin == 0) {
              $usertypeList = $usertypeList->where('slug', 'admin')->get();
            }
          }
          else{
            $usertypeList = $usertypeList->where('slug','sp_admin')->get();
          }
          $this->data['usertypeList']=$usertypeList;

        }
        elseif($users->user_type == 'admin') //for admin
         {
          $usertypeList=DB::table('user_type')->where('slug', '!=', 'tenant')->where('slug','!=','super_admin')->where('slug','!=','osool_admin')->where('slug','!=','admin')->where('status',1)->get();
          $this->data['usertypeList']=$usertypeList;
          $this->data['project_name']= Helper::getProjectName($users->project_id);
          $this->data['project_id'] = $users->project_id;

        }
        elseif($users->user_type == 'admin_employee') //for admin employee
        {
          $usertypeList=DB::table('user_type')->where('slug', '!=', 'tenant')->where('slug','!=','super_admin')->where('slug','!=','admin')->where('slug','!=','osool_admin')->where('slug','!=','admin_employee')->where('status',1)->get();
          $this->data['usertypeList']=$usertypeList;
          if(!empty($users->project_id)){
            $this->data['project_name']= Helper::getProjectName($users->project_id);
            $this->data['project_id'] = $users->project_id;
          }else{
            $get_project_id_admin=DB::table('users')->select('project_id')->where('id',$users->project_user_id)->first();
            $this->data['project_name'] = Helper::getProjectName($get_project_id_admin->project_id);
            $this->data['project_id'] = $get_project_id_admin->project_id;
          }
           //$this->data['project_id'] = $users->project_id;
        }
        elseif($users->user_type == 'sp_admin') //for sp admin
         {
          $usertypeList=DB::table('user_type')->where('slug', '!=', 'tenant')->where('slug','!=','super_admin')->where('slug','!=','admin')->where('slug','!=','admin_employee')->where('slug','!=','sp_admin')->where('slug','!=','building_manager')->where('slug','!=','building_manager_employee')->where('slug','!=','osool_admin')->where('status',1)->get();
          $this->data['usertypeList']=$usertypeList;

        }
            /*****************************************/
            $user_service_provider_id = $this->data['u_data']['service_provider'];
            /************company - service pro list *****************/
            $companyList=DB::table('service_providers');
            if(Session::has('entered_project_id')) //check session data
            {
              $companyList = $companyList->where('user_id',$logedin_user->project_user_id);
            }
            $companyList = $companyList->where('global_sp','1')->where('is_deleted','no')->where('status',1)
            ->whereNotExists(function($query) use ($user_service_provider_id)
            {
                $query->select(DB::raw(1))
                      ->from('users')
                      ->whereRaw("users.service_provider = service_providers.id and service_providers.id != '".$user_service_provider_id."'
                      ");
            })
            ->get();
            $this->data['companyList']=$companyList;
            /************existing company array *****************/
            $existingcompanyList=DB::table('users')->select('service_provider')->where('user_type','sp_admin')->where('is_deleted','no')->where('status',1)->get();
            $existingcompanyArr=array();
            if(isset($existingcompanyList))
            {
                foreach ($existingcompanyList as $key=>$value) {
                    $existingcompanyArr[]=$value->service_provider;
                }
            }
            $this->data['existingcompanyArr']=$existingcompanyArr;
            $companyListforSpadmin=DB::table('service_providers')->where('is_deleted','no')->where('status',1)->whereNotIn('id',$existingcompanyArr)->get();
            $this->data['companyListforSpadmin']=$companyListforSpadmin;
            /************building manager list *****************/
            $building_manager_list=DB::table('users')->where('user_type','building_manager')->where('is_deleted','no')
            ->where('project_user_id',$logedin_user->project_user_id)
            ->where('status',1)->get();
            $this->data['building_manager_list']=$building_manager_list;
            /************spga admin list *****************/
            $spga_admin_list=DB::table('users')->where('user_type','osool_admin')->where('is_deleted','no')->where('status',1)->get();
            $this->data['spga_admin_list']=$spga_admin_list;

            $company_id=$this->data['u_data']->service_provider;
            if($company_id>0)
            {
               $sp_admin_list = User::where(['is_deleted' => 'no', 'status' => 1,'service_provider' => $company_id,'user_type'=>'sp_admin'])->get();

                if($logedin_user->user_type == "sp_admin")
                {
                  $sp_supervisor_list = User::where(['is_deleted' => 'no', 'status' => 1,'service_provider' => $company_id,'user_type'=>'supervisor', 'sp_admin_id'=>$logedin_user->id])->get();
                }
                else{
                  $sp_supervisor_list = User::where(['is_deleted' => 'no', 'status' => 1,'service_provider' => $company_id,'user_type'=>'supervisor', 'project_user_id' => $logedin_user->project_user_id])->get();
                }
               $this->data['sp_admin_list']=$sp_admin_list;
               $this->data['sp_supervisor_list']=$sp_supervisor_list;
            }
            /**************added for osool admin ****************************/
            if($logedin_user->user_type=='super_admin')//for super admin
            {
              return view($this->view_path.'.edit.osool_admin.create-info',['data'=>$this->data]);
            }
            else if($logedin_user->user_type=='sp_admin')//for sp admin
            {
                return view($this->view_path.'.edit.edit-info_sp_admin',['data'=>$this->data]);
            }
            else
            {
              return view($this->view_path.'.edit.edit-info',['data'=>$this->data]);
            }
        }
    }

    /**
     * GET users/pspa-users-list/edit_ps/{service_provider?}/{id}
     *
     * Return view for Step -1 for eding users belonging to public service provider side.
     * Return step-1 form view for editing users from public service providers side with required data.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function editPSPUsers($supervisor_id,$id)
    {
      Session::forget('user_info');
      Session::forget('asset_categories_contacts');
      Session::forget('service_provider_id');
      Session::forget('edit_spa_user');
      Session::forget('worker_privilege');

      $logedin_user=auth()->user();
      if($id==null){//check parameter
          return redirect()->route('users.list');
      }
      else
      {

          $id = Crypt::decryptString($id);
          Session::put('edit_spa_user', 0);
          $countryList=Country::where('status',1)->get();
          $this->data['countryList']=$countryList;
          $cityList=City::where('status',1)->get();
          $this->data['cityList']=$cityList;
          $this->data['u_data']=User::where('id', $id)->first();
          /************user type dd *****************/
          $users = Auth::user();
          if($users->user_type == 'super_admin') {
            $usertypeList=DB::table('user_type')->where('slug', '!=', 'tenant')->where('status',1);
            $project_id = Session::get('entered_project_id');
            //dd($project_id);
            if(isset($project_id)) { //check project id
              $isHasAdmin = User::where('user_type','admin')->where('project_id',$project_id)->count();
              if($isHasAdmin == 0) {
                $usertypeList = $usertypeList->where('slug', 'admin')->get();
              }
              else{
                $usertypeList = $usertypeList->where('slug','!=','super_admin')->where('slug','!=','osool_admin')->where('slug','!=','admin')->get();
              }
            } else {
              $usertypeList = $usertypeList->where('slug', 'osool_admin')->get();
            }
            //dd($usertypeList);
            $this->data['usertypeList']=$usertypeList;
          } elseif($users->user_type == 'osool_admin') // for osool admin
          {
            $usertypeList=DB::table('user_type')->where('slug', '!=', 'tenant')->where('status',1);
            $project_id = Session::get('entered_project_id');
            $isHasAdmin = User::where('user_type','admin')->where('project_id',$project_id)->count();
            if($isHasAdmin == 0) //check if project has owner admin
            {
              $usertypeList = $usertypeList->where('slug', 'admin')->get();
            }
            else{
              $usertypeList = $usertypeList->where('slug','!=','super_admin')->where('slug','!=','osool_admin')->where('slug','!=','admin')->get();
            }
            $this->data['usertypeList']=$usertypeList;
          }
          elseif($users->user_type == 'admin') //for admin
          {
            $usertypeList=DB::table('user_type')->where('slug', '!=', 'tenant')->where('slug','!=','super_admin')->where('slug','!=','osool_admin')->where('slug','!=','admin')->where('status',1)->get();
            $this->data['usertypeList']=$usertypeList;
            $this->data['project_name']= Helper::getProjectName($users->project_id);
            $this->data['project_id'] = $users->project_id;
          }
          elseif($users->user_type == 'admin_employee') //for admin employee
          {
            $usertypeList=DB::table('user_type')->where('slug', '!=', 'tenant')->where('slug','!=','super_admin')->where('slug','!=','admin')->where('slug','!=','osool_admin')->where('slug','!=','admin_employee')->where('status',1)->get();
            $this->data['usertypeList']=$usertypeList;
            if(!empty($users->project_id)){
              $this->data['project_name']= Helper::getProjectName($users->project_id);
              $this->data['project_id'] = $users->project_id;
            }else{
              $get_project_id_admin=DB::table('users')->select('project_id')->where('id',$users->project_user_id)->first();
              $this->data['project_name'] = Helper::getProjectName($get_project_id_admin->project_id);
              $this->data['project_id'] = $get_project_id_admin->project_id;
            }
          }
          elseif($users->user_type == 'sp_admin') //for sp admin
          {
            $usertypeList=DB::table('user_type')->where('slug', '!=', 'tenant')->where('slug','!=','super_admin')->where('slug','!=','admin')->where('slug','!=','admin_employee')->where('slug','!=','sp_admin')->where('slug','!=','building_manager')->where('slug','!=','building_manager_employee')->where('slug','!=','osool_admin')->where('status',1)->get();
            $this->data['usertypeList']=$usertypeList;
          }
          /*****************************************/
          $user_service_provider_id = $this->data['u_data']['service_provider'];
          /************company - service pro list *****************/
          $companyList=DB::table('service_providers')
          ->select('service_providers.*')
          ->where('is_deleted','no')->where('service_providers.status',1);
          if(($logedin_user->user_type == "admin" || $logedin_user->user_type == "admin_employee" || $logedin_user->user_type == "osool_admin" || $logedin_user->user_type == "super_admin") && $this->data['u_data']->user_type == "supervisor")//for various user types
          {
              $project_id = Helper::getProjectID();
              $companyList = $companyList->leftjoin('service_providers_project_mapping','service_providers_project_mapping.service_provider_id','service_providers.id');
              $companyList = $companyList->where('service_providers_project_mapping.project_id',$project_id);
              $companyList = $companyList->orWhere('service_providers.user_id',$logedin_user->id);
          }
          else
          {
            $companyList = $companyList->whereNotExists(function($query) use ($user_service_provider_id)
            {
              $query->select(DB::raw(1))
                    ->from('users')
                    ->whereRaw("users.service_provider = service_providers.id and service_providers.id != '".$user_service_provider_id."'
                    ");
            });
          }
          $companyList = $companyList->get();
          $this->data['companyList'] = $companyList;
          /************existing company array *****************/
          $existingcompanyList=DB::table('users')->select('service_provider')->where('user_type','sp_admin')->where('is_deleted','no')->where('status',1)->get();
          $existingcompanyArr=array();
          if(isset($existingcompanyList))
          {
              foreach ($existingcompanyList as $key=>$value) {
                  $existingcompanyArr[]=$value->service_provider;
              }
          }
          $this->data['existingcompanyArr']=$existingcompanyArr;

          $companyListforSpadmin=DB::table('service_providers')->where('is_deleted','no')->where('status',1)->whereNotIn('id',$existingcompanyArr)->get();
          $this->data['companyListforSpadmin']=$companyListforSpadmin;

          /************building manager list *****************/
          $building_manager_list=DB::table('users')->where('user_type','building_manager')->where('is_deleted','no')
          ->where('project_user_id',$logedin_user->project_user_id)
          ->where('status',1)->get();
          $this->data['building_manager_list']=$building_manager_list;

          /************spga admin list *****************/
          $spga_admin_list=DB::table('users')->where('user_type','osool_admin')->where('is_deleted','no')->where('status',1)->get();
          $this->data['spga_admin_list']=$spga_admin_list;

          $company_id=$this->data['u_data']->service_provider;
          if($this->data['u_data']->user_type == "sp_worker" && $this->data['u_data']->email != $this->data['u_data']->emp_id)// for worker
          {
            $this->data['u_data']->emp_id = $this->data['u_data']->email;
          }
          if($company_id>0)
          {
             $sp_admin_list = User::where(['is_deleted' => 'no', 'status' => 1,'service_provider' => $company_id,'user_type'=>'sp_admin'])->get();

              if($logedin_user->user_type == "sp_admin")// for sp admin
              {
                $sp_supervisor_list = User::where(['is_deleted' => 'no', 'status' => 1,'service_provider' => $logedin_user->service_provider,'user_type'=>'supervisor', 'sp_admin_id'=>$logedin_user->id])->get();
              }
              else{
                $sp_supervisor_list = User::where(['is_deleted' => 'no', 'status' => 1,'service_provider' => $company_id,'user_type'=>'supervisor', 'project_user_id' => $logedin_user->project_user_id])->get();
              }
             $this->data['sp_admin_list']=$sp_admin_list;
             $this->data['sp_supervisor_list']=$sp_supervisor_list;
          }
          // If Public service provider is adding users
          //dd($supervisor_id);
          if(!empty($supervisor_id)) {

            $is_admin_created = Helper::getServiceProviderAdminIds($supervisor_id);
            if(count($is_admin_created)>0) { // Is the Project Admin Created
                // getting user types
                $sp_admin_exits = DB::table('users')
                ->where('service_provider',$supervisor_id)
                ->where('user_type','sp_admin')
                ->count();
                if($sp_admin_exits == 0) {
                  $user_roles = ['sp_admin','supervisor','sp_worker'];
                } else {
                  $user_roles = ['sp_admin','supervisor','sp_worker'];
                }

                $usertypeList=DB::table('user_type')
                                  ->whereIn('slug',$user_roles)
                                  ->where('status',1)
                                  ->get();
                $this->data['usertypeList']=$usertypeList;
                // end getting user types

                // getting company name
                $companyList = DB::table('service_providers')
                                ->where('id',$supervisor_id)
                                ->where('is_deleted','no')->where('status',1)
                                ->get();
                $this->data['companyList'] = $companyList;


                $sp_admin = DB::table('users')
                                ->where('service_provider',$supervisor_id)
                                ->where('user_type','sp_admin')
                                ->where('status',1)
                                ->get();

                $this->data['sp_admin'] = $sp_admin;

                $supervisors_list = DB::table('users')
                                ->where('service_provider',$supervisor_id)
                                ->where('user_type','supervisor')
                                ->where('status',1)
                                ->get();

                $this->data['supervisors_list'] = $supervisors_list;
                $this->data['admin_count'] = count($is_admin_created);
            } else {
              $sp_admin_exits = DB::table('users')
                ->where('service_provider',$supervisor_id)
                ->where('user_type','sp_admin')
                ->count();
              //dd($sp_admin_exits);
              if($sp_admin_exits == 0) {
                $user_roles = ['sp_admin','supervisor','sp_worker'];
              } else {
                $user_roles = ['sp_admin','supervisor','sp_worker'];
              }

              $usertypeList=DB::table('user_type')
                                ->whereIn('slug',$user_roles)
                                ->where('status',1)
                                ->get();
              $this->data['usertypeList']=$usertypeList;
              // end getting user types
              //dd($this->data['usertypeList']);
              // getting company name
              $companyList = DB::table('service_providers')
                              ->where('id',$supervisor_id)
                              ->where('is_deleted','no')->where('status',1)
                              ->get();
              $this->data['companyList'] = $companyList;
              $this->data['admin_count'] = 0;
            }
          }

          // Get the professions of the Worker
          //$workerProfessions = WorkerProfession::all();
          $workerProfessions = WorkerProfession::where('id', '!=', 10)->orderBy('id')->latest()->get();//10 is id of other profession
          $workerProfessions->push(WorkerProfession::find(10)); //10 is id of other profession
          $this->data['workerProfessions'] = $workerProfessions;

          // Get the Nationalities
          $nationalities = Country::all();
          $this->data['nationalities'] = $nationalities;

          /**************added for osool admin ****************************/
          if($logedin_user->user_type=='super_admin' || $logedin_user->user_type=='osool_admin' || $logedin_user->user_type=='admin')// for admins
          {

            $project_id = Session::get('entered_project_id');
            if(isset($project_id)) {
              return view($this->view_path.'.edit.osool_admin.create-info',['data'=>$this->data]);
            } else {
              if(!Session::has('service_provider_id')) {
                Session::put('service_provider_id', $supervisor_id);
              } else{
                Session::put('service_provider_id', $supervisor_id);
              }
              //dd($this->data);
              return view($this->view_path.'.edit.public_sp.create-info',['data'=>$this->data]);
            }
          }
          else if($logedin_user->user_type=='sp_admin')
          {
              return view($this->view_path.'.edit.edit-info_sp_admin',['data'=>$this->data]);
          }
          else
          {
            return view($this->view_path.'.edit.edit-info',['data'=>$this->data]);
          }
      }
    }

    /**
     * GET users/{id}/edit
     *
     * Return view for Step -1 for eding normal user types.
     * Return step-1 form view for editing normal user types with required existing user data.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function editUsers($id)
    {
      Session::forget('user_info');
      Session::forget('asset_categories_contacts');
      Session::forget('service_provider_id');
      Session::forget('edit_spa_user');
      Session::forget('worker_privilege');

        $logedin_user=auth()->user();
        if($id==null){//check parameter
            return redirect()->route('users.list');
        }
        else
        {
            $id = Crypt::decryptString($id);
            Session::put('edit_spa_user', 0);
            $countryList=Country::where('status',1)->get();
            $this->data['countryList']=$countryList;
            
            $cityList=City::where('status',1)->get();
            $this->data['cityList']=$cityList;
            $this->data['u_data']=User::where('id', $id)->first();
            /************user type dd *****************/
            $users = Auth::user();
            if($users->user_type == 'super_admin') {
              $usertypeList=DB::table('user_type')->where('slug', '!=', 'tenant')->where('status',1);
              $project_id = Session::get('entered_project_id');
              if(isset($project_id)) { //check project id
                $isHasAdmin = User::where('user_type','admin')->where('project_id',$project_id)->count();
                if($isHasAdmin == 0) {
                  $usertypeList = $usertypeList->where('slug', 'admin')->get();
                }
                else{
                  $usertypeList = $usertypeList->where('slug','!=','super_admin')->where('slug','!=','osool_admin')->where('slug','!=','admin')->get();
                }
              } else {
                $usertypeList = $usertypeList->where('slug', 'osool_admin')->get();
              }
              $this->data['usertypeList']=$usertypeList;
            } elseif($users->user_type == 'osool_admin') // for osool admin
            {
              $usertypeList=DB::table('user_type')->where('slug', '!=', 'tenant')->where('status',1);
              $project_id = Session::get('entered_project_id');
              $isHasAdmin = User::where('user_type','admin')->where('project_id',$project_id)->count();
              if($isHasAdmin == 0) //check if project has owner admin
              {
                $usertypeList = $usertypeList->where('slug', 'admin')->get();
              }
              else{
                $usertypeList = $usertypeList->where('slug','!=','super_admin')->where('slug','!=','osool_admin')->where('slug','!=','admin')->get();
              }
              $this->data['usertypeList']=$usertypeList;
            }
            elseif($users->user_type == 'admin') //for admin
            {
              $usertypeList=DB::table('user_type')->where('slug', '!=', 'tenant')->where('slug','!=','super_admin')->where('slug','!=','osool_admin')->where('slug','!=','admin')->where('status',1)->get();
              $this->data['usertypeList']=$usertypeList;
              $this->data['project_name']= Helper::getProjectName($users->project_id);
              $this->data['project_id'] = $users->project_id;
            }
            elseif($users->user_type == 'admin_employee') //for admin employee
            {
              $usertypeList=DB::table('user_type')->where('slug', '!=', 'tenant')->where('slug','!=','super_admin')->where('slug','!=','admin')->where('slug','!=','osool_admin')->where('slug','!=','admin_employee')->where('status',1)->get();
              $this->data['usertypeList']=$usertypeList;
              if(!empty($users->project_id)){
                $this->data['project_name']= Helper::getProjectName($users->project_id);
                $this->data['project_id'] = $users->project_id;
              }else{
                $get_project_id_admin=DB::table('users')->select('project_id')->where('id',$users->project_user_id)->first();
                $this->data['project_name'] = Helper::getProjectName($get_project_id_admin->project_id);
                $this->data['project_id'] = $get_project_id_admin->project_id;
              }
            }
            elseif($users->user_type == 'sp_admin') //for sp admin
            {
              $usertypeList=DB::table('user_type')->where('slug', '!=', 'tenant')->where('slug','!=','super_admin')->where('slug','!=','admin')->where('slug','!=','admin_employee')->where('slug','!=','sp_admin')->where('slug','!=','building_manager')->where('slug','!=','building_manager_employee')->where('slug','!=','osool_admin')->where('status',1)->get();
              $this->data['usertypeList']=$usertypeList;
            }
            /*****************************************/
            $user_service_provider_id = $this->data['u_data']['service_provider'];
            /************company - service pro list *****************/
            $companyList=DB::table('service_providers')
            ->select('service_providers.*')
            ->where('is_deleted','no')->where('service_providers.status',1);
            if(($logedin_user->user_type == "admin" || $logedin_user->user_type == "admin_employee" || $logedin_user->user_type == "osool_admin" || $logedin_user->user_type == "super_admin") && $this->data['u_data']->user_type == "supervisor")//for various user types
            {
                $project_id = Helper::getProjectID();
                $companyList = $companyList->leftjoin('service_providers_project_mapping','service_providers_project_mapping.service_provider_id','service_providers.id');
                $companyList = $companyList->where('service_providers_project_mapping.project_id',$project_id);
                $companyList = $companyList->orWhere('service_providers.user_id',$logedin_user->id);
            }
            else
            {
              $companyList = $companyList->whereNotExists(function($query) use ($user_service_provider_id)
              {
                $query->select(DB::raw(1))
                      ->from('users')
                      ->whereRaw("users.service_provider = service_providers.id and service_providers.id != '".$user_service_provider_id."'
                      ");
              });
            }
            $companyList = $companyList->get();
            $this->data['companyList'] = $companyList;
            /************existing company array *****************/
            $existingcompanyList=DB::table('users')->select('service_provider')->where('user_type','sp_admin')->where('is_deleted','no')->where('status',1)->get();
            $existingcompanyArr=array();
            if(isset($existingcompanyList))
            {
                foreach ($existingcompanyList as $key=>$value) {
                    $existingcompanyArr[]=$value->service_provider;
                }
            }
            $this->data['existingcompanyArr']=$existingcompanyArr;

            $companyListforSpadmin=DB::table('service_providers')->where('is_deleted','no')->where('status',1)->whereNotIn('id',$existingcompanyArr)->get();
            $this->data['companyListforSpadmin']=$companyListforSpadmin;

            /************building manager list *****************/
            $building_manager_list=User::where('user_type','building_manager')->where('is_deleted','no')
            ->where('project_user_id',$logedin_user->project_user_id)
            ->where('status',1)->get();
            $this->data['building_manager_list']=$building_manager_list;

            /************spga admin list *****************/
            $spga_admin_list=DB::table('users')->where('user_type','osool_admin')->where('is_deleted','no')->where('status',1)->get();
            $this->data['spga_admin_list']=$spga_admin_list;

            $company_id=$this->data['u_data']->service_provider;
            if($this->data['u_data']->user_type == "sp_worker" && $this->data['u_data']->email != $this->data['u_data']->emp_id)// for worker
            {
              $this->data['u_data']->emp_id = $this->data['u_data']->email;
            }
            if($company_id>0)
            {
               $sp_admin_list = User::where(['is_deleted' => 'no', 'status' => 1,'service_provider' => $company_id,'user_type'=>'sp_admin'])->get();

                if($logedin_user->user_type == "sp_admin")// for sp admin
                {
                  $sp_supervisor_list = User::where(['is_deleted' => 'no', 'status' => 1,'service_provider' => $logedin_user->service_provider,'user_type'=>'supervisor'])->get();
                }
                else{
                  $sp_supervisor_list = User::where(['is_deleted' => 'no', 'status' => 1,'service_provider' => $company_id,'user_type'=>'supervisor', 'project_user_id' => $logedin_user->project_user_id])->get();
                }
               $this->data['sp_admin_list']=$sp_admin_list;
               $this->data['sp_supervisor_list']=$sp_supervisor_list;
            }

            // Get the professions of the Worker
            //$workerProfessions = WorkerProfession::all();
            $workerProfessions = WorkerProfession::where('id', '!=', 10)->orderBy('id')->latest()->get();//10 is id of other profession
            $workerProfessions->push(WorkerProfession::find(10)); //10 is id of other profession
            $this->data['workerProfessions'] = $workerProfessions;

            // Get the Nationalities
            $nationalities = Country::all();
            $this->data['nationalities'] = $nationalities;
            $this->data['roleOptions']= EnumRole::labels();
            $this->data['proficiencyOptions'] = Proficiency::limitedLabels();
          //  dd($this->data);
            /**************added for osool admin ****************************/
            if($logedin_user->user_type=='super_admin' || $logedin_user->user_type=='osool_admin' || $logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee')// for admins
            {
              return view($this->view_path.'.edit.osool_admin.create-info',['data'=>$this->data]);
            }
            else if($logedin_user->user_type=='sp_admin')
            {
                if ($this->data['u_data']->user_type == 'store_keeper')
                    return view($this->view_path . '.edit.osool_admin.create-info', ['data' => $this->data]);
                return view($this->view_path.'.edit.edit-info_sp_admin',['data'=>$this->data]);
            }
            else
            {
             
              return view($this->view_path.'.edit.edit-info',['data'=>$this->data]);
            }
        }
    } 

    /**
     * GET/POST users/{id}/edit/role
     *
     * Return view for Step -2 for eding normal user types.
     * Return step-2 form view for editing normal user types with required existing user data while storing data from step-1 from into sessions.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function editUserRole(Request $request, $id)
    {
//       dd($request->all());
      if(count($request->all())==0){

          $session_project_id = Session::get('entered_project_id');
          if(isset($session_project_id)) {
            if(!empty(Auth::user()->user_type == 'super_admin') || !empty(Auth::user()->user_type == 'osool_admin')){
              return redirect()->route('users.edit.info',['id' => $id]);
            } else
            {
              return redirect()->route('users.edit.info',['id' => $id]);
            }
          } else {
            if(Session::has('service_provider_id')) {
              $session_service_provider_id = Session::get('service_provider_id');
              return redirect(url('/').'/user/pspa-users-list/edit_ps/'.$session_service_provider_id.'/'.$id);
            } else{
              if(!empty(Auth::user()->user_type == 'super_admin') || !empty(Auth::user()->user_type == 'osool_admin')){
                return redirect()->route('users.edit.info',['id' => $id]);
              }else
              {
                return redirect()->route('users.edit.info',['id' => $id]);
              }
            }
          }
      }
      $logedin_user=auth()->user();
      $id = Crypt::decryptString($id);
      $this->data['pageTitle']='Add new user | user Role';
      if($logedin_user->user_type=='super_admin' ||
      $logedin_user->user_type=='osool_admin' ||
      $logedin_user->user_type=='admin' ||
      $logedin_user->user_type=='admin_employee' ||
      $logedin_user->user_type=='building_manager' || $logedin_user->user_type=='building_manager_employee' ||
      $logedin_user->user_type=='sp_admin' ||
      $logedin_user->user_type=='supervisor') //for supervisors
      {
        $created_user_type=$request->user_type;
        if($created_user_type!='sp_worker' && $created_user_type!='team_leader')//if creating worker
        {
          $validatedData = $request->validate([
              'name' => 'required',
              'email' => 'nullable',
              'phone'=>'nullable',// @flip1@ remove from require
              'country_id'=>'required',
              'city_id'=>'required',
              'emp_dept'=>'nullable'
          ]);
        }else{
          $validatedData = $request->validate([
            'name' => 'required',
            'email' => 'nullable',
           'phone'=>'nullable',
            'emp_dept'=>'nullable'
          ]);
        }
      }
      $validatedData['emp_id']= $request->emp_id;
      // if(isset($request->emp_id)) //check parameter
      // {
      //   $validatedData['emp_id']= $request->emp_id;
      // }
      if(isset($request->supervisor_id)) //check parameter
      {
        $validatedData['supervisor_id']= $request->supervisor_id;
      }
      if($logedin_user->user_type=='sp_admin')//for sp admin
      {
        $validatedData['supervisor_id'] = $request->supervisor_id; // $id; //
      }
      if(isset($request->sp_admin_id)) //check parameter
      {
        $validatedData['sp_admin_id']= $request->sp_admin_id;
      }
      if(isset($request->isImageRemove)) //check parameter
      {
        $validatedData['isImageRemove']= $request->isImageRemove;
      }
      if(isset($request->profession_id)) //check parameter
      {
        $validatedData['profession_id']= $request->profession_id;
      }
      if(isset($request->nationality_id)) //check parameter
      {
        $validatedData['country_id']= $request->nationality_id;
      }
      if(isset($request->favorite_language)){
        $validatedData['favorite_language'] = $request->favorite_language;
      }
      else
      {
        $validatedData['favorite_language'] = '-';
      }
      $user_details =User::where('id', $id)->first();
      if($logedin_user->user_type=='building_manager' && $request->user_type == 'building_manager_employee') //for bm and bme
      {
        $validatedData['sp_admin_id'] =  $user_details->sp_admin_id;
      }

        if ($user_details->user_type == 'building_manager') {
//            dd($request->all());
            $validatedData['is_bma_area_manager']= isset($request->bma_area_manager) ? $request->bma_area_manager : '0';
        }

      $users=Auth::user();
      $created_user_type=$request->user_type;
      $validatedData['user_type']=$created_user_type;
      $project_id = Helper::getProjectID();

          $project_details = DB::table('projects_details')
                   ->select('project_name')
                   ->where('id',$project_id)
                   ->first();
          if(isset($project_details))
          {
            $projectname = trim($project_details->project_name);
          }
          else
          {
            $projectname = '';
          }
      if($logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee') //for admin
      {
        $properties=DB::table('properties')->select('region_id','city_id')
        ->where('user_id',$users->project_user_id)
        ->groupBy('region_id','city_id')->get();
        $regionsArr=array();
        $cityArr=array();
        foreach($properties as $propertiesdata)
        {
          $regionsArr[]=$propertiesdata->region_id;
          $cityArr[]=$propertiesdata->city_id;
        }
        $roles_city=json_decode($users->role_cities);
        if(isset($roles_city->id))
        {
          $selectedcityArr=$roles_city->id;
        }
        else
        {
          $selectedcityArr=explode(",",$users->role_cities);
        }
        //dd($user_details->role_regions);
        $cityArr = DB::table('cities')->whereIn('region_id', explode(",", $user_details->role_regions))->pluck('id')->toArray();
        $users_service_provider=$request->service_provider;
        //dd($users_service_provider);
        $properties=DB::table('properties')->select('city_id')->where('user_id',$users->project_user_id)->groupBy('region_id','city_id')->get();
        $cityProperties=array();
        if(!empty($properties)) //check data
        {
          foreach($properties as $propertiesdata)
          {
            $cityProperties[]=$propertiesdata->city_id;
          }
          if(!empty($cityArr))
          {
            $cityArr = array_intersect($cityArr, $cityProperties);
          }
          //dd($cityArr);
        }
      }
      else if($logedin_user->user_type=='super_admin' || $logedin_user->user_type=='osool_admin')//fpr super admin and osool admin
      {
        if($request->user_type=="supervisor")
        {
          $sp_admin_id=$request->sp_admin_id;
          $sp_admin_details=DB::table('users')->where('id', $sp_admin_id)->first();
          $sp_admin_service_provider=$sp_admin_details->service_provider;
          $contractsOsool=DB::table('contracts')->where('service_provider_id',$sp_admin_service_provider)->where('contracts.end_date','>=',date('Y-m-d'))
          //->where('user_id',$users->project_user_id)
          ->get();
          foreach($contractsOsool as $contractsdata)
          {
            $regionsArr[]=$contractsdata->region_id;
            $cityArr[]=$contractsdata->city_id;
          }
          //dd($cityArr);
        }
        if($request->user_type=="building_manager_employee")//for bme
        {
          $bm_details = User::where('id', $request->sp_admin_id)->first();
              if(isset($bm_details)) //check data
              {
                $regionsArr = explode(',', $bm_details->role_regions);
                $cityArr = explode(',', $bm_details->role_cities);
              }


          // $all_region_data=DB::table('regions')->where('is_deleted', 'no')->get();
          //     foreach ($all_region_data as $key => $value) {
          //         $regionsArr[]=$value->id;
          //     }
          // $regionsArr = $regionsArr;
          // $all_city_data=DB::table('cities')->where('is_deleted', 'no')->get();
          // foreach ($all_city_data as $key => $value) {
          //     $cityArr[]=$value->id;
          // }
          // $cityArr = $cityArr;
        }
        if($request->user_type=="building_manager") //for bm
        {
          $all_region_data=DB::table('regions')->where('is_deleted', 'no')->get();
              foreach ($all_region_data as $key => $value) {
                  $regionsArr[]=$value->id;
              }
          $regionsArr = $regionsArr;
          $all_city_data=DB::table('cities')->where('is_deleted', 'no')->get();
          foreach ($all_city_data as $key => $value) {
              $cityArr[]=$value->id;
          }
          $cityArr = $cityArr;
        }
      }
      else if($logedin_user->user_type=='sp_admin') //for sp admin
      {
        if($request->user_type=="supervisor")
        {
          $logedin_service_provider=$logedin_user->service_provider;
          $contracts=DB::table('contracts')
          ->where('end_date','>=',date('Y-m-d'))
          ->where('status',1)->where('is_deleted','no')
          ->where('service_provider_id',$logedin_service_provider);
          $contracts= $contracts->get();
          foreach($contracts as $contractsdata)
          {
            $regionsArr[]=$contractsdata->region_id;
            $cityArr[]=$contractsdata->city_id;
          }
        }
      }
      else
      {
        $regions=json_decode($users->role_regions);
        if(isset($regions->id))
        {
            $regionsArr=$regions->id;
        }
        else
        {
            $regionsArr=explode(",",$users->role_regions);
        }

        $city=json_decode($users->role_cities);
        if(isset($city->id))
        {
            $cityArr=$city->id;
        }
        else
        {
          $cityArr=explode(",",$users->role_cities);
        }
      }
      // dd($regionsArr);
      if($request->user_type=="supervisor") //for supervisor
      {
        $validatedData['service_provider']=$request->service_provider;
      }
      if($request->user_type=="sp_admin") //for sp admin
      {
        $validatedData['service_provider']=$request->service_provider;
      }
      if($request->user_type=="sp_worker") //for sp worker
      {
        $validatedData['phone']=$request->phone;
      }



      if(empty($regionsArr)||$regionsArr[0]=='')
      {
          $all_region_data=DB::table('regions')->where('is_deleted', 'no')->get();
          foreach ($all_region_data as $key => $value) {
              $regionsArr[]=$value->id;
          }
      }
      if(!isset($cityArr) && (empty($cityArr)||$cityArr[0]==''))
      {
        $all_city_data=DB::table('cities')->where('is_deleted', 'no')->get();
        foreach ($all_city_data as $key => $value) {
          $cityArr[]=$value->id;
        }
      }

      $user_details =DB::table('users')->where('id', $id)->first();
      $selected_region = !empty($user_details->role_regions) ? explode(',',$user_details->role_regions) : [];
      if(is_array($regionsArr))
      {
        $regions_string = $regionsArr[0];
        $searchForValue = ',';
        if( strpos($regions_string, $searchForValue) !== false ) {
          $regionsArr = explode(',', $regions_string);
        }
      }
      if(is_array($cityArr) && !empty($cityArr))
      {
        $cityArr = array_values($cityArr);
        $city_string = $cityArr[0];
        $searchForValue = ',';
        if( strpos($city_string, $searchForValue) !== false ) {
          $cityArr = explode(',', $city_string);
        }
      }
      $this->data['filter_regions']=Helper::getUsersRegions($regionsArr);
      $this->data['filter_city']=Helper::getUserCity($cityArr,$selected_region);

      //dd($regionsArr);
      $this->data['filter_properties'] = [];
      /***********************for uploading logo ***********************/
      if($request->hasFile('profile_img'))//check for uploaded image
      {
        $image = $request->file('profile_img');
        $validatedData['profile_img'] = ImagesUploadHelper::compressedImage($image, 'profile_images');

      }
      else
      {
        // @flip1@ is user remove image
        if($request->isImageRemove){

          $validatedData['profile_img'] = NULL;
        }
      }
      $validatedData['id']= $id;
      if(isset($request->user_type)) //check parameter
      {
        $validatedData['user_type']= $request->user_type;
      }
      /*******************************************************************/
      if ($validatedData['user_type'] === 'sp_worker') {
        $validatedData['salary'] = $request->input('salary');
        $validatedData['attendance_target'] = $request->input('attendance_target');
        $validatedData['role'] = $request->input('role');
        $validatedData['admin_level'] = $request->input('admin_level');
        $validatedData['attendance_mandatory'] = $request->input('attendance_mandatory') ?? 0;
        $validatedData['show_extra_info'] = $request->input('show_extra_info') ?? 0;
      }

      $User = new User();
      $User->fill($validatedData);
      Log::info("2308",['data' => json_encode($validatedData)]);
      $request->session()->put('user_info', $validatedData);
      $categoryList = DB::table('asset_categories')
        ->select('asset_categories.*','priorities.priority_level')
        ->join('priorities','priorities.id','=','asset_categories.priority_id')
        //->where('asset_categories.is_deleted','=','no')
        //->where('priorities.is_deleted','=','no')
        ->where(['asset_categories.user_id' => Auth::user()->project_user_id])
        ->where(['priorities.user_id' => Auth::user()->project_user_id]);
      $this->data['categoryList']=$categoryList;
      $this->data['u_data']=User::where('id', $id)->first();
      if(isset($this->data['u_data']->role_regions))//check region ids
      {
        $this->data['roles_regions']=explode(',', $this->data['u_data']->role_regions);
      }
      if(isset($this->data['u_data']->role_cities)) // check city ids
      {
        $this->data['roles_city']=explode(',', $this->data['u_data']->role_cities);
      }
      if(isset($this->data['u_data']->asset_categories))//check asset categories
      {
        $this->data['asset_cat']=explode(',', $this->data['u_data']->asset_categories);
      }

      if($logedin_user->user_type=='supervisor') // for supervisor
      {
        $selected_contracts = explode(',',$this->data['u_data']->contract_ids);
        $contract_building_data = DB::table('contract_property_buildings')->whereIn('contract_id',$selected_contracts)->get();
        $building_ids_arr=array();
        if(isset($contract_building_data))//check data
        {
          foreach ($contract_building_data as $key => $value) {
            $building_ids_arr[]=$value->property_building_id;
          }
        }
        $this->data['buildingList'] = $building_ids_arr;
      }else{

        $assigned_buildings = $this->data['u_data']->building_ids;
        if(!empty($assigned_buildings))
        {
          $assigned_buildings_arr=explode(',', $assigned_buildings);
        } else {
          $assigned_buildings_arr = [];
        }
        $this->data['buildingList'] =  Helper::getBuildingIds($assigned_buildings_arr);
      }
      if(isset($this->data['u_data']->building_ids))
      {
        $this->data['building_id_selected']=explode(',', $this->data['u_data']->building_ids);
      }
      if(isset($this->data['u_data']->contract_ids))
      {
        $this->data['contract_id_selected']=explode(',', $this->data['u_data']->contract_ids);
      }
      if(($created_user_type=='sp_admin' || $created_user_type=='admin' || $created_user_type=='admin_employee') && (($users->user_type=='super_admin' || $users->user_type=='admin' || $users->user_type=='admin_employee' || $users->user_type=='osool_admin')))//for admins
      {
        $data=$request->session()->get('user_info');
        // To update exiting poe and set previllages / building_ids / regions and cities etc as null

        if($created_user_type=='admin_employee'){
          $data['user_privileges']= NULL;
        }
        $this->data['pageTitle']='Edit user | user confirmation';
        $randompw='123456';
        $hashedPassword = Hash::make($randompw);
        $data['password'] = $hashedPassword;
        //User::where('id', $id)->update($data);
        $user = User::find($id);
        $companyId = $user->crmUser?->crm_user_id;
        if (isset($companyId) && !empty($companyId)) {
          $createCustomer = $this->crmCompany->updateCompnay($companyId, [
            'name'            => $data['name'],
            'contact'         => $data['phone'],
            'email'           => $data['email'],
        ]);
        Log::info(['$createCustomerEdit',$createCustomer]);
        }
        $user->update($data);
        $request->session()->forget('user_info');
        $request->session()->forget('worker_privilege');
        $this->data['name']=$data['name'];
        $this->data['user_id']=$data['id'];
        $this->data['created_user_type']=$created_user_type;
        $this->data['user_type'] = $created_user_type;
        // dd($this->data);
        // @flip1@ redirect to privileg page for edit privileges
        if($created_user_type == 'sp_admin'){
          $this->data['u_previ'] = json_decode($this->data['u_data']->user_privileges);
          $request->session()->put('user_info', [ 'user_type'=>'sp_admin', 'name'=>$data['name'],'isAkaunting_Vendor'=>$request->isAkaunting_Vendor=='on' ? true:false]);
          return view($this->view_path.'.edit.osool_admin.create-contract-previleges',['data'=>$this->data]);
        }
        return view($this->view_path.'.edit.edit-confirmation', $this->data);
      }
      // $categoryList=DB::table('asset_categories')->where('status',1)->where('is_deleted', 'no');
      $categoryList = DB::table('asset_categories')
      ->select('asset_categories.*')
      //->select('asset_categories.*','priorities.priority_level')
      //->join('priorities','priorities.id','=','asset_categories.priority_id')
      //->where('asset_categories.is_deleted','=','no')
      //->where('priorities.is_deleted','=','no')
      ->where(['asset_categories.user_id' => Auth::user()->project_user_id]);
      //->where(['priorities.user_id' => Auth::user()->project_user_id]);
      if($logedin_user->user_type=='building_manager')
      {
        $assigned_cat=$users->asset_categories;
        if(!empty($assigned_cat))
        {
          $assigned_cat_arr=explode(',', $assigned_cat);
          $categoryList->whereIn('asset_categories.id',$assigned_cat_arr);
        }
      }
      else if($logedin_user->user_type=='sp_admin')//for sp admin
      {
        if($request->user_type=="supervisor")//if creating supervisor
        {
          if(!empty($contracts))
          {
            foreach($contracts as $contractsdata)
            {
              $assigned_cat_all[]=$contractsdata->asset_categories;
            }
            $asset_cat_str="";
            if(!empty($assigned_cat_all))
            {
              foreach ($assigned_cat_all as $assigned_cat)
              {
                if($asset_cat_str=='')
                {
                  $asset_cat_str.= $assigned_cat;
                }
                else
                {
                  $asset_cat_str.= ",".$assigned_cat;
                }
              }
              $asset_cat_arr=array_unique(explode(',', $asset_cat_str));
              $categoryList->whereIn('asset_categories.id',$asset_cat_arr);
            }
          }
        }
        if($request->user_type=="sp_worker" || $request->user_type=="team_leader")// if creating worker
        {
          $supervisor_id=$request->supervisor_id;
          $asset_cat_arr = Helper::getUserAssetIds($supervisor_id);
          //dd($asset_cat_arr);
          if(!empty($asset_cat_arr))
          {
            $categoryList->whereIn('asset_categories.id',$asset_cat_arr);
          }
        }
      }
      else if($logedin_user->user_type=='super_admin' || $logedin_user->user_type=='osool_admin')//for super admin and osool admin
      {
        if($request->user_type=="supervisor")//if creating supervisor
        {
          if(!empty($contractsOsool))
          {
            foreach($contractsOsool as $contractsdata)
            {
              $assigned_cat_all[]=$contractsdata->asset_categories;
            }
            $asset_cat_str="";
            if(!empty($assigned_cat_all))//check categories
            {
              foreach ($assigned_cat_all as $assigned_cat)
              {
                if($asset_cat_str=='')
                {
                  $asset_cat_str.= $assigned_cat;
                }
                else
                {
                  $asset_cat_str.= ",".$assigned_cat;
                }
              }
              $asset_cat_arr=array_unique(explode(',', $asset_cat_str));
              $categoryList->whereIn('asset_categories.id',$asset_cat_arr);
            }
          }
        }
        if($request->user_type=="sp_worker" || $request->user_type=="team_leader")//if creating worker
        {
          $supervisor_id=$request->supervisor_id;
          $asset_cat_arr = Helper::getUserAssetIds($supervisor_id);
          if(!empty($asset_cat_arr))
          {
            $categoryList->whereIn('asset_categories.id',$asset_cat_arr);
          }
        }
        if($request->user_type=='building_manager' || $request->user_type=='building_manager_employee')//for bm and bme
        {
          if($request->user_type=="building_manager_employee") //if creating supervisor
          {
            $bm_details = User::select('asset_categories')->where('id', $request->sp_admin_id)->first();
            $categoryList->whereIn('asset_categories.id',explode(',', $bm_details->asset_categories));
          }
          $categoryList->where('asset_categories.user_id', $logedin_user->project_user_id);
        }
      }
      elseif($logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee')// fora admin and admin employee
      {
        //dd($request->all());
        if($request->user_type=="building_manager_employee") //if creating supervisor
        {
          $bm_details = User::select('asset_categories')->where('id', $request->sp_admin_id)->first();
          if(isset($bm_details))
          {
            $categoryList->whereIn('asset_categories.id',explode(',', $bm_details->asset_categories));
          }
        }
        $categoryList->where('asset_categories.user_id', $logedin_user->project_user_id);
      }
      
      $categoryList=$categoryList->get();
      
      if(isset($categoryList))
      {
        foreach($categoryList as $k => $r)
        {
          if(isset($this->data['asset_cat']) && count($this->data['asset_cat'])>0)
          {
            if(in_array($r->id,$this->data['asset_cat']))
            {
              if($r->deleted_at != "")
              {
                $deleted = " [".__("work_order.bread_crumbs.deleted")."]";
                $r->asset_category = $r->asset_category.$deleted;
              }
            }
            else
            {
              if($r->deleted_at != "")
              {
                unset($categoryList[$k]);
              }
            }
          }
        }
      }

      $this->data['categoryList']=$categoryList;

      $contractList=DB::table('contracts')->select('contracts.*', 'projects_details.project_name','projects_details.project_name_ar')->leftjoin('users', 'users.id', '=', 'contracts.user_id')->leftjoin('projects_details', 'projects_details.id', '=', 'users.project_id')->where('contracts.end_date','>=',date('Y-m-d'))->where('contracts.is_deleted','no')->where('contracts.status',1);

      if($logedin_user->user_type !='sp_admin' && $logedin_user->user_type !='supervisor' && Session::has('entered_project_id'))//for users other than sp admin
      {
        $user_data = Helper::userDetail($id);
        if($user_data) {
          $request->service_provider = $user_data->service_provider;
          $contractList->where('contracts.service_provider_id', $request->service_provider);
        }
        //If superadmin enters in to the project he will see contracts irrespective of the Project he is in.. Chnage is discussed with Mr.Abdulrahaman and Mr. Haitham
        if($logedin_user->user_type != 'super_admin' && $logedin_user->user_type != 'osool_admin')
        {
          $contractList = $contractList->where('contracts.user_id', $logedin_user->project_user_id);
        }
        $contractList = $contractList->where('service_provider_id',$request->service_provider);
      }
      if($logedin_user->user_type=='sp_admin')//for sp admin
      {
        //dd('yes');
        $logedin_service_provider=$logedin_user->service_provider;
        $contractList = $contractList->where('service_provider_id',$logedin_service_provider);
        if($request->user_type=="sp_worker" || $request->user_type=="team_leader")
        {
          //dd('yes');
          if(isset($request->supervisor_id))//check parameter
          {
            //dd($request->all());
            $supervisor_id=$request->supervisor_id;
            $supervisor_details_sp_admin=DB::table('users')->whereIn('id', $supervisor_id)->get();

            if(isset($supervisor_details_sp_admin))
            {
              $assigned_contracts='';
            foreach ($supervisor_details_sp_admin as $sps) {// @flip1@ for multiple SPS concate all contract id.
              if($sps->contract_ids != '' && $sps->contract_ids != null){
                $assigned_contracts .=  ','.$sps->contract_ids;
              }
            }
              // $assigned_contracts=$supervisor_details_sp_admin->contract_ids;

              if(!empty($assigned_contracts))
              {
                $assigned_contracts_arr = explode(',', $assigned_contracts);
                $contractList->whereIn('contracts.id',$assigned_contracts_arr);
              }
              //dd($assigned_contracts_arr);
            }
          }
        }
        else if($request->user_type=="supervisor")//if creating supervisor
        {
          $user_data = Helper::userDetail($id);
          if($user_data) {
            $request->service_provider = $user_data->service_provider;
            $role_regions = !empty($user_data->role_regions) ? explode(',',$user_data->role_regions) : [];
            $role_citys = !empty($user_data->role_cities) ? explode(',',$user_data->role_cities) : [];

            $contractList->where('contracts.service_provider_id', $request->service_provider);
            //$contractList->whereIn('contracts.region_id', $role_regions);
            //$contractList->whereIn('contracts.city_id', $role_citys);
            $find_in_set_city = "(";

                            for ($i = 0; $i < count($role_citys); $i++) {
                                if ($i == 0) {
                                    $find_in_set_city .= "FIND_IN_SET(" . $role_citys[$i] . ",contracts.city_id)";
                                } else {
                                    $find_in_set_city .= " OR FIND_IN_SET(" . $role_citys[$i] . ",contracts.city_id)";
                                }
                            }
            $find_in_set_city .= ")";
            $contractList->whereraw($find_in_set_city);
          }
        }
        else
        {
          $assigned_contracts=$users->contract_ids;
          if(!empty($assigned_contracts))
          {
            $assigned_contracts_arr=explode(',', $assigned_contracts);
            $contractList->whereIn('contracts.id',$assigned_contracts_arr);
          }
        }
      }
      else if($logedin_user->user_type=='super_admin' || $logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee' || $logedin_user->user_type=='osool_admin')//for admin and super admin
      {
        if($request->user_type=="supervisor")
        {
          //$contractList->where('contracts.service_provider_id', $request->service_provider);
          //dd($id);
          $user_data = Helper::userDetail($id);
          if($user_data) {
            $request->service_provider = $user_data->service_provider;
            $role_regions = !empty($user_data->role_regions) ? explode(',',$user_data->role_regions) : [];
            $role_citys = !empty($user_data->role_cities) ? explode(',',$user_data->role_cities) : [];

            $find_in_set_region = "(";

                            for ($i = 0; $i < count($role_regions); $i++) {
                                if ($i == 0) {
                                    $find_in_set_region .= "FIND_IN_SET(" . $role_regions[$i] . ",contracts.city_id)";
                                } else {
                                    $find_in_set_region .= " OR FIND_IN_SET(" . $role_regions[$i] . ",contracts.city_id)";
                                }
                            }
            $find_in_set_region .= ")";



            $find_in_set_city = "(";

                            for ($i = 0; $i < count($role_citys); $i++) {
                                if ($i == 0) {
                                    $find_in_set_city .= "FIND_IN_SET(" . $role_citys[$i] . ",contracts.city_id)";
                                } else {
                                    $find_in_set_city .= " OR FIND_IN_SET(" . $role_citys[$i] . ",contracts.city_id)";
                                }
                            }
            $find_in_set_city .= ")";


            //dd($role_citys);
            $contractList->where('contracts.service_provider_id', $request->service_provider)
            //->whereraw($find_in_set_region)
            ->whereraw($find_in_set_city);
            // $contractList->whereIn('contracts.region_id', $role_regions);
            // $contractList->whereIn('contracts.city_id', $role_citys);
          }
        }
        if($request->user_type=="sp_worker" || $request->user_type=="team_leader")
        {
          if(Session::has('service_provider_id') && !Session::has('entered_project_id')) {
            $supervisor_id= $request->supervisor_id;
            // dd($supervisor_id);
            $supervisor_details=DB::table('users')->where('id', $supervisor_id)->first();
            $assigned_contracts=$supervisor_details->contract_ids;
            if(!empty($assigned_contracts))
            {
                $assigned_contracts_arr=explode(',', $assigned_contracts);
                $contractList->whereIn('contracts.id',$assigned_contracts_arr);
            }
          } else {
            $supervisor_id=$request->supervisor_id;
            // $supervisor_details=DB::table('users')->where('id', $supervisor_id)->first();
            $supervisor_details=DB::table('users')->whereIn('id', $supervisor_id)->get();// @flip1@ get all SPS data

            $assigned_contracts='';
            foreach ($supervisor_details as $sps) {// @flip1@ for multiple SPS concate all contract id.
              if($sps->contract_ids != '' && $sps->contract_ids != null){
                $assigned_contracts .=  ','.$sps->contract_ids;
              }
            }

            if(!empty($assigned_contracts))
            {
                $assigned_contracts_arr=explode(',', $assigned_contracts);
                $contractList->whereIn('contracts.id',$assigned_contracts_arr);
            }
          }

        }
      }
      // else if($logedin_user->user_type=='osool_admin') //for osool admin
      // {
      //   $contractList->where('contracts.user_id',$logedin_user->project_user_id);
      // }
      else if ($logedin_user->user_type=='supervisor') //for supervisor
      {
        if($request->user_type=="sp_worker" || $request->user_type=="team_leader")
        {
          //dd($logedin_user->service_provider);
          $contractList->whereIn('contracts.id',explode(',',$logedin_user->contract_ids));
          $logedin_service_provider = $logedin_user->service_provider;
          $contractList = $contractList->where('service_provider_id',$logedin_service_provider);
        }
      }

      if(trim($projectname) != "")
      {
        //If superadmin enters in to the project he will see contracts irrespective of the Project he is in.. Chnage is discussed with Mr.Abdulrahaman and Mr. Haitham
        if($logedin_user->user_type != 'super_admin' && $logedin_user->user_type != 'osool_admin' && $logedin_user->user_type != 'sp_admin' && $logedin_user->user_type != 'supervisor')
        {
          $contractList = $contractList->where('contracts.user_id',$logedin_user->project_user_id);
        }
      }

      $new_contract_ids = $contractList->pluck('id');
      $contractList=$contractList->get();
      //dd($contractList);
      $this->data['contractList']=$contractList;
      //dd($this->data['contractList']);
      $buildingList=DB::table('properties')
        ->select('property_buildings.*','properties.complex_name', 'properties.property_type')
        ->join('property_buildings','properties.id','=','property_buildings.property_id','left')
        ->where('property_buildings.is_deleted','no');
      if(trim($projectname) != "")
      {
        //If superadmin enters in to the project he will see Buildings irrespective of the Project he is in.. Chnage is discussed with Mr.Abdulrahaman and Mr. Haitham
        if($logedin_user->user_type != 'super_admin' && $logedin_user->user_type != 'osool_admin' && $logedin_user->user_type != 'sp_admin' && $logedin_user->user_type != 'supervisor')
        {
          $buildingList= $buildingList->where('properties.user_id',$logedin_user->project_user_id);
        }
      }
      if($logedin_user->user_type=='building_manager')//for building manager
      {
          $assigned_buildings=$users->building_ids;
          //dd($assigned_buildings);
          if(!empty($assigned_buildings))
          {
            $assigned_buildings_arr=array_values(array_filter(explode(',', $assigned_buildings)));
            $buildingList->where('properties.user_id', $users->project_user_id);
            $buildingList->whereIn('property_buildings.id',$assigned_buildings_arr);
            $buildingList = $buildingList->whereIn('properties.city_id', $this->data['roles_city']);
          }
      }
      else if($logedin_user->user_type=='super_admin' || $logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee')//for admin and superadmin
      {
        if($request->user_type=="supervisor")//if creating supervisor
        {
          $supervisor_id = $request->user_id;
          //dd($supervisor_id);
          $contract_ids = DB::table('users')->select('contract_ids', 'building_ids', 'role_regions')->where('id', $supervisor_id)->first();
          $contracts = [];
          $s_role_regions = [];
          if(isset($contract_ids))
          {
            $contracts = explode(',', $contract_ids->contract_ids);
            $buildings = explode(',', $contract_ids->building_ids);
            $s_role_regions = explode(',', $contract_ids->role_regions);
          }
          //dd($contracts);
          if(!empty($contracts))
          {
            $selected_regions_arr = !empty($user_data->role_regions) ? explode(',',$user_data->role_regions) : $s_role_regions;
            $selected_city_arr = !empty($user_data->role_cities) ? explode(',',$user_data->role_cities) : [] ;
            //dd($selected_regions_arr);
            $buildingList->whereIn('properties.region_id',$selected_regions_arr);
            //$buildingList->whereIn('properties.city_id',$selected_city_arr);
            $contract_building_data = DB::table('contract_property_buildings')->whereIn('contract_id',$contracts)->get();
            $building_ids_arr=array();
            if(isset($contract_building_data))
            {
              foreach ($contract_building_data as $key => $value) {
                $building_ids_arr[]=$value->property_building_id;
              }
              $buildingList->whereIn('property_buildings.id',array_values(array_filter($building_ids_arr)));
            }
          }
        }
        if($request->user_type=="sp_worker" || $request->user_type=="team_leader")//if creating worker
        {
          $supervisor_id=$request->supervisor_id;
          $supervisor_details=DB::table('users')->where('id', $supervisor_id)->first();

          $assigned_buildings=$supervisor_details->building_ids;
          if(!empty($assigned_buildings))
          {
            
            $assigned_buildings_arr=array_values(array_filter(explode(',', $assigned_buildings)));
            $buildingList->whereIn('property_buildings.id',$assigned_buildings_arr);
          }
        }
        if($request->user_type=="building_manager")//if creating building manager
        {
          $assigned_buildings = $this->data['u_data']->building_ids;
          // dd($this->data['roles_city']);
          if(!empty($assigned_buildings))
          {
            $assigned_buildings_arr=explode(',', $assigned_buildings);
            $city_ids = explode(',', $this->data['u_data']->city_id);

            // dd($city_ids);
            $buildingList->whereIn('properties.city_id', $this->data['roles_city']);
            $buildingList->where('properties.user_id', Auth::user()->project_user_id);
          }
        }
        //dd($request->all());
        if($request->user_type=="building_manager_employee")
        {
          if(isset($request->building_admin))
          {
            $bm_id = $request->building_admin;
          }
          else
          {
            $bm_id = $request->sp_admin_id;
          }
          $user_details = User::where('id',$bm_id)->first();
          $assigned_buildings=$user_details->building_ids;
          if(!empty($assigned_buildings))//check data
          {
            $assigned_buildings_arr=array_values(array_filter(explode(',', $assigned_buildings)));
            $buildingList = $buildingList->whereIn('property_buildings.id',$assigned_buildings_arr);
            $city_ids = explode(',', $this->data['u_data']->city_id);

            //dd($this->data['roles_city']);
            $buildingList = $buildingList->whereIn('properties.city_id', $this->data['roles_city']);
          }
        }
      }
      else if($logedin_user->user_type=='osool_admin')// for osool admin
      {
        if($request->user_type=="supervisor")//for superviosr
        {
          $sp_admin_id=User::select('sp_admin_id')->where('id',$id)->first();
          $sp_admin_id =  $sp_admin_id->sp_admin_id;
          $sp_admin_details=DB::table('users')->where('id', $sp_admin_id)->first();
          $sp_admin_service_provider=$sp_admin_details->service_provider;
          $contractsOsool=DB::table('contracts')->where('service_provider_id',$sp_admin_service_provider)
          //->where('user_id',$users->project_user_id)
          ->get();
          if(!empty($contractsOsool))
          {
            $contract_id = [];
            foreach($contractsOsool as $contractsdata)
            {
              $contract_id[]=$contractsdata->id;
            }
            $contract_building_data = DB::table('contract_property_buildings')->whereIn('contract_id', $new_contract_ids)->get();
            $building_ids_arr=array();
            if(isset($contract_building_data))
            {
              foreach ($contract_building_data as $key => $value) {
                  $building_ids_arr[]=$value->property_building_id;
              }
              $buildingList->whereIn('property_buildings.id',array_values(array_filter($building_ids_arr)));
            }
          }
        }
        if($request->user_type=="sp_worker" || $request->user_type=="team_leader")//if creating worker
        {
          $supervisor_id=$id;
          $supervisor_details=DB::table('users')->where('id', $supervisor_id)->first();
          $assigned_buildings=$supervisor_details->building_ids;
          if(!empty($assigned_buildings))
          {
            $assigned_buildings_arr=array_values(array_filter(explode(',', $assigned_buildings)));
            $buildingList->whereIn('property_buildings.id',$assigned_buildings_arr);
          }
        }
        if($request->user_type=="building_manager")//if creating building manager
        {
          $assigned_buildings = $this->data['u_data']->building_ids;
          if(!empty($assigned_buildings))
          {
            $assigned_buildings_arr=array_values(array_filter(explode(',', $assigned_buildings)));
            $buildingList->whereIn('property_buildings.id',$assigned_buildings_arr);
          }
        }
        if($request->user_type=="building_manager_employee")
        {
          if(isset($request->building_admin))
          {
            $bm_id = $request->building_admin;
          }
          else
          {
            $bm_id = $request->sp_admin_id;
          }
          $user_details = User::where('id',$bm_id)->first();
          $assigned_buildings=$user_details->building_ids;
          if(!empty($assigned_buildings))//check data
          {
            $assigned_buildings_arr=array_values(array_filter(explode(',', $assigned_buildings)));
            $buildingList = $buildingList->whereIn('property_buildings.id',$assigned_buildings_arr);
            $city_ids = explode(',', $this->data['u_data']->city_id);

            //dd($this->data['roles_city']);
            $buildingList = $buildingList->whereIn('properties.city_id', $this->data['roles_city']);
          }
        }
      }
      else if($logedin_user->user_type=='sp_admin')//for sp admin
      {
        if($request->user_type=="supervisor")//if creating supervisor
        {
          $supervisor_id = $request->user_id;
          $contract_ids = DB::table('users')->select('contract_ids', 'building_ids')->where('id', $supervisor_id)->first();
          $contracts = [];
          if(isset($contract_ids))
          {
            $contracts = explode(',', $contract_ids->contract_ids);
            $buildings = explode(',', $contract_ids->building_ids);
          }
          //dd($contract_ids);
          if(!empty($contracts))
          {
            $selected_regions_arr = !empty($user_data->role_regions) ? explode(',',$user_data->role_regions) : [] ;
            $selected_city_arr = !empty($user_data->role_cities) ? explode(',',$user_data->role_cities) : [] ;
            $buildingList->whereIn('properties.region_id',$selected_regions_arr);
            //$buildingList->whereIn('properties.city_id',$selected_city_arr);
            $contract_building_data = DB::table('contract_property_buildings')
            ->join('property_buildings', 'property_buildings.id','=','contract_property_buildings.property_building_id')
            ->join('properties', 'properties.id','=','property_buildings.property_id')
            ->whereIn('contract_id',$contracts)
            ->whereIn('properties.city_id', explode(',',$user_data->role_cities))
            ->get();
            $building_ids_arr=array();
            if(isset($contract_building_data))
            {
              foreach ($contract_building_data as $key => $value) {
                $building_ids_arr[]=$value->property_building_id;
              }
              $buildingList->whereIn('property_buildings.id',array_values(array_filter($building_ids_arr)));
            }
          }
        }
        if($request->user_type=="sp_worker" || $request->user_type=="team_leader")//if creating worker
        {
          //dd($request->all());
          $supervisor_id = $request->supervisor_id;
          $contract_ids = DB::table('users')->select('contract_ids', 'building_ids')->where('id', $supervisor_id)->first();
          $contracts = [];
          if(isset($contract_ids))
          {
            $contracts = explode(',', $contract_ids->contract_ids);
            $buildings = explode(',', $contract_ids->building_ids);
          }
          if(isset($contracts) && isset($buildings))
          {
            $assigned_buildings = DB::table('contract_property_buildings')->whereIn('contract_id', $contracts)->whereIn('property_building_id',$buildings)->get()->pluck('property_building_id');

            if(!empty($assigned_buildings))
            {
              $supervisor_id= $request->supervisor_id;
              //dd($supervisor_id);
              $supervisor_details=DB::table('users')->where('id', $supervisor_id)->first();
              $building_ids=$supervisor_details->building_ids;
              $buildingids_arr=explode(',',$building_ids);
              $buildingList->whereIn('property_buildings.id',array_values(array_filter($buildingids_arr)));
            }
          }
        }
      }
      else if ($logedin_user->user_type=='supervisor') //for supervisor
      {
        $supervisor_id = Auth::user()->id;
        $contract_ids = DB::table('users')->select('contract_ids', 'building_ids')->where('id', $supervisor_id)->first();
        $contracts = [];
        if(isset($contract_ids))
        {
          $contracts = explode(',', $contract_ids->contract_ids);
          $buildings = explode(',', $contract_ids->building_ids);
        }
        if(isset($contracts) && isset($buildings))
        {
          $assigned_buildings = DB::table('contract_property_buildings')->whereIn('contract_id', $contracts)->whereIn('property_building_id',$buildings)->get()->pluck('property_building_id');

          if(!empty($assigned_buildings))
          {
            //dd($supervisor_id);
            $supervisor_details=DB::table('users')->where('id', $supervisor_id)->first();
            $building_ids=$supervisor_details->building_ids;
            $buildingids_arr=explode(',',$building_ids);
            $buildingList->whereIn('property_buildings.id',array_values(array_filter($buildingids_arr)));
          }
        }
      }
      $buildingList=$buildingList->get();
      //dd($buildingList);
      $this->data['buildingList']=$buildingList;
      if($logedin_user->user_type=='supervisor' || $logedin_user->user_type=='sp_admin') //for sp admin and supervisor
      {
        $this->data['buildingList']=$buildingList;
      }
      else if ($request->user_type == 'sp_worker' || $request->user_type=="team_leader") //if creating worker
      {
        $selected_contracts = explode(',',$this->data['u_data']->contract_ids);
        $contract_building_data = DB::table('contract_property_buildings')->whereIn('contract_id',$selected_contracts)->get();
        $building_ids_arr=array();
        if(isset($contract_building_data))//check data
        {
          foreach ($contract_building_data as $key => $value) {
            $building_ids_arr[]=$value->property_building_id;
          }
        }
        $buildingList = $buildingList->whereIn('property_buildings.id',array_values(array_filter($building_ids_arr)));
      } elseif($request->user_type != "supervisor" && $logedin_user->user_type !='building_manager' && $request->user_type != "building_manager") //if creating supervisor // && $logedin_user->user_type !='building_manager' is added reason=> on edit only need to show the buildings for that city in case of building manager creating bme.
      {
        if($request->user_type != "building_manager_employee")
        {
          $assigned_buildings = $this->data['u_data']->building_ids;
          if(!empty($assigned_buildings))
          {
            $assigned_buildings_arr=explode(',', $assigned_buildings);
          } else {
            $assigned_buildings_arr = [];
          }
          $this->data['buildingList'] = Helper::getBuildingIds($assigned_buildings_arr);
        }
      }
      //dd($this->data['buildingList']);
      if($request->user_type=="sp_admin") //if creating sp admin
      {
        $this->data['user_id']=$id;
        $this->data['user_type']= $request->user_type;
        return view($this->view_path.'.edit.edit-confirmation', $this->data);
      }

      if($request->user_type=="building_manager_employee")//for bme
      {
        if(isset($request->sp_admin_id))
        {
          $bm_id = $request->sp_admin_id;
        }
        if(isset($request->building_admin))
        {
          $bm_id = $request->building_admin;
        }
        if($logedin_user->user_type =='building_manager')
        {
          $bm_id = $logedin_user->id;
        }

        $bm_details_reg = User::where('id', $bm_id)->first();
        $this->data['filter_regions']=Helper::getUsersRegions(explode(',',$bm_details_reg->role_regions));
        $this->data['filter_city']=Helper::getUserCity(explode(',',$bm_details_reg->role_cities),$selected_region);
      }
      //dd($request->user_type);
      $this->data['user_type'] = '';
      if(isset($request->user_type))
      {
        $this->data['user_type'] = $request->user_type;
      }

        if ($request->user_type == 'store_keeper') {
//            dd($id);
            $allWarehousesList = [];
            $companyID = Auth::user()->projectDetails->projectCompany->company_id;
//            dd($request->service_provider);
            if ($request->has('service_provider') && !is_null($request->service_provider) && $request->service_provider != 1) {
                $companyID = ServiceProvider::where('id', '=', $request->service_provider)->first()->serviceProviderAdmin->userCompany->company_id;
            }
            $allWarehousesList = WorkorderHelper::getAllWarehousesByCompanyId($companyID);
            $allWarehousesList = WarehouseData::collection($allWarehousesList);
            $this->data['warehouses'] = $allWarehousesList->toArray();

            $bm_details_reg = User::query()->where('id','=', $id)->first();
            $filter_warehouses_ids= explode(',',$bm_details_reg->keeper_warehouses);
            $this->data['filter_warehouses']= $allWarehousesList->filter(function ($warehouse) use ($filter_warehouses_ids){
                return in_array($warehouse->id, $filter_warehouses_ids);
            })->toArray();

            $this->data['warehousesSelectedIds'] = explode(',',$bm_details_reg->keeper_warehouses);
//            dd($this->data);
        }

        if($request->user_type == 'team_leader')
        {
          $user_data = Helper::userDetail($id);
            $building_ids =  !empty($user_data->building_ids) ? explode(',',$user_data->building_ids) : [0];
        $contract_ids =  !empty($user_data->contract_ids) ? explode(',',$user_data->contract_ids) : [0];
        $supervisor_ids = count($request->supervisor_id) > 0 ? $request->supervisor_id : [0];

        $this->data['assigned_workers'] = User::where('user_type', 'sp_worker')
                        ->where(function ($query) use ($building_ids) {
                            foreach ($building_ids as $id) {
                                $query->orWhereRaw("FIND_IN_SET(?, building_ids)", [$id]);
                            }
                        })
                        ->where(function ($query) use ($contract_ids) {
                            foreach ($contract_ids as $id) {
                                $query->orWhereRaw("FIND_IN_SET(?, contract_ids)", [$id]);
                            }
                        })
                        ->where(function ($query) use ($supervisor_ids) {
                            foreach ($supervisor_ids as $id) {
                                $query->orWhereRaw("FIND_IN_SET(?, supervisor_id)", [$id]);
                            }
                        })
                        ->where('status', 1)
                        ->where('is_deleted', 'no')
                    ->get(['id', 'name']);
        }

       //Strcuture for Complex wise & bulding wise property
       $this->data['formatted_buildingList'] = Helper::FormatComplexwiseBuilding($this->data['buildingList']);
      return view($this->view_path.'.edit.edit-role',['data'=>$this->data]);
    }

    /**
     * GET/POST users/{id}/edit/privileges
     *
     * Return view for Step -3 for eding normal user types.
     * Return step-3 form view for editing normal user types with required existing user data while storing data from step-2 from into sessions.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function editUserPrivileges(Request $request, $id)
    {
          if(count($request->all())==0)
          {
              $session_project_id = Session::get('entered_project_id');
              if(isset($session_project_id)) {
                if(!empty(Auth::user()->user_type == 'super_admin') || !empty(Auth::user()->user_type == 'osool_admin')){
                  return redirect()->route('users.edit.info',['id' => $id]);
                } else
                {
                  return redirect()->route('users.edit.info',['id' => $id]);
                }
              } else {
                if(Session::has('service_provider_id')) {
                  $session_service_provider_id = Session::get('service_provider_id');
                  return redirect(url('/').'/user/pspa-users-list/edit_ps/'.$session_service_provider_id.'/'.$id);
                } else{
                  if(!empty(Auth::user()->user_type == 'super_admin') || !empty(Auth::user()->user_type == 'osool_admin')){
                    return redirect()->route('users.edit.info',['id' => $id]);
                  }else
                  {
                    return redirect()->route('users.edit.info',['id' => $id]);
                  }
                }
              }
          }
      $id = Crypt::decryptString($id);
      $this->data['pageTitle']='Add new user | user Role';
      $user_info=$request->session()->get('user_info');

//       dd($user_info);
      if(!isset($user_info['user_type']))
      {
        $user_info['user_type'] = "";
      }

      //dd($user_info);
      $this->data['u_data'] = User::where('id', $id)->first();
      //$this->data['u_previ'] = json_decode($this->data['u_data']->user_privileges);
      if($request->role_cities)//check city ids
      {
        $user_info['role_cities']= implode(",", $request->role_cities);
      }
      if($request->role_regions)//check region ids
      {
        $user_info['role_regions']= implode(",", $request->role_regions);
      }
        if ($request->warehouse)//check parameter
        {
            $user_info['keeper_warehouses'] = implode(",", $request->warehouse);
        }
      if($request->asset_categories)//check asset category ids
      {
        //dd($user_info['user_type']);
        if(isset($user_info['user_type']) && ($user_info['user_type'] == 'supervisor' || $user_info['user_type'] == 'sp_worker')) //if creating worker or supervisor
        {
          $arr = [];
          foreach($request->asset_categories as $sel_cat) {
            $arr[] = explode(',',$sel_cat)[1];
          }
          $user_info['asset_categories']= implode(",", array_unique($arr));
          $asset_categories_contacts = $request->asset_categories;
          //dd($asset_categories_contacts);
          $request->session()->put('asset_categories_contacts',$asset_categories_contacts);
        } else {
        $user_info['asset_categories']= implode(",", $request->asset_categories);
        }
      }
      if($request->building_ids)//check building ids
      {
        /************insert propery ids ******************/
        $propList=DB::table('property_buildings')->whereIn('id',$request->building_ids)->get();
        $prop_arr = [];
        if(isset($propList))//check data
        {
          $prp_arr=array();
          foreach ($propList as $key => $value) {
            $prop_arr[]= $value->property_id;
          }
          $user_info['properties']= implode(",", array_unique($prop_arr));
        }
        /**************************************************/
        $user_info['building_ids']= implode(",", $request->building_ids);
      }
      if($request->contract_ids)//check contract ids
      {
        $user_info['contract_ids']= implode(",", $request->contract_ids);
      }
      $user_info['status']=$request->status;
      if($user_info['user_type'] == 'team_leader')
        {
            $user_info['assigned_workers']= implode(",", $request->assigned_workers);
        }
      Log::info("2308",['data' => json_encode($user_info)]);
      $request->session()->put('user_info', $user_info);
      $this->data['u_previ']=json_decode($this->data['u_data']->user_privileges);

      
      $logedin_user=auth()->user();
      //dd($logedin_user);

      if($logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee' || $logedin_user->user_type=='osool_admin' || $logedin_user->user_type=='super_admin') // for admin and admin employee
      {
        // dd($user_info['user_type']);
        if($user_info['user_type']=='building_manager_employee' || $user_info['user_type']=='building_manager')//if creating bm or bme
        {
          if($user_info['user_type']=='building_manager')//if creating bm or bme
          {
            $this->data['pageTitle']='edit new user | user confirmation';
            $data=$request->session()->get('user_info');
            $data['properties'] = Helper::getPropertiesByBuildingIds($data['building_ids']);

            // dd('Yes');
            return view($this->view_path.'.edit.osool_admin.create-contract-previleges-bm',['data'=>$this->data]);
          }
          else
          {
            if($logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee')
            {
              if(trim($this->data['u_data']->sp_admin_id) != "")
              {

                $get_ba_details = DB::table('users')->select('id','user_privileges')->where('id',$this->data['u_data']->sp_admin_id)->first();
                if(isset($get_ba_details) && !empty($get_ba_details))
                {
                  if(trim($get_ba_details->user_privileges) != "")
                  {
                    $this->data['ba_id'] = $get_ba_details->id;

                    $this->data['ba_user_privileges'] = json_decode($get_ba_details->user_privileges,true);
                  }
                  else
                  {
                    $this->data['ba_user_privileges'] = array();
                  }

                }
                else
                {
                  $this->data['ba_user_privileges'] = array();
                }

              }
              else
              {
                $this->data['ba_user_privileges'] = array();
              }
            }
            else
            {
              $this->data['ba_user_privileges'] = array();
            }
            // dd($this->data);
            return view($this->view_path.'.edit.edit-building-previleges',['data'=>$this->data]);
          }
        }
        elseif($user_info['user_type']=='admin' || $user_info['user_type']=='admin_employee' || $user_info['user_type']=='store_keeper')//if creating admin or admin employee
        {
          return view($this->view_path.'.edit.edit-previleges',['data'=>$this->data]);
        }
        elseif($user_info['user_type']=='supervisor')//if creating supervisor
        {
          //dd('test');
          return view($this->view_path.'.edit.osool_admin.create-contract-previleges',['data'=>$this->data]);
        }
        elseif($user_info['user_type']=='sp_worker' || $user_info['user_type']=='team_leader')//if creating worker
        {
          return view($this->view_path.'.edit.osool_admin.create-contract-previleges',['data'=>$this->data]);

          //return view($this->view_path.'.edit.osool_admin.create-supervisor-password',['data'=>$this->data]);
        }
        elseif($user_info['user_type']=='sp_admin')//if creating sp admin
        {
          return view($this->view_path.'.create.create-building-previleges',['data'=>$this->data]);
        }
      }
      if($logedin_user->user_type=='sp_admin' || $logedin_user->user_type=='supervisor')//for sp admin and supervisor
      {
        //dd($this->data);
        if($user_info['user_type']=='supervisor')//if creating supervisor
        {
            return view($this->view_path.'.edit.osool_admin.create-contract-previleges',['data'=>$this->data]);
        }
        elseif($user_info['user_type']=='store_keeper'){
          return view($this->view_path.'.edit.edit-previleges',['data'=>$this->data]);
        }
        elseif($user_info['user_type']=='sp_worker' || $user_info['user_type']=='team_leader')//if creating worker
        {
          return view($this->view_path.'.edit.osool_admin.create-contract-previleges',['data'=>$this->data]);

            //return view($this->view_path.'.edit.osool_admin.create-supervisor-password',['data'=>$this->data]);
        }
      }
      if($logedin_user->user_type=='building_manager')//for building manger
      {
        return view($this->view_path.'.edit.edit-building-previleges',['data'=>$this->data]);
      }
      if($logedin_user->user_type=='super_admin' || $logedin_user->user_type=='osool_admin')//for osool admin and super admin
      {
          if($user_info['user_type']=='admin'||$user_info['user_type']=='admin_employee')// if creating admin or admin employee
          {
              return view($this->view_path.'.edit.edit-previleges',['data'=>$this->data]);
          }
          elseif($user_info['user_type']=='supervisor')//if creating supervisor
          {
              return view($this->view_path.'.edit.osool_admin.create-contract-previleges',['data'=>$this->data]);
          }
          elseif($user_info['user_type']=='sp_worker' || $user_info['user_type']=='team_leader')//if creating worker
          {
              return view($this->view_path.'.edit.osool_admin.create-supervisor-password',['data'=>$this->data]);
          }
          elseif($user_info['user_type']=='building_manager_employee' || $user_info['user_type']=='building_manager')//if creating bme or bm
          {
              $this->data['pageTitle']='edit new user | user confirmation';
              $data=$request->session()->get('user_info');
              $data['properties'] = Helper::getPropertiesByBuildingIds($data['building_ids']);
              return view($this->view_path.'.edit.osool_admin.create-contract-previleges-bm',['data'=>$this->data]);
          }
          elseif($user_info['user_type']=='sp_admin')//if creating sp admin
          {
            return view($this->view_path.'.create.create-building-previleges',['data'=>$this->data]);
          }
      }
      if($logedin_user->user_type=='admin'|| $logedin_user->user_type=='admin_employee' || $logedin_user->user_type=='sp_admin')
      {
        if($user_info['user_type']=='procurement_admin'){
          session()->put('initiate_status', $request->input('initiate_status',1));
          session()->put('send_status', $request->input('send_status',1));
          session()->put('receive_status', $request->input('receive_status',1));
          session()->put('app_reject_status', $request->input('app_reject_status',1));
          session()->put('manage_status', 1);
          session()->put('createpo_status', $request->input('createpo_status',1));
          session()->put('auto_purchase', $request->input('auto_purchase',1));
          
          session()->put('max_ammount', $request->input('max_ammount'));

          return view($this->view_path.'.edit.edit-previleges',['data'=>$this->data]);
        }
      }
    }

    /**
     * GET/POST users/{id}/edit/confirm
     *
     * Return view for Step -4 for editing normal user types.
     * Return step-4 conformation form view for editing normal user types after updating data stored in session from previous steps in database.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function editUserConfirm(Request $request, $id){

     
      if(count($request->all())==0){
        if(!empty(Auth::user()->user_type == 'super_admin') || !empty(Auth::user()->user_type == 'osool_admin')){
          return redirect()->route('users.edit.info',['id' => $id]);
        }else
        {
          return redirect()->route('users.edit.info',['id' => $id]);
        }
      }
      $id = Crypt::decryptString($id);
      $this->data['pageTitle']='edit new user | user confirmation';
      $data = $request->session()->get('user_info');
      if($data == null || empty($data) || !isset($data['user_type'])){
        return redirect()->route('users.list');
      }


      if ($data['user_type'] == 'vendor') {
          $user = User::find($id);
          $user->syncRoles($data['roles']);
      }


      // @flip1@ for edit contract privileg
      if($data['user_type'] == 'sp_admin') //if creating sp_admin
      {
          $sp_admin = User::where('id', $id)
              ->first();
          $sp_admin->update(['user_privileges' => json_encode(['contracts' => $request->contracts]),]);
            if ($data['isAkaunting_Vendor']) {

              $this->updateSpAdminAndSyncWithAkauntingVendor($id);
            }

          //make sync with osool dash.
          $actedBy = auth()->user() ?? null;
          $projectID = Helper::getProjectID();
          $currentProject = ProjectsDetails::query()->where('id', '=', $projectID)->first();

          Log::info("Save tenant data", [
              $currentProject?->use_crm_module, $actedBy?->crm_api_token
          ]);
          if ($currentProject && $currentProject->use_crm_module && !empty($actedBy->crm_api_token)) {
              $crmUserId = $sp_admin?->crmUser?->crm_user_id ?? null;
              dispatch(new VendorAccounts(actedBy: $actedBy, vendorData: $sp_admin->toArray(), dashVendorID: $crmUserId, enableAccountLogin: true));
          }

            $request->session()->forget('user_info');
            $this->data['name'] = $data['name'];
        return view($this->view_path.'.edit.edit-confirmation', ['data'=>$this->data,'created_user_type'=>'sp_admin', 'user_id'=> $id, 'user_type'=> 'sp_admin']);
      }
      
      if($data['user_type'] == "sp_worker")//if creating worker
      {
        $data['email'] = $data['emp_id'];
      }
      $asset_categories_contacts = $request->session()->get('asset_categories_contacts');
      // dd($data);
      $logedin_user=auth()->user();
      if($logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee') //for admin
      {
        if($data['user_type'] =='building_manager' || $data['user_type'] =='building_manager_employee')//if creating a bme
        {
          $user_previleges =
          [
              'workorder'=>$request->workorder,
              'assets'=>$request->assets,
              'contracts'=>$request->contracts,
              'tenant'=>$request->tenant

          ];
          if ($data['user_type'] == 'building_manager') {
              $user_previleges['inventory'] = $request->inventory;
              $allowAkauntingforEmployee = in_array('view', $request->inventory) ? 1 : 0;
              $data['allow_akaunting'] = $allowAkauntingforEmployee;
              User::where('sp_admin_id', $data['id'])->update(['allow_akaunting' => $allowAkauntingforEmployee]);
              User::where('id', $data['id'])->update(['allow_akaunting' => $allowAkauntingforEmployee]);
            }
        }
        elseif($data['user_type'] =='team_leader')// if creating supervisor
          {
              $user_previleges =
              [
                'lead_action_on_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[0] : ['no'],
                'lead_scan_on_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[1] : ['no'],
                'lead_report_maintenance_issues_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[2] : ['no']
              ];
          }
        elseif ($data['user_type'] == 'store_keeper') {
            $user_previleges =
                [
                    'warehouse' => $request->warehouse,
                ];
        }elseif ($data['user_type'] == 'procurement_admin') {
          Log::info('user privielege1');
          $user_previleges =
              [
                  'purchases'=>$request->purchases,
                  'warehouses' => $request->warehouses,
                  'inventory'=>$request->inventory,
                  'vendor'=>$request->vendor,
                  'workorder'=>$request->workorder,
              ];
      }
        else
        {
          $user_previleges=[
              'property'=>$request->property,
              'contracts'=>$request->contracts,
              'beneficiary'=>$request->beneficiary,
              'service_provider'=>$request->service_provider,

          ];
        }
      }
      if($logedin_user->user_type=='building_manager') //for building manager
      {
        $user_previleges=[
            'workorder'=>$request->workorder,
            'assets'=>$request->assets,
            'contracts'=>$request->contracts,
            'tenant'=>$request->tenant

        ];
      }
      if($logedin_user->user_type=='super_admin' || $logedin_user->user_type=='osool_admin' || $logedin_user->user_type =='admin'|| $logedin_user->user_type =='admin_employee')//for super admin and osool admin
      {
        if($data['user_type'] =='admin'||$data['user_type'] =='admin_employee')//if creating admin or admin employee
        {
          $user_previleges =
          [
              'property'=>$request->property,
              'contracts'=>$request->contracts,
              'beneficiary'=>$request->beneficiary,
              'service_provider'=>$request->service_provider
          ];
        }
        elseif($data['user_type'] =='team_leader')// if creating supervisor
          {
              $user_previleges =
              [
                'lead_action_on_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[0] : ['no'],
                'lead_scan_on_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[1] : ['no'],
                'lead_report_maintenance_issues_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[2] : ['no']
              ];
          }
        elseif($data['user_type'] =='building_manager_employee')//if creating a bme
        {
          $user_previleges =
          [
              'workorder'=>$request->workorder,
              'assets'=>$request->assets,
              'contracts'=>$request->contracts,
              'tenant'=>$request->tenant

          ];
          $data['properties'] = Helper::getPropertiesByBuildingIds($data['building_ids']);
        }
        elseif( $data['user_type'] =='building_manager')//if creating building manager
        {
          $user_previleges =
          [
            'workorder'=>$request->workorder,
            'assets'=>$request->assets,
            'contracts'=>$request->contracts,
            'tenant'=>$request->tenant,
            'inventory'=>$request->inventory,

          ];
          $data['properties'] = Helper::getPropertiesByBuildingIds($data['building_ids']);
        }
        elseif($data['user_type'] =='supervisor')//if creating supervisor
        {
          $user_previleges =
            [
              'contracts'=>$request->contracts
            ];
        }
        elseif($data['user_type'] =='sp_worker')//if creating supervisor
        {
          $user_previleges =
          [
            'work_qr'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[0] : ['no'],
            'workorder'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[1] : ['no']
          ];
        }
        elseif($data['user_type'] =='team_leader')// if creating supervisor
          {
              $user_previleges =
              [
                'lead_action_on_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[0] : ['no'],
                'lead_scan_on_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[1] : ['no'],
                'lead_report_maintenance_issues_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[2] : ['no']
              ];
          }
        elseif ($data['user_type'] == 'store_keeper') {
            $user_previleges =
                [
                    'warehouse' => $request->warehouse,
                ];
        }elseif (($logedin_user->user_type =='admin' || $logedin_user->user_type=='admin_employee') && $data['user_type'] == 'procurement_admin') {
          Log::info('user privielege1');
          $user_previleges =
              [
                  'purchases'=>$request->purchases,
                  'warehouses' => $request->warehouses,
                  'inventory'=>$request->inventory,
                  'vendor'=>$request->vendor,
                  'workorder'=>$request->workorder,
              ];
      }
        else
        {
          $user_previleges =[];
        }
      }
      
      if($logedin_user->user_type=='sp_admin' || $logedin_user->user_type=='supervisor')//for sp admin and supervisor
      {
        if($data['user_type'] != 'sp_worker' && $data['user_type'] != 'store_keeper' && $data['user_type'] != 'team_leader')//if not creating worker
        {
          $user_previleges =
          [
              'contracts'=>$request->contracts
          ];
          if(isset($user_previleges)) //check data
          {
            $data['user_privileges']=json_encode($user_previleges);
          }
        }
        elseif($data['user_type'] == 'team_leader')// if creating supervisor
          {
              $user_previleges =
              [
                'lead_action_on_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[0] : ['no'],
                'lead_scan_on_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[1] : ['no'],
                'lead_report_maintenance_issues_behalf_of_workers'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[2] : ['no']
              ];
          }
        elseif ($data['user_type'] == 'store_keeper') {
            $user_previleges =
                [
                    'warehouse' => $request->warehouse,
                ];
        }elseif ($logedin_user->user_type=='sp_admin' && $data['user_type'] == 'procurement_admin') {
          Log::info('user privielege1');
          $user_previleges =
              [
                  'purchases'=>$request->purchases,
                  'warehouses' => $request->warehouses,
                  'inventory'=>$request->inventory,
                  'vendor'=>$request->vendor,
                  'workorder'=>$request->workorder,
              ];
      }
        else{
          $user_previleges =
            [
              'work_qr'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[0] : ['no'],
              'workorder'=>(session()->get('worker_privilege'))?session()->get('worker_privilege')[1] : ['no']
            ];
        }
        $user_data = Helper::userDetail($id);
        $data['service_provider'] = $user_data->service_provider;
        if(isset($data['building_ids']))
        {
          $data['properties'] = Helper::getPropertiesByBuildingIds($data['building_ids']);
        }
      }
      //dd($user_previleges);
      if($logedin_user->user_type=='super_admin' || $logedin_user->user_type=='admin' ||$logedin_user->user_type=='admin_employee' || $logedin_user->user_type=='building_manager' )//fpr admin, super admin, admin employee, building manager
      {
        if($data['user_type'] == 'building_manager_employee') //if creating bme
        {
          if(isset($user_previleges)) //check data
          {
            $data['user_privileges']=json_encode($user_previleges);
          }
          $data['properties'] = Helper::getPropertiesByBuildingIds($data['building_ids']);
        }
        if($data['user_type'] == 'building_manager') //if creating bme
        {
          $data['properties'] = Helper::getPropertiesByBuildingIds($data['building_ids']);
        }
      }
      if(isset($user_previleges)) //check data
      {
        $data['user_privileges']=json_encode($user_previleges);
      }
      $data['created_by'] = $logedin_user->id;
      $randompw = '';
      if(isset($request->new_password))//check if creating new password
      {
        $randompw=$request->new_password;
        $hashedPassword = Hash::make($randompw);
        $data['password'] = $hashedPassword;
      }
      if(isset($data['supervisor_id']) && !empty($data['supervisor_id']) && is_array($data['supervisor_id']))
      {
        $data['supervisor_id'] = implode(',', $data['supervisor_id']);
      }
      if (!isset($data['profile_img']) && isset($data['isImageRemove']) === true) {
        $data['profile_img'] = null;
      }
      unset($data['isImageRemove']);
      
  
      if (isset($data['user_type']) && $data['user_type'] === 'sp_worker') {
          $data['salary'] = $data['salary'] ?? null;
          $data['attendance_target'] = $data['attendance_target'] ?? null;
          $data['role'] = $data['role'] ?? null;
          $data['admin_level'] = $data['admin_level'] ?? null;
          $data['attendance_mandatory'] = $data['attendance_mandatory'] ?? 0;
          $data['show_extra_info'] = $data['show_extra_info'] ?? 0;
      }

      Log::info("6089",['data' => json_encode($data)]);
      $user = User::find($id);
      $user->update($data);
      if($data['user_type'] == 'sp_worker' || $data['user_type'] == 'team_leader')
            {
              
              if(isset($data['contract_ids']))
              {
                $newContractIds = explode(',',$data['contract_ids']);
                //When create worker then add contract worker mapping
                $this->updateWorkerContracts($newContractIds,$id,'update');
              }
        }
      // dd(User::where('id', $data['id'])->get(),$id, $data, $user);


      if( $data['user_type'] =='building_manager')//if creating building manager
        {
          $allowAkauntingforEmployee = in_array('view', $request->inventory) ? 1 : 0;
          $data['allow_akaunting'] =$allowAkauntingforEmployee;
          User::where('sp_admin_id', $data['id'])->update(['allow_akaunting' => $allowAkauntingforEmployee]);

          ProjectsDetails::where('id', $user->project_id)->update(['use_erp_module'=>$allowAkauntingforEmployee ]);
        }
      if(isset($request->work_order_approve)){
            $dataToInsert = UserSubPrivileges::updateOrCreate(
              ['privilage_id'=>(Helper::getSubPrivilegeIdBySlug(Helper::getUserTypeIdBySlug($user->user_type),'work_order_approve'))?Helper::getSubPrivilegeIdBySlug(Helper::getUserTypeIdBySlug($user->user_type),'work_order_approve'):1,
              'privilage_user_id'=>$user->id],
              ['value' => $request->work_order_approve[0]]
          );
      }
      if($data['user_type'] == 'supervisor' || $data['user_type'] == 'sp_worker') //if creating supervisor or worker
      {
        //dd($asset_categories_contacts);
        if(isset($asset_categories_contacts)) {
          if(count($asset_categories_contacts)>0) {
            $res = DB::table('user_assets_mapping')->where('user_id',$id)->delete();

            foreach($asset_categories_contacts as $rr) {
              $arr = explode(',',$rr);
              DB::table('user_assets_mapping')->insert([
                'user_id' => $id,
                'user_type' => $data['user_type'],
                'contract_id' =>$arr[0],
                'asset_id' =>$arr[1]
              ]);
            }

          }
        }
        Helper::updateSupervisersWorkOrders($id);
      }

      if($data['user_type'] == 'building_manager') {

        if(isset($data['contract_ids'])) {
          $up_data = [
            'building_ids' => $data['building_ids'],
            'properties' => $data['properties'],
            'contract_ids' => $data['contract_ids'],
            'role_regions' => $data['role_regions'],
            'role_cities' => $data['role_cities'],
            'asset_categories' => $data['asset_categories'],
          ];
        } else {
          $up_data = [
            'building_ids' => $data['building_ids'],
            'properties' => $data['properties'],
            'role_regions' => $data['role_regions'],
            'role_cities' => $data['role_cities'],
            'asset_categories' => $data['asset_categories'],
          ];
        }
      }
      if($data['user_type'] == 'store_keeper'){
          $user->syncWarehouses($data['keeper_warehouses']);
      }
      $request->session()->forget('user_info');
      // @flip1@ add creadential for worker
      if($data['user_type'] == 'sp_worker' || $data['user_type'] == 'team_leader'){
        $this->data['worker_id']= $data['email'];
        if($randompw!= ''){
          $this->data['password']= $randompw;
        }
        $this->data['is_password_same'] = 'yes';
        if (!Hash::check(Auth::user()->password, $user->password)) {
          $this->data['is_password_same'] = 'no';
        }

      }
      if($logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee' || $logedin_user->user_type=='sp_admin'){
      if($data['user_type'] =='procurement_admin'){
        // $data['approved_max_amount'] = session('max_ammount', 0);
        $existing_user = User::find($id);
        $existing_user->update($data);
          $this->updatePrAdminAndSyncWithAkauntingCutomer($existing_user->id);
        if ($existing_user->permissionPa) {
          $existing_user->permissionPa->update([
            'initiate_pr'=>session::get('initiate_status',1),
            'send_pr_to_v'=>session::get('send_status',1),
            'view_v_q'=>session::get('receive_status',1),
            'require_app'=>session::get('app_reject_status',1),
            'manage_pay'=>session::get('manage_status',1),
            'request_bafo'=>session::get('createpo_status',1),
            'auto_purchase'=>session::get('auto_purchase') ?? 1,
            'amount_field'=>session::get('max_ammount') ?? 0,
           
          ]);
      } else {
          // Optionally, create a new permissionPa record if none exists
          $existing_user->permissionPa()->create([
              'user_id' => $existing_user->id,
              'initiate_pr'=>session::get('initiate_status',1),
            'send_pr_to_v'=>session::get('send_status',1),
            'view_v_q'=>session::get('receive_status',1),
            'require_app'=>session::get('app_reject_status',1),
            'manage_pay'=>session::get('manage_status',1),
            'request_bafo'=>session::get('createpo_status',1),
            'auto_purchase'=>session::get('auto_purchase') ?? 1,
            'amount_field'=>session::get('max_ammount') ?? 1,
          
            ]);
      }
        Log::info($existing_user);

    }}
      if( !Session::has('entered_project_id') && Session::has('service_provider_id') ) {
        $supervisor_id = Session::get('service_provider_id');
        //dd($supervisor_id);
        $this->data['service_provider'] = $supervisor_id;
        //dd($this->data['service_provider']);
        $this->data['user_id']=$id;
        $this->data['user_type'] = $data['user_type'];
        return view($this->view_path.'.edit.public_sp.create-confirmation', ['data'=>$this->data]);
      } else {
        $this->data['user_id']=$id;
        $this->data['user_type'] = $data['user_type'];

        $request->session()->forget('initiate_status');
        $request->session()->forget('send_status');
        $request->session()->forget('receive_status');
        $request->session()->forget('app_reject_status');
        $request->session()->forget('manage_status');
        $request->session()->forget('createpo_status');
          $request->session()->forget('auto_purchase') ;
       
        $request->session()->forget('max_ammount');
        
        return view($this->view_path.'.edit.edit-confirmation', $this->data);
      }

    }

    /**
     * GET users/ajax/get_city_list/{id?}
     *
     * Return JSON response list of cities in selected Region.
     * Return JSON response with a list of cities for the selected region.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function ajaxGetCityList(Request $request, $id=null){
      if($request->ajax()){//check for ajax request
        if(!empty($request->id) || !empty($request->_token)){
          return response()->json(Helper::getCityList($request->id));
        }
      }
    }

    /**
     * GET users/resend_email_varification
     *
     * Return JSON with success message.
     * Resend verification email to the provided email id
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function resend_email_varification(Request $request) {
      $user_id = $request->user_id;
      $user_detail = DB::table('users')->where('id',$user_id)->first();
      if ($user_detail->email_attempts >= 3 && now()->diffInHours($user_detail->last_email_attempt_at) < 24) {
        $message = trans('user_management_module.email.email_attempts_times');
          return response()->json([
              'status' => 'error',
              'success' =>$message
          ]);
      }
      if (now()->diffInHours($user_detail->last_email_attempt_at) >= 24) {
          DB::table('users')->where('id', $request->user_id)->update([
              'email_attempts' => 1,
              'last_email_attempt_at' => now(),
          ]);
      } else {
          DB::table('users')->where('id', $request->user_id)->increment('email_attempts');
          DB::table('users')->where('id', $request->user_id)->update([
              'last_email_attempt_at' => now(),
          ]);
      }
      $to_name = $user_detail->name;
      $to_email = $user_detail->email;
      $subject = trans('user_management_module.email.resend_email_verification_subject');
      $sender_email = Helper::getAdminContactMail();
      event(new VarificationEmail($to_name, $to_email,$subject,$sender_email));
      $message = trans('user_management_module.email.success_message_resend_varification_mail');
      return json_encode(['status'=>'success','success'=>$message]);
    }

    /**
     * GET users/ajax/get_city_list_region/{id?}
     *
     * Return JSON to Get list of cities in selected Region Id
     * Return JSON respnse with a list of cities for the selected region.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function ajaxGetCityListRegionsId(Request $request, $id=null)
    {
      // dd($request->id);
      if($request->ajax()){//check for ajax request
        if( isset($request->id) && (!empty($request->id) || !empty($request->_token))){//check params
          $users=Auth::user();
          $session_user_info=session()->get('user_info');
          //dd($session_user_info);
          $roles_city=json_decode($users->role_cities);
          if(isset($roles_city->id)) //check params
          {
            $selectedcityArr=$roles_city->id;
          }
          else
          {
            $selectedcityArr=explode(",",$users->role_cities);
          }
          $project_id = Helper::getProjectID();
          $project_details = DB::table('projects_details')
                   ->select('project_name')
                   ->where('id',$project_id)
                   ->first();
          if(isset($project_details))
          {
            $projectname = trim($project_details->project_name);
          }
          else
          {
            $projectname = '';
          }
          $contract_city_ids = DB::table('contracts')->select('city_id')->get();
          if(isset($contract_city_ids))
          {
            $contract_city_ids = json_decode(json_encode($contract_city_ids), true);
            $contract_city_ids = array_column($contract_city_ids, 'city_id');
          }
          else
          {
              $contract_city_ids = array(0);
          }
          $property_city_ids = DB::table('properties')->select('city_id')->where('project',$projectname)->whereIn('id',$contract_city_ids)->get();
          if(isset($property_city_ids))
          {
            $property_city_ids = json_decode(json_encode($property_city_ids), true);
            $property_city_ids = array_column($property_city_ids, 'RegionId');
          }
          else
          {
              $property_city_ids = array(0);
          }
          $all_city_data=DB::table('cities')
          ->whereIn('region_id',$request->id)
          //->whereIn('id',$property_city_ids)
          ->get();
          foreach ($all_city_data as $key => $value) {
            $cityArr[]=$value->id;
          }
          $cityArrFiltered=array();
          if($users->user_type=='building_manager') //for bm
          {
            if(!empty($cityArr))
            {
              if(!empty($selectedcityArr)&&$selectedcityArr[0]!='')
              {
                $cityArrFiltered = array_intersect($cityArr, $selectedcityArr);
              }
            }
          }
          else if($users->user_type=='admin' || $users->user_type=='admin_employee' )//for admin
          {
            $cityArr=array();
            //dd($session_user_info['user_type']);
            if($session_user_info['user_type'] == "building_manager_employee")
            {
              //dd($session_user_info['id']);
              if(isset($session_user_info['id']))
              {
                $u_details = DB::table('users')->where('id', $session_user_info['id'])->first();
                //dd($u_details);
                $bm_details = User::where('id', $u_details->sp_admin_id)->first();
                //dd($bm_details);
                if(isset($bm_details))
                {
                  $cityArrFiltered = DB::table('cities')->whereIn('region_id', $request->id)->whereIn('id', explode(',', $bm_details->role_cities))->pluck('id');
                }
              }
              if(isset($session_user_info['sp_admin_id']))
              {
                $bm_details = User::where('id', $session_user_info['sp_admin_id'])->first();
                //dd($bm_details);
                if(isset($bm_details))
                {
                  $cityArrFiltered = DB::table('cities')->whereIn('region_id', $request->id)->whereIn('id', explode(',', $bm_details->role_cities))->pluck('id');
                }
              }
              elseif(isset($session_user_info['building_admin']))
              {
                $bm_details = User::where('id', $session_user_info['building_admin'])->first();
                //dd($bm_details);
                if(isset($bm_details))
                {
                  $cityArrFiltered = DB::table('cities')->whereIn('region_id', $request->id)->whereIn('id', explode(',', $bm_details->role_cities))->pluck('id');
                }
              }
            }
            else
            {
              $properties=DB::table('properties')->select('city_id');
              //dd($request->id);
              if(isset($request->id))
              {
                if($session_user_info['user_type'] == "building_manager")
                {
                  $properties = $properties->whereIn('region_id', $request->id);
                }
                else
                {
                  //dd($projectname);
                  $properties = $properties->whereIn('region_id', $request->id);
                }
              }
              $properties = $properties->where('user_id',Auth::user()->project_user_id)->groupBy('region_id','city_id')->get();
              //dd($properties);
              $cityProperties=array();
              if(!empty($properties))//check data
              {
                foreach($properties as $propertiesdata)
                {
                  $cityProperties[]=$propertiesdata->city_id;
                }

                if(!empty($cityArr))//check cities
                {
                  $cityArrFiltered = array_intersect($cityArr, $cityProperties);
                }
                else
                {
                  $cityArrFiltered = $cityProperties;
                }
              }
            }
          }
          else if($users->user_type=='sp_admin')//for sp admin
          {
            if($session_user_info['user_type']=='supervisor' || $session_user_info['user_type']=='store_keeper')//if creating supervisor
            {
              $users_service_provider=$users->service_provider;
              //dd($users_service_provider);
              $contracts=DB::table('contracts')->where('service_provider_id',$users_service_provider)->where('contracts.end_date','>=',date('Y-m-d'))->get();
              //dd($contracts);
              if(!empty($contracts))
              {
                $cityContracts = [];
                foreach($contracts as $contractsdata)
                {
                  $cities = explode(',',$contractsdata->city_id);
                  if(count($cities) > 1)
                  {
                    foreach($cities as $city)
                    {
                      $cityContracts[] = $city;
                    }
                  }
                  else
                  {
                    $cityContracts[] = $cities[0];
                  }
                }
                //dd($cityContracts);
                if(!empty($cityArr) && !empty($cityContracts))//check data
                {
                  $cityArrFiltered = array_intersect($cityArr, $cityContracts);
                }
              }
              //dd($cityArrFiltered);
            }
          }
          else if($users->user_type=='super_admin' || $users->user_type=='osool_admin')//for super admin and osool admin
          {
            if($session_user_info['user_type']=='supervisor')//if creating supervisor
            {
              $sp_admin_id=$session_user_info['sp_admin_id'];
              $sp_admin_details=DB::table('users')->where('id', $sp_admin_id)->first();
              $sp_admin_service_provider=$sp_admin_details->service_provider;
              $contracts=DB::table('contracts')->where('service_provider_id',$sp_admin_service_provider)->where('contracts.end_date','>=',date('Y-m-d'))->get();
              if(!empty($contracts))//check data
              {
                $cityContracts = [];
                foreach($contracts as $contractsdata)
                {
                  $cities = explode(',',$contractsdata->city_id);
                  if(count($cities) > 1)
                  {
                    foreach($cities as $city)
                    {
                      $cityContracts[] = $city;
                    }
                  }
                  else
                  {
                    $cityContracts[] = $cities[0];
                  }
                }
                //dd($cityContracts);
                if(!empty($cityArr) && !empty($cityContracts))//check data
                {
                  $cityArrFiltered = array_intersect($cityArr, $cityContracts);
                }
              }

              // dd( $cityArrFiltered);
            }
            else if($session_user_info['user_type']=='building_manager_employee')//if creating bme
            {
              $sp_admin_id=$request->admin_id;
              if(isset($session_user_info['sp_admin_id']))
              {
                $sp_admin_id=$session_user_info['sp_admin_id'];
              }else
              {
                $sp_admin_id=$session_user_info['building_admin'];
              }
              $sp_admin_details=DB::table('users')->where('id', $sp_admin_id)->first();
              $cityBuildingManagerArr=explode(",",$sp_admin_details->role_cities);
              if(!empty($cityArr))
              {
                if(!empty($cityBuildingManagerArr))
                $cityArrFiltered = array_intersect($cityArr, $cityBuildingManagerArr);
              }
            }
            else
            {
              $cityArrFiltered=$cityArr;
            }
          }
          else
          {
            $cityArrFiltered=$cityArr;
          }
          //dd($cityArrFiltered);
          return response()->json(Helper::getUserCity($cityArrFiltered));
        }
      }
    }

    /**
     * GET users/ajax/get_building_list_region/{id?}
     *
     * Return JSON list of buildings in selected Region Id
     * Return JSON response with a list of buildings for the selected region from.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function ajaxGetBuildingListRegionsId(Request $request, $id=null)
    {
      $selected_user_type = '';
      if(session()->has('user_info')){ //check session data
        $selected_user_type = session()->get('user_info')['user_type'];
      }
      if($request->ajax()){//check for ajax request
        if(isset($request->id) && (!empty($request->id) || !empty($request->_token))){//check parameters
          $users=Auth::user();
          //dd(session()->get('user_info'));
          $city_ids = array();
          $city_ids = $request->id;
          $regions_id = $request->regions_id?? '';
          if($users->user_type == 'admin') {
            $service_provider_id = isset(session()->get('user_info')['service_provider']) ? session()->get('user_info')['service_provider'] : 0;
          } else {
            $service_provider_id = $request->sp_id;
          }
          $find_in_set = "(";

                for ($i = 0; $i < count($city_ids); $i++) {
                    if ($i == 0) {
                        $find_in_set .= "FIND_IN_SET(" . $city_ids[$i] . ",contracts.city_id)";
                    } else {
                        $find_in_set .= " OR FIND_IN_SET(" . $city_ids[$i] . ",contracts.city_id)";
                    }
                  }
              // if($regions_id){
              //   for ($i = 0; $i < count($regions_id); $i++) {// @flip1@ add also region
              //     $find_in_set .= " OR FIND_IN_SET(" . $regions_id[$i] . ",contracts.region_id)";
              //   }
              // }
          $find_in_set .= ")";
          $contractList=DB::table('contracts')
          ->select('contracts.*','projects_details.project_name','projects_details.project_name_ar')
          ->leftJoin('users','users.id','=','contracts.user_id')
          ->leftJoin('projects_details','projects_details.id','=','users.project_id')
          ->where('contracts.is_deleted','no')
          ->where('contracts.deleted_at',null)

          ->where('contracts.status',1)
          ->where('contracts.end_date','>=',date('Y-m-d'))
          ->whereraw($find_in_set);
          //->whereIn('contracts.city_id',$city_ids);
          if($users->user_type!='sp_admin')// for users other than sp admin
          {
            //dd($users->project_user_id);
            if(!Session::has('entered_project_id')) { // Global SP Creation
              //dd($users->project_user_id);
              if(Session::has('service_provider_id')) {
                $service_provider_id = Session::get('service_provider_id');
                //dd($service_provider_id);
                $contractList = $contractList->where('contracts.service_provider_id',$service_provider_id);
              } else {
                //dd($service_provider_id);
                if(!$service_provider_id)
                {
                  if(isset(session()->get('user_info')['service_provider']))
                  {
                    $service_provider_id = session()->get('user_info')['service_provider'];
                  }
                }
                $contractList = $contractList->where('contracts.service_provider_id',$service_provider_id);
              }
              if( !empty(Auth::user()->user_type == 'super_admin') || !empty(Auth::user()->user_type == 'admin') || !empty(Auth::user()->user_type == 'osool_admin') || !empty(Auth::user()->user_type == 'admin_employee'))
              {
                if( !empty(Auth::user()->user_type != 'super_admin') && !empty(Auth::user()->user_type != 'osool_admin') )
                {
                  $contractList = $contractList->where("contracts.user_id", "=", Auth::user()->project_user_id);
                }
              }
            } else { // Inside a project
              //dd($users->project_user_id);
              //If superadmin enters in to the project he will see Buildings irrespective of the Project he is in.. Chnage is discussed with Mr.Abdulrahaman and Mr. Haitham
              if(Auth::user()->user_type != 'super_admin' && Auth::user()->user_type != 'osool_admin')
              {
                $contractList = $contractList->where('contracts.user_id',$users->project_user_id);
              }
            }
          }
          if($users->user_type=='sp_admin')//for sp admin
          {
            $logedin_service_provider=$users->service_provider;
            $contractList = $contractList->where('contracts.service_provider_id',$logedin_service_provider);
          }
          // $query = str_replace(array('?'), array('\'%s\''), $contractList->toSql());
          // $query = vsprintf($query, $contractList->getBindings());
          // dd($query);
          $contractList_clone = clone $contractList;
          $contractList = $contractList->get();
          //dd($contractList);

          $all_building_data=DB::table('properties')->select('property_buildings.id','property_buildings.building_name as building_tag','properties.complex_name','properties.property_type')->join('property_buildings','properties.id','=','property_buildings.property_id')
          ->whereIn('properties.city_id',$city_ids);
          if($users->user_type != 'sp_admin')// for users other than sp admin
          {
            if(!Session::has('entered_project_id')) { // Global SP Creation
              $project_user_id = Helper::getServiceProviderAdminIds($service_provider_id);
              if(Session::has('service_provider_id')) {
                $service_provider_id = Session::get('service_provider_id');
                $all_building_data = $all_building_data->whereIn('properties.user_id',$project_user_id);
              } else {
                $project_user_id = $users->project_user_id;
                $all_building_data = $all_building_data->where('properties.user_id',$project_user_id);
              }
            } else { // Inside a project
              $all_building_data = $all_building_data->where('properties.user_id',$users->project_user_id);
            }
          }
          if($users->user_type=='building_manager')// for users bm and bme
          {
            $assigned_buildings=$users->building_ids;
            if(!empty($assigned_buildings))
            {
              $assigned_buildings_arr=explode(',', $assigned_buildings);
              $all_building_data = $all_building_data->whereIn('property_buildings.id',$assigned_buildings_arr);
            }
          }
          if($selected_user_type == 'building_manager_employee') //if seleted a bme
          {
            $building_manager_id = $request->admin_id;
            //dd(session()->get('user_info'));
            if(isset(session()->get('user_info')['sp_admin_id']))
            {
              $building_manager_id = session()->get('user_info')['sp_admin_id'];
            }
            elseif(isset(session()->get('user_info')['building_admin']))
            {
              if(isset(session()->get('user_info')['sp_admin_id']))
              {
                $building_manager_id = session()->get('user_info')['sp_admin_id'];
              }
              elseif(isset(session()->get('user_info')['building_admin']))
              {
                $building_manager_id = session()->get('user_info')['building_admin'];
              }
            }
            elseif(isset(session()->get('user_info')['id']))
            {
              $user_details = User::where('id',session()->get('user_info')['id'])->first();
              $building_manager_id = $user_details->sp_admin_id;
            }
            //dd(session()->get('user_info'));
            $user_details = User::where('id',$building_manager_id)->first();
            if(isset($user_details))
            {
              $assigned_buildings=$user_details->building_ids;
              if(!empty($assigned_buildings))//check data
              {
                $assigned_buildings_arr=explode(',', $assigned_buildings);
                $all_building_data = $all_building_data->whereIn('property_buildings.id',$assigned_buildings_arr);
              }
            }
          }
          if($selected_user_type == 'supervisor' || $selected_user_type == 'sp_worker') //if selected supervisor or worker
          {
            $sp_admin_id = session()->get('user_info')['service_provider'];
            //dd($sp_admin_id);
            $user_details = User::where('user_type','sp_admin')->where('service_provider',$sp_admin_id)->first();
            $service_provider_id = $sp_admin_id;
            if(isset($sp_admin_id))
            {
              //dd($service_provider_id);
              $contractList = $contractList_clone->where('service_provider_id', $service_provider_id)->get();
             // ->whereIn('city_id',$request->id);
              $contractList2 = [];
              foreach($contractList as $cl){
                Array_push($contractList2,$cl);
              }
              $contractList = $contractList2;
            }
            $contracts = DB::table('contracts')->join('contract_property_buildings', 'contract_property_buildings.contract_id', '=', 'contracts.id')->where('service_provider_id', $service_provider_id)->get()->pluck('property_building_id')->toArray();
            if(!empty($contracts))//check contract data
            {
              $all_building_data = $all_building_data->whereIn('property_buildings.id',$contracts);
              if($users->user_type!='sp_admin')// for users other than sp admin
              {
                $all_building_data = $all_building_data->where('properties.user_id',$user_details->project_user_id);
              }
            }
          }
          $all_building_data = $all_building_data
          ->whereNull('property_buildings.deleted_at')
          ->get();
          if(count($all_building_data)>0 || count($contractList)>0)
          {//check data count
            //Strcuture for Complex wise & bulding wise property
            $formatted_buildingList = Helper::FormatComplexwiseBuilding($all_building_data);
            header('Content-Type:application/json');
            if($selected_user_type == "supervisor"){//for supervisor
              die(json_encode(array("status"=>"1","message"=>"Success","formatted_buildingList"=>$formatted_buildingList, "all_building_data"=>$all_building_data, "contractList"=>$contractList)));
            }
            else{
              die(json_encode(array("status"=>"1","message"=>"Success","formatted_buildingList"=>$formatted_buildingList, "all_building_data"=>$all_building_data, "contractList"=>$contractList)));
            }
          }
          else
          {
            header('Content-Type:application/json');
            die(json_encode(array("status"=>"0","message"=>"Failed")));
          }
        }
      }
    }

    /**
     * GET users/ajax/get_sp_list/{id?}
     *
     * Return JSON response to Get list of users - Sp_ admin and supervisors
     * Return JSON respnse with a list of users Sp_ admin and supervisors for the selected service provider company.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function ajaxGetSPList(Request $request)
    {
      $company_id=$request->input('id');
      $response = array();
      if($company_id != "")//check paramerters
      {
        $sp_admin_list = User::where(['is_deleted' => 'no', 'status' => 1,'service_provider' => $company_id,'user_type'=>'sp_admin'])->get();

        if(Auth::user()->user_type == "admin_employee") //for admin employee
        {
          $sp_supervisor_list = User::where(['is_deleted' => 'no', 'status' => 1,'service_provider' => $company_id,'user_type'=>'supervisor'])->get();
        }
        else
        {
          $sp_supervisor_list = User::where(['is_deleted' => 'no', 'status' => 1,'service_provider' => $company_id,'user_type'=>'supervisor'])->get();
        }
        $response['sp_admin_list']=$sp_admin_list;
        $response['supervisor_list']=$sp_supervisor_list;
      }
      return response()->json($response);
    }

    /**
     * GET users/user_type_filter
     *
     * Return view with filter users for osool admin
     * To Filter users for osool admin.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function user_type_filter(Request $request)
    {
      $usertypeList=DB::table('user_type')
      ->where('slug','!=','osool_admin')
      ->where('slug','!=','super_admin')
      ->where('slug','!=','admin_employee')
      ->where('slug','!=','admin')
      ->where('slug','!=','tenant')
      ->where('status',1)->groupBy('user_type_tag')->get();
      $this->data['usertypeList']=$usertypeList;
      return view($this->view_path.'.create.osool_admin.filter_user', $this->data);
    }

    /**
     * GET users/ajax/get_userrole_list/{id?}
     *
     * Return JSON response for user roles list.
     * Return JSON respnse with a list of user roles from database.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function get_userrole_list(Request $request)
    {
      $user_type_tag = $request->input('user_type_tag');
      $user_role_list = DB::table('user_type')->where(['status' => 1,'user_type_tag' => $user_type_tag])->get();
      return response()->json($user_role_list);
    }

    /**
     * POST users/populate_by_contract
     *
     * Return JSON response after get building and asset categories data.
     * Return JSON respnse with building and asset categories data for selected contracts.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function populate_by_contract(Request $request)
    {
      $logedin_user=auth()->user();
      $session_user_info=session()->get('user_info');
      $selected_contracts = array();
      if($request->input('selected_contracts'))//check parameters
      {
        $selected_contracts = $request->input('selected_contracts');
      }
      $asset_cat_arr=array();
      $property_arr=array();
      $contracts_data = DB::table('contracts')->whereIn('id',$selected_contracts)->get();
      $asset_cat_str="";
      foreach($contracts_data as $contract)
      {
        $asset_cat_str.=",".$contract->asset_categories;
        $property_arr[]=$contract->properties;
      }
      $asset_cat_arr=explode(',',$asset_cat_str);
      if($logedin_user->user_type=='super_admin' || $logedin_user->user_type=='sp_admin')//for sp admin and super admin
      {
        if($session_user_info['user_type']=='sp_worker')//if selected worker
        {
          $supervisor_id=$session_user_info['supervisor_id'];
          $supervisor_details=DB::table('users')->where('id', $supervisor_id)->first();
          $asset_categories=$supervisor_details->asset_categories;
          $asset_categories_arr=explode(',',$asset_categories);

          $result_asset_category=array_intersect($asset_cat_arr,$asset_categories_arr);
          $asset_cat_data = DB::table('asset_categories')->where('status',1)->where('is_deleted', 'no')->whereIn('id',$result_asset_category)->get();
        }
        else
        {
          $asset_cat_data = DB::table('asset_categories')->where('status',1)->where('is_deleted', 'no')->whereIn('id',$asset_cat_arr)->get();
        }
      }
      else
      {
        $asset_cat_data = DB::table('asset_categories')->where('status',1)->where('is_deleted', 'no')->whereIn('id',$asset_cat_arr)->get();
      }
      /***********************modified due to contrcat prop building table *********************/
      $contract_building_data = DB::table('contract_property_buildings')->whereIn('contract_id',$selected_contracts)->get();
      $building_ids_arr=array();
      if(isset($contract_building_data))//check data
      {
        foreach ($contract_building_data as $key => $value) {
          $building_ids_arr[]=$value->property_building_id;
        }
      }
      if($logedin_user->user_type=='super_admin' || $logedin_user->user_type=='sp_admin')//for sp admin and super admin
      {
        if($session_user_info['user_type']=='sp_worker')//if selected worker
        {
          $supervisor_id=$session_user_info['supervisor_id'];
          $supervisor_details=DB::table('users')->where('id', $supervisor_id)->first();
          $building_ids=$supervisor_details->building_ids;
          $buildingids_arr=explode(',',$building_ids);
          $result=array_intersect($building_ids_arr,$buildingids_arr);
          $building_data = DB::table('property_buildings')
          ->whereNull('property_buildings.deleted_at')
          ->where('is_deleted','no')->whereIn('id',$result)->get();
        }
        else
        {
          $building_data = DB::table('property_buildings')->whereNull('property_buildings.deleted_at')->where('is_deleted','no')->whereIn('id',$building_ids_arr)->get();
        }
      }
      else
      {
        $building_data = DB::table('property_buildings')->whereNull('property_buildings.deleted_at')->where('is_deleted','no')->whereIn('id',$building_ids_arr)->get();
      }
      /****************************************************************************************/
      $response=array();
      $response['asset_cat_data']=$asset_cat_data;
      $response['building_data']=$building_data;
      return response()->json($response);
    }

    /**
     *
     * Function: test_mail()
     * Purpose: testing e-mail
     * Description:  Test new User registration email trigger
    */
    public function test_mail()
    {
      /******************************send email to user *******************************/
      $user_id_str=base64_encode(1);
      $data['name']="test";
      $data['email']="<EMAIL>";
      $slug = 'registration-successfully';
      $variable_value=[
          '##USERNAME##'=>"{$data['name']}",
          '##EMAIL##'=>"{$data['email']}",
          '##PASSWORD##'=>"123456",
          '##SITELINK##'=>"<a href='".route('reset_password',$user_id_str)."' alt='Osool Link'>".url('/')."</a>",
      ];
      $mail_content=Helper::emailTemplateMail($slug,$variable_value);
      $sender_email=Helper::getAdminContactMail();
      $to_name= $data['name'];
      $to_email= $data['email'];
      $subject="Registration Successful";
      if($to_email!='')//check null email id
      {

        try{


        Mail::send('mail.send_email', ['mail_content' => $mail_content],function($message) use ($to_name, $to_email,$subject,$sender_email) {
        $message->to($to_email, $to_name)
        ->subject($subject);
        $message->from($sender_email,'Osool Team');
        });
       }
        catch (\Exception $e) {
          //$request->session()->put('emailNotSent', true);

          Log::info('email sending fail :'. $e->getMessage());
          dd($e->getMessage());
          //dd($e);
        }
      }
      /*******************************************************************************/
    }

    /**
     * GET users/reset_password/{user_id}
     *
     * Return view to Reset password validate link.
     * Check and confirm password reset requests and if the reset link is expired for the user email.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function reset_password($user_id)
    {
      //dd('test');
      $user_id = base64_decode($user_id);
      $user_detail = DB::table('users')->select('email')->where('id',$user_id)->first();
      $email = $user_detail->email;
      //dd($email);
      $this->data['email'] = $email;
      //dd($this->data['email']);
      $this->data['pageTitle']='Reset Password';

      $data = \DB::table('password_resets')->where('email', $email)->first();
        if(isset($data))
        {

                $to_time = strtotime(date('Y-m-d H:i:s'));
                $from_time = strtotime($data->created_at);

                $diff_minutes = round(abs($to_time - $from_time) / 60);
                if($diff_minutes < 60)
                {
                    return view('auth/passwords/reset',['data'=>$this->data]);
                }
                else
                {
                    return view('applications.admin.user.reset_password_link_expired');
                }
        }
        else
        {
          return view('applications.admin.user.reset_password_link_expired');
        }


    }

    /**
     * POST users/update_reset_password
     *
     * Return redirect update password.
     * Password reset/ update in database while checking if thedate reset link is expired for the user request and redirect to login after successful password update.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function update_reset_password(Request $request)
    {
      if(isset($request->token)) {
        $email=$request->email;
        $token =  $request->token;

        //dd($hash_token);
        $data = DB::table('password_resets')->where('email', $email)->first();
        //dd($data);
        if(isset($data))
        {
              if (Hash::check($token, $data->token)) {
                $data=array();
                $pw=$request->password;
                $hashedPassword = Hash::make($pw);
                $data['password'] = $hashedPassword;
                User::where('email', $email)->update($data);
                $delete_pass_request = DB::table('password_resets')->where('email', $email)->delete();
                return redirect()->to('/login')->with('success',trans('login.login_page.password_reset_successfully'));
              }
        }
        else
        {
          return view('applications.admin.user.reset_password_link_expired');
        }

      } else {
        $email=$request['email'];
        //dd($hash_token);
        $userdata = DB::table('password_resets')->where('email', $email)->first();
        if(isset($userdata))
        {
                $email=$request['email'];

                $pw=$request['password'];
                $hashedPassword = Hash::make($pw);
                $data['password'] = $hashedPassword;
                User::where('email', $email)->update($data);
                $delete_pass_request = DB::table('password_resets')->where('email', $email)->delete();
                return redirect()->to('/')->with('success',trans('login.login_page.password_reset_successfully'));

        }
        else
        {
          return view('applications.admin.user.reset_password_link_expired');
        }
      }
    }

    /**
     * GET users/ajax/get_sp_admins_list/{user_type_val?}
     *
     * Return JSON response get service providers list.
     * Return JSON response with service providers data.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function ajaxGetSpAdminsList(Request $request, $id=null)
    {
      $users=Auth::user();
      $project_id = Helper::getProjectID();
      $companyList = ServiceProvider::serviceProvidersAjax($users, $project_id, $request->user_type_val == NULL);
      return json_encode(array("status"=>"1","message"=>"Success", "companyList"=>$companyList));
    }

    public function ajaxGetSpAdminsListById(Request $request)
    {
      $sp_id = User::where('id',$request->id)->value('service_provider');
      $sp_admins = User::where('service_provider',$sp_id)
                        ->where('user_type','sp_admin')
                        ->where('id','!=',$request->id)
                        ->get();
      return $sp_admins;
    }

    /**
     * GET users/ajax/get_categories_list/{id?}
     *
     * Return JSON response to get asset categories list for slected contracts.
     * Return JSON respnse with asset categories data and building data for slected contracts.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function ajaxGetCategoriesList(Request $request, $id=null)
    {
      $session_user_info=session()->get('user_info');
      $building_data = [];
      $logedin_user=Auth::user();
      //dd($logedin_user);

      if(empty($request->contact_ids)){
         header('Content-Type:application/json');
         die(json_encode(array("status"=>"3","message"=>"Success", "content"=>[],'building_data'=>[])));
        }
      //dd($request->cities);
      $selected_contracts=$request->contact_ids;
      $building_ids_arr=array();
      if(!empty($selected_contracts)) {//check data
        $contract_building_data = DB::table('contract_property_buildings')
        ->join('property_buildings', 'property_buildings.id','=','contract_property_buildings.property_building_id')
        ->join('properties', 'properties.id','=','property_buildings.property_id')
        ->whereIn('contract_id',$selected_contracts);
        if (!empty($session_user_info['user_type']) && $session_user_info['user_type'] == "supervisor" && !empty($request->cities)) {
          $contract_building_data = $contract_building_data->whereIn('properties.city_id', $request->cities);
        }
        $contract_building_data = $contract_building_data->get();
        if(isset($contract_building_data))//check building data
        {
          foreach ($contract_building_data as $key => $value) {
            $building_ids_arr[]=$value->property_building_id;
          }
        }
      }
      //dd($building_ids_arr);
      if(($logedin_user->user_type=='super_admin' || $logedin_user->user_type=='osool_admin' || $logedin_user->user_type=='admin' || $logedin_user->user_type=='admin_employee') && $logedin_user->user_type !='supervisor')//for sp admin and super admin
      {
        if($session_user_info['user_type']=='sp_worker')//if creating worker
        {
          $supervisor_id=$session_user_info['supervisor_id'];
          //dd($supervisor_id);
          $supervisor_details=DB::table('users')->where('id', $supervisor_id)->first();
          $building_ids=$supervisor_details->building_ids;
          //dd($building_ids);
          $buildingids_arr=explode(',',$building_ids);
          $buildingids_arr = array_intersect($building_ids_arr, $buildingids_arr);
          //dd($buildingids_arr);
          //$result=$building_ids_arr;
          $building_data = DB::table('property_buildings')
          ->select('property_buildings.*', 'properties.complex_name', 'properties.property_type')
          ->join('properties','properties.id','=','property_buildings.property_id')
          ->where('property_buildings.is_deleted','no')
          //->whereIn('property_buildings.id',$result)
          ->whereIn('property_buildings.id',$buildingids_arr)
          ->get();
        }
        else
        {
          $building_data = DB::table('property_buildings')
          ->select('property_buildings.*', 'properties.complex_name', 'properties.property_type')
          ->join('properties','properties.id','=','property_buildings.property_id')
          ->where('property_buildings.is_deleted','no')
          ->whereNull('property_buildings.deleted_at')
          ->whereIn('property_buildings.id',$building_ids_arr)->get();
        }
      }
      elseif($logedin_user->user_type=='sp_admin') {
        if($session_user_info['user_type']=='sp_worker')//if creating worker
        {
          $supervisor_id = $session_user_info['supervisor_id'];
          //dd($supervisor_id);
          $supervisor_details=DB::table('users')->where('id', $supervisor_id)->first();
          $building_ids=$supervisor_details->building_ids;
          $buildingids_arr=explode(',',$building_ids);
          $result=$buildingids_arr;
          //dd($result);
          $result = array_intersect($building_ids_arr, $buildingids_arr);
          $building_data = DB::table('property_buildings')
          ->select('property_buildings.*', 'properties.complex_name', 'properties.property_type')
          ->join('properties','properties.id','=','property_buildings.property_id')
          ->where('property_buildings.is_deleted','no')
          ->whereNull('property_buildings.deleted_at')
          ->whereIn('property_buildings.id',$result)
          ->whereIn('property_buildings.id',$building_ids_arr)

          ->get();
        }else {

          $building_data = DB::table('property_buildings')
          ->select('property_buildings.*', 'properties.complex_name', 'properties.property_type')
          ->join('properties','properties.id','=','property_buildings.property_id')
          ->where('property_buildings.is_deleted','no')
          ->whereNull('property_buildings.deleted_at')
          ->whereIn('property_buildings.id',$building_ids_arr)

          ->get();

        }
      }
      elseif($logedin_user->user_type=='supervisor' )//for sp admin and super admin
      {
        $supervisor_id = $logedin_user->id;
        //dd($supervisor_id);
        $supervisor_details=DB::table('users')->where('id', $supervisor_id)->first();
        $building_ids=$supervisor_details->building_ids;
        $buildingids_arr=explode(',',$building_ids);
        $result=$buildingids_arr;
        //dd($result);
        $buildingids_arr = array_intersect($building_ids_arr, $buildingids_arr);
        $building_data = DB::table('property_buildings')
        ->select('property_buildings.*', 'properties.complex_name', 'properties.property_type')
        ->join('properties','properties.id','=','property_buildings.property_id')
        ->where('property_buildings.is_deleted','no')
        //->whereIn('property_buildings.id',$result)
        ->whereIn('property_buildings.id',$buildingids_arr)
        ->get();
      }
      else
      {
        $building_data = DB::table('property_buildings')
        ->select('property_buildings.*', 'properties.complex_name', 'properties.property_type')
        ->join('properties','properties.id','=','property_buildings.property_id')
        ->where('property_buildings.is_deleted','no')
        ->whereNull('property_buildings.deleted_at')
        ->whereIn('property_buildings.id',$building_ids_arr)

        ->get();
      }
      //dd($building_data);
      if(count($building_data) > 0) {
        foreach($building_data as $bd){
          if($bd->property_type =='complex'){
            //$bd->building_tag=  $bd->complex_name.' - '.$bd->building_name;
            $bd->building_tag=$bd->building_name;
          }
          else{
            $bd->building_tag=$bd->building_name;
          }
        }
      }
      if($request->ajax()){//check for ajax request
        if(!empty($request->id) || !empty($request->_token)){
          $users=Auth::user();
          /*
          if($users->user_type == 'supervisor') {
            $user_id = $users->id;
          } else {
            $user_id = $request->user_id;
          }
          */
          if($users->user_type == 'supervisor' ) {
            if(session()->has('user_info') && session()->get('user_info')['user_type'] == 'sp_worker' && isset(session()->get('user_info')['id']) ) {
              $user_id = session()->get('user_info')['id']; // Worker ID
              $user_asset_catids = Helper::getUserAssetContactIds( $user_id );
            } else if(session()->has('user_info') && session()->get('user_info')['user_type'] == 'sp_worker') {
              $user_id = $users->id;
              $user_asset_catids = [];
            }
          }
          elseif($users->user_type == 'sp_admin') {
            if($users->user_type == 'sp_admin' && (session()->get('user_info')['user_type'] == 'sp_worker' || session()->get('user_info')['user_type'] == 'supervisor') && isset(session()->get('user_info')['id'])) {
              $user_id = session()->get('user_info')['id']; // Super visor

            } elseif($users->user_type == 'sp_admin' && (session()->get('user_info')['user_type'] == 'sp_worker' || session()->get('user_info')['user_type'] == 'supervisor')) {
              $user_id = $users->id; // SPAdmin ID
            }
            //dd($user_id);
            $user_asset_catids = Helper::getUserAssetContactIds( $user_id );
          }
          elseif( ($users->user_type == 'super_admin' || $users->user_type == 'osool_admin') && !Session::has('entered_project_id')) { // Global SP ADMIN,Supervisor,Worker Creation
            if(session()->get('user_info')['user_type'] == 'supervisor' && isset(session()->get('user_info')['sp_admin_id'])) {
              $user_id = session()->get('user_info')['sp_admin_id']; // Sp Admin ID
              if(isset(session()->get('user_info')['id'])) {
                $supervisor_id = session()->get('user_info')['id'];
                $user_asset_catids = Helper::getUserAssetContactIds( $supervisor_id );
              } else{
                $supervisor_id = session()->get('user_info')['sp_admin_id'];
                $user_asset_catids = Helper::getUserAssetContactIds( $supervisor_id );
              }

            } elseif(session()->get('user_info')['user_type'] == 'sp_worker') {
              $user_id = $users->id; // SPAdmin ID
              if(isset(session()->get('user_info')['id'])) {
                $supervisor_id = session()->get('user_info')['id'];
                $user_asset_catids = Helper::getUserAssetContactIds( $supervisor_id );
              } else{
                //dd(session()->get('user_info'));
                $supervisor_id = session()->get('user_info')['supervisor_id'];
                $user_asset_catids = [];
              }
            }
          }
          elseif( ($users->user_type == 'super_admin' || $users->user_type == 'osool_admin') && Session::has('entered_project_id')) { // Global SP ADMIN,Supervisor,Worker Creation
            if(session()->get('user_info')['user_type'] == 'supervisor' && isset(session()->get('user_info')['sp_admin_id'])) {
              $user_id = session()->get('user_info')['sp_admin_id']; // Sp Admin ID
              if(isset(session()->get('user_info')['id'])) {
                $supervisor_id = session()->get('user_info')['id'];
                //dd($supervisor_id);
                $user_asset_catids = Helper::getUserAssetContactIds( $supervisor_id );
                //dd($user_asset_catids);
              } else{
                $supervisor_id = session()->get('user_info')['sp_admin_id'];
                $user_asset_catids = Helper::getUserAssetContactIds( $supervisor_id );
              }

            } elseif(session()->get('user_info')['user_type'] == 'sp_worker') {
              $user_id = $users->id; // SPAdmin ID
              if(isset(session()->get('user_info')['id'])) {
                $supervisor_id = session()->get('user_info')['id'];
                $user_asset_catids = Helper::getUserAssetContactIds( $supervisor_id );
              } else{
                //dd(session()->get('user_info'));
                $supervisor_id = session()->get('user_info')['supervisor_id'];
                $user_asset_catids = [];
              }
            }
          }
          else {
            $user_asset_catids = [];
            $user_id = '';
            if(isset(session()->get('user_info')['id'])) {
              $supervisor_id = session()->get('user_info')['id'];
              $user_asset_catids = Helper::getUserAssetContactIds( $supervisor_id );
            }
          }
          // dd($user_asset_catids);
          //dd($user_asset_catids);
          $contact_ids=array();
          if(!empty($request->contact_ids)) //check contract ids
          {
            $contact_ids=$request->contact_ids;
          }
          //dd($contact_ids);
          $contractList=DB::table('contracts')->where('is_deleted','no')->where('status',1)->whereIn('contracts.id',$contact_ids);
          if($users->user_type != 'sp_admin' && $users->user_type != 'supervisor' && Session::has('entered_project_id')) //for users other than sp admin
          {
            //$contractList = $contractList->where('user_id',$users->project_user_id);
          }
          $contractList = $contractList->get();
          if(count($contractList)>0)
          {
            $contractList_html = '';
            foreach($contractList as $list) {
              $con_selected = '';

              if(in_array($list->id,$user_asset_catids)) //check required ids
              {
                $con_selected = 'checked';

                if(!isset($request->edit)){
                }
              }
              $contractList_html .= '<li> <input onchange="getSelectedAssetName();" '.$con_selected.' class="parent-category" type="checkbox" id="' .$list->id.  '" value="' .$list->contract_number.  '"><label for="tel"> ' .$list->contract_number.  ' </label> ';
              $cats_arr = [];
              if(!empty($list->asset_categories)) {
                $cats_arr = explode(',',$list->asset_categories);
              }
              $assetcontractList=DB::table('asset_categories')
              //->where('is_deleted','no')
              ->whereIn('id',$cats_arr);

              //dd($user_id);
              if($session_user_info['user_type'] == 'sp_worker' && ($users->user_type == 'supervisor') ) //for worker and supervisors
              {

                if($users->user_type == 'supervisor' ) {
                  if(session()->has('user_info') && session()->get('user_info')['user_type'] == 'sp_worker' && isset(session()->get('user_info')['id']) ) {
                    $user_id = $users->id;
                  }
                }elseif($users->user_type == 'sp_admin' ) {
                  if($users->user_type == 'sp_admin' && (session()->get('user_info')['user_type'] == 'sp_worker' || session()->get('user_info')['user_type'] == 'supervisor') && isset(session()->get('user_info')['id'])) {
                    $user_id = $users->id; // logged in id
                  }
                }
                $hasAssetAssigned = Helper::hasAssetAssigned($user_id);
                if(count($hasAssetAssigned)>0) {
                  $assetcontractList = $assetcontractList->whereIn('id',$hasAssetAssigned);
                }
              }
              $user_asset_ids = false;
              if(session()->get('user_info')['user_type'] == 'sp_worker' && $users->user_type != 'supervisor'){
                // dd($session_user_info['supervisor_id']);
              $hasAssetAssigned = Helper::hasAssetAssigned($session_user_info['supervisor_id']);
              if(count($hasAssetAssigned)>0) {
                $assetcontractList = $assetcontractList->whereIn('id',$hasAssetAssigned);
              }
              }

              $assetcontractList = $assetcontractList->get();
              // dd( $assetcontractList);
              if(count($assetcontractList)>0) //check data count
              {
                //dd($user_id);
                $contractList_html .= '<ul class="asset-ul">';

                foreach($assetcontractList as $sub_cat) {

                  if($sub_cat->deleted_at != "")
                  {
                        $deleted = " [".__("work_order.bread_crumbs.deleted")."]";

                        $sub_cat->asset_category = $sub_cat->asset_category.$deleted;
                  }

                  if($users->user_type == 'supervisor' ) {
                    if(session()->has('user_info') && session()->get('user_info')['user_type'] == 'sp_worker' && isset(session()->get('user_info')['id']) ) {
                      $user_id = session()->get('user_info')['id'];
                      $user_asset_ids = Helper::getUserAssetIdsNew($user_id,$list->id,$sub_cat->id);

                    } else {
                      $user_asset_ids = false;
                    }
                  }elseif($users->user_type == 'sp_admin' ) {
                    if($users->user_type == 'sp_admin' && (session()->get('user_info')['user_type'] == 'sp_worker' || session()->get('user_info')['user_type'] == 'supervisor') && isset(session()->get('user_info')['id'])) {
                      $user_id = session()->get('user_info')['id']; // Super visor
                      $user_asset_ids = Helper::getUserAssetIdsNew($user_id,$list->id,$sub_cat->id);
                    } elseif($users->user_type == 'sp_admin' && (session()->get('user_info')['user_type'] == 'sp_worker' || session()->get('user_info')['user_type'] == 'supervisor')) {
                      $user_id = $users->id; // SPAdmin ID
                      $user_asset_ids = Helper::getUserAssetIdsNew($user_id,$list->id,$sub_cat->id);
                    }
                  }elseif(($users->user_type == 'super_admin' || $users->user_type == 'osool_admin') && !Session::has('entered_project_id')) { // Global SP ADMIN,Supervisor,Worker Creation
                    if(session()->get('user_info')['user_type'] == 'supervisor') {
                      if(isset(session()->get('user_info')['id'])) {
                        $user_id = session()->get('user_info')['id']; // Super visor
                        $user_asset_ids = Helper::getUserAssetIdsNew($user_id,$list->id,$sub_cat->id);
                      } else {
                        $user_asset_ids = false;
                      }
                    } elseif(session()->get('user_info')['user_type'] == 'sp_worker') {
                      if(isset(session()->get('user_info')['id'])) {
                        $user_id = session()->get('user_info')['id']; // Super visor
                        $user_asset_ids = Helper::getUserAssetIdsNew($user_id,$list->id,$sub_cat->id);
                      } else {
                        $user_asset_ids = false;
                      }
                      //dd( $user_asset_ids );
                    }
                  }elseif(($users->user_type == 'super_admin' || $users->user_type == 'osool_admin') && Session::has('entered_project_id')) { // Global SP ADMIN,Supervisor,Worker Creation
                    if(session()->get('user_info')['user_type'] == 'supervisor') {
                      if(isset(session()->get('user_info')['id'])) {
                        $user_id = session()->get('user_info')['id']; // Super visor
                        $user_asset_ids = Helper::getUserAssetIdsNew($user_id,$list->id,$sub_cat->id);
                      } else {
                        $user_asset_ids = false;
                      }

                    } elseif(session()->get('user_info')['user_type'] == 'sp_worker') {
                      if(isset(session()->get('user_info')['id'])) {
                        $user_id = session()->get('user_info')['id']; // Super visor
                        $user_asset_ids = Helper::getUserAssetIdsNew($user_id,$list->id,$sub_cat->id);
                      } else {
                        $user_asset_ids = false;
                      }
                      //dd( $user_asset_ids );
                    }
                  }
                  elseif(($users->user_type == 'admin' || $users->user_type == 'admin_employee' )) { // Global SP ADMIN,Supervisor,Worker Creation
                    if(session()->get('user_info')['user_type'] == 'supervisor') {
                      if(isset(session()->get('user_info')['id'])) {
                        $user_id = session()->get('user_info')['id']; // Super visor
                        $user_asset_ids = Helper::getUserAssetIdsNew($user_id,$list->id,$sub_cat->id);
                      } else {
                        $user_asset_ids = false;
                      }

                    } elseif(session()->get('user_info')['user_type'] == 'sp_worker') {
                      if(isset(session()->get('user_info')['id'])) {
                        $user_id = session()->get('user_info')['id']; // Super visor
                        $user_asset_ids = Helper::getUserAssetIdsNew($user_id,$list->id,$sub_cat->id);
                      } else {
                        $user_asset_ids = false;
                      }
                    }
                  }
                  // dd($user_asset_ids);

                  $asset_selected = '';
                  if($user_asset_ids==true) {
                    if(!isset($request->edit)){
                      $asset_selected = 'checked';

                    }
                  }
                  $contractList_html .= '<li> <input onchange="getSelectedAssetName();" '.$asset_selected.' name="asset_categories[]" class="asset-item child-'.$list->id.'" type="checkbox" id="' .$sub_cat->asset_category.  '" value="' .$list->id.','.$sub_cat->id.  '"><label for="tel"> ' .$sub_cat->asset_category.  ' </label> </li>';



                }
                $contractList_html .=  '</ul>';
              }
              $contractList_html .= '</li>';

            }
            // Dynamic Asset show to UI
            $contractList_html .= '<script>
            function getSelectedAssetName()
                {

                    var selected_items = [];
                    var count_checked = $("[name=\'asset_categories[]\']:checked").length;

                    if(count_checked > 0 ) {
                      $(".asset-item:checkbox:checked").each(function() {
                        selected_items.push($(this).val());
                      });
                    }

                    $.ajax({
                        url: "'. route("users.selected_assets") .'",
                        method: "GET",
                        data: {
                            _token: $(\'meta[name="csrf-token"]\').attr("content"),
                            selected_items: selected_items,
                        },
                        dataType: "json",
                        beforeSend: function () {},
                        success: function (data) {

                            console.log(data.selected_assets)
                            $("#filter").val(data.selected_assets_count);
                            //$("#selected_items").html(data.selected_assets);
                        },
                        error: function (data) {
                            toastr.error(data, translations.general_sentence.validation.Error, {
                                timeOut: 5000,
                                positionClass: "toast-top-center",
                                progressBar: true,
                            });
                        },
                  });

                }
            </script>';
            // Dynamic Asset show to UI ends
            $formatted_buildingList = Helper::FormatComplexwiseBuilding($building_data);

            header('Content-Type:application/json');
            die(json_encode(array("status"=>"1","message"=>"Success", "content"=>$contractList_html,"formatted_buildingList"=>$formatted_buildingList, 'building_data'=>$building_data)));
          }
          else
          {
            header('Content-Type:application/json');
            die(json_encode(array("status"=>"0","message"=>"Failed")));
          }
        }
      }
    }


    public function fetchAssignedWorkers(Request $request)
    {
        $building_ids = $request->building_ids;
        $contract_ids = $request->contract_ids;
        $supervisor_ids = count(session()->get('user_info')['supervisor_id']) > 0 ? session()->get('user_info')['supervisor_id'] : [0];
    
        $assigned_workers = User::where('user_type', 'sp_worker')
                        ->where(function ($query) use ($building_ids) {
                            foreach ($building_ids as $id) {
                                $query->orWhereRaw("FIND_IN_SET(?, building_ids)", [$id]);
                            }
                        })
                        ->where(function ($query) use ($contract_ids) {
                            foreach ($contract_ids as $id) {
                                $query->orWhereRaw("FIND_IN_SET(?, contract_ids)", [$id]);
                            }
                        })
                        ->where(function ($query) use ($supervisor_ids) {
                            foreach ($supervisor_ids as $id) {
                                $query->orWhereRaw("FIND_IN_SET(?, supervisor_id)", [$id]);
                            }
                        })
                        ->where('status', 1)
                        ->where('is_deleted', 'no')
                    ->get(['id', 'name']);


                    if ($assigned_workers->isEmpty()) {
                          return response()->json([
                              'status' => false,
                              'message' => 'No workers found for the selected filters.',
                              'workers' => []
                          ]);
                      }
                    return response()->json([
                    'status' => true,
                    'workers' => $assigned_workers
                ]);

      }

    /**
     * GET users/verify-email/{lang}/{email}
     *
     * Return View flag user email as verified
     * Update email verification status in database for the email
     *
     * @authenticated
     * @group Users
     *
     * @responseField data array
     * @response 200 {data: []}
     */
    public function verify_email($lang,$email) {
      App::setLocale($lang);
      $email =  Crypt::decryptString($email);

      $user_detail = DB::table('users')->where('email',$email)->first();
      if(trim($user_detail->email_verified) == 1)
      {
          return view($this->view_path.'.verifed-validate-email',['data'=>$this->data]);
      }
      else
      {
        $to_name = $user_detail->name;
      $subject = trans('user_management_module.email.verify_your_email_successful');
      $sender_email = Helper::getAdminContactMail();
      event(new VarificationEmailSuccess($to_name, $email,$subject,$sender_email));

      DB::table('users')->where('email',$email)->update(['email_verified'=>1]);

      //return redirect()->route('login');
      $this->data['username'] = '';
      return view($this->view_path.'.validate-email',['data'=>$this->data]);
      }
    }

    /**
     * GET users/selected_assets
     *
     * Return JSON response to show selected asset for the user
     * Return JSON respnse with selected asset names with the total count for the selected user.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function selected_assets(Request $request) {
      if( isset($request->selected_items) && count($request->selected_items)>0  ) {
        $assets_selected = $request->selected_items;
        $selected_assets = [];
        foreach($assets_selected as $asset) {

          $arr = explode(',',$asset);
          $contact_id = $arr[0];
          $asset_id = $arr[1];
          $contact_data = DB::table('contracts')->select('contract_number')->where('id',$contact_id)->first();
          $asset_data = DB::table('asset_categories')->select('asset_category')->where('id',$asset_id)->first();
          $selected_assets[] = '<span class="custom-badge asset-badge">'.$contact_data->contract_number.': '.$asset_data->asset_category.'</span>';
        }
        $selected_assets_list = implode(' ',$selected_assets);
        echo json_encode(['selected_assets'=>$selected_assets_list,'selected_assets_count'=>count($selected_assets).' '.__('data_contract.contract_forms.place_holder.service_type_selected')]);
      } else{
        echo json_encode(['selected_assets'=>'','selected_assets_count'=>'']);
      }
    }

    /**
     * GET users/user-activation
     *
     * Return JSON response after change user status to active
     * Update users status in database as active.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function user_activation(Request $request)
    {
      if($request->status == 'active'){
        if(DB::table('users')->where('id', $request->id)->update(['status'=>1])){
          return json_encode(['status'=> 'success']);
        }else{
          return json_encode(['status'=> 'error']);
        }
      }else{
        if(DB::table('users')->where('id', $request->id)->update(['status'=>0])){
          DB::table('oauth_access_tokens')
            ->where('user_id', $request->id)
            ->delete();
          return json_encode(['status'=> 'success']);
        }else{
          return json_encode(['status'=> 'error']);
        }
      }

    }

        /**
     * GET users/user-show-hide-wo-columns
     *
     * Return JSON response after change user status to active
     * Update users status in database as active.
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function show_hide_wo_columns(Request $request)
    {

      // dd($request->All());
      DB::table('work_orders_columns')->where('user_id',Auth::user()->id)->where("page",$request->page)
      ->delete();
      if(!empty($request->columns)){

        foreach($request->columns as $col){
            $data = [
              'user_id' => Auth::user()->id,
              'columns_name' => $col,
              'page' => $request->page
            ];
             DB::table('work_orders_columns')->insert($data);
        }
     }
   }


   /**
     * GET users/create-availability-request
     *
     * Return JSON response after create request
     * Create availability change request on behalf of worker
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
   public function createAvailabilityRequest(CreateAvailabilityStatusRequest $request)
    {
        // Parse the original date with Carbon
        $from_datetime = strtotime($request->from_datetime);
        $to_datetime = strtotime($request->to_datetime);

        // Convert the format to Y-m-d H:i:s
        $from_datetime = date('Y-m-d H:i:s', $from_datetime);
        $to_datetime = date('Y-m-d H:i:s', $to_datetime);


            $attachment_array = [];
            if (!empty($request->file('logo'))) { //If the request has attachment
                foreach ($request->file('logo') as $attachment) {
                    if ($attachment != null) {
                        if (!\Storage::disk('public')->exists('worker_availability_status')) {
                            \Storage::disk('public')->makeDirectory('worker_availability_status');
                        }
                        array_push($attachment_array, \Storage::disk('public')->put('worker_availability_status', $attachment));
                    }
                }
            }
        $status_data = WorkerAvailabilityRequestReasonType::where('id',$request->reason_type_id)->first();
        $availability_request_id = WorkorderHelper::createAvailabilityRequest($request->create_request_user_id,$request->reason_type_id,$request->reason_description, json_encode($attachment_array), $from_datetime, $to_datetime);

        if (!empty($availability_request_id)) {
            // Save notification
            $worker = Helper::userDetail($request->create_request_user_id);
            if (!empty($worker)) {
              $registration_ids[] = $worker->device_token;

              $message = 'Your status has been set to offline by the supervisor';

              if ($worker->selected_app_langugage == 'ar' || $worker->selected_app_langugage == 'ur') {
                $message = $worker->selected_app_langugage == 'ar' ? 'المشرف غير الحالة الى غير متواجد' : 'آپ کا اسٹیٹس سپروائزر کے ذریعہ آف لائن پر سیٹ کر دیا گیا ہے';
              }
              $message = [
                'title' => $message,
                  'notification_type' => 'new_availability_change_request',
              ];
                    $res = ApiHelper::send_notification_worker_FCM($request->create_request_user_id, $registration_ids, $message, auth()->user()->building_ids, 'new_availability_change_request', $availability_request_id);
            }
            //WorkorderHelper::sendCreateAvailabilityRequestNotification($availability_request_id,$request->create_request_user_id);
            return response()->json(['status' => true, 'result'=> $status_data, 'message' => 'Your status request has been sent! Wait for service provider approval']);
        } else {
            return response()->json(['status' => false, 'message' => 'Something went wrong. Please try again!']);
        }

    }




    /**
     * GET users/fetch-availability-request-details
     *
     * Return JSON response after Fetch request
     * Fetch availability change request data
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
   public function fetchAvailabilityRequestDetails(Request $request)
   {
       $request_id = $request->request_id;

       $result = ManageWorkerAvailabilityStatus::select('manage_worker_availability_statuses.*','users.name','users.contract_ids','users.email',
       DB::raw("DATE_FORMAT(manage_worker_availability_statuses.from_datetime, '%d-%m-%Y %I:%i %p') as start_date_formatted"),
       DB::raw("DATE_FORMAT(manage_worker_availability_statuses.to_datetime, '%d-%m-%Y %I:%i %p') as end_date_formatted"))
                ->leftJoin("users",function($join){
                  $join->on("users.id","=","manage_worker_availability_statuses.worker_id");
                  })
                  ->where('manage_worker_availability_statuses.id',$request_id)
                  ->first();

       if (!empty($result)) {
          if(trim($result->attachments) != "[]")
          {
            $result->contract_data = '';

            $result->reason_type_name = '';
            $reason_data = WorkerAvailabilityRequestReasonType::where('id',$result->reason_type_id)->first();
            if(isset($reason_data))
            {

              $result->reason_type_name = App::getLocale() == 'en' ? trim($reason_data->reason_type_en) : trim($reason_data->reason_type_ar);
            }
            if($result->approval_status == 'pending')
            {
              $contract_id = $result->contract_ids ?? 0;
              $result->contract_data = Contracts::select('id','contract_number')
              ->whereIn('id',explode(',',$contract_id))
              ->get()->toArray();
              $result->approval_status = $result->from_datetime > now() ? 'pending' : 'expired';
            }

            if($result->approval_status == 'approved')
            {
              $contract_id = trim($result->contract_id) ?? 0;
              $result->contract_data = Contracts::select('id','contract_number')
              ->whereIn('id',explode(',',$contract_id))
              ->get()->toArray();
            }
            foreach(json_decode($result->attachments) as $k=>$v)
            {
                $filename = 'file'.$k;
                if (Str::endsWith($v, '.pdf'))
                    {
                      $result->$filename = url('storage/worker_availability_status/' . preg_replace('/\[|\]|"/', '', $v));
                    }
                    else {
                      $result->$filename = ImagesUploadHelper::displayImage($v, 'worker_availability_status');
                    }
            }
          }
          else
          {
            $result->file0 = '';
            $result->file1 = '';
            $result->file2 = '';
          }
           return response()->json(['status' => true, 'result'=> $result, 'message' => '']);
       } else {
           return response()->json(['status' => false, 'message' => 'Something went wrong. Please try again!']);
       }

   }




   /**
     * GET users/reject-availability-request
     *
     * Return JSON response after create request
     * Reject availability change request of worker
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function rejectAvailabilityRequest(RejectAvailabilityStatusRequest $request)
    {
        $availability_request_id = $request->offline_request_id;
        $request_reject_reason = $request->request_reject_reason;
        $workerdata = ManageWorkerAvailabilityStatus::where('id',$availability_request_id)->first();
        if($workerdata)
        {
          $worker_id = $workerdata->worker_id;
        }
        else
        {
          return response()->json(['status' => false, 'message' => 'No Record found!']);
        }

        $update_result = WorkorderHelper::rejectAvailabilityRequest($availability_request_id,$request_reject_reason);

        if (!empty($update_result)) {
            // Save notification

            $worker = Helper::userDetail($worker_id);
            if (!empty($worker)) {
              $registration_ids[] = $worker->device_token;

              $message = 'Your leave request has been rejected';

              if ($worker->selected_app_langugage == 'ar' || $worker->selected_app_langugage == 'ur') {
                $message = $worker->selected_app_langugage == 'ar' ? 'تم رفض طلب المغادرة الخاص بك' : 'آپ کی چھٹی کی درخواست مسترد کر دی گئی ہے';
              }

              $message = [
                'title' => $message,
                'body' => '',
                  'notification_type' => 'reject_availability_change_request',
              ];
                    $res = ApiHelper::send_notification_worker_FCM($worker_id, $registration_ids, $message, auth()->user()->building_ids, 'reject_availability_change_request', $availability_request_id);
            }

            //WorkorderHelper::sendRejectAvailabilityRequestNotification($availability_request_id,$worker_id);
            return response()->json(['status' => true, 'message' => 'Your status request to offline has been rejected']);
        } else {
            return response()->json(['status' => false, 'message' => 'Something went wrong. Please try again!']);
        }

    }



    /**
     * GET users/approve-availability-request
     *
     * Return JSON response after create request
     * Approve availability request of worker
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function approveAvailabilityRequest(Request $request)
    {
        $availability_request_id = $request->offline_request_id;

        $contract_id = $request->contract_id ?? '';

        $workerdata = ManageWorkerAvailabilityStatus::where('id',$availability_request_id)->first();
        if($workerdata)
        {
          $worker_id = $workerdata->worker_id;
          $reason_type_id = $workerdata->reason_type_id;
        }
        else
        {
          return response()->json(['status' => false, 'message' => 'No Record found!']);
        }

        $update_result = WorkorderHelper::acceptAvailabilityRequest($availability_request_id,$contract_id);

        $status_data = WorkerAvailabilityRequestReasonType::where('id',$reason_type_id)->first();

        if (!empty($update_result)) {
            // Save notification

            $worker = Helper::userDetail($worker_id);
            if (!empty($worker)) {

                $registration_ids[] = $worker->device_token;
                // if($reason_type_id == 1)
                // {
                //     //Vacation
                //     $message = 'Your status has been changed to "Vacation"';
                //     if ($worker->selected_app_langugage == 'ar' || $worker->selected_app_langugage == 'ur') {
                //       $message = $worker->selected_app_langugage == 'ar' ? 'تم تغيير حالتك إلى "إجازة”' : 'آپ کی حیثیت کو "چھٹی" میں تبدیل کر دیا گیا ہے';
                //     }
                // }
                // elseif($reason_type_id == 2)
                // {
                //     //Not available
                //     $message = 'Your status has been changed to "Not Available”';
                //     if ($worker->selected_app_langugage == 'ar' || $worker->selected_app_langugage == 'ur') {
                //       $message = $worker->selected_app_langugage == 'ar' ? 'تم تغيير حالتك إلى "غير متاح”' : 'آپ کی حیثیت کو "دستیاب نہیں" میں تبدیل کر دیا گیا ہے';
                //     }
                // }
                // elseif($reason_type_id == 3)
                // {
                //     //Work Injury
                //     $message = 'Your status has been changed to "Work Injury”';
                //     if ($worker->selected_app_langugage == 'ar' || $worker->selected_app_langugage == 'ur') {
                //       $message = $worker->selected_app_langugage == 'ar' ? 'تم تغيير حالتك إلى "إصابة عمل”' : 'آپ کی حیثیت کو "کام کی چوٹ" میں تبدیل کر دیا گیا ہے';
                //     }
                // }
                // else
                // {
                //     $message = 'Your status request to offline has been approved';
                //     if ($worker->selected_app_langugage == 'ar' || $worker->selected_app_langugage == 'ur') {
                //       $message = $worker->selected_app_langugage == 'ar' ? 'تم قبول طلب تغيير الحالة الى غير متواجد' : 'آپ کی آف لائن حیثیت کی درخواست منظور کر لی گئی ہے';
                //     }
                //     $result_array = array('user_id' => $worker_id, 'message' => 'Your status request to offline has been approved', 'message_ar' => 'تم قبول طلب تغيير الحالة الى غير متواجد', 'section_type' => 'worker_app', 'building_ids' => auth()->user()->building_ids, 'notification_type' => 'approve_availability_change_request', 'notification_sub_type' => 'approve_availability_change_request', 'additional_param' => $availability_request_id, 'created_by' => Auth::user()->id, 'created_at' => date('Y-m-d H:i:s'));
                // }

                  $message = 'Your leave request has been approved';
                    if ($worker->selected_app_langugage == 'ar' || $worker->selected_app_langugage == 'ur') {
                      $message = $worker->selected_app_langugage == 'ar' ? 'تمت الموافقة على طلب المغادرة الخاص بك' : 'آپ کی چھٹی کی درخواست منظور کر لی گئی ہے';
                    }
                    $result_array = array('user_id' => $worker_id, 'message' => 'Your leave request has been approved', 'message_ar' => 'تم قبول طلب تغيير الحالة الى غير متواجد', 'section_type' => 'worker_app', 'building_ids' => auth()->user()->building_ids, 'notification_type' => 'approve_availability_change_request', 'notification_sub_type' => 'approve_availability_change_request', 'additional_param' => $availability_request_id, 'created_by' => Auth::user()->id, 'created_at' => date('Y-m-d H:i:s'));

                $message = [
                  'title' => $message,
                  'body' => '',
                    'notification_type' => 'approve_availability_change_request',
                ];

                $res = ApiHelper::send_notification_worker_FCM($worker_id, $registration_ids, $message, auth()->user()->building_ids, 'approve_availability_change_request', $availability_request_id);
            }

            //WorkorderHelper::sendApprovedAvailabilityRequestNotification($availability_request_id,$worker_id,$reason_type_id);
            return response()->json(['status' => true, 'result'=> $status_data, 'message' => 'Your status request to offline has been approved']);
        } else {
            return response()->json(['status' => false, 'message' => 'Something went wrong. Please try again!']);
        }
    }




    /**
     * GET users/change-availability-request-status
     *
     * Return JSON response after create request
     * Reject availability change request of worker
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function changeAvailabilityRequestStatus(Request $request)
    {
        $availability_request_id = $request->availability_request_id;
        $workerdata = ManageWorkerAvailabilityStatus::where('id',$availability_request_id)->first();
        if($workerdata)
        {
          $worker_id = $workerdata->worker_id;
        }
        else
        {
          return response()->json(['status' => false, 'message' => 'No Record found!']);
        }

        $update_result = WorkorderHelper::changeAvailabilityRequest($availability_request_id);

        if (!empty($update_result)) {
          $worker = Helper::userDetail($worker_id);
            if (!empty($worker)) {

                $registration_ids[] = $worker->device_token;

                  $message = 'Your ongoing leave request has been terminated';
                    if ($worker->selected_app_langugage == 'ar' || $worker->selected_app_langugage == 'ur') {
                      $message = $worker->selected_app_langugage == 'ar' ? 'لقد تم إنهاء طلب المغادرة الخاص بك' : 'آپ کی جاری چھٹی کی درخواست منسوخ کر دی گئی ہے';
                    }

                $message = [
                  'title' => $message,
                  'body' => '',
                    'notification_type' => 'leave_terminated',
                ];

                $res = ApiHelper::send_notification_worker_FCM($worker_id, $registration_ids, $message, auth()->user()->building_ids, 'leave_terminated', $availability_request_id);
              }
                return response()->json(['status' => true, 'message' => 'Your status request has been updated']);
        } else {
            return response()->json(['status' => false, 'message' => 'Something went wrong. Please try again!']);
        }

    }



    /**
     * GET users/workers-list
     *
     * Return view a display workers list.
     * Return a view with list of users respective of the logged in user type with filters such as user-type/role and search query and total count.
     *
     * @authenticated
     * @group Users
     *
     * @responseField data array
     * @response 200 {data:[]}
     */
    public function getWorkerlist(Request $request, $availability_request_id = null, $notification_read = null, $notification_id = null)
    {
      Session::forget('user_info');
      Session::forget('asset_categories_contacts');
      Session::forget('service_provider_id');
      Session::forget('edit_spa_user');
      Session::forget('worker_privilege');

      $selected_role = ''; //set default as null
      if(isset($request->user_role)) // Filter Users Role-Wise
      {
        $selected_role=$request->user_role;
        $request->session()->put('selected_role', $selected_role); //Add requested role to session data
      } else {
        Session::forget('selected_role'); //Unset session variable
      }


      $logedin_user=auth()->user();
      $isHasAdmin=0; //Set default value
      $this->data['pageTitle']='Worker List';

       // Check if the 'notification_read' parameter is provided and equal to 'notification-read'
       if (!empty($notification_read) && $notification_read == 'notification-read') {
          // When user reads a notification, fetch details of the maintenance request
          $read_availability_notification = WorkorderHelper::readAvailabilityRequestNotification($notification_read, $notification_id, $availability_request_id, $logedin_user);
          $this->data['notification_request_status'] = $read_availability_notification['notification_request_status'];
          $this->data['notification_request_id'] = $read_availability_notification['notification_request_id'];
      }
      else
      {
        $this->data['notification_request_status'] = "-";
        $this->data['notification_request_id'] = "-";
      }

      // PROJECT USER ID CONDITION FOR VARIOUS USER TYPES
      if($logedin_user->user_type!='super_admin'&& $logedin_user->user_type!='osool_admin') //When not logged in as Super admin or admin
      {
        if(Session::has('entered_project_id')) //check sesssion data
        {
          $project_id = Session::get('entered_project_id');
        }
        elseif($logedin_user->user_type == 'sp_admin') //for SP admin user type
        {
          $user_id = $logedin_user->id;
          $service_provider = $logedin_user->service_provider; //get service providers

          $data = User::select('users.id')->leftJoin('worker_professions','worker_professions.id','=','users.profession_id')->leftJoin('service_providers','service_providers.id','=','users.service_provider')->where('users.id','!=',$logedin_user->id);

          $projectIds = ServiceProviderProjectMapping::where('service_provider_id', $logedin_user->service_provider)
                ->pluck('project_id')->implode(',');
          $supervisor_ids = User::select('id')->where('user_type','supervisor')
          //->where('sp_admin_id',$user_id)
          ->where('service_provider', $service_provider)
          ->where(['is_deleted' => 'no', 'status' => 1])
          ->get()->pluck('id')->implode(',');//get supervisors
          if($supervisor_ids == "") //if has no supervisors
          {
            $where = "((users.user_type = 'sp_worker' and users.supervisor_id in ( 0 )))";
          }
          else{ //If has supervisors
            $where = "((user_type = 'sp_worker' and supervisor_id in ( $supervisor_ids ) ))";
          }
          $this->data['total_user'] = $data->whereRaw($where)->where([['users.deleted_at',NULL],['users.is_deleted', 'no']])->count();
        }
        elseif($logedin_user->user_type == 'supervisor')//when logged in as Supervisor
        {
          $user_id = $logedin_user->id;
          $this->data['total_user'] = User::select('id')->where('user_type','sp_worker')->whereRaw("find_in_set($logedin_user->id, users.supervisor_id)")->where([['deleted_at',NULL],['users.is_deleted', 'no']])->count();
        }
      }
      else //When logged in as Super admin and osool admin
      {
        $this->data['total_user'] = 0;
      }
      $this->data['request_status_type'] = WorkerAvailabilityRequestReasonType::get()->toArray();


      return view($this->view_path.'.list-workers',['data'=>$this->data]);
    }



    /**
     * GET users/leave-request-list
     *
     * Return view a display worker's leave request list.
     * Return a view with list of users respective of the logged in user type with filters such as user-type/role and search query and total count.
     *
     * @authenticated
     * @group Users
     *
     * @responseField data array
     * @response 200 {data:[]}
     */
    public function getLeaveRequestList(Request $request, $availability_request_id = null, $notification_read = null, $notification_id = null)
    {
      if(!AkauntingService::hasErpEnabledProjects())
      {
        abort(404);
      }
      Session::forget('user_info');
      Session::forget('asset_categories_contacts');
      Session::forget('service_provider_id');
      Session::forget('edit_spa_user');
      Session::forget('worker_privilege');

      $selected_role = ''; //set default as null
      if(isset($request->user_role)) // Filter Users Role-Wise
      {
        $selected_role=$request->user_role;
        $request->session()->put('selected_role', $selected_role); //Add requested role to session data
      } else {
        Session::forget('selected_role'); //Unset session variable
      }


      $logedin_user=auth()->user();
      $isHasAdmin=0; //Set default value
      $this->data['pageTitle']='Leave Request';

       // Check if the 'notification_read' parameter is provided and equal to 'notification-read'
       if (!empty($notification_read) && $notification_read == 'notification-read') {
          // When user reads a notification, fetch details of the maintenance request
          $read_availability_notification = WorkorderHelper::readAvailabilityRequestNotification($notification_read, $notification_id, $availability_request_id, $logedin_user);
          $this->data['notification_request_status'] = $read_availability_notification['notification_request_status'];
          $this->data['notification_request_id'] = $read_availability_notification['notification_request_id'];
      }
      else
      {
        $this->data['notification_request_status'] = "-";
        $this->data['notification_request_id'] = "-";
      }

      $worker_ids = array();
      $this->data['total_leave_request'] = 0;
      $worker_data = Helper::fetchWorkerofSP();

        if(isset($worker_data) && !empty($worker_data))
        {
            $worker_data = $worker_data->toArray();
            $worker_ids = array_column($worker_data,'id');
        }
        else
        {
          $worker_data = array();
        }

        if(count($worker_ids) > 0)
        {
          $this->data['total_leave_request'] = ManageWorkerAvailabilityStatus::whereIn('worker_id',$worker_ids)
          ->count();
        }
      $this->data['workers'] = $worker_data;
      $checkSp = ServiceProvider::where('id',$logedin_user->service_provider)->pluck('global_sp')->first();
      if(isset($checkSp) && $checkSp == 1)
      {
        $this->data['is_global_sp'] = 'yes';
      }
      else
      {
        $this->data['is_global_sp'] = 'no';
      }
      return view($this->view_path.'.list-leave-requests',['data'=>$this->data]);
    }



    /**
     * GET users/fetchLeaveRequestListAjax/{id?}
     *
     * Return JSON collection of user list ajax.
     * Return a JSON collection of list of workers with filters such search query and total count.
     *
     * @authenticated
     * @group Users
     *
     * @responseField data array
     * @response 200 {data:[]}
     */
    public function fetchLeaveRequestListAjax(Request $request)
    {
      $selected_role = ''; //Set default filter as null

      $logedin_user=auth()->user();
      if($request->ajax()) //check for ajax request
      {
        $worker_ids = array();
        $this->data['total_leave_request'] = 0;
        $worker_data = Helper::fetchWorkerofSP();
          if(isset($worker_data) && !empty($worker_data))
          {
              $worker_data = $worker_data->toArray();

              $worker_ids = array_column($worker_data,'id');
          }
          else
          {
            $worker_data = array();
          }

        $searchValue = trim($request->search_text);

        $data = ManageWorkerAvailabilityStatus::select('manage_worker_availability_statuses.*','users.id as user_id','users.name',DB::raw("DATE_FORMAT(manage_worker_availability_statuses.created_at, '%d-%m-%Y %I:%i %p') as reported_at"),
        DB::raw("DATE_FORMAT(manage_worker_availability_statuses.from_datetime, '%d-%m-%Y %I:%i %p') as expire_date"))
        ->whereIn('worker_id',$worker_ids);
        $data=$data->leftJoin("users",function($join){
          $join->on("users.id","=","manage_worker_availability_statuses.worker_id");
          });


        if(!empty($searchValue)) // If requested with a search filter
        {
          $data = $data->where(function ($query) use ($searchValue) {
            $query->whereRaw("(users.name LIKE '%{$searchValue}%' OR manage_worker_availability_statuses.leave_request_id LIKE '%{$searchValue}%')");
            });
        }

        $date_search = trim($request->date_range);
        if($date_search != "")
        {
          $date_range = explode(',',$date_search);
          $date = array('startd' => $date_range[0], 'endd' => $date_range[1]);
          if ($date_search != "" && !empty($date['startd'])) {
              // Filter by date range
              $data = $data->whereBetween('manage_worker_availability_statuses.created_at', [$date['startd'] . ' 00:00:00', $date['endd'] . ' 23:59:59']);
          }
        }

        $request_status = trim($request->status);

        $selected_worker = trim($request->selected_worker);

        $expired_leave = clone $data;
        $expired_leave = $expired_leave->whereRaw("(manage_worker_availability_statuses.approval_status = 'expired' OR (manage_worker_availability_statuses.approval_status = 'pending' AND  manage_worker_availability_statuses.from_datetime < NOW()))")
        ->get()->pluck('id')->implode(',');

        if($expired_leave == "")
        {
          $expired_leave = 0;
        }

        if ($request_status != "") {
          $request_status = explode(',',trim($request->status));


          if (in_array('expired', $request_status)) {
            $request_status = array_map(function ($status) {
              return '"' . $status . '"';
            }, $request_status);
            $request_status = implode(",",$request_status);

            if(in_array('pending', explode(',',trim($request->status))))
            {
                $data = $data->whereIn('manage_worker_availability_statuses.approval_status', explode(',',trim($request->status)));
            }
            else
            {
              $data = $data->where(function ($query) use ($request_status) {
                $query->whereRaw("(manage_worker_availability_statuses.approval_status IN ($request_status) OR (manage_worker_availability_statuses.approval_status = 'pending' AND  manage_worker_availability_statuses.from_datetime < NOW()))");
                });
            }

          } else {
            if (in_array('pending', $request_status))
            {
              $request_status = array_map(function ($status) {
                return '"' . $status . '"';
              }, $request_status);
              $request_status = implode(",",$request_status);

              $data = $data->where(function ($query) use ($request_status,$expired_leave) {
                $query->whereRaw("((manage_worker_availability_statuses.approval_status IN ($request_status) AND manage_worker_availability_statuses.id NOT IN ($expired_leave)) OR (manage_worker_availability_statuses.approval_status = 'pending' AND  manage_worker_availability_statuses.from_datetime > NOW()))");
                });
            }
            else
            {
              $data = $data->whereIn('manage_worker_availability_statuses.approval_status', $request_status);
            }

          }
      }

      if ($selected_worker != "") {
        $selected_worker = explode(',',trim($request->selected_worker));
          $data = $data->whereIn('manage_worker_availability_statuses.worker_id', $selected_worker);
      }


      $page_length = $request->page_length ?? 10;
        $data=$data->where([['users.deleted_at',NULL],['users.is_deleted', 'no']])
        ->orderBy('manage_worker_availability_statuses.id','DESC')
        ->groupBy('manage_worker_availability_statuses.id')
        ->paginate($page_length);

        $total_user_count=$data->total();

        if($total_user_count > 0) {
          foreach($data->items() as $key=>$row) {
            $data->items()[$key]->reason_type_name = '';

            if($data->items()[$key]->approval_status == 'pending')
            {
              $data->items()[$key]->approval_status = $data->items()[$key]->from_datetime > now() ? 'pending' : 'expired';
            }

            $reason_data = WorkerAvailabilityRequestReasonType::where('id',$data->items()[$key]->reason_type_id)->first();
            if(isset($reason_data))
            {
              $data->items()[$key]->reason_type_name = App::getLocale() == 'en' ? trim($reason_data->reason_type_en) : trim($reason_data->reason_type_ar);
            }
          }
        }

        return json_encode(array('data'=>$data,'total_leave_request'=>$total_user_count,'user_type'=>$logedin_user->user_type));
      }
    }



    /**
     * GET users/attendancec-list
     *
     * Return view a display workers list.
     * Return a view with list of users respective of the logged in user type with filters such as user-type/role and search query and total count.
     *
     * @authenticated
     * @group Users
     *
     * @responseField data array
     * @response 200 {data:[]}
     */
    public function getAttendancelist(Request $request)
    {
      if(!AkauntingService::hasErpEnabledProjects())
      {
        abort(404);
      }
      Session::forget('user_info');
      Session::forget('asset_categories_contacts');
      Session::forget('service_provider_id');
      Session::forget('edit_spa_user');
      Session::forget('worker_privilege');

      $selected_role = ''; //set default as null
      if(isset($request->user_role)) // Filter Users Role-Wise
      {
        $selected_role=$request->user_role;
        $request->session()->put('selected_role', $selected_role); //Add requested role to session data
      } else {
        Session::forget('selected_role'); //Unset session variable
      }


      $logedin_user=auth()->user();
      $isHasAdmin=0; //Set default value
      $this->data['pageTitle']='Attendance List';
      $worker_data = Helper::fetchWorkerofSP();
      if(isset($worker_data) && !empty($worker_data))
        {
            $worker_data = $worker_data->toArray();
            $worker_ids = array_column($worker_data,'id');
        }
        else
        {
          $worker_data = array();
        }
        $this->data['workers'] = $worker_data;
      // PROJECT USER ID CONDITION FOR VARIOUS USER TYPES
      if($logedin_user->user_type!='super_admin'&& $logedin_user->user_type!='osool_admin') //When not logged in as Super admin or admin
      {

        $worker_data = Helper::fetchWorkerofSP();
          if(isset($worker_data) && !empty($worker_data))
          {
              $worker_data = $worker_data->toArray();
              $worker_ids = array_column($worker_data,'id');
          }
          else
          {
            $worker_data = array();
          }
        if(Session::has('entered_project_id')) //check sesssion data
        {
          $project_id = Session::get('entered_project_id');
        }
        elseif($logedin_user->user_type == 'sp_admin') //for SP admin user type
        {
          $user_id = $logedin_user->id;
          if($worker_ids)
            {
              $this->data['total_records'] = WorkerAttendances::whereIn('worker_id',$worker_ids)->whereNull('clock_out_datetime')->count();
            }
            else
            {
              $this->data['total_records'] = 0;
            }
        }
        elseif($logedin_user->user_type == 'supervisor')//when logged in as Supervisor
        {
          $user_id = $logedin_user->id;
          if($worker_ids)
            {
              $this->data['total_records'] = WorkerAttendances::whereIn('worker_id',$worker_ids)->whereNull('clock_out_datetime')->count();
            }
            else
            {
              $this->data['total_records'] = 0;
            }
        }
      }
      else //When logged in as Super admin and osool admin
      {
        $this->data['total_records'] = 0;
      }

      $checkSp = ServiceProvider::where('id',$logedin_user->service_provider)->pluck('global_sp')->first();
      if(isset($checkSp) && $checkSp == 1)
      {
        $this->data['is_global_sp'] = 'yes';
      }
      else
      {
        $this->data['is_global_sp'] = 'no';
      }

      return view($this->view_path.'.attendance-list',['data'=>$this->data]);
    }


    /**
     * GET users/attendanceListAjax/{id?}
     *
     * Return JSON collection of attendance list ajax.
     * Return a JSON collection of list of workers with filters such search query and total count.
     *
     * @authenticated
     * @group Users
     *
     * @responseField data array
     * @response 200 {data:[]}
     */
    public function fetchAttendanceListAjax(Request $request)
    {
      $selected_role = ''; //Set default filter as null

      $logedin_user=auth()->user();
      if($request->ajax()) //check for ajax request
      {

        try {
          $worker_ids = array();
          $this->data['total_records'] = 0;
          $worker_data = Helper::fetchWorkerofSP();
            if(isset($worker_data) && !empty($worker_data))
            {
                $worker_data = $worker_data->toArray();
                $worker_ids = array_column($worker_data,'id');
            }
            else
            {
              $worker_data = array();
            }
          $searchValue = trim($request->search_text);

          $data = WorkerAttendances::select('worker_attendances.*','users.id as user_id','users.name', 'users.email',DB::raw("DATE_FORMAT(worker_attendances.clock_in_datetime, ' %I:%i %p %d/%m/%Y') as clock_in_datetime_formatted"))
          ->whereNull('clock_out_datetime')
          ->whereIn('worker_id',$worker_ids);
          $data=$data->leftJoin("users",function($join){
            $join->on("users.id","=","worker_attendances.worker_id");
            });


          if(!empty($searchValue)) // If requested with a search filter
          {
            $data = $data->where(function ($query) use ($searchValue) {
              $query->whereRaw("(users.name LIKE '%{$searchValue}%' OR users.email LIKE '%{$searchValue}%')");
              });
          }


          $page_length = $request->page_length ?? 10;

          $data=$data->where([['users.deleted_at',NULL],['users.is_deleted', 'no']])
          ->orderBy('worker_attendances.id','DESC')
          ->groupBy('worker_attendances.id')
          ->paginate($page_length);

          $total_records=$data->total();

          if($total_records > 0) {
            foreach($data->items() as $key=>$row) {
              $data->items()[$key]->clock_in_building_name = PropertyBuildings::where('id',$data->items()[$key]->clock_in_property_id)->pluck('building_name')->first();
              $data->items()[$key]->profile_img = ImagesUploadHelper::displayImage($data->items()[$key]->profile_img, 'uploads/profile_images', 0);
            }
          }

          return json_encode(array('data'=>$data,'total_records'=>$total_records,'user_type'=>$logedin_user->user_type));
        }
        catch (\Throwable $th) {
          //\Log::error("fetchAttendanceListAjax Error: ".$th);
          return response()->json(['status' => false, 'total_records'=> 0,'user_type'=>$logedin_user->user_type, 'message' => 'Server Error'],500);
      }

      }
    }



    /**
     * GET users/attendanceHistoryAjax/{id?}
     *
     * Return JSON collection of attendance list ajax.
     * Return a JSON collection of list attendance history of workers
     * @authenticated
     * @group Users
     *
     * @responseField data array
     * @response 200 {data:[]}
     */
    public function fetchAttendanceHistoryAjax(Request $request)
    {
      $logedin_user=auth()->user();
      if($request->ajax()) //check for ajax request
      {
        try {
              $worker_ids = array();
              $this->data['total_records'] = 0;
              $worker_data = Helper::fetchWorkerofSP();
                if(isset($worker_data) && !empty($worker_data))
                {
                    $worker_data = $worker_data->toArray();
                    $worker_ids = array_column($worker_data,'id');
                }
                else
                {
                  $worker_data = array();
                }
              $searchValue = trim($request->search_text);

              $data = WorkerAttendances::select('worker_attendances.*','users.id as user_id','users.name', 'users.email',DB::raw("DATE_FORMAT(worker_attendances.clock_in_datetime, ' %I:%i %p %d/%m/%Y') as clock_in_datetime_formatted"),
              DB::raw("DATE_FORMAT(worker_attendances.clock_out_datetime, ' %I:%i %p %d/%m/%Y') as clock_out_datetime_formatted"))
              ->whereNotNull('clock_out_datetime')
              ->whereIn('worker_id',$worker_ids);
              $data=$data->leftJoin("users",function($join){
                $join->on("users.id","=","worker_attendances.worker_id");
                });


              if(!empty($searchValue)) // If requested with a search filter
              {
                $data = $data->where(function ($query) use ($searchValue) {
                  $query->whereRaw("(users.name LIKE '%{$searchValue}%' OR users.email LIKE '%{$searchValue}%' OR worker_attendances.attendance_id LIKE '%{$searchValue}%')");
                  });
              }

              $date_search = trim($request->date_range);
              if($date_search != "")
              {
                $date_range = explode(',',$date_search);
                $date = array('startd' => $date_range[0], 'endd' => $date_range[1]);
                if ($date_search != "" && !empty($date['startd'])) {
                    // Filter by date range
                    $data = $data->whereBetween('worker_attendances.created_at', [$date['startd'] . ' 00:00:00', $date['endd'] . ' 23:59:59']);
                }
              }

              $selected_worker = trim($request->selected_worker);

              if ($selected_worker != "") {
                  $selected_worker = explode(',',trim($request->selected_worker));
                  $data = $data->whereIn('worker_attendances.worker_id', $selected_worker);
              }

              $page_length = $request->page_length ?? 10;
              $data=$data->where([['users.deleted_at',NULL],['users.is_deleted', 'no']])
              ->orderBy('worker_attendances.id','DESC')
              ->groupBy('worker_attendances.id')
              ->paginate($page_length);

              $total_records=$data->total();

              if($total_records > 0) {
                foreach($data->items() as $key=>$row) {
                  $data->items()[$key]->clock_in_building_name = PropertyBuildings::where('id',$data->items()[$key]->clock_in_property_id)->pluck('building_name')->first();
                  $data->items()[$key]->clock_out_building_name = PropertyBuildings::where('id',$data->items()[$key]->clock_out_property_id)->pluck('building_name')->first();
                  $data->items()[$key]->clock_out_by_usertype = User::where('id',$data->items()[$key]->clock_out_by)->pluck('user_type')->first();
                  $data->items()[$key]->profile_img = ImagesUploadHelper::displayImage($data->items()[$key]->profile_img, 'uploads/profile_images', 0);
                }
              }

              return json_encode(array('data'=>$data,'total_records'=>$total_records,'user_type'=>$logedin_user->user_type));
        }
        catch (\Throwable $th) {
           // \Log::error("fetchAttendanceHistoryAjax Error: ".$th);
            return response()->json(['status' => false, 'total_records'=> 0,'user_type'=>$logedin_user->user_type, 'message' => 'Server Error'],500);
        }

      }
    }


    /**
     * GET users/attendancce-details
     *
     * Return JSON response after Fetch request
     * Fetch attendance details
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
   public function fetchAttendanceDetails(Request $request)
   {
       $request_id = $request->request_id;

       $result = WorkerAttendances::select('worker_attendances.*','users.id as user_id','users.name', 'users.building_ids', 'users.email',DB::raw("DATE_FORMAT(worker_attendances.clock_in_datetime, ' %I:%i %p %d/%m/%Y') as clock_in_datetime_formatted"))
        ->whereNull('clock_out_datetime')
        ->where('worker_attendances.id',$request_id)
        ->leftJoin("users",function($join){
          $join->on("users.id","=","worker_attendances.worker_id");
          })
          ->first();

       if (!empty($result)) {
        $result->building_data = '';
          if(trim($result->building_ids) != "")
          {
            $result->building_data = PropertyBuildings::whereIn('id',explode(',',$result->building_ids))->get();
          }
           return response()->json(['status' => true, 'result'=> $result, 'message' => '']);
       } else {
           return response()->json(['status' => false, 'message' => 'Something went wrong. Please try again!']);
       }
   }



   /**
     * GET users/clock-out-by-sp
     *
     * Return JSON response after create request
     * Colck out perform by sp on behalf of worker
     *
     * @authenticated
     * @group Users
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function clockOutBySp(Request $request)
    {
        $request_id = $request->attendance_request_id;
        $property_id = $request->selected_clockout_property;

        // Parse the original date with Carbon
        $clockout_datetime = strtotime($request->selected_clockout_datetime);
        // Convert the format to Y-m-d H:i:s
        $clockout_datetime = date('Y-m-d H:i:s', $clockout_datetime);
        $workerdata = WorkerAttendances::whereNull('clock_out_datetime')->where('id',$request_id)->first();
        if($workerdata)
        {
          $worker_id = $workerdata->worker_id;
          $clock_in_datetime = $workerdata->clock_in_datetime;
        }
        else
        {
          return response()->json(['status' => false, 'message' => 'No Record found!']);
        }

        $update_result = WorkorderHelper::clockOutBySp($clock_in_datetime, $clockout_datetime, $property_id, $request_id);


        if (!empty($update_result)) {
            // Save notification

            $worker = Helper::userDetail($worker_id);
            if (!empty($worker)) {

                $registration_ids[] = $worker->device_token;
                  $message = 'You have been clocked out by your supervisor';
                    if ($worker->selected_app_langugage == 'ar' || $worker->selected_app_langugage == 'ur') {
                      $message = $worker->selected_app_langugage == 'ar' ? 'لقد تم تسجيل خروجك من قبل المشرف الخاص بك' : 'آپ اپنے سپروائزر کی جانب سے خروج کر دیےگئے ہیں۔';
                    }

                $message = [
                  'title' => $message,
                  'body' => '',
                    'notification_type' => 'clock_out_by_sp',
                ];

                $res = ApiHelper::send_notification_worker_FCM($worker_id, $registration_ids, $message, auth()->user()->building_ids, 'clock_out_by_sp', $request_id);
            }

            return response()->json(['status' => true, 'result'=> '', 'message' => 'success']);
        } else {
            return response()->json(['status' => false, 'message' => 'Something went wrong. Please try again!']);
        }
    }



    /**
     * GET users/attendance-export/{id?}
     *
     * Return JSON collection of attendance list ajax.
     * Perform export feature of workers attendance
     * @authenticated
     * @group Users
     *
     * @responseField data array
     * @response 200 {data:[]}
     */
    public function fetchAttendanceExportData(Request $request)
    {
      $type = trim($request->input('type'));
      $language = trim($request->input('language'));
      $start_date = date('Y-m-d', strtotime(trim($request->input('export_from_date'))));
      $end_date = date('Y-m-d', strtotime(trim($request->input('export_to_date'))));
      $selected_workers = trim($request->input('selected_workers'));
      $start_date_time = $start_date.' 00:00:00';
      $end_date_time = $end_date.' 23:59:59';



        $worker_ids = array();
          if(isset($selected_workers) && ($selected_workers != ""))
          {
              $worker_ids = explode(',',$selected_workers);
          }

        $data = WorkerAttendances::select('worker_attendances.*','users.id as user_id','users.name', 'users.email',DB::raw("DATE_FORMAT(worker_attendances.clock_in_datetime, ' %I:%i %p %d/%m/%Y') as clock_in_datetime_formatted"),
        DB::raw("DATE_FORMAT(worker_attendances.clock_out_datetime, ' %I:%i %p %d/%m/%Y') as clock_out_datetime_formatted"))
        ->whereNotNull('clock_out_datetime')
        ->whereIn('worker_id',$worker_ids);
        $data=$data->leftJoin("users",function($join){
          $join->on("users.id","=","worker_attendances.worker_id");
          });

        if ($start_date && $end_date) {
          $data = $data->whereBetween('worker_attendances.created_at', [$start_date_time, $end_date_time]);
        }

        $data = $data->where([['users.deleted_at',NULL],['users.is_deleted', 'no']])->count();

        if($data > 0 )
        {
          $lang = App::getLocale();
      $logedin_user = Auth::user();
      App::setLocale($language);
      $link_start = asset('uploads/');
      $report_number = 'RPT' . rand(1000, 9999);
              if ($request->type == 'pdf') {
                $filename = rand(10000000, 99999999) . '.pdf';

                $report_details = ['user_id' => Auth::user()->id, 'filename' => $filename, 'report_no' => $report_number, 'report_type' => $request->type, 'requested_at' => date('Y-m-d H:i:s'), 'status' => 'pending', 'start_date' => $start_date, 'end_date' => $end_date, 'project_user_id' => Auth::user()->project_user_id];

                $translations = Helper::translateIndustryType();
                $report_id = ReportQueryHelper::createReport($report_details);

                if ($report_id) {
                  SendAttendanceReportJob::dispatch($request->all(), Auth::user(), $report_number, $filename, $lang, $_SERVER["DOCUMENT_ROOT"], $link_start, $report_id, Auth::user()->project_user_id, $translations);
                    App::setLocale($lang);
                    return response()->json(['status' => true, 'message' => '']);
                }
            } else {
                $translations = Helper::translateIndustryType();
                $filename = rand(10000000, 99999999) . '.xlsx';
                $report_details = ['user_id' => Auth::user()->id, 'filename' => $filename, 'report_no' => $report_number, 'report_type' => $request->type, 'requested_at' => date('Y-m-d H:i:s'), 'status' => 'pending', 'start_date' => $start_date, 'end_date' => $end_date, 'project_user_id' => Auth::user()->project_user_id];
                $report_id = ReportQueryHelper::createReport($report_details);
                if ($report_id) {
                  SendAttendanceReportJob::dispatch($request->all(), Auth::user(), $report_number, $filename, $lang, $_SERVER["DOCUMENT_ROOT"], $link_start, $report_id, Auth::user()->project_user_id, $translations);
                }
                App::setLocale($lang);
                return response()->json(['status' => true, 'message' => '']);
            }
        }
        else
        {
          return response()->json(['status' => false, 'message' => 'no record found']);
        }


    }

    public function checkUnqiueBmaAreaManagerForProject($projectID,$userID)
    {

        $query = User::query()
            ->where('is_bma_area_manager', '=', 1)
            ->where('project_id', '=', $projectID);


        if ($userID) {
            $query->where('id', '!=', $userID);
        }
//        dd($query->count(),$userID,$projectID);
        return $query->count() === 0;
    }

    public function ajax_check_bma_area_manager_for_project(Request $request, $userID = null)
    {
        $checker= $this->checkUnqiueBmaAreaManagerForProject($request->project_id, $userID);
//        dd($checker);
        return  $checker ? '1' : '0';
    }

}
