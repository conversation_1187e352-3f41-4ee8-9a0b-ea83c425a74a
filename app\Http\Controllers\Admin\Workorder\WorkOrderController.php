<?php
namespace App\Http\Controllers\Admin\Workorder;
use Akaunting\Api\Akaunting;
use Akaunting\Api\Data\ItemData;
use Akaunting\Api\Base\TableConfigInterface;
use Akaunting\Api\Data\Providers\BaseDataProvider;
use Akaunting\Api\Data\Providers\FormDataProvider;
use Akaunting\Api\Data\WarehouseData;
use Akaunting\Api\View\Configs\ContractItemTableConfig;
use Akaunting\Api\View\Configs\WorkOrderItemFormTableConfig;
use App\Events\MaintananceRequestCreated;
use App\Events\WorkOrderClosed;
use App\Http\Controllers\Api\V1\Tenant\ApiHelper;
use App\Http\Controllers\Controller;
use App\Http\Helpers\Helper;
use App\Http\Helpers\WorkorderHelper;
use App\Http\Helpers\ServiceProviderHelper;
use App\Http\Helpers\NotificationHelper;
use Akaunting\Api\Http\Services\ItemElementService;
use Akaunting\Api\Http\Requests\BaseListElementsDTO;
use Illuminate\Support\Facades\Log;
use App\Http\Helpers\ImagesUploadHelper;
use Illuminate\Support\Facades\Session;
use \App\Http\Helpers\MaintenancePortalHelper;
use App\Enums\AdvanceContract\ContractTypes;

use App\Models\{ContractUsableItem,
    ServiceProvider,
    City,
    WorkOrderItem,
    WorkOrders,
    RoomsTypeFloors,
    Property,
    Asset,
    AssetCategory,
    AssetName,
    ChecklistTasks,
    WorkOrderChat,
    Priorities,
    User,
    Checklists,
    MaintenanceRequest,
    ReopenWorkOrderDetail,
    Notification,
    PreventiveWoAction,
    NoChecklistAction,WorkOrderRequestedItem,ServiceProviderMissingItemRequest,ServiceProvieRequestedMissingItem,
    WorkOrderItemRequest, WorkerLocationLog, WorkerLocation, Contracts};
use App\Models\WorkOrderWorkers;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Saloon\Http\Response;
use Spatie\LaravelData\DataCollection;
use Yajra\Datatables\Datatables;
use stdClass;
use Lang;
use App;

use App\Http\Traits\FunctionsTrait;
use App\Http\Traits\SmartAssigningContractTrait;
use App\Http\Traits\ProjectSettingsTrait;
use App\Http\Traits\ContractsTrait;
use App\Http\Traits\WorkOrdersTrait;
use App\Http\Traits\SmartAssignCriteriaDescriptionTrait;
use App\Http\Traits\UserTrait;
use App\Http\Traits\WorkOrderEmailJobTrait;
use App\Models\ContractAssetCategories;
use App\Enums\{
    WorkerAssigningDescision,
    WorkOrderStatus,
    AssignType,
    WorkOrder\PassFail,
    WorkOrder\AssignedTo
};
use App\Notifications\{
    WorkerAssignedNotification,
    WorkerAssignedDatabaseNotification
};

class WorkOrderController extends Controller
{
    use FunctionsTrait, SmartAssigningContractTrait, ProjectSettingsTrait, ContractsTrait, WorkOrdersTrait, SmartAssignCriteriaDescriptionTrait, UserTrait, WorkOrderEmailJobTrait;

    private $view_path='applications.admin.workorder';
    private $pageTitle;
    public function __construct(protected ItemElementService $itemElementService){
        $this->middleware(function ($request, $next) {
            $redirectinactiveuser = \Helper::redirectinactiveuser();
           if($redirectinactiveuser != "active user")
           {
               return $redirectinactiveuser;
           }
            if(auth()->user()->user_type == 'super_admin') {
                if(empty(auth()->user()->project_id)){
                    return redirect()->route('workspace.home')->withFlashMessage('You are not authorized to access that page.')->withFlashType('warning');
                }
            }
            $userPrivileges = auth()->user()->user_privileges;

            // Ensure it is properly decoded if necessary
            if (is_string($userPrivileges)) {
                $userPrivileges = json_decode($userPrivileges, true);
            }

            if (auth()->check() && isset($userPrivileges['workorder']) && is_array($userPrivileges['workorder']) && in_array("no_view", $userPrivileges['workorder'])) {
                return redirect()->route('admin.dashboard');
            }

            return $next($request);
        });
    }

    /**
     * GET workorder/ (OR) workorder/list/{type}
     *
     * Return view with service providers list
     * Return a view of list of service providers for all the subtabs of the work orders. These service providers are mainly displayed for user types such as Super admin, Osool admin, POA and POE. There are conditions to also display the deleted service providers if user checked a checkbox to view the deleted service providers. When the view button of serviceprovider(SP) is clicked it will list the workorders of that particular SP.
     *
     * @authenticated
     * @group Service providers
     *
     * @responseField data array
     * @response 200 {"data": []}
     */
    public function serviceProviderList(Request $request, $type = 'all')
    {
        // Check if user has privileges, redirect if not
        $privilegeCheck = Helper::checkLoggedinUserPrivileges('no_view', 'workorder', false);
        if (!$privilegeCheck['success']) {
            return $privilegeCheck['redirect'];
        }

        $this->data['pageTitle'] = 'Service Provider';
        $user_id = Auth::user()->id;

        // Determine the type based on the request and conditions
        $ptype = $type;
        if (isset($request->deleted) && $request->deleted == 'yes') {
            $ptype = 'deleted';
        }

        // Fetch service providers based on user and search criteria
        $service_providers = ServiceProviderHelper::getProvidersBySearchAndType($user_id, '', $ptype);
        $this->data['total_count'] = $service_providers['cnt'];

        // Handle redirect for single service provider scenarios
        if ($service_providers['deleted_service_providers'] <= 1 && $service_providers['cnt'] == 1) {
            $redirectRoute = WorkorderHelper::getRedirectRouteBasedOnType($type);
            return redirect()->route($redirectRoute, Crypt::encryptString($service_providers['data'][0]['id']));
        }

        if(Session::has('selected_multiple_sp_id')) {
            $selected_multiple_sp_id = Session::get('selected_multiple_sp_id');
            $redirectRoute = WorkorderHelper::getRedirectRouteBasedOnType($type);
            return redirect()->route($redirectRoute, Crypt::encryptString($selected_multiple_sp_id[0]));
        }

        // Handle AJAX requests and regular views based on type
        if (request()->ajax()) {
            $search_keyword = $request->search_text;
            $view = '.service-provider-list-ajax';
            return view($this->view_path . $view)->with([
                'service_providers' => $service_providers,
                'type' => $type,
                'count' => $service_providers['cnt']
            ]);
        }

        // Return the appropriate view and data
        $this->data['type'] = $type;
        $this->data['ptype'] = $ptype;
        $view = '.service-provider-list';
        return view($this->view_path . $view, ['data' => $this->data]);
    }

    /**
     * GET workorder/work-order-list/{id}
     *
     * Return view with workorders list
     * Return a view with list of workorders. The Filters also can be applied. For the user types such as Super admin, Osool admin, POA and POE the work orders will listed according to the service providers selected.
     *
     * @authenticated
     * @group Workorders
     *
     * @responseField data array
     * @response 200 {"data": []}
     */
    public function list(Request $request)
    {
        // Check user privileges
        $privilegeCheck = Helper::checkLoggedinUserPrivileges('no_view', 'workorder', false);
        if (!$privilegeCheck['success']) {
            return $privilegeCheck['redirect_url'];
        }
        // Retrieve page from route
        $page = explode('/', request()->route()->uri)[1];
        $pagePrivileges = json_decode(auth()->user()->user_privileges);

        // Set page title and retrieve service provider ID
        $this->data['pageTitle'] = 'All Workorder List';
        $serviceProviderId = Crypt::decryptString($request->route('id'));
        $this->data['service_provider_id'] = $serviceProviderId;

        $this->data['show_provider_filter'] = 'no';

        $this->data['fetch_multiple_service_providers'] = [];

        $serviceproviderids = [];

        $fetch_multiple_service_providers = ServiceProviderHelper::getProvidersBySearchAndType(Auth::user()->id, '', 'all');

        if ($fetch_multiple_service_providers['deleted_service_providers'] <= 1 && $fetch_multiple_service_providers['cnt'] == 1) {
            // Handle redirect for single service provider scenarios
            if(Session::has('selected_multiple_sp_id')) {
                $selected_multiple_sp_id = Session::get('selected_multiple_sp_id');

                $serviceproviderids = $selected_multiple_sp_id;
            }
            else
            {
                //if(in_array(Auth::user()->user_type,array('building_manager','building_manager_employee'))) {
                    $serviceproviderids = collect($fetch_multiple_service_providers['data'])->pluck('id')->take(1)->toArray();
                    Session::put('selected_multiple_sp_id', $serviceproviderids);
                // } else{
                //     Session::put('selected_multiple_sp_id', explode(',',$serviceProviderId));
                //     $serviceproviderids = explode(',',$serviceProviderId);
                // }
            }
        }
        else
        {
            $this->data['show_provider_filter'] = 'yes';
            $this->data['fetch_multiple_service_providers'] = $fetch_multiple_service_providers;
            if(Session::has('selected_multiple_sp_id')) {
                $selected_multiple_sp_id = Session::get('selected_multiple_sp_id');

                $serviceproviderids = $selected_multiple_sp_id;
            }
            else
            {
                if(in_array(Auth::user()->user_type,array('building_manager','building_manager_employee'))) {
                    $serviceproviderids = collect($fetch_multiple_service_providers['data'])->pluck('id')->toArray();
                    Session::put('selected_multiple_sp_id', $serviceproviderids);
                } else{
                    Session::put('selected_multiple_sp_id', explode(',',$serviceProviderId));
                    $serviceproviderids = explode(',',$serviceProviderId);
                }
            }
        }

        if(!in_array(Auth::user()->user_type,array('super_admin','osool_admin','admin','admin_employee','building_manager','building_manager_employee')))
        {
            $this->data['show_provider_filter'] = 'no';
        }
        // Retrieve logged in user and related data
        $loggedInUser = Auth::user();
        $userId = $loggedInUser->id;

        // Retrieve properties and asset categories
        $this->data['properties'] = WorkorderHelper::getFilteredProperties($loggedInUser, $userId, $serviceproviderids);
        $this->data['workers'] = WorkorderHelper::getFilteredWorkers();
        $this->data['asset_categories'] = WorkorderHelper::getFilteredAssetCategories($loggedInUser);

        $this->data['assets'] = WorkorderHelper::getAllAssetsFromAssetCategories($this->data['asset_categories']);
        // Retrieve supervisor data
        $this->data['supervisors'] = WorkorderHelper::getFilteredSupervisors($loggedInUser, $serviceProviderId);
        // Retrieve the work_orders_columns data using the Eloquent relationship and Assign the data to the property
        $this->data['work_orders_columns'] = $loggedInUser->workOrderColumns()
            ->where('page','work-order-list')
            ->pluck('columns_name')
            ->unique()
            ->toArray();

        // Retrieve datatable parameters
        $draw = $request->get('draw');
        $start = $request->get('start');
        $length = $request->get('length');

        $is_rating_filter =false;
        //We retrieve rate filter param stored in cookie
        if (isset($_COOKIE['rate_filter'])) {
            $request->rating = $_COOKIE['rate_filter'];
            $is_rating_filter =$_COOKIE['rate_filter'];
            setcookie('rate_filter', '', 1, '/');
        }else{
            $is_rating_filter =false;
        }

        $search = array(
            'passfail' => $request->passfail,
            'type' => $request->type,
            'buildings' => $request->buildings,
            'status'=> $request->status,
            'dateRange'=>array('startd'=>@$request->dateRange[0], 'endd'=>@$request->dateRange[1]),
            'draw' => $request->get('draw'),
            'start' => $request->get('start'),
            'length' => $request->get('length'),
            'wo_id' => $request->wo_id,
            'supervisors' => (isset($request->supervisors))?$request->supervisors : null,
            'workers' => (isset($request->workers)) ? $request->workers : null,
            'service_types' => (isset($request->service_types))?$request->service_types : null,
            'rating' => (isset($request->rating))?$request->rating : null,
            'isShowingSelected' => (isset($request->isShowingSelected))?$request->isShowingSelected : false,
            'ids' => (isset($request->ids))?$request->ids : null,
            'assets' => (isset($request->assets))?$request->assets : null,
            'columnIndex' => request()->input('order.0.column'), // Column index from DataTables
            'sortDirection' => request()->input('order.0.dir'),  // 'asc' or 'desc'
        );




        // Define the statuses you want to count
        $statusesToCount = [1, 2, 3, 4, 5, 6, 7, 8];

        $statusCounts = [];
        foreach ($statusesToCount as $status) {
            $searchFiltered['status'] = $status;
            $statusKey = strtolower(WorkOrders::getStatusString($status)) . '_row_count'; // Or use any other logic to generate the key
            $statusCounts[$statusKey] = 0;
        }
        // Merge the status counts data into the existing data array
        $this->data = array_merge($this->data, $statusCounts);

        //$sqlList = WorkOrders::get_work_orders($userId, $serviceProviderId, $search, $request->segment(2));
        if($request->ajax())
        {
                if(Session::has('selected_multiple_sp_id'))
                {
                    $serviceProviderId = count(Session::get('selected_multiple_sp_id')) ? Session::get('selected_multiple_sp_id') : explode(',',$serviceProviderId) ;
                }
                else
                {
                    $serviceProviderId = explode(',',$serviceProviderId);
                }

            // Retrieve total workorders according to the conditions
            $workOrders = WorkorderHelper::getFilteredWorkOrders($loggedInUser, $serviceProviderId, $search, $request->segment(2));
            $this->data['total_row_count'] = $workOrders['count'];
            // Merge the status counts data into the existing data array
            $this->data = array_merge($this->data, $workOrders['statusCounts']);
            $sqlList = $workOrders['result'];
            // Generate DataTable using the helper function
            return WorkorderHelper::generateDatatable($sqlList, $this->data);
        }
        $this->data['page'] = $request->segment(2);
        $this->data['is_rating_filter'] = $is_rating_filter;
        return view($this->view_path.'.list',['data'=>$this->data]);
    }

    /**
     * GET workorder/{id?}/{notificationread?}/{notification_id?}
     *
     * Return view with workorder details
     * Return a view with details of workorders. This will have the chat functionality, timeline etc.
     *
     * @authenticated
     * @group Workorders
     *
     * @response 200
     */
    public function show($id,$notification_read = null,$notification_id = null)
    {
        // @flip@ if logged in user has [no_view] privileges redirect to dashboard
        $res_privileges=Helper::checkLoggedinUserPrivileges('view','workorder');
        if(!$res_privileges['success']){
            return $res_privileges['redirect_url'];
        }
        // dd($id);
        if(!isset($id) || $id =='edit-preventive-save-data' || $id == 'single'){ //If has id
            return redirect()->route('workorder.workorders.list', Crypt::encryptString(1));
        }
        //dd($id);
        $id = Crypt::decryptString($id);

        $user_id = Auth::user()->id;
        if(!empty($notification_read) && $notification_read == 'notification-read') {

            $notification_id = Crypt::decryptString($notification_id);
            //dd($notification_id);
            $notification = DB::table('notifications')->where('id',$notification_id)->first();
            //dd($notification);
            $read_user_arr = !empty($notification->is_read_by_users) ? explode(',',$notification->is_read_by_users) : [];

            if(!in_array($user_id,$read_user_arr)) {
                $read_user_arr[] = $user_id;
            }
            $read_user_list = implode(',',$read_user_arr);
            DB::table('notifications')->where('id',$notification->id)->update(['is_read_by_users'=>$read_user_list]);
        }
        //dd($id);
        $this->data['pageTitle']='Work Order Details';
        $wo_type = WorkOrders::where('id',$id)->value('work_order_type');
        if($wo_type =='preventive'){
            $work_orders_details = WorkOrders::get_work_order_details_pm($id);
        }
        else{
            $work_orders_details = WorkOrders::get_work_order_details($id);
        }

        $kpiservice = new \App\Services\PerformanceIndicatorService();
        $workOrder = WorkOrders::find($id);

        // Assuming $workOrder is already defined and is an instance of your WorkOrder model
        $contractId = $workOrder->contract_id; // Assuming you have a contract_id foreign key in your work_orders table

        // Retrieve the contract even if it is trashed
        $contract = Contracts::withTrashed()->find($contractId);

        $performanceIndicatorData = $kpiservice->getDataForContractOverview($contract);

        if(isset($work_orders_details->unit_id) && $work_orders_details->unit_id !=0){ //If unit id not equal is 0
            // if(!isset($work_orders_details->floor) || !isset($work_orders_details->room))
            // {
                if(DB::table('room_types_floors')-> where('id',$work_orders_details->unit_id )
            ->value('deleted_at')!= ''){
                $r_del =  __('general_sentence.modal.deleted');
            }
            else{
                $r_del = '';
            }
                $work_orders_details->floor = DB::table('room_types_floors')-> where('id',$work_orders_details->unit_id )
            ->value('floor');
                $work_orders_details->room = DB::table('room_types_floors')->where('id',$work_orders_details->unit_id )
            ->value('room')
            .$r_del ;
            // }
        }
        if(!empty($work_orders_details)) //If has work order details
        {
            $wtfs = DB::table('work_time_frame')
                      ->select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
                      ->where('user_id', $work_orders_details->project_user_id)
                      ->first();
            if($work_orders_details->wtf_start_time == '' || ($work_orders_details->work_order_type == "preventive" && $work_orders_details->status == 1)){
                if(!isset($wtfs)) //If work order time frame has added
                {
                    $time = "00:00:00";
                }
                 else {
                    $time = $wtfs->start_time;
                }
            }
            else{
                $time = $work_orders_details->wtf_start_time;
            }
            if($work_orders_details->work_order_type == "preventive") //If work_order_type is preventive
            {
              $work_orders_details->created_at = $work_orders_details->start_date.' '.$time;
            }
            $contract_id=$work_orders_details->contract_id;
            $contractDetails = DB::table('contracts')->where('id',$contract_id)->where('end_date','>=',date('Y-m-d'))->where('status',1)->where('is_deleted','no')->first();
            $today=date('Y-m-d');
            if(isset($contractDetails)) //If has contracts
            {
                $status=__("data_contract.table.active");
            }
            else
            {
                $status=__("data_contract.contract_forms.label.expired");
            }
            $status=__("data_contract.contract_forms.label.contract_number");
            $project_user_id = $work_orders_details->project_user_id;

            if(isset($project_user_id) && $project_user_id != 0) //If has project_user_id and project_user_id not equal to 0
            {
                $userDetails = DB::table('users')->where('id', $project_user_id)->first();
                if ((Auth::user()->user_type != "sp_admin" && Auth::user()->user_type != "supervisor") && !($userDetails->project_user_id==Auth::user()->project_user_id) ) {
                    return redirect()->route('admin.dashboard');
                }
                Log::info('Project User ID Authentificad : ' . Auth::user()->project_user_id . ', Project User ID From WorkOrder : ' . $userDetails->project_user_id);
                $project_id = $userDetails->project_id;
            }
            else
            {
                $userDetails = DB::table('users')->where('id', $work_orders_details->created_by)->first();
                if ((Auth::user()->user_type != "sp_admin" && Auth::user()->user_type != "supervisor") && !($userDetails->project_user_id==Auth::user()->project_user_id) ) {
                    return redirect()->route('admin.dashboard');
                }
                Log::info('Project User ID Authentificad : ' . Auth::user()->project_user_id . ', Project User ID From WorkOrder : ' . $userDetails->project_user_id);
                DB::table('work_orders')->where('id', $work_orders_details->id)->update(['project_user_id' => $userDetails->project_user_id]);
                $project_id = $userDetails->project_id;
            }
            $projectSetting = DB::table('project_settings')
                            ->select('workorder_reopen_periods')
                            ->where('project_id',$project_id)->first();

            if($projectSetting) { //If has project settings
                $workorder_reopen_periods= $projectSetting->workorder_reopen_periods;
            } else {
                $workorder_reopen_periods= '14';
            }
            $timeline = WorkOrders::get_work_order_timeline($id, $work_orders_details->unique_id);
            //dd($work_orders_details->created_at);
            $chats = WorkOrders::get_work_order_chat($id);
            $workers = WorkOrders::get_workers($work_orders_details->contract_id, $work_orders_details->asset_category_id, $work_orders_details->building_id);
            $cu_la = \App::getLocale();
            if(isset($workers) && !empty($workers))
            {
                foreach($workers as $key => $val)
                {
                    $availability_record = Helper::CheckChangeAvailabilitystatusworkorder($val['id']);
                    if($availability_record['id'] != "no_record")
                    {
                        $leave_reason = $cu_la == "ar" ? $availability_record['reason_type_ar'] : $availability_record['reason_type_en'];
                        $workers[$key]['name'] = $val['name'] ." - ". $leave_reason ." - ". $availability_record['from_datetime'] ." - ". $availability_record['to_datetime'];
                    }
                }
            }
            $images = Helper::getWorkorderChecklistImages($id);
            $worker_comment = WorkOrders::get_checklist_worker_comment($id);

            if((isset($work_orders_details->supervisor_id) && $work_orders_details->supervisor_id != NULL && $work_orders_details->assigned_to == 'supervisor') || (count(explode(',', $work_orders_details->supervisor_id)) == 1 && $work_orders_details->assigned_to == 'sp_worker'))
            {
                $supervisors = WorkOrders::get_supervisors($work_orders_details->supervisor_id);
            }
            else
            {
                $supervisors = WorkOrders::get_supervisors($work_orders_details->service_provider_id);
            }


            $contract_details = DB::table('contracts')->select('*')->where('id',$work_orders_details->contract_id)->first();
            $work_orders_details->contract_deleted = false;
            if($contract_details->is_deleted != 'no' || $contract_details->deleted_at !=''){
                $contract_details->contract_number =$contract_details->contract_number.__('general_sentence.modal.deleted') ;
                $work_orders_details->contract_deleted = true;
            }
            $contract_number = $contract_details->contract_number;
            $work_orders_details->contract_number = $contract_details->contract_number;
            $work_orders_details->contract_id = $contract_details->id;
            $contractTypeId = $contract_details->contract_type_id ?? '';

            //dd($contract_number);
            $sla_asset_categories = DB::table('contract_asset_categories')
                ->where('asset_category_id', $work_orders_details->asset_category_id)
                ->where('contract_number', $contract_number)
                ->orderBy('id', 'desc')
                ->first();
            $response_time = 0;
            $service_window = 0;
            $response_time_type = 'hours';
            $service_window_type = 'minutes';
            //dd($work_orders_details->priority_id);
            if($work_orders_details->work_order_type == "reactive") //If work_order_type is reactive
            {
                if(in_array($contractTypeId, ContractTypes::advanced())) 
                {
                    $priority_id = $work_orders_details->wo_priority_id; //If contract type is 6 or 7 then use wo_priority_id
                }
                else
                {
                    $priority_id = '0'; //Default priority_id is 0
                    if(!empty($sla_asset_categories->priority_id) || !empty($work_orders_details->sla_service_window_priority)){
                        if($work_orders_details->sla_service_window_priority != 0)
                        {
                            $priority_id = $work_orders_details->sla_service_window_priority;
                        }
                        else
                        {
                            $priority_id = $sla_asset_categories->priority_id;
                        }    
                    }    
                }
                if($priority_id != '' && $priority_id != 0){ //If sla asset categories has priority_id
                    Log::info('Priority Id: '.$priority_id.' Contract Number: '.$contract_number.' Asset Category: '.$work_orders_details->asset_category_id);
                    $contract_priorities = DB::table('contract_priorities')
                        ->where('priority_id', $priority_id)
                        ->where('contract_number', $contract_number)
                        ->orderBy('id', 'desc')
                        ->first();
                    if(empty($contract_priorities))
                    {
                        $contract_priorities = DB::table('priorities')
                            ->where('id', $priority_id)
                            //->where('contract_number', $contract_number)
                            ->orderBy('id', 'desc')
                            ->first();
                    }
                    if(!empty($work_orders_details->wo_priority_id) && $work_orders_details->wo_priority_id != 0)
                    {
                        $contract_priorities = DB::table('contract_priorities')
                            ->where('priority_id', $priority_id)
                            ->where('contract_number', $contract_number)
                            ->orderBy('id', 'desc')
                            ->first();
                            if(empty($contract_priorities))
                            {
                                    $contract_priorities = DB::table('priorities')
                                    ->where('id', $work_orders_details->wo_priority_id)
                                    ->orderBy('id', 'desc')
                                    ->first();
                            }
                    
                        $priority_id = $work_orders_details->wo_priority_id;
                    }
                    if(isset($contract_priorities)) {
                        $response_time = $contract_priorities->response_time;
                        $service_window = $contract_priorities->service_window;
                        $response_time_type = $contract_priorities->response_time_type;
                        $service_window_type = $contract_priorities->service_window_type;

                        $priorities = DB::table('priorities')->select('priority_level', 'deleted_at')->where('id', $priority_id)->first();
                        //dd($priorities);
                        if (isset($priorities)) {
                            $deleted = "";
                            if ($priorities->deleted_at != "") {
                                $deleted = " [" . __("work_order.bread_crumbs.deleted") . "]";
                            }
                            $work_orders_details->priority_level = $priorities->priority_level . $deleted;
                        }
                        $priorities = DB::table('priorities')->select('priority_level', 'deleted_at')->where('id', $priority_id)->first();
                        //dd($priorities);
                        $work_orders_details->response_time_priority_level = $work_orders_details->priority_level;
                        if (isset($priorities)) {
                            $deleted = "";
                            if ($priorities->deleted_at != "") {
                                $deleted = " [" . __("work_order.bread_crumbs.deleted") . "]";
                            }
                            $work_orders_details->response_time_priority_level = $priorities->priority_level . $deleted;
                        }
                    }
                }
            }
            elseif($work_orders_details->work_order_type == "preventive" && $work_orders_details->wo_priority_id != 0) //If work_order_type is preventive and priority_id is not equal to 0
            {
                $priority_id = $work_orders_details->wo_priority_id;
                if($work_orders_details->sla_service_window_priority != 0)
                {
                    $priority_id = $work_orders_details->sla_service_window_priority;
                }
                $contract_priorities = DB::table('contract_priorities')
                      ->where('priority_id', $priority_id)
                      ->where('contract_number', $contract_number)
                      ->orderBy('id', 'desc')
                      ->first();
                if(empty($contract_priorities))
                {
                    $contract_priorities = DB::table('priorities')
                        ->where('id', $priority_id)
                        //->where('contract_number', $contract_number)
                        ->orderBy('id', 'desc')
                        ->first();
                }
                if(!empty($work_orders_details->wo_priority_id) && $work_orders_details->wo_priority_id != 0)
                {
                    $contract_priorities = DB::table('priorities')
                        ->where('id', $work_orders_details->wo_priority_id)
                        ->orderBy('id', 'desc')
                        ->first();
                }
                if(isset($contract_priorities))
                {
                  $response_time = $contract_priorities->response_time;
                  $service_window = $contract_priorities->service_window;
                  $response_time_type = $contract_priorities->response_time_type;
                  $service_window_type = $contract_priorities->service_window_type;
                  //dd($service_window);
                  $priorities = DB::table('priorities')->select('priority_level', 'deleted_at')->where('id', $work_orders_details->wo_priority_id)->first();
                    //dd($priorities);
                    if(isset($priorities))
                    {
                        $deleted = "";
                        if($priorities->deleted_at != "")
                        {
                            $deleted = " [".__("work_order.bread_crumbs.deleted")."]";
                        }
                        $work_orders_details->priority_level = $priorities->priority_level.$deleted;
                    }
                    $priorities = DB::table('priorities')->select('priority_level', 'deleted_at')->where('id', $work_orders_details->wo_priority_id)->first();
                    //dd($priorities);
                    $work_orders_details->response_time_priority_level = $work_orders_details->priority_level;
                    if(isset($priorities))
                    {
                        $deleted = "";
                        if($priorities->deleted_at != "")
                        {
                            $deleted = " [".__("work_order.bread_crumbs.deleted")."]";
                        }
                        $work_orders_details->response_time_priority_level = $priorities->priority_level.$deleted;
                    }
                }
            }
            else
            {
              $contract_frequencies = DB::table('frequencies_master')
                        ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                        ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                        ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                        ->where('frequencies_master.id', $work_orders_details->frequency_id)
                        ->where('contract_frequencies.contract_number', $contract_number)
                        ->first();
                      $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
                      $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : '';
                      $response_time_type = isset($contract_priorities->response_time_type)?$contract_priorities->response_time_type:'hours';
                      $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
            }
            $priorities = DB::table('priorities')->select('priority_level', 'deleted_at')->where('id', $work_orders_details->sla_service_window_priority)->first();
            //dd($priorities);
            if(isset($priorities))
            {
                $deleted = "";
                if($priorities->deleted_at != "")
                {
                    $deleted = " [".__("work_order.bread_crumbs.deleted")."]";
                }
                $work_orders_details->priority_level = $priorities->priority_level.$deleted;
            }
            $priorities = DB::table('priorities')->select('priority_level', 'deleted_at')->where('id', $work_orders_details->sla_response_time_priority)->first();
            //dd($priorities);
            $work_orders_details->response_time_priority_level = $work_orders_details->priority_level;

            if(isset($priorities))
            {
                $deleted = "";
                if($priorities->deleted_at != "")
                {
                    $deleted = " [".__("work_order.bread_crumbs.deleted")."]";
                }
                $work_orders_details->response_time_priority_level = $priorities->priority_level.$deleted;
            }
            if($work_orders_details->job_started_at == NULL || $work_orders_details->bm_approve_issue == 2) //If job_started_at is null
            {
                $target_date = $work_orders_details->target_date;
                $work_orders_details->target_date = $target_date;
            }
            else
            {
                if($work_orders_details->work_order_type == "preventive" && $work_orders_details->target_date != ""  && $work_orders_details->target_date != "00:00:00")
                {
                    $target_date = $work_orders_details->target_date;
                }
                else
                {
                    $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($work_orders_details->job_started_at)));
                }
                //dd($service_window.' '.$service_window_type);

                $work_orders_details->target_date = $target_date;


            }
            $tdate = date('Y-m-d H:i:s');
            if($work_orders_details->job_started_at != '' && $work_orders_details->status != 4 && $work_orders_details->pass_fail == "pending") //If job_started_at not equal to empty and status not equal to 4 and workorder_journey not equal to job_approval
            {
                if(isset($work_orders_details->pause_time_spent_minutes) && trim($work_orders_details->pause_time_spent_minutes) != "" && $work_orders_details->pause_time_spent_minutes > 0)
                {
                    $newDateTime = Carbon::now()->subMinutes($work_orders_details->pause_time_spent_minutes);
                    $newDateTime = $newDateTime->format('Y-m-d H:i:s');
                }
                else
                {
                    $newDateTime = date('Y-m-d H:i:s');
                }

                if(strtotime($target_date) >= strtotime($newDateTime)) //If target_date greater than equal to todays date
                {
                    $work_orders_details->pass_fail = 'Pass';
                }
                else
                {
                    $work_orders_details->pass_fail = 'Fail';
                }
            }
            $user_id = auth()->user()->id;
            DB::table('work_order_chat')->where('work_order_id', $id)->where('receiver_id',$user_id)->update(array('is_read' => '1'));

            $work_orders_details->actual_response_time =  __("work_order.bread_crumbs.pending");
            $work_orders_details->actual_execution_time =  __("work_order.bread_crumbs.pending");
            $work_orders_details->job_evaluation_time = __("work_order.bread_crumbs.pending");

            $priority_details = DB::table('contract_priorities')
                            ->select('id', 'service_window', 'service_window_type', 'response_time', 'response_time_type')
                            ->where('priority_id', $work_orders_details->priority_id)
                            ->where('contract_number', $contract_number)
                            ->orderBy('id', 'desc')
                            ->first();
            if (!$priority_details) {
                $priority_details = DB::table('priorities')->where('id', $work_orders_details->priority_id)->first();
            }
            
            if($priority_details){
                //dd($work_orders_details->sla_response_time);
                if($work_orders_details->sla_response_time)
                {
                    $response_time = $work_orders_details->sla_response_time;
                    $response_time_type = $work_orders_details->response_time_type;
                }
                $work_orders_details->sla_response_time = $response_time.' '.__('configration_priority.priority_forms.label.'.$response_time_type);
                if($work_orders_details->sla_service_window)
                {
                    $service_window = $work_orders_details->sla_service_window;
                    $service_window_type = $work_orders_details->service_window_type;
                }
                $work_orders_details->sla_excecution_time = $service_window.' '. __('configration_priority.priority_forms.label.'.$service_window_type);
            }
            else
            {
                $work_orders_details->sla_response_time = '-';
                $work_orders_details->sla_excecution_time = '-';
            }
            if($work_orders_details->Job_approved_by_sp_at != '' && $work_orders_details->worker_started_at != ''){
                //$t1 = Carbon::parse($work_orders_details->Job_approved_by_sp_at);
                //$t2 = Carbon::parse($work_orders_details->worker_started_at);
                $datetime1 = strtotime($work_orders_details->worker_started_at);
                $datetime2 = strtotime($work_orders_details->Job_approved_by_sp_at);
                $interval  = abs($datetime2 - $datetime1);
                $ret = "";
                /*** get the days ***/
                $days = intval(intval($interval) / (3600*24));
                if($days> 0)
                {
                    $ret .= "$days ". __("configration_assets.comminucation_table.Days").' ';
                }

                /*** get the hours ***/
                $hours = (intval($interval) / 3600) % 24;
                if($hours > 0)
                {
                    $ret .= "$hours ". __("configration_assets.comminucation_table.Hours").' ';
                }

                /*** get the minutes ***/
                $minutes = (intval($interval) / 60) % 60;
                if($minutes > 0)
                {
                    $ret .= "$minutes ". __("configration_assets.comminucation_table.Minutes").' ';
                }

                /*** get the seconds ***/
                $seconds = intval($interval) % 60;
                if ($seconds > 0) {
                    $ret .= "$seconds ".__("configration_assets.comminucation_table.Seconds");
                }
                $work_orders_details->job_evaluation_time  = $ret;
                if($work_orders_details->job_completed_by == 'SP'){
                    $work_orders_details->job_evaluation_time  = __("work_order.bread_crumbs.automatically_evaluation");
                }

                //$work_orders_details->job_evaluation_time  = $response_time.' '. __("configration_assets.comminucation_table.Minutes");
            }
            elseif($work_orders_details->status == 4 && $work_orders_details->Job_approved_by_sp_at == '')
            {
                $work_orders_details->job_evaluation_time  =  __("work_order.forms.label.not_calculated_yet");
            }
            if($work_orders_details->job_started_at != ''){
                $created_at = $work_orders_details->created_at;
                //dd($created_at.' -- '.$work_orders_details->job_started_at);

                $datetime1 = strtotime($created_at);
                $datetime2 = strtotime($work_orders_details->job_started_at);
                $interval  = abs($datetime2 - $datetime1);
                $ret = "";
                /*** get the days ***/
                $days = intval(intval($interval) / (3600*24));
                if($days> 0)
                {
                    $ret .= "$days ". __("configration_assets.comminucation_table.Days").' ';
                }

                /*** get the hours ***/
                $hours = (intval($interval) / 3600) % 24;
                if($hours > 0)
                {
                    $ret .= "$hours ". __("configration_assets.comminucation_table.Hours").' ';
                }

                /*** get the minutes ***/
                $minutes = (intval($interval) / 60) % 60;
                if($minutes > 0)
                {
                    $ret .= "$minutes ". __("configration_assets.comminucation_table.Minutes").' ';
                }

                /*** get the seconds ***/
                $seconds = intval($interval) % 60;
                if ($seconds > 0) {
                    $ret .= "$seconds ".__("configration_assets.comminucation_table.Seconds");
                }
                if($ret == "")
                {
                    $ret = __("work_order.bread_crumbs.automatically_assigned");
                }
                // if($work_orders_details->sp_approove == 0) //If sp_approove is 0
                // {
                //     //return 'Automatically assigned';
                //     $ret = __('work_order.bread_crumbs.automatically_assigned');
                // }
                $work_orders_details->actual_response_time  = $ret;

                //$to = Carbon::createFromFormat('Y-m-d H:s:i', $work_orders_details->job_started_at);
                //$from = Carbon::createFromFormat('Y-m-d H:s:i', $work_orders_details->target_date);
                //$work_orders_details->actual_response_time  = $to->diffInMinutes($from).' '. __("configration_assets.comminucation_table.Minutes");
            }
            if($work_orders_details->job_started_at != '' && $work_orders_details->job_submitted_at != ''){
                if(isset($work_orders_details->time_spent_by_worker) && trim($work_orders_details->time_spent_by_worker) != "")
                {
                    // Step 1: Split the input (hours:minutes:seconds)
                    list($hours, $minutes, $seconds) = explode(':', $work_orders_details->time_spent_by_worker);

                    // Convert hours, minutes, and seconds to total seconds
                    $totalSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;

                    // Step 2: Convert total seconds to days, hours, minutes, and seconds
                    $days = floor($totalSeconds / 86400); // 86400 seconds in a day
                    $seconds = $totalSeconds % 86400;

                    $hours = floor($seconds / 3600); // 3600 seconds in an hour
                    $seconds %= 3600;

                    $minutes = floor($seconds / 60); // 60 seconds in a minute
                    $seconds %= 60;

                }
                else
                {
                    $datetime1 = strtotime($work_orders_details->worker_started_at);
                    $datetime2 = strtotime($work_orders_details->job_submitted_at);
                    $interval  = abs($datetime2 - $datetime1);
                    /*** get the days ***/
                    $days = intval(intval($interval) / (3600*24));
                    /*** get the hours ***/
                    $hours = (intval($interval) / 3600) % 24;
                    /*** get the minutes ***/
                    $minutes = (intval($interval) / 60) % 60;
                    /*** get the seconds ***/
                    $seconds = intval($interval) % 60;
                }


                $ret = "";

                if($days> 0)
                {
                    $ret .= "$days ". __("configration_assets.comminucation_table.Days").' ';
                }


                if($hours > 0)
                {
                    $ret .= "$hours ". __("configration_assets.comminucation_table.Hours").' ';
                }


                if($minutes > 0)
                {
                    $ret .= "$minutes ". __("configration_assets.comminucation_table.Minutes").' ';
                }


                if ($seconds > 0) {
                    $ret .= "$seconds ".__("configration_assets.comminucation_table.Seconds");
                }
                if($ret == "")
                {
                    $ret = __("work_order.bread_crumbs.automatically_assigned");
                }
                $work_orders_details->actual_execution_time  = $ret;
            }
            // if($work_orders_details->job_completion_date != ''){
            //     $to = Carbon::createFromFormat('Y-m-d H:s:i', $work_orders_details->job_completion_date);
            //     $from = Carbon::createFromFormat('Y-m-d H:s:i', $work_orders_details->target_date);
            //     $work_orders_details->job_completion_date  =Carbon::createFromFormat('Y-m-d H:s:i', $work_orders_details->target_date)->format('d M Y - H:i' );

            // }
            // $diff_in_hours = $to->diffInMinutes($from);

            //dd($work_orders_details->created_at);
            $work_orders_details->created_at2  = date('d-m-Y H:i', strtotime($work_orders_details->created_at));
            $work_orders_details->maintenance_request = MaintenanceRequest::with('user')->where('id', $work_orders_details->maintanance_request_id)->first();

            if($work_orders_details->status == 1)
            {
                if(!is_null($work_orders_details->schedule_start_time) && trim($work_orders_details->schedule_start_time))
                {
                    $scheduled_date_time = date('Y-m-d', strtotime($work_orders_details->start_date)) . ' ' . $work_orders_details->schedule_start_time;
                }
                elseif(!is_null($work_orders_details->wtf_start_time) && trim($work_orders_details->wtf_start_time) != "")
                {
                    $scheduled_date_time = date('Y-m-d', strtotime($work_orders_details->start_date)) . ' ' . $work_orders_details->wtf_start_time;
                }
                else
                {
                    //nothing to change
                    $scheduled_date_time = '';
                }
                
                if($scheduled_date_time != '')
                {
                    if(strtotime($scheduled_date_time) > strtotime(date('Y-m-d H:i:s')))
                    {
                        $work_orders_details->status = 7;
                    }
                }
                
                //dd($work_orders_details->status);
            }
            //dd($work_orders_details->status);
            /*---- All the code is regarding asset popup opening-----*/
            $property_id = $work_orders_details->property_id;
            $zones  = RoomsTypeFloors::where('building_id', $work_orders_details->building_id)
            ->groupBy('floor')->pluck('floor');
            //dd($work_orders_details);
            $floor_data  = RoomsTypeFloors::where([['building_id', $work_orders_details->building_id],['floor',$work_orders_details->floor]])
            ->get();


            $asset_number_id = $work_orders_details->asset_number_id;
            $logedin_user = Auth::user();
            $row_data=Asset::where('id', $work_orders_details->asset_number_id)->first();
            // Retrieve the asset's data
            $row_data = Asset::with(['assetCategories'  => function ($query) {
                $query->withTrashed();
            }])->where('id', $asset_number_id)->first();

            $assetCategoryNames = [];
            if($row_data)
            {
                // Check if $row_data->assetCategories exists and has items
                if ($row_data->assetCategories && count($row_data->assetCategories) > 0) {
                    $row_data->asset_categories = $row_data->assetCategories->pluck('id')->toArray();
                } else {
                    $row_data->asset_categories = [$row_data->asset_category_id];
                }
            }
            $this->data['row_data'] = $row_data;

            // Parse damage images
            if (!empty($this->data['row_data']->damage_images)) {
                $this->data['row_data']->damage_images = explode(',', $row_data->damage_images);
                $this->data['damage_images'] = $this->data['row_data']->damage_images;
            } else {
                $this->data['damage_images'] = [];
            }

            // Get asset names with related asset categories
            $this->data['asset_category'] = AssetName::with(['categories' => function ($query) use ($logedin_user) {
                $query->where('service_type', 'hard')->where('is_deleted', 'no')->where('user_id', $logedin_user['project_user_id']);
            }])
            ->with(['directCategory' => function ($query) use ($logedin_user) {
                $query->where('service_type', 'hard')->where('is_deleted', 'no');
            }]) // Load the direct category
            ->where('is_deleted', 'no')
            ->where('user_id', $logedin_user['project_user_id'])
            ->where('id', $work_orders_details->asset_name_id)
            ->orderBy('id', 'asc')
            ->first();

            // Get asset names with related asset categories
            $this->data['asset_name'] = AssetName::with(['categories' => function ($query) use ($logedin_user) {
                $query->where('is_deleted', 'no')->where('user_id', $logedin_user['project_user_id']);
            }])
            ->with('directCategory') // Load the direct category
            ->where('is_deleted', 'no')
            ->where('user_id', $logedin_user['project_user_id'])
            ->orderBy('id', 'asc')
            ->get();

            // Get asset files
            $this->data['assets_files'] = DB::table('assets_files')->select('*')->where('assets_id', $id)->get();

            // Retrieve floor and room information
            if ($row_data && $row_data->unit_id != 0) {
                $row_data->floor = DB::table('room_types_floors')->where('id', $row_data->unit_id)
                    ->value('floor');
                $row_data->room_id = $row_data->unit_id;
            }

            $this->data['asset_floor']=$zones;
            $this->data['asset_room']=$floor_data;
            $this->data['id']=$id;
            $this->data['property_id']=$property_id;
            $property_data=Property::where('id', $property_id)->first();
            $this->data['builiding_name'] = isset($row_data['building_id'])?DB::table('property_buildings')->where('id', $row_data['building_id'])->value('building_name'):0;
            $this->data['building_id'] = isset($row_data['building_id'])?$row_data['building_id']:0;

            $this->data['maintanance_request'] = MaintenanceRequest::with('user')->where('id', $work_orders_details->maintanance_request_id)->first();

            $work_orders_details->related_wos = WorkOrders::select('id','work_order_id','status')->where('unique_id', $work_orders_details->unique_id)->where('unique_id', '!=', NULL)->where('work_order_type', 'reactive')->get();
            if(isset($work_orders_details->maintenance_request))
            {
                $work_orders_details['getMaintenanceCreatedName'] = MaintenancePortalHelper::getMaintenanceCreatedName($work_orders_details->maintenance_request->generated_from,
            $work_orders_details->maintenance_request->name,
                $work_orders_details->maintenance_request->phone, $work_orders_details->maintenance_request->user_id);
            }
            else
            {
                $work_orders_details['getMaintenanceCreatedName'] = __('user_management_module.user_forms.label.notAdded');
            }


            $project_user_id = $work_orders_details->project_user_id;
            if(isset($project_user_id) && $project_user_id != 0) //If has project_user_id and project_user_id not equal to 0
            {
                $userDetails = DB::table('users')->where('id', $project_user_id)->first();
                $project_id = $userDetails->project_id;
            }
            else
            {
                $userDetails = DB::table('users')->where('id', $work_orders_details->created_by)->first();
                $project_id = $userDetails->project_id;
            }
            $projectSetting = DB::table('project_settings')
                            ->select('workorder_reminder_periods')
                            ->where('project_id',$project_id)->first();
            if(isset($projectSetting) && !empty($projectSetting))
            {
                $workorder_reminder_periods = trim($projectSetting->workorder_reminder_periods) != "" ? trim($projectSetting->workorder_reminder_periods) : '7';
            }
            else
            {
                $workorder_reminder_periods = '7';
            }

            /*---- All the code is regarding asset popup opening-----*/
            if((trim($work_orders_details->wo_reminder_sent_on) != null) && (trim($work_orders_details->wo_reminder_sent_on) != ""))
            {
                $wo_reminder_sent_on_timestamp = strtotime($work_orders_details->wo_reminder_sent_on);
                $workorder_reminder_periods_in_sec = 86400*$workorder_reminder_periods;

                if($wo_reminder_sent_on_timestamp > time() + $workorder_reminder_periods_in_sec) {
                    $show_counter = 'no';
                 } else {
                    $show_counter = 'yes';
                 }
            }
            else
            {
                $show_counter = 'no';
            }
            if(trim($work_orders_details->wo_reminder_sent_on) != "")
            {
                $wo_reminder_sent_on_formatted = date_create(trim($work_orders_details->wo_reminder_sent_on));
                // date_add($wo_reminder_sent_on_formatted,date_interval_create_from_date_string("$workorder_reminder_periods days"));
                $work_orders_details->wo_reminder_sent_on_formatted = date_format($wo_reminder_sent_on_formatted,"Y-m-d H:i:s");
            }
            else
            {
                $work_orders_details->wo_reminder_sent_on_formatted = "";
            }
            $this->data['all_supervisors'] = [];
            if(Auth::user()->user_type == "sp_admin")
            {
                $this->data['all_supervisors'] = User::select('users.id','name')
                    ->join('user_assets_mapping', 'user_assets_mapping.user_id', '=', 'users.id')
                    ->where('users.user_type','supervisor')
                    ->where('service_provider', Auth::user()->service_provider)
                    ->whereRaw("find_in_set($work_orders_details->building_id, users.building_ids)")
                    ->where('user_assets_mapping.contract_id', $work_orders_details->contract_id)
                    ->where('user_assets_mapping.asset_id', $work_orders_details->asset_category_id)
                    ->where(['users.is_deleted' => 'no', 'users.status' => 1])
                    ->groupBy('users.id')
                    ->get();
            }

            //For showing the chcklist popup
            $checklists = Checklists::find($work_orders_details->checklist_id);
            if(isset($checklists))
            {
                $checklists['tasks'] = ChecklistTasks::with('checklist_subtasks')->where('checklist_id',$checklists->list_id)->where('is_deleted', 'no')->get()->toArray();
            }
            $this->data['checklists'] = $checklists;

            $this->data['user_type'] = Auth::user()->user_type;
            $this->data['reopen_wo_count'] = DB::table('reopen_work_order_details')->where('wo_id',$work_orders_details->id)->count();

            // Get logged In user details
            $loggedInUser = Auth::user();

            $this->data['projectDetails'] = $projectDetails = $loggedInUser->projectDetails;
            if ($projectDetails && isset($projectDetails->projectSettings)) {
                $this->data['projectSettings'] = $projectDetails->projectSettings;
                $this->data['warrantyStatus'] = Helper::getWarrantyStatus($this->data['projectSettings'], $this->data['maintanance_request']);
            } else {
                $this->data['projectSettings'] = null; // handle the absence of projectSettings
                $this->data['warrantyStatus'] = null;
            }

            $workOrderItems = WorkOrderItem::where('work_order_id', $id)->get();
            $request= new Request;
            $response = $this->itemElementService->listElements(BaseListElementsDTO::fromRequest($request));

            $workOrderRequestedItems = WorkOrderItemRequest::with('requestedItems.contractUsableItem', 'user')
                ->where('work_order_id', operator: $id)
                ->first();

            // Check if requestedItems is set
            if (isset($workOrderRequestedItems->requestedItems)) {
                // Access the relationship data
                foreach ($workOrderRequestedItems->requestedItems as $key => $requestedItem) {
                    // Get the $contractUsableItem object
                    if ($requestedItem->contractUsableItem !== null) {
                        $contractUsableItem = $requestedItem->contractUsableItem->getItem();
                    } else {
                        $contractUsableItem = null; // or some default value
                    }

                    // Fetch the item data
                    $itemData = $requestedItem->getItem();

                    // Check if $itemData->items is not null and is an instance of a collection
                    $itemsArray = $itemData->items ? $itemData->items->toArray() : [];

                    // Check if there are any items
                    if (!empty($itemsArray)) {
                        // Get the first item from the array
                        $firstItem = $itemsArray[0];

                        // Get the warehouse_id and warehouse name
                        $warehouse_id = $firstItem['warehouse_id'] ?? null;
                        $getWarehouseName = $firstItem['warehouse']['name'] ?? null;
                    } else {
                        $getWarehouseName = $requestedItem->getWarehouse()->name ?? '';
                        $warehouse_id = $requestedItem->getWarehouse()->id ?? 0;
                    }

                    // Assign the name property to the requestedItem
                    $requestedItem->name = isset($contractUsableItem->name) ? $contractUsableItem->name : '';
                    $requestedItem->category_name = isset($contractUsableItem->category->name) ? $contractUsableItem->category->name : '';
                    $requestedItem->sale_price_formatted = isset($contractUsableItem->sale_price_formatted) ? $contractUsableItem->sale_price_formatted : '';
                    $requestedItem->sale_price_vat_formatted = isset($contractUsableItem->sale_price_vat_formatted) ? $contractUsableItem->sale_price_vat_formatted : '';
                    $requestedItem->country_of_origin_formatted = isset($contractUsableItem->country_of_origin_formatted) ? $contractUsableItem->country_of_origin_formatted : '';
                    $requestedItem->status = $workOrderRequestedItems->status;
                    $requestedItem->approved_by = $workOrderRequestedItems->user->name ?? '';
                    $requestedItem->warehouse_name = $getWarehouseName;
                    $requestedItem->warehouse_id = $warehouse_id;
                    $requestedItem->missing_quantity = $workOrderRequestedItems->status == 'partially_given' ? $requestedItem->quantity_accepted : $requestedItem->missing_quantity;
                }
            }

            $items = $workOrderItems;
            $requestedItems = isset($workOrderRequestedItems->requestedItems) ? $workOrderRequestedItems->requestedItems : [];
            // usable items is just an inbetween we need to call the items method for each usable item
            $selectableItems = $contract->usableItems->map(function (ContractUsableItem $item) {
                return $item->getItem();
            });

            $dataProvider = new FormDataProvider([
                'items' => $items,
                'selectableItems' => $selectableItems,
            ]);

            if($contract->warehouse_owner == 'no_inventory')
            {
                $company_id = null;
            }
            else
            {
                $warehouse_owner = $contract->warehouse_owner ?? 'admin';

                /** @var App\Models\Contracts $contract */

                if ($warehouse_owner == 'admin') {
                    $company_id = $contract->projectsDetails()->first()->projectCompany->company_id ?? null;
                } else {
                    $company_id = $contract->serviceProvider->serviceProviderAdmin->userCompany->company_id ?? null;
                }
            }

            $allWarehousesList = WorkorderHelper::getAllWarehousesByCompanyId($company_id);

            $allWarehousesList = WarehouseData::collection($allWarehousesList);

            $allWarehousesList = $allWarehousesList->filter(function (WarehouseData $warehouse) use ($contract) {
                if (trim($warehouse->name) === 'Main Warehouse'){
                    return true;
                }

                if (trim($warehouse->name) === '[Warehouse] '.trim($contract->contract_number)){
                    return true;
                }

                if (trim($warehouse->name) === trim($contract->contract_number)){
                    return true;
                }

                return false;
            })->all();

            $allItems = WorkorderHelper::getFilteredCollection($selectableItems);

            $isItemRequest = WorkorderHelper::checkItemRequestsByWorker($id);
            $checkItemRequestsForPo = WorkorderHelper::checkItemRequestsForPo($id);
            $requestedItemsBySp = WorkorderHelper::ItemsRequestedBySp($id);
            // Get all workers with start and end timings and service provider name for the work order
            $this->data['workersDetails'] = WorkorderHelper::getWorkersDetailsForWorkOrder($work_orders_details->id);
            app()->bind(TableConfigInterface::class, WorkOrderItemFormTableConfig::class);

            $this->data['totalWorkersofPendingWork'] = $this->data['workersDetails']->filter(function ($item) {
                return empty($item['start_time']) && empty($item['end_time']);
            })->count();
           $checkItemRequestsByWorkerApprovedOrRejected = WorkorderHelper::checkItemRequestsByWorkerApprovedOrRejected($work_orders_details->id);

           $itemsData = [];
           $aproovedBy = null;
           if($checkItemRequestsByWorkerApprovedOrRejected == 'worker') {
            $itemsData = $requestedItems;
            $aproovedBy =WorkOrderItemRequest::with('aproovedBy')->where('work_order_id', $work_orders_details->id)->first()->aproovedBy->name ??  null;
           } elseif($checkItemRequestsByWorkerApprovedOrRejected == 'serviceprovider') {
            $aproovedBy =ServiceProviderMissingItemRequest::with('aproovedBy')->where('work_order_id', $work_orders_details->id)->first()->aproovedBy->name  ??  null;
            $itemsData = $requestedItemsBySp->requestedItems ?? [];
           }
           $usedItems = [];
           $statusCheck = ['partially_given', 'fully_given', 'accepted'];
            if (isset($workOrderRequestedItems) && in_array($workOrderRequestedItems->status, $statusCheck)) {
                $usedItems = $workOrderRequestedItems->requestedItems;
            } elseif (isset($requestedItemsBySp->status) && in_array($requestedItemsBySp->status, $statusCheck)) {
                $usedItems = $requestedItemsBySp->requestedItems;
            }

            $criteria = $this->getSmartAssignCriteriaDescriptionDetailsByValues('work_order_id', $work_orders_details->id);

            if($work_orders_details->work_order_type == "reactive"){
                $worker = isset($work_orders_details->worker_id) ? $this->getUserInformationsById($work_orders_details->worker_id) : null;
            }

            else{
                if($work_orders_details->assign_type == AssignType::Normal->value){
                    $worker = isset($work_orders_details->worker_id) ? $this->getUserInformationsById($work_orders_details->worker_id) : null;
                }

                else{
                    if($work_orders_details->worker_id == 0){
                        $worker = isset($work_orders_details->worker_id) ? $this->getUserInformationsById($work_orders_details->permanent_worker) : null;
                    }

                    else{
                        $worker = isset($work_orders_details->worker_id) ? $this->getUserInformationsById($work_orders_details->worker_id) : null;
                    }
                }
            }

            $assignType = $work_orders_details->assign_type;
            $action = null;

            if($work_orders_details->work_order_type == "preventive"){
                if($assignType == AssignType::Smart->value){
                    $currentDate = $this->getCurrentDate();
                    $currentDateNewFormat = $this->changeDateFormat('Y-m-d', $currentDate);

                    if($work_orders_details->status == WorkOrderStatus::InProgress->value){
                        if($work_orders_details->start_date > $currentDateNewFormat){
                            $action = WorkOrderStatus::Scheduled->value;
                        }
                    }
                }
            }
 
            //dd($action);
            return view($this->view_path.'.show',[
                'warehouse_owner' => $contract->warehouse_owner ?? 'admin',
                'checkItemRequestsByWorkerApprovedOrRejected' => $checkItemRequestsByWorkerApprovedOrRejected,
                'itemsData' => $itemsData,
                'usedItems' => $usedItems,
                'dataProvider' => $dataProvider,
                'isItemRequest' => $isItemRequest,
                'workOrderItemRequest'=> WorkOrderItemRequest::with('aproovedBy')->where('work_order_id', $work_orders_details->id)->first(),
                'workOrderItemRequestBySp'=> ServiceProviderMissingItemRequest::where('work_order_id', $work_orders_details->id)->orderBy('id', 'desc')->first(),
                'allWarehousesList' => $allWarehousesList,
                'itemRequestApprovedBy' => $aproovedBy,
                'allItems' => $allItems,
                'checkItemRequestsForPo'=>$checkItemRequestsForPo,
                'requestedItemsBySp'=>$requestedItemsBySp->requestedItems ?? [],
                'workOrderRequestedItems'=> $requestedItems,
                'performanceIndicatorData' => $performanceIndicatorData,
                'show_counter'=> $show_counter,
                'workorder_reminder_periods'=>$workorder_reminder_periods,
                'work_orders_details'=> $work_orders_details,
                'timeline'=> $timeline,
                'chats'=> $chats,
                'workers'=> $workers,
                'images' => $images,
                'supervisors' => $supervisors,
                'worker_comment' => $worker_comment,
                'workorder_reopen_periods' => $workorder_reopen_periods,
                'status' => $status,
                'data'=>$this->data,
                'criteria' => $criteria,
                'worker' => $worker ?? null,
                "action" => $action ?? null
            ]);
        }
        return \Redirect::to(url()->previous());
        return abort(404);
    }

    /**
     * GET workorder/pm_work-order-list
     *
     * Return view with workorders list
     * Return a view with list of preventive workorders. Preventive workorder are repeative workorders and here it will display the group name and opther details of the workorders.
     *
     * @authenticated
     * @group Workorders
     *
     * @responseField data array
     * @response 200 {"data": []}
     */
    public function PMWorkOrderList(Request $request)
    {
        // @flip@ if logged in user has [no_view] privileges redirect to dashboard
        $res_privileges=Helper::checkLoggedinUserPrivileges('no_view','workorder',false);
        if(!$res_privileges['success']){
            return $res_privileges['redirect_url'];
        }
        $this->data['pageTitle']='Work Order List';
        $user_id = auth()->user()->project_user_id;
        $search = array(
            'passfail' => $request->passfail,
            'type' => $request->type,
            'status'=>$request->status,
            'buildings'=>$request->buildings,
            'preventive_title'=>$request->preventive_title,
            'dateRange'=>array('startd'=>@$request->dateRange[0],'endd'=>@$request->dateRange[1])
        );
        if(!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee')) //If user_type is building_manager or building_manager_employee
        {
            $user_id = auth()->user()->id;
            $this->data['total_count'] = count(WorkOrders::get_bm_work_orders_preventive_count($user_id));
            // Retrieve properties and asset categories
            $this->data['properties'] = WorkorderHelper::getFilteredProperties(Auth::user(), Auth::user()->id);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin')) //If user_type is sp_admin
        {
            $this->data['total_count'] = WorkOrders::get_pm_preventive_sp_work_orders_count($user_id);
            // Retrieve properties and asset categories
            $this->data['properties'] = WorkorderHelper::getFilteredProperties(Auth::user(), Auth::user()->id);
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor')) //If user_type is supervisor
        {
            $this->data['total_count'] = WorkOrders::get_pm_preventive_supervisor_work_orders_count($user_id);
            // Retrieve properties and asset categories
            $this->data['properties'] = WorkorderHelper::getFilteredProperties(Auth::user(), Auth::user()->id);
        }
        else
        {
            $service_provider_id = Crypt::decryptString($request->route('id'));
            $this->data['service_provider_name'] = ServiceProvider::where('id', $service_provider_id)->value('name');
            $this->data['service_provider_id'] = $service_provider_id;
            $this->data['total_count'] = WorkOrders::get_pm_preventive_work_orders_count($service_provider_id);

            // Retrieve properties and asset categories
            $this->data['properties'] = WorkorderHelper::getFilteredProperties(Auth::user(), Auth::user()->id, $service_provider_id);
        }
        if(request()->ajax()) //If ajax is called
        {
            if(!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee')) //If user_type is building_manager or building_manager_employee
            {
                $work_orders = WorkOrders::get_bm_manage_pm_work_order_preventive($user_id, $search);
            }
            elseif(!empty(Auth::user()->user_type == 'sp_admin')) //If user_type is sp_admin
            {
                $work_orders = WorkOrders::get_sp_manage_pm_work_order_preventive($user_id, $search);
            }
            elseif(!empty(Auth::user()->user_type == 'supervisor')) //If user_type is supervisor
            {
                $work_orders = WorkOrders::get_supervisor_manage_pm_work_order_preventive($user_id, $search);
            } else {
                $service_provider_id = Crypt::decryptString($request->route('id'));
                $work_orders = WorkOrders::get_pm_preventive_admin_work_orders($service_provider_id, $search);
            }
            return view($this->view_path.'.pm-list-ajax')->with(['work_orders' => $work_orders, 'data'=>$this->data]);
        }
        return view($this->view_path.'.pm-list', ['data'=>$this->data]);
    }

    /**
     * GET workorder/sp_preventive_details/{id}
     *
     * Return view with preventive workorder details
     * Return a view with details of preventive workorder. Preventive workorder are repeative workorders and here it will display the details of the preventive workorders.
     *
     * @authenticated
     * @group Workorders
     *
     * @responseField work_orders_details array
     * @response 200 {"work_orders_details": []}
     */
    function sp_preventive_details($id)
    {
        // @flip@ if logged in user doesn't has  [view] privileges redirect to dashboard
       $res_privileges=Helper::checkLoggedinUserPrivileges('view','workorder');
       if(!$res_privileges['success']){
           return $res_privileges['redirect_url'];
       }

        $this->data['pageTitle']='Work Order Details';
        $id = Crypt::decryptString($id);

        $first_service_provider = WorkOrders::get_sp_admin_id(Auth::user()->service_provider);
        $sp_id = isset($first_service_provider)?$first_service_provider:Auth::user()->id;

        $work_orders_details = WorkOrders::get_work_order_details_pm($id);

        if(isset($work_orders_details) && !empty($work_orders_details))
        {
            $actual_start_date = $work_orders_details->start_date;
            $work_order_id = WorkOrders::where('unique_id', $work_orders_details->unique_id)->where('worker_id', 0);
            if(Auth::user()->user_type == "sp_admin")
            {
                $work_order_id = $work_order_id->whereIn('service_provider_id', $sp_id);
            }
            $work_order_id = $work_order_id->orderBy('id', 'desc')->value('id');
            if(!isset($work_order_id))
            {
                $work_order_id = $id;
            }
            //dd($work_order_id);
            $work_orders_details = WorkOrders::get_work_order_details_pm($work_order_id);
            $work_orders_details->actual_start_date = $actual_start_date;
            //dd($work_orders_details);
        }
        else
        {
            return redirect()->route('workorder.workorders.list.pm');
        }

        if($work_orders_details->unit_id !=0){
            if(DB::table('room_types_floors')-> where('id',$work_orders_details->unit_id )
            ->value('deleted_at')!= ''){
                $r_del =  __('general_sentence.modal.deleted');
            }
            else{
                $r_del = '';
            }

            $work_orders_details->floor = DB::table('room_types_floors')-> where('id',$work_orders_details->unit_id )
            ->value('floor');
                $work_orders_details->room = DB::table('room_types_floors')->where('id',$work_orders_details->unit_id )
            ->value('room')
            .$r_del ;
        }

        if(!empty($work_orders_details)) //If has work_orders_details
        {
            $work_orders_details->frequency = DB::table('frequencies_master')->where('id', $work_orders_details->frequency_id)->first();

            $next_commence = WorkOrders::where([['unique_id',
            $work_orders_details->unique_id],['start_date','>' ,date('Y-m-d') ]] );
            if(Auth::user()->user_type == "sp_admin")
            {
                $next_commence = $next_commence->whereIn('service_provider_id', $sp_id);
            }
            $work_orders_details->next_commence = $next_commence->first();

            $last_work_order = WorkOrders::where('unique_id', $work_orders_details->unique_id);
            if(Auth::user()->user_type == "sp_admin")
            {
                $last_work_order = $last_work_order->whereIn('service_provider_id', $sp_id);
            }

            $work_orders_details->last_work_order = $last_work_order->orderBy('start_date','desc')->first();

            $total_count = WorkOrders::where('unique_id', $work_orders_details->unique_id);
            if(Auth::user()->user_type == "sp_admin")
            {
                $total_count = $total_count->whereIn('service_provider_id', $sp_id);
            }
            $work_orders_details->total_count = $total_count->count();

            $current_count = WorkOrders::where([['unique_id', $work_orders_details->unique_id],['end_date','<=',date('Y-m-d')]]);
            if(Auth::user()->user_type == "sp_admin")
            {
                $current_count = $current_count->whereIn('service_provider_id', $sp_id);
            }
            $work_orders_details->current_count = $current_count->count();

            $work_orders_details->workers = WorkOrders::get_workers($work_orders_details->contract_id, $work_orders_details->asset_category_id, $work_orders_details->building_id);

            $wtfs = DB::table('work_time_frame')
                    ->select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
                    ->where('user_id', Auth()->user()->project_user_id)
                    ->first();

            if($work_orders_details->wtf_start_time == ''){


                if(!isset($wtfs)) //If work order time frame has added
                {
                $wtfs->start_time= "00:00:00";
                }
                    else {
                    $wtfs->start_time= $wtfs->start_time;
                }
            }
            else{
                $wtfs->start_time = $work_orders_details->wtf_start_time;

            }

            if(isset($work_orders_details->next_commence))
            {
                $work_orders_details->sp_approove = $work_orders_details->next_commence->sp_approove;
            }
            if(!empty($work_orders_details->next_commence) && isset($work_orders_details->next_commence)){
                $work_orders_details->next_commence->starting_time ='';
                if($work_orders_details->next_commence->wtf_start_time == ''){


                    if(!isset($wtfs)) //If work order time frame has added
                    {
                    $wtfs->start_time= "00:00:00";
                    }
                        else {
                        $wtfs->start_time= $wtfs->start_time;
                    }

                 $work_orders_details->next_commence->starting_time =$work_orders_details->next_commence->start_date.$wtfs->start_time ;

                }
                else{
                    $wtfs->start_time = $work_orders_details->wtf_start_time;
                    $work_orders_details->next_commence->starting_time =$work_orders_details->next_commence->start_date.$work_orders_details->next_commence->wtf_start_time ;

                }
            }

            $history = WorkOrders:: where([['unique_id', $work_orders_details->unique_id],['start_date','<=',date('Y-m-d')], ['work_orders.is_deleted', '=', "no"], ]);
            if(Auth::user()->user_type == "sp_admin")
            {
                $history = $history->whereIn('service_provider_id', $sp_id);
            }
            $history = $history->orderBy('start_date','desc')->get();
            if(!empty($history))
            {
                foreach($history as $key => $row)
                {

                    if(isset($wtfs)) //If there is work time frame added
                    {
                        $preventive_start_time = WorkorderHelper::getWorkorderstarttime($wtfs->start_time,$row->preventive_start_time);

                        $history[$key]->start_date = date('d-M-Y h:i A', strtotime($row->start_date.' '.$preventive_start_time));
                    }
                    else
                    {
                        $history[$key]->start_date = date('d-M-Y h:i A', strtotime($row->start_date." 00:00:00"));
                    }
                    if(!isset($row->job_completion_date))
                    {
                        $history[$key]->job_completion_date = $row->job_completion_date;
                    }
                    else
                    {
                        $history[$key]->job_completion_date = date('d-M-Y h:i A', strtotime($row->job_completion_date));
                    }
                    $history[$key]->url = route('workorder.show', Crypt::encryptString($row->id));
                }
            }
            //dd($history);
            $work_orders_details->history = $history;
            $work_orders_details->pm_end_date = WorkOrders::where('unique_id', $work_orders_details->unique_id)->orderBy('start_date','desc')->value('pm_end_date');

            $work_orders_details->pm_start_date = WorkOrders::where('unique_id', $work_orders_details->unique_id)->orderBy('start_date','asc')->value('start_date');

            foreach($work_orders_details->history as $wohistory){
                $wohistory->assigned_worker_name = '';
                if(isset($wohistory->worker_id)){ //If has worker_id
                    $wohistory->assigned_worker_name = DB::table('users')->where('id',$wohistory->worker_id)->value('name');
                }
                if(!isset($wohistory->pass_fail) || $wohistory->pass_fail ==''){ //If has pass_fail or pass_fail is empty
                    $wohistory->pass_fail = 'pending';
                }
                $wohistory->pass_failcolor = '';
                if($wohistory->pass_fail == 'pass') //If pass_fail is pass
                {
                  $wohistory->pass_failcolor = 'color-white bg-success';
                  $wohistory->pass_fail = __('work_order.bread_crumbs.pass');
                }
                elseif($wohistory->pass_fail == 'fail') //If pass_fail is fail
                {
                  $wohistory->pass_failcolor = 'color-white bg-danger';
                  $wohistory->pass_fail = __('work_order.bread_crumbs.fail');
                }
                else
                {
                  $wohistory->pass_failcolor = 'color-white bg-light';
                  $wohistory->pass_fail = __('work_order.bread_crumbs.pending');
                }
                if($wohistory->status == 5){
                    $wohistory->work_order_id =$wohistory->work_order_id.__('general_sentence.modal.deleted');
                }
            }

            $contract_id = WorkOrders::select('contract_id')->where('unique_id', $work_orders_details->unique_id);
            if(Auth::user()->user_type == "sp_admin")
            {
                $contract_id = $contract_id->whereIn('service_provider_id', $sp_id);
            }
            $contract_id = $contract_id->orderBy('id', 'desc')->value('contract_id');
            $work_orders_details->contract_no = DB::table('contracts')->where('id',$contract_id)->value('contract_number');

            $latestWorker = WorkOrders::where([['unique_id', $work_orders_details->unique_id],['worker_started_at', '=', null]])->pluck('permanent_worker')->first();
            if($latestWorker)
            {
                $work_orders_details->permanent_worker_id = $latestWorker;
            }
            else
            {
                $latestWorker = WorkOrders::where('unique_id', $work_orders_details->unique_id)->whereNotNull('worker_started_at')->orderBy('worker_started_at', 'desc')->pluck('permanent_worker')->first();
                if($latestWorker)
                {
                    $work_orders_details->permanent_worker_id = $latestWorker;
                }
                else
                {
                    $work_orders_details->permanent_worker_id = $work_orders_details->worker_id;
                }
            }
            $work_orders_details->worker_assigned = DB::table('users')->where('id',$work_orders_details->permanent_worker_id)->first();
            $user_id = auth()->user()->id;
            if($work_orders_details->checklist_id != 0)
            {
                $work_orders_details->checklists = $this->view_checklist_details($work_orders_details->checklist_id);
            }
            //dd($work_orders_details);
            $user = $this->getAuthenticatedUser();
            $projectSettings = $this->grtProjectSettingsInformationByValues('project_id', $user->project_id);
            $contractInformation = $this->getContractDetailsByValues('id', $contract_id);
            $checkSmartAssign = (isset($projectSettings) && $projectSettings->use_smart_assigning) && (isset($contractInformation) && $contractInformation->use_smart_assigning) ? true : false;
            $smartAssignServices = $this->getSmartAssigningContractInformationByValues('contract_id', $contract_id);
            $explodeSmartAssignServices = isset($smartAssignServices) ? $this->explodeDataFromField($smartAssignServices->service_id) : [];
            $checkServices = isset($explodeSmartAssignServices) && count($explodeSmartAssignServices) > 0 ? in_array($work_orders_details->asset_category_id, $explodeSmartAssignServices): false;
            $currentDate = $this->getCurrentDateWithCarbon();
            return view($this->view_path.'.sp-pm-details',['work_orders_details'=> $work_orders_details, 'checkServices' => $checkServices, 'checkSmartAssign' => $checkSmartAssign, 'currentDate' => $currentDate]);
        }
        return abort(404);
    }

    /**
     * GET workorder/work-order-closed-list-count-ajax/{id}
     *
     * Return closed workorders count
     * Return count of closed workorders. The Filters also can be applied. For the user types such as Super admin, Osool admin, POA and POE the closed work orders will listed according to the service providers selected.
     *
     * @authenticated
     * @group Workorders
     *
     * @responseField count
     * @response 200
     */
    public function list_ajax_closed_count(Request $request)
    {
       $this->data['pageTitle']='All Workorder List';
       $this->data['service_provider_id'] = Crypt::decryptString($request->route('id'));
       $logedin_user=auth()->user();
       $user_id = $logedin_user->id;
       $search = array(
                'passfail' => $request->passfail,
                'type' => $request->type,
                'status'=> 4,
                'dateRange'=>array('startd'=>@$request->dateRange[0],'endd'=>@$request->dateRange[1])
            );
        $total_row_count = WorkOrders::get_work_orders_count($user_id, NULL, $search);
        return $total_row_count;
    }

    /**
     * GET workorder/work-order-closed-list-count-ajax/{id}
     *
     * Return workorders count
     * Return count of workorders. The Filters also can be applied. For the user types such as Super admin, Osool admin, POA and POE the work orders will listed according to the service providers selected.
     *
     * @authenticated
     * @group Workorders
     *
     * @responseField count
     * @response 200
     */
    public function list_ajax_count(Request $request)
    {
       $this->data['pageTitle']='All Workorder List';
       $this->data['service_provider_id'] = Crypt::decryptString($request->route('id'));
       $logedin_user=auth()->user();
       $user_id = $logedin_user->id;
       $search = array(
                'passfail' => $request->passfail,
                'type' => $request->type,
                'status'=>$request->status,
                'dateRange'=>array('startd'=>@$request->dateRange[0],'endd'=>@$request->dateRange[1])
            );
        $total_row_count = WorkOrders::get_work_orders_count($user_id, Crypt::decryptString($request->route('id')), $search);
        return $total_row_count;
    }

    /**
     * GET workorder/assign_pm_worker
     *
     * Assigning the permanent worker to the repeative preventive workorder and he will be responsible for completing the workorders.
     *
     * @authenticated
     * @group Workorders
     *
     * @response 200
     */
    function assign_pm_worker(Request $request)
    {
        if(isset($request->sp_approval) && $request->sp_approval==0){ //If request sp_approval and sp_approval is 0
            $sp_approve = 0;
        }
        else{
            $sp_approve = 1;
        }
        $user_id = auth()->user()->id;
        $wo = DB::table('work_orders')->select('work_orders.*')->where('unique_id','=',$request->unique_id)->first();

        $wtfs = DB::table('work_time_frame')
                ->select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
                ->where('user_id', $wo->project_user_id)
                ->first();
        $start_time = " 00:00:00";

        if($wo->wtf_start_time == ''){
            if(!isset($wtfs)) //If work order time frame has added
            {
            $wtfs->start_time= "00:00:00";
            }
            else {
                $start_time = ' '.$wtfs->start_time;
            }
        }
        else{
            // $wtfs->start_time = ;
            $start_time = ' '.$wo->wtf_start_time;
        }
       // $worker_id = $request->worker_id;
       $newDate = $wo->start_date." ".$start_time;
       $worker_id = $this->getFinalWorkerId($request->contract_id, $request->property_id, $request->asset_id, $request->decision ?? null, $request->worker_id ?? null, $newDate, $wo->id, $request->unique_id, $request->permanent_worker_id ?? null);

        //dd($worker_id);
        if(isset($worker_id) && $worker_id != 0)
        {
            $start_job = 2;
            $workorder_journey = 'job_execution';
            $pass_fail = "pending";
            $job_started_at = DB::raw("CONCAT(start_date,'$start_time')");
        }
        else
        {
            $start_job = 1;
            $workorder_journey = 'submitted';
            $worker_id = 0;
            $pass_fail = "";
            $job_started_at = NULL;
        }
        $data = [];
        $data['worker_id'] = $worker_id;
        $data['status'] = $start_job;
        $data['workorder_journey'] = $workorder_journey;
        $data['sp_approove'] = $sp_approve;
        $data['pass_fail'] = $pass_fail;
        $data['permanent_worker'] = $worker_id;
        $data['assign_type'] = isset($request->decision) && $request->decision == WorkerAssigningDescision::SmartAssign->value ? AssignType::Smart->value : AssignType::Normal->value;
        //dd($worker_id);
        //DB::enableQueryLog();
        $wo_orders = DB::table('work_orders')->select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->where('unique_id','=',$request->unique_id)->get();
        if(isset($wo_orders) && !Empty($wo_orders)){ //If has wo_orders
            $data['job_started_at'] =  $job_started_at;
            //dd($data);
            $supervisor_id = NULL;
            if(Auth()->user()->user_type == "sp_admin")
            {
                $sp_admin_id = Auth()->user()->id;
                $service_provider_id = Auth()->user()->service_provider;
            }
            elseif(Auth()->user()->user_type == "supervisor")
            {
                $sp_admin_id = Auth()->user()->sp_admin_id;
                $supervisor_id = Auth()->user()->id;
                $service_provider_id = Auth()->user()->service_provider;
            }

            DB::table('work_orders')->where([['unique_id', $request->unique_id],['worker_started_at', '=', null]])->update($data);


            $data['sp_admin_id'] = $sp_admin_id;
            $data['service_provider_id'] = $service_provider_id;
            $data['supervisor_id'] = $supervisor_id;
            $data['unique_id'] = $request->unique_id;
            $data['project_user_id'] = $wo->project_user_id;
            $data['contract_id'] = $wo->contract_id;
            unset($data['job_started_at']);

            if(isset( $data['worker_id']) && $data['worker_id'] <> 0){
                PreventiveWoAction::create($data);
            }
            $worker_name = DB::table('users')->where('id', $request->worker_id)->value('name');
            if((isset($worker_id) && $worker_id != 0))
            {
                // @flip1@ due to change input field to bold
                $message = 'Worker <strong>'.$worker_name.'</strong> has been <strong><i>automatically</i></strong> assigned to the work order by <strong>'.Auth::user()->name.'</strong>';
                $message_ar = 'تم تعيين العامل <strong>'.$worker_name.'</strong> <strong><i>تلقائيا</i></strong> لأداء أمر العمل من قبل <strong>'.Auth::user()->name.'</strong>';

                if($data['assign_type'] == AssignType::Smart->value){
                    $workerInformation = $this->getUserInformationsById($worker_id);

                    if(isset($workerInformation)){
                        $message2 = 'Worker <strong>'.$workerInformation->name."</strong> has been assigned to work order using smart assign";
                        $message2_ar = 'تم تعيين العامل <strong>'.$workerInformation->name.'</strong> عبر التعيين الذكي لاداء أمر العمل';
                    }
                }
            }
            elseif($wo_orders[0]->worker_id != 0)
            {
                $message = 'The current worker is not automatically assigned to these preventive work orders anymore, therefore has been removed from this work order.';
                $message_ar = 'الفني الحالي لم يعد معيناً تلقائياً لهذه الأوامر الوقائية, بنائاً على ذلك تمت إزالته من أمر العمل هذا';
            }
            $open_workorders = DB::table('work_orders')->select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->where('unique_id','=',$request->unique_id)->where('worker_started_at','=',Null)->get();

            // To insert two rows each for Notification and timeline declaring two empty arrays

            $notifications = [];
            $timeline = [];
            $timeline2 = [];

            if(!empty($open_workorders))
            {
                foreach($open_workorders as $key => $owo)
                {
                    if((isset($message) && $message != NULL))
                    {
                        $notifications[] = array(
                            'user_id' => $user_id,
                            'message' => $message,
                            'message_ar' => $message_ar,
                            'section_type' => 'work_order',
                            'section_id' => $owo->id,
                            'is_automatic' => 'yes',
                            'created_at' => date('Y-m-d H:i:s'),
                            'is_timeline' => 'no'
                        );
                        $timeline[] = array(
                            'user_id' => $user_id,
                            'message' => $message,
                            'message_ar' => $message_ar,
                            'section_type' => 'work_order',
                            'section_id' => $owo->id,
                            'is_automatic' => 'yes',
                            'created_at' => date('Y-m-d H:i:s'),
                            'is_timeline' => 'yes'

                        );

                        if($data['assign_type'] == AssignType::Smart->value){
                            $timeline2[] = array(
                                'user_id' => $user_id,
                                'message' => $message2,
                                'message_ar' => $message2_ar,
                                'section_type' => 'work_order',
                                'section_id' => $owo->id,
                                'is_automatic' => 'yes',
                                'created_at' => date('Y-m-d H:i:s'),
                                'is_timeline' => 'no'
                            );
                        }
                    }
                    if($owo->td_before_assigning_wo != NULL)
                    {
                        DB::table('work_orders')->where([['id', $owo->id]])->update(['target_date' => $owo->td_before_assigning_wo]);
                    }

                }
                //dd($notifications);
                if((isset($message) && $message != NULL))
                {
                    // Insert for notification
                    DB::table('notifications')->insert($notifications);
                    try {
                        $worker = Helper::userDetail($worker_id);
                        if (!empty($worker)) {
                            $registration_ids[] = $worker->device_token;
                            $notification_type= 'new_wo_assigned_to_worker';
//                            $res = ApiHelper::send_notification_worker_FCM($worker_id, $registration_ids, $message, $wo->property_id, $notification_type, $wo->id);
                            //$res = ApiHelper::send_notification_worker_FCM($worker_id, $registration_ids, $message, $wo->property_id, 'new_wo_assigned_to_worker', $wo->id);
//                            $messgae = array(
//                                "title" => $notification_title,
//                                "body" => $reason,
//                                'section_id' => $wo->id,
//                                'notification_type' => $notification_type
//                            );
//                            $res = ApiHelper::send_notification_worker_FCM($worker_id, $registration_ids, $message, $wo->property_id, 'new_wo_assigned_to_worker', $wo->id,$wo_orders->created_by);
                            // Insert for notification
                            //DB::table('notifications')->insert($notifications);
//                            $notification['title']= ($worker->selected_app_langugage == 'ar') ? $message_ar:$message;
//                            $notification['title']= ($worker->selected_app_langugage == 'ar') ? strip_tags($message_ar):strip_tags($message);
//                            App\Jobs\WOSendNotificationToWorker::dispatch($worker_id,$worker,$notification);
                            $notification_message = 'Worker ' . $worker_name . ' has been automatically assigned to the work order by ' . Auth::user()->name;
                            $notification_message_ar = 'تم تعيين العامل ' . $worker_name . ' تلقائيا لأداء أمر العمل من قبل ' . Auth::user()->name;
                            $notification['title'] = ($worker->selected_app_langugage == 'ar') ? $notification_message_ar : $notification_message;
                            $notification['body'] = $wo->pm_title??null;
                            App\Jobs\WOSendNotificationToWorker::dispatch($worker_id, $worker, $notification);
                        }
                    } catch (\Exception $exception) {
                        Log::error("Send FCM|".$exception->getMessage());
                    }
                    // Insert for timeline
                    DB::table('notifications')->insert($timeline);

                    if(isset($timeline2) && count($timeline2) > 0){
                        DB::table('notifications')->insert($timeline2);
                    }
                }
            }
        }
        return redirect()->route('workorder.sp_preventive_details', Crypt::encryptString($request->work_order_id));
    }

    /**
     * GET workorder/delete-work-order-preventive/{id?}
     *
     * Deleting the preventive workorder and this will delete all the group of workorders in it.
     *
     * @authenticated
     * @group Workorders
     *
     * @response 200
     */
    public function deletePreventiveWorkOrder(Request $request){
        $ipaddress=Helper::getIPAddr();
        $user_id = auth()->user()->id;
        $id = Crypt::decryptString($request->id);
        $data = array(
            'workorder_journey'=>'finished',
            'status'=>'5',
            'last_ip'=>$ipaddress,
            'is_deleted'=>'yes',
            'modified_at'=>date('Y-m-d H:i:s')
        );
        $response = DB::table('work_orders')->where('unique_id','=',$id)->update($data);
        if(!empty($response)) //If has response
        {
          $work_orders = DB::table('work_orders')->select('property_buildings.building_name', 'work_orders.id', 'work_orders.work_order_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.unique_id', $id)->get();
          $work_orders = json_decode(json_encode($work_orders), true);
          if(!empty($work_orders)) //If has work orders
          {
            foreach($work_orders as $wo)
            {
              $work_order_id = $wo['id'];
              DB::table('work_orders')->where('id', $work_order_id)->update(array('is_deleted' => 'yes'));
            //   $message = 'Building Manager deleted Workorder '.$wo['building_name'].' '.$wo['work_order_id'];
            //   $message_ar = 'مدير المبنى قام بحذف أمر العمل '.$wo['building_name'].' '.$wo['work_order_id'];

            // @flip1@ due to change input fields to bold
            $message = 'Building Manager deleted Workorder <strong>'.$wo['building_name'].' '.$wo['work_order_id'].'</strong>';
            $message_ar = 'مدير المبنى قام بحذف أمر العمل <strong>'.$wo['building_name'].' '.$wo['work_order_id'].'</strong>';

            //Insert for notification
              DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'), 'is_timeline' => 'no'));
            //Insert for timeline
              DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'), 'is_timeline' => 'yes'));

            }
          }
          return response()->json(['success'=>__('general_sentence.modal.deleted_successfully'),'']);
        }
    }

    /**
     * GET workorder/delete-work-order-bm/{id?}
     *
     * Deleting the preventive workorder and this will delete all the group of workorders in it.
     *
     * @authenticated
     * @group Workorders
     *
     * @response 200
     */
    public function deletePreventiveWorkOrderBm(Request $request, $id){
        $ipaddress=Helper::getIPAddr();
        $user_id = auth()->user()->id;
        $data = array(
            'workorder_journey'=>'finished',
            'status'=>'5',
            'last_ip'=>$ipaddress,
            'is_deleted'=>'yes',
            'modified_at'=>date('Y-m-d H:i:s')
        );
        $response = DB::table('work_orders')->where('id','=',$request->id)->update($data);
        if(!empty($response)) //If work order is updated
        {
          $wo = DB::table('work_orders')->select('property_buildings.building_name', 'work_orders.id', 'work_orders.work_order_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.id', $request->id)->first();
          if(isset($wo)) //If has workorder with conditions
          {
            // $message = 'Building Manager deleted Workorder '.$wo->building_name.' '.$wo->work_order_id;
            // $message_ar = 'مدير المبنى قام بحذف أمر العمل '.$wo->building_name.' '.$wo->work_order_id;

            // @flip1@ due to input field change to bold
            $message = 'Building Manager <strong>'.auth()->user()->name .'</strong>  deleted the Work order';
            $message_ar = 'مدير المبنى <strong>'.auth()->user()->name .'</strong> قام بحذف أمر العمل ';

            //Insert for Notification
            DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'section_id' => $request->id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline' => 'no'));
            //Insert for timeline
            DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'section_id' => $request->id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline' => 'yes'));

          }
          return response()->json(['success'=>__('general_sentence.modal.deleted_successfully'),'']);
        }
    }

    /**
     * GET workorder/checklist-show/{id}
     *
     * Return view with checklist tasks
     * Return view with checklist tasks using the checklist id selected in the workorder.
     *
     * @authenticated
     * @group Workorders
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function checklistShow($id)
    {
        // dd('gg');
        $this->data['pageTitle']='Work Order| Result Details';
        $data=null;
        if($id){ //If there is id
            $data_set=[];
            $data_query=DB::table('work_orders')
                    ->select('work_orders.*','users.name as raised_by')
                    ->join('users','users.id','=','work_orders.created_by')
                    ->where('work_orders.id',$id)
                    ->where('work_orders.status','=',2)
                    ->first();
            $data_query=json_decode(json_encode($data_query), true);
            if(isset($data_query['checklist_id'])){
                if($data_query['checklist_id'] > 0)  //If there is checklist_id
                {
                    $ben_data = WorkorderHelper::getCheckListTask($data_query['checklist_id'], $data_query['id']);
                    $data_query['checklist_task']=$ben_data;
                }
                $data_query['images'] = Workorders::get_checklist_images($data_query['id']);
                //dd($data_query);
                $data = json_encode($data_query);
            }
            $this->data['data_record'] = json_decode($data);
            return view($this->view_path.'.work-order-checklist', $this->data);
        }
        return abort(404);
    }

    /**
     * GET workorder/workorder/{id?}
     *
     * Return view with work order chat messages
     * Return a view with work order chat messages. This chat will be basically between the building manager and the service providers.
     *
     * @authenticated
     * @group Workorder chat messages
     *
     * @response 200
     */
    public function fetch_unread_message(Request $request)
    {
        $user_id = auth()->user()->id;
        $unreadMsg =[];
        $userTypeExcluded =['building_manager','building_manager_employee','sp_admin','supervisor'];
        $unreadMsg = DB::table('work_order_chat')
                 ->select('work_order_id', 'created_at', DB::raw('count(*) as total'));
        if(in_array(auth()->user()->user_type,$userTypeExcluded)){ //If user type is equal to building_manager or building_manager_employee or sp_admin or supervisor
            $unreadMsg = $unreadMsg->where('receiver_id',$user_id);
        }
        $unreadMsg = $unreadMsg->where('is_read','0')
                 ->groupBy('work_order_id')
                 ->orderBy('created_at', 'DESC')
                 ->get();
        $data = [];
        $workOrderNumber =[];
        $created_at = date('Y-m-d H:i:s');
        foreach($unreadMsg as $key=>$message)
        {
            $created_at = $message->created_at;
            $work_order_id=$message->work_order_id;
            $record = DB::table('work_orders')->select('work_order_id')->where('id', $work_order_id)->first();
            $workOrderNumber[$key]['work_order_id'] = $record->work_order_id;
            $workOrderNumber[$key]['id'] = $work_order_id;
            $workOrderNumber[$key]['created_at'] = $created_at;
        }
        $data['created_at'] = $created_at;
        $data['workOrderNumber'] = $workOrderNumber;
        $data['countUnread'] = count($unreadMsg);
        $data['renderHtml'] = view($this->view_path.'.unreadmessage', $data )->render();
        return json_encode($data);
    }

    /**
     * POST workorder/submit-message
     *
     * Return view with work order chat messages
     * Return a view with work order chat messages. This chat will be basically between the building manager and the service providers. Here, users will submit a message and in return we will return some chats.
     *
     * @authenticated
     * @group Workorder
     *
     * @response 200
     */
    public function submit_message(Request $request)
    {
        // validate request data
        $request->validate([
            'message'=>'required_without:attach_file',
            'attach_file'=>'required_without:message|mimes:png,jpg,jpeg,pdf,doc,docx,xlsx',
        ]);
        // return $request;

        if (!\Storage::exists('work_order_chat_files')) {
            \Storage::makeDirectory('work_order_chat_files');
        }
        if (!\Storage::disk('public')->exists('work_order_chat_files')) {
            \Storage::disk('public')->makeDirectory('work_order_chat_files');
        }
        if(!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee')){ //If user_type is building_manager or building_manager_employee
            $user_type = "bm";
        }elseif(!empty(Auth::user()->user_type == 'sp_admin')  || !empty(Auth::user()->user_type == 'supervisor')){ // If user_type is sp_admin or supervisor
            $user_type = "sp";
        }
        $user_id = auth()->user()->id;
        $message = $request->message;
        $work_order_id = $request->work_order_id;
        $created_by = $request->created_by;
        $data = [];
        $data['user_id'] = $user_id;
        $data['message'] = $message;
        $attachment_data = [];
        //@ attach file upload ,add path to db
        if($file=$request->file('attach_file')){
           // array_push($attachment_data,\Storage::disk('public')->put('work_order_chat_files', $file));
           if(in_array($file->getClientMimeType(),array('image/jpeg','image/jpg','image/png')))
           {
                $imageName = ImagesUploadHelper::compressedImage($file, 'work_order_chat_files');
                array_push($attachment_data,$imageName);
           }
           else
           {
                // for non image files
                array_push($attachment_data,\Storage::disk('public')->put('work_order_chat_files', $file));
                $imageName = $attachment_data[0];
           }
            $filename = explode('/',$imageName);
            $data['attach_file_name']= isset($filename[1]) ? $filename[1] : "";
            $data['attach_file_mime_type']=$file->getClientMimeType();
            //$path = $file->store('work_order_chat_files');

            $data['attach_file'] = $imageName;
        }

        $data['work_order_id'] = $work_order_id;
        $data['user_type'] = $user_type;
        $data['receiver_id'] = $created_by;
        $data['created_at'] = date('Y-m-d H:i:s');
        $lastInsertedID=DB::table('work_order_chat')->insertGetId($data);

        $wo = DB::table('work_orders')->select('property_buildings.building_name', 'work_orders.id', 'work_orders.work_order_id','work_orders.property_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.id', $work_order_id)->first();

        // Insert Into Notifications
        // @if message empty then set default message

        // $message=$message??'File Attached';

        //Insert for Notification
        DB::table('notifications')->insert(array('user_id' => $user_id,'not_receivers_user_id'=>$user_id, 'message' => '', 'message_ar' => '', 'section_type' => 'work_order', 'building_ids'=>$wo->property_id, 'notification_sub_type'=> 'new_chat_message', 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no' ));

        //Insert for Timeline
        DB::table('notifications')->insert(array('user_id' => $user_id,'not_receivers_user_id'=>$user_id, 'message' => '', 'message_ar' => '', 'section_type' => 'work_order', 'building_ids'=>$wo->property_id, 'notification_sub_type'=> 'new_chat_message', 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));


        $chat = DB::table('work_order_chat')->where('id',$lastInsertedID)->first();
        if(auth()->user()->profile_img == "") //If has profile image
        {
            $img_path = "dummy_profile_image.png";
        }
        else
        {
            $img_path = auth()->user()->profile_img;
        }
        // $data['renderHtml'] = view($this->view_path.'.add_message', ['img_path'=> $img_path,'message'=>$message,'created_at'=>date('d M Y h:i A', strtotime($chat->created_at))] )->render();

        // dd($chat);

        $user_chat=WorkOrderChat::where('id',$chat->id)->with('user')->first();
        $user_chat->usert_type = '';
        $user_chat->userttype = '' ;

        $usertype =  '';
        $cu_la = \App::getLocale();
        $user_chat->user_type = DB::table('users')->where('id', $user_chat->user_id)->value('user_type');
        if($user_chat->user_type == "sp_worker"){
            if($cu_la == "ar"){
                $usertype =  'عامل'  ;

            }
            else{
                $usertype = 'Worker';

            }
        }
        else if($user_chat->user_type == "super_admin"){
            //$usertype = 'Super Admin';
            if($cu_la == "ar"){
                $usertype =  'Super Admin'  ;

            }
            else{
                $usertype = 'Super Admin';

            }

        }
        else if($user_chat->user_type == "osool_admin"){
            //$usertype = 'Super Admin';
            if($cu_la == "ar"){
                $usertype =  'Osool Admin'  ;

            }
            else{
                $usertype = 'Osool Admin';

            }
        }
        else if($user_chat->user_type == "admin"){
            if($cu_la == "ar"){
                $usertype =  'مالك المشروع'  ;

            }
            else{
                $usertype = 'Project Owner Admin';

            }

        }
        else if($user_chat->user_type == "admin_employee"){
            if($cu_la == "ar"){
                $usertype =  'نائب مالك المشروع'  ;

            }
            else{
                $usertype = 'Project Owner Employee';

            }
            //$usertype = 'Admin Employee';
        }
        else if($user_chat->user_type == "building_manager"){
            if($cu_la == "ar"){
                $usertype =  'مدير المبنى'  ;

            }
            else{
                $usertype = 'Building Manager Admin';

            }
        }
        else if($user_chat->user_type == "building_manager_employee"){

            if($cu_la == "ar"){
                $usertype =  'نائب مدير المبنى'  ;

            }
            else{
                $usertype = 'Building Manager Employee';

            }


        }
        else if($user_chat->user_type == "sp_admin"){

            if($cu_la == "ar"){
                $usertype =  'مسؤول مقدم الخدمة'  ;

            }
            else{
                $usertype = 'Service Provider Admin';

            }

        }
        else if($user_chat->user_type == "supervisor"){
            if($cu_la == "ar"){
                $usertype =  'مشرف مقدم الخدمة'  ;

            }
            else{
                $usertype = 'Service Provider Supervisor';
            }
        }
        $user_chat->usertype = $usertype;
        if($user_chat->attach_file && in_array($user_chat->attach_file_mime_type,array('image/jpeg','image/jpg','image/png')))
        {
            $user_chat->file_exist_path = (trim(ImagesUploadHelper::displayImage($user_chat->attach_file, 'work_order_chat_files')) != "");
            $user_chat->file_path = ImagesUploadHelper::displayImage($user_chat->attach_file, 'work_order_chat_files');
        }
        elseif($user_chat->attach_file && !in_array($user_chat->attach_file_mime_type,array('image/jpeg','image/jpg','image/png')))
        {
            $user_chat->file_exist_path = file_exists(storage_path('app/public/'.$user_chat->attach_file));
            $user_chat->file_path = url('storage/' . $user_chat->attach_file);
        }
        else
        {
            $user_chat->file_exist_path = '';
            $user_chat->file_path = '';
        }
        $data['chat'] = $user_chat;
        $data['renderHtml'] = view($this->view_path.'.chat_message_template.sender_message_template', ['user_chat'=>$user_chat,'img_path'=> $img_path,'message'=>$message,'created_at'=>date('d M Y h:i A', strtotime($chat->created_at))] )->render();

        // echo json_encode($data);
        return response($data);
    }

    /**
     * GET workorder/check-receiver-message
     *
     * Return work order chat messages
     * Return work order chat messages. This chat will be basically between the building manager and the service providers.
     *
     * @authenticated
     * @group Workorder
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function checkReceiverMessage(Request $request){
        // bm,sp
        // dd($request->all());
                $search_user_type='';
        $user=Auth::user();
        // return $user;
        $lastChatObj=WorkOrderChat::where('id',$request->last_chat_id)->first();
        $search_user_type=($user->user_type=="building_manager")?'sp':'bm';

        $data   = [];
        $data['renderHtml'] = '';

        $new_chats=WorkOrderChat::where('id','>',$lastChatObj->id??'')
                            ->where('user_type',$search_user_type)
                            ->where('is_read',0)
                            ->where('work_order_id',$request->w_id)
                            ->with('user','receiver')->get();
                            if($new_chats){
           foreach ($new_chats as $key => $nc) {
               $nc->update(['is_read'=>1]);
                $cat=new Carbon($nc->created_at);

               $new_chats[$key]->created_at_str=date('d M Y H:i A',strtotime($nc->created_at));
               $new_chats[$key]->file_encrypted_link=asset(url('/work_order_chat_files/'.Crypt::encryptString($nc['id'])));
               if($new_chats[$key]->attach_file && in_array($new_chats[$key]->attach_file_mime_type,array('image/jpeg','image/jpg','image/png')))
               {
                $new_chats[$key]->file_exist_path = (trim(ImagesUploadHelper::displayImage($new_chats[$key]->attach_file, 'work_order_chat_files')) != "");
                $new_chats[$key]->file_path = ImagesUploadHelper::displayImage($new_chats[$key]->attach_file, 'work_order_chat_files');
               }
               elseif($new_chats[$key]->attach_file && !in_array($new_chats[$key]->attach_file_mime_type,array('image/jpeg','image/jpg','image/png')))
               {
                $new_chats[$key]->file_exist_path = file_exists(storage_path('app/public/'.$new_chats[$key]->attach_file));
                $new_chats[$key]->file_path = url('storage/' . $new_chats[$key]->attach_file);
               }
               else
               {
                $new_chats[$key]->file_exist_path = '';
                $new_chats[$key]->file_path = '';
               }
               $search_user = ($user->id == $nc->user_id) ? $nc->receiver : $nc->user;
               if($search_user->profile_img == "") //If has profile image
               {
                   $img_path = asset('/uploads/profile_images/'."dummy_profile_image.png");
               }
               else
               {
                   $img_path = ImagesUploadHelper::displayImage($search_user->profile_img, 'uploads/profile_images');
               }


               $nc->usert_type = '';
               $usertype =  '';
               $nc->usertype = '' ;

               $cu_la = \App::getLocale();
               $nc->user_type = DB::table('users')->where('id', $nc->user_id)->value('user_type');
               if($nc->user_type == "sp_worker"){
                   if($cu_la == "ar"){
                       $usertype =  'عامل'  ;

                   }
                   else{
                       $usertype = 'Worker';

                   }
               }
               else if($nc->user_type == "super_admin"){
                   //$usertype = 'Super Admin';
                   if($cu_la == "ar"){
                       $usertype =  'Super Admin'  ;

                   }
                   else{
                       $usertype = 'Super Admin';

                   }

               }
               else if($nc->user_type == "osool_admin"){
                   //$usertype = 'Super Admin';
                   if($cu_la == "ar"){
                       $usertype =  'Osool Admin'  ;

                   }
                   else{
                       $usertype = 'Osool Admin';

                   }
               }
               else if($nc->user_type == "admin"){
                   if($cu_la == "ar"){
                       $usertype =  'مالك المشروع'  ;

                   }
                   else{
                       $usertype = 'Project Owner Admin';

                   }

               }
               else if($nc->user_type == "admin_employee"){
                   if($cu_la == "ar"){
                       $usertype =  'نائب مالك المشروع'  ;

                   }
                   else{
                       $usertype = 'Project Owner Employee';

                   }
                   //$usertype = 'Admin Employee';
               }
               else if($nc->user_type == "building_manager"){
                   if($cu_la == "ar"){
                       $usertype =  'مدير المبنى'  ;

                   }
                   else{
                       $usertype = 'Building Manager Admin';

                   }
               }
               else if($nc->user_type == "building_manager_employee"){

                   if($cu_la == "ar"){
                       $usertype =  'نائب مدير المبنى'  ;

                   }
                   else{
                       $usertype = 'Building Manager Employee';

                   }


               }
               else if($nc->user_type == "sp_admin"){

                   if($cu_la == "ar"){
                       $usertype =  'مسؤول مقدم الخدمة'  ;

                   }
                   else{
                       $usertype = 'Service Provider Admin';

                   }

               }
               else if($nc->user_type == "supervisor"){
                   if($cu_la == "ar"){
                       $usertype =  'مشرف مقدم الخدمة'  ;

                   }
                   else{
                       $usertype = 'Service Provider Supervisor';
                   }
               }
               $nc->usertype = $usertype ;




               $data['renderHtml'] .= view($this->view_path.'.chat_message_template.receiver_message_template', ['user_chat'=>$nc,'img_path'=> $img_path,'message'=>$nc->message,'created_at'=>date('d M Y h:i A', strtotime($nc->created_at))] )->render();
           }

        }
        // dd($new_chats);
        $data['new_chats']=$new_chats;
        return $data;
    }

    /**
     * POST workorder/submit-warranty-comment
     *
     * Building manager will submit a comment and close this warranty workorder.
     *
     * @authenticated
     * @group workorder
     *
     * @response 200
     */
    public function submit_warranty_comment(Request $request)
    {
        $user_id = auth()->user()->id;
        $warranty_comment = $request->warranty_comment;
        $work_order_id = $request->work_order_id;
        $data = [];
        $data['warranty_closed_bm_id'] = $user_id;
        $data['warranty_comment'] = $warranty_comment;
        $data['status'] = 4;
        $data['workorder_journey'] = "finished";
        $data['job_completion_date'] = date('Y-m-d H:i:s');
        //  Added this code to update service request status update for warranty workorder
        $wo = DB::table('work_orders')
        ->where('id', $work_order_id)
        ->first();
        if($wo->maintanance_request_id != null) {
            MaintenanceRequest::where('id',$wo->maintanance_request_id)->update([
                'status' => 'Completed'
                ]);
        }
        DB::table('work_orders')
                ->where('id', $work_order_id)
                ->update($data);
                 if (isset($data['workorder_journey']) && $data['workorder_journey'] == 'finished') {
               $WOTasksTrait = (new class { use \App\Http\Traits\CRMProjects\WOTasksTrait; });
                $checkTangibleData= $WOTasksTrait->markAsCompleted($work_order_id);
                }
        /**
        * Sending Notifications
        **/
        $wo = DB::table('work_orders')->select('property_buildings.building_name', 'work_orders.id', 'work_orders.work_order_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.id', $work_order_id)->first();
        if(isset($wo))
        { // If has the work order
            $message = 'Warranty workorder '.$wo->building_name.' '.$wo->work_order_id.' has been closed';
            $message_ar = 'تم إقفال أمر العمل (تحت الضمان) '.$wo->building_name.' '.$wo->work_order_id;
            //Insert for notification
            DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));
            //Insert for Timeline
            DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));
        }
        return "success";
    }

    /**
     * POST workorder/re-open-job
     *
     * Building manager can reopen a completed work order with in a specific time set in the configuration.
     *
     * @authenticated
     * @group workorder
     *
     * @response 200
     */
    public function re_open_job(Request $request)
    {
        $user_id = auth()->user()->id;
        $reopen_reason = $request->reopen_reason;
        $work_order_id = $request->work_order_id;
        $data = [];
        $data['reason'] = $reopen_reason;
        $data['status'] = 6;
        $data['workorder_journey'] = "submitted";
        $data['reopen'] = "yes";
        $data['sp_reopen_status'] = 0;
        $data['mark_penalty_candidate'] = 0;
        $data['mark_delay_material'] = 0;

        WorkOrders::where('id', $work_order_id)->update($data);

        
               $WOTasksTrait = (new class { use \App\Http\Traits\CRMProjects\WOTasksTrait; });
                $checkTangibleData= $WOTasksTrait->markAsNotCompleted($work_order_id);
               
        /**
        * Sending Notifications
        **/
        $wo = DB::table('work_orders')->select('property_buildings.building_name', 'work_orders.id', 'work_orders.work_order_id','work_orders.property_id', 'work_orders.worker_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.id', $work_order_id)->first();

        // @flip1@ due to input fields changed || 15-BM reopens WO
        $message = 'Building Manager <strong>'.Auth::user()->name.'</strong> has reopened the work order due to <strong>'.$reopen_reason.'</strong>, pending for Service Provider action';
        $message_ar = 'مدير المبنى <strong>'.Auth::user()->name.'</strong> قام بإعادة فتح أمر العمل بسبب <strong>'.$reopen_reason.'</strong>، بانتظار رد مقدم الخدمة';

        $notification_sub_type = 'bm_has_reopend_wo';
        $additional_param = Auth::user()->name;
        //Insert for notifications
        Notification::insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids'=>$wo->property_id,'notification_sub_type'=>$notification_sub_type,'additional_param'=>$additional_param, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));

        //Insert for Timeline
        Notification::insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids'=>$wo->property_id,'notification_sub_type'=>$notification_sub_type,'additional_param'=>$additional_param, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));

        $reopen_work_order_details_id = ReopenWorkOrderDetail::insertGetId(array('wo_id'=>$work_order_id, 'created_by'=>auth::user()->id, 'created_at' => date('Y-m-d H:i:s')));
        NoChecklistAction::where('work_order_id', $work_order_id)
                ->where('is_reopen_wo', 0)
                ->update(array('is_reopen_wo'=> 1,'reopen_wo_id'=> $reopen_work_order_details_id));
        return "success";
    }

    /**
     * POST workorder/sp-reopen-job
     *
     * Service provider can reopen a completed work order on the request of building manager.
     *
     * @authenticated
     * @group workorder
     *
     * @response 200
     */
    public function sp_reopen_job(Request $request)
    {
        $user_id = auth()->user()->id;
        $reason = $request->reason;
        $reopen_option = $request->reopen_option;
        $worker_id = $request->worker_id;
        $work_order_id = $request->work_order_id;
        $data = [];
        if($reopen_option == 2) // Work order reopened
        {
            $data['status'] = 2;
            $data['workorder_journey'] = "job_execution";
            $data['reason'] = $reason;
            $data['worker_id'] = $worker_id;
            $data['sp_reopen_status'] = 2;
            $data['worker_started_at'] = NULL;
            $data['job_completion_date'] = NULL;
            $data['job_submitted_at'] = NULL;
            $data['assigned_to'] = $worker_id?'sp_worker':$request->assigned_to;

            NoChecklistAction::where('work_order_id', $work_order_id)->where('is_reopen_wo', 0)->delete();
        }
        else
        {
            $data['workorder_journey'] = "submitted";
            $data['reason'] = $reason;
            $data['sp_reopen_status'] = 1;
        }

        WorkOrders::where('id', $work_order_id)->update($data);

        /**
        * Sending Notifications
        **/
        $wo = DB::table('work_orders')->select('property_buildings.building_name', 'work_orders.id','work_orders.work_order_id','work_orders.work_order_type','work_orders.property_id', 'work_orders.worker_id', 'work_orders.old_worker_id','work_orders.is_handle_by_team_leader','work_orders.team_leader_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.id', $work_order_id)->first();
        if(isset($request->assigned_to))
        {
            $message = 'The service provider <strong>'.Auth::user()->name.'</strong> restarted the work on behalf of the worker';
            $message_ar = 'قام مقدم الخدمة <strong>'.Auth::user()->name.'</strong> بإعادة العمل على أمر العمل بالنيابة عن العامل';
             //Insert for notification
             Notification::insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order','building_ids' => $wo->property_id, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));

            //Insert for Timeline
            Notification::insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order','building_ids' => $wo->property_id, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));

        }
        else
        {
            if(isset($wo)) //If has workorder
            {
                if($reopen_option == 2) //If workorder is reopened
                {
                    $wr = User::select('users.name')->where('users.id', $request->worker_id)->first();
                    // $message = 'Service Provider agrees on re-opening workorder '.$wo->building_name.' '.$wo->work_order_id.' and has assigned '.$wr->name;
                    // $message_ar = 'مقدم الخدمة موافق على إعادة فتح أمر العمل '.$wo->building_name.' '.$wo->work_order_id.' وقام بتعيين العامل '.$wr->name;

                    // @flip1@ change message and input field bold || 18-SP accepts reopening WO, sent to worker
                    $message = 'Service Provider <strong>'.Auth::user()->name.'</strong> agrees on reopening the work order, and has assigned the worker <strong>'.$wr->name.'</strong>';
                    $message_ar = 'مقدم الخدمة <strong>'.Auth::user()->name.'</strong> وافق على إعادة فتح أمر العمل، وقام بتعيين العامل <strong>'.$wr->name.'</strong>';
                    $notification_sub_type = 'sp_aggres_on_reopend_wo';

                    $WorkerId = $wo->is_handle_by_team_leader == 1 ? $wo->team_leader_id : $request->worker_id;

                    $user_details = Helper::userDetail($WorkerId);
                    if(!empty($user_details))
                    { //If there is a user details
                        $registration_ids[] = $user_details->device_token;
                            if($wo->worker_id == $wo->old_worker_id || $wo->old_worker_id == 0 || $wo->is_handle_by_team_leader == 1)
                            {
                                    //if work order assigning to same worker
                                        //send reopen wo notification to worker
                                        if($user_details->selected_app_langugage == 'ar' || $user_details->selected_app_langugage == "ar")
                                        {
                                            $notification_title = "تمت إعادة فتح أمر العمل";
                                        }
                                        elseif($user_details->selected_app_langugage == 'ur' || $user_details->selected_app_langugage == "ur")
                                        {
                                            $notification_title = "ورک آرڈر دوبارہ کھول دیا گیا ہے";
                                        }
                                        else
                                        {
                                            $notification_title = "A work order has been reopened";
                                        }
                                        $notification_type = 'bm_has_reopend_wo';
                            }
                            else
                            {
                                    // if work order assigning to new worker
                                    if($user_details->selected_app_langugage == 'ar' || $user_details->selected_app_langugage == "ar")
                                    {
                                        $notification_title = "أمر عمل جديد تم تعيينه";
                                    }
                                    elseif($user_details->selected_app_langugage == 'ur' || $user_details->selected_app_langugage == "ur")
                                    {
                                        $notification_title = "نیا ورک آرڈر دیا گیا ہے";
                                    }
                                    else
                                    {
                                        $notification_title = "A new work order has been assigned";
                                    }
                                    $notification_type = 'new_wo_assigned_to_worker';
                            }

                                $messgae = array(
                                    "title" => $notification_title,
                                    "body" => $reason,
                                    'section_id' => $wo->id,
                                    'notification_type' => $notification_type
                                );
                                $res = ApiHelper::send_notification_worker_FCM($WorkerId, $registration_ids, $messgae, $wo->property_id,$notification_type,$wo->id);
                    }
                }
                else
                {

                    // @flip1@ due to input fields  || 16-SP rejects reopens WO
                    $message = 'Service provider <strong>'.Auth::user()->name.'</strong> did not agree on reopening the work order due to <strong>'.$reason.'</strong>, pending for Building manager action';
                    $message_ar = 'مقدم الخدمة <strong>'.Auth::user()->name.'</strong> لم يوافق على إعادة فتح أمر العمل بسبب <strong>'.$reason.'</strong>، بانتظار رد مدير المبنى';
                    $notification_sub_type = 'sp_not_aggres_on_reopend_wo';
                }
                Notification::insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order','building_ids' => $wo->property_id,'notification_sub_type'=>$notification_sub_type, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));
                //Insert for Timeline
                Notification::insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order','building_ids' => $wo->property_id,'notification_sub_type'=>$notification_sub_type, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));
            }
            $work_order_number = $wo->id;
            // Save in Work Order Email Table
            $data = array(
                'wo_id'=> $work_order_number,
                'work_order_type'=>$wo->work_order_type,
                'status'=>'WO-Status-Updated',
                'created_at'=>date('Y-m-d H:i:s'),
            );
        }
        //DB::table('work_orders_email_jobs')->insert($data);
        return "success";
    }

    /**
     * POST workorder/update-response-action
     *
     * Service provider can update a workorder and move it to job execution step.
     *
     * @authenticated
     * @group workorder
     *
     * @response 200
     */
    public function update_response_action(Request $request)
    {
        // WORKER ASSIGN

        $user_id = auth()->user()->id;
        $start_job = $request->start_job;
        $work_order_id = $request->work_order_id;
        $reject_reason = $request->reject_reason;
		$isReactive = false;
        $isScheduled  = false;
        $data = [];
        $worker_id = null;
        
        if($start_job == 2) { // Assign worker
            $currentDateTime = $this->getCurrentDateTime();
            $currentDateTimeNewFormat = $this->changeDateFormat('Y-m-d H:i:s', $currentDateTime);
            $workOrderDetails = $this->getWorkOrderInformationsByValues('id', $work_order_id);
            $scheduledDateTime = isset($workOrderDetails->schedule_start_time) ? $workOrderDetails->start_date.' '.$workOrderDetails->schedule_start_time : null;
            $wtfStartDateTime = isset($workOrderDetails->wtf_start_time) ? $workOrderDetails->start_date.' '.$workOrderDetails->wtf_start_time : null;
            $worker_id = $request->worker_id;

            $workOrderData = DB::table('work_orders')->select('work_orders.*', 'contract_priorities.priority_id', 'contracts.contract_number', 'work_orders.priority_id as workOrderPriorityId')
            ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
            ->leftjoin('contract_asset_categories', function($join){
                $join->on('contract_asset_categories.asset_category_id', '=', 'work_orders.asset_category_id');
                $join->on('contract_asset_categories.contract_number','=', 'contracts.contract_number');
            })
            ->leftjoin('contract_priorities', function($join){
                $join->on('contract_priorities.priority_id', '=', 'contract_asset_categories.priority_id');
                $join->on('contract_priorities.contract_number','=', 'contracts.contract_number');
            })->where('work_orders.id', $work_order_id)
            ->first();
            $notificationType = 'new_wo_assigned_to_worker';
            $registrationIds = [];

            if((isset($scheduledDateTime) && $scheduledDateTime > $currentDateTimeNewFormat && $workOrderDetails->status = WorkOrderStatus::Open->value && $workOrderDetails->workorder_journey == "submitted" && !isset($request->assignToChecked))){
                $data = [
                    'worker_id' => $worker_id,
                    'assigned_to' => 'sp_worker', 
                    'assign_type' => AssignType::Normal->value,
                    'worker_for_responsible' => $user_id,
                    'workorder_journey' => 'job_execution' // new addition for API issue
                ];

                $workerDetails = \Helper::userDetail($worker_id);
                
                if(isset($workerDetails)){
                    if(!is_null($workerDetails->device_token) && !empty($workerDetails->device_token)){
                        $registrationIds[] = $workerDetails->device_token;
                    }

                    if ($workerDetails->selected_app_langugage == 'ar' || $workerDetails->selected_app_langugage == 'ur') {
                        $notificationTitle = ($workerDetails->selected_app_langugage == 'ar') ? 'تم تعيين أمر عمل مجدول' : 'ایک طے شدہ ورک آرڈر تفویض کیا گیا ہے۔';
                    } else {
                        $notificationTitle = 'A scheduled work order has been assigned';
                    }

                    $messageArray = [
                        'title' => $notificationTitle,
                        'body' => $workOrderData->description,
                        'section_id' => $workOrderData->id,
                        'notification_type' => $notificationType,
                    ];
    
                    // Send notifications
                    $res = ApiHelper::send_notification_worker_FCM($worker_id, $registrationIds, $messageArray, $workOrderData->property_id, $notificationType, $workOrderData->id);
                
                }

                else{
                    Log::info("update_response_action error: No worker found with this ID: ".$worker_id);
                }   
            }

            elseif(isset($wtfStartDateTime) && $wtfStartDateTime > $currentDateTimeNewFormat && $workOrderDetails->status = WorkOrderStatus::Open->value && $workOrderDetails->workorder_journey == "submitted" && !isset($request->assignToChecked)){
                $worker_id = isset($request->newSelectedWorker) ? $request->newSelectedWorker : $request->worker_id; // From blade file some times i got worker id and sometimes new Selected Worker id 

                $data = [
                    'worker_id' => $worker_id,
                    'assigned_to' => 'sp_worker', 
                    'assign_type' => AssignType::Normal->value,
                    'worker_for_responsible' => $user_id,
                    'workorder_journey' => 'job_execution' // new addition for API issue
                ];
     
                $workerDetails = \Helper::userDetail($worker_id);

                if(isset($workerDetails)){
                    if(!is_null($workerDetails->device_token) && !empty($workerDetails->device_token)){
                        $registrationIds[] = $workerDetails->device_token;
                    }

                    if ($workerDetails->selected_app_langugage == 'ar' || $workerDetails->selected_app_langugage == 'ur') {
                        $notificationTitle = ($workerDetails->selected_app_langugage == 'ar') ? 'تم تعيين أمر عمل مجدول' : 'ایک طے شدہ ورک آرڈر تفویض کیا گیا ہے۔';
                    } else {
                        $notificationTitle = 'A scheduled work order has been assigned';
                    }

                    $messageArray = [
                        'title' => $notificationTitle,
                        'body' => $workOrderData->description,
                        'section_id' => $workOrderData->id,
                        'notification_type' => $notificationType,
                    ];
    
                    // Send notifications
                    $res = ApiHelper::send_notification_worker_FCM($worker_id, $registrationIds, $messageArray, $workOrderData->property_id, $notificationType, $workOrderData->id);
                
                }

                else{
                    Log::info("update_response_action error: No worker found with this ID: ".$worker_id);
                }  
            }

            else{
            $workorder_journey = 'job_execution';
            $worker_id = $request->worker_id;
            $work_order_id = $request->work_order_id;
           
          //  $data = [];
            // check that $request->selectedWorkers is an array
            if (is_array($request->selectedWorkers)) {
                // Assuming $request->selectedWorkers contains an array of worker IDs
                $selectedWorkers = $request->selectedWorkers;

                // Prepare an array of data for insertion
                $workersData = [];
                foreach ($selectedWorkers as $workerId) {
                    $workersData[] = [
                        'work_order_id' => $work_order_id,
                        'worker_id' => $workerId,
                    ];
                }

                // Use insert to insert multiple records at once
                WorkOrderWorkers::insert($workersData);
            }

            $data['worker_id'] = $worker_id?$worker_id:0;
            $data['status'] = $start_job;
            $data['workorder_journey'] = $workorder_journey;
            $data['assigned_to'] = $worker_id?'sp_worker':$request->assigned_to;
            $data['worker_for_responsible'] = $user_id;
           // $data['worker_started_at'] =  $this->getCurrentDateTime(); // new addition because of start on behalf issue (popup + new option)
            $data['worker_started_at'] = isset($request->assignToChecked) ?  $this->getCurrentDateTime() : $workOrderData->worker_started_at; // new addition because of start on behalf issue (popup + new option) and for API issue
           $wo_check = DB::table('work_orders')->select('id')->where('work_orders.id', $work_order_id)->where('job_started_at', NULL)->count();
            if($wo_check != 0)
            {
                $data['job_started_at'] = date('Y-m-d H:i:s');
            }
            //$data['job_started_at'] = date('Y-m-d H:i:s');
            $data['pass_fail'] = "pending";

            //DB::enableQueryLog();
            $wo = DB::table('work_orders')->select('work_orders.*', 'contract_priorities.priority_id', 'contracts.contract_number', 'work_orders.priority_id as workOrderPriorityId')
            ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id');
            $wo = $wo->leftjoin('contract_asset_categories', function($join)
            {
                $join->on('contract_asset_categories.asset_category_id', '=', 'work_orders.asset_category_id');
                $join->on('contract_asset_categories.contract_number','=', 'contracts.contract_number');
            });
            $wo = $wo->leftjoin('contract_priorities', function($join)
            {
                $join->on('contract_priorities.priority_id', '=', 'contract_asset_categories.priority_id');
                $join->on('contract_priorities.contract_number','=', 'contracts.contract_number');
            });
            $wo = $wo->where('work_orders.id', $work_order_id);
            $wo = $wo->first();

            $data['td_before_assigning_wo'] = $wo->target_date;

            if(!empty(Auth::user()->user_type == 'sp_admin')) //If user type is sp_admin
            {
                $sup_ids = explode(',', $wo->supervisor_id);
                $data['supervisor_id'] = $sup_ids[0];
            }
            elseif(!empty(Auth::user()->user_type == 'supervisor')) //If user type is supervisor
            {
                $data['supervisor_id'] = $user_id;
            }
            //dd(DB::getQueryLog());
            $wtfs = DB::table('work_time_frame')
                    ->select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
                    ->where('user_id', $wo->project_user_id)
                    ->first();
            if($wo->wtf_start_time == '' || ($wo->work_order_type == "preventive" && $wo->status == 1)){
                if(!isset($wtfs)) //If work order time frame has added
                {
                    $time = "00:00:00";
                    $endTime = "00:00:00";
                }
                else {
                    $time = $wtfs->start_time;
                    $endTime = $wtfs->end_time;
                }
            }
            else{
                $time = $wo->wtf_start_time;
                $endTime = $wtfs->end_time;
            }

            if($wo->work_order_type == "preventive") //If the work order type is Preventive
            {
                $data['wtf_start_time'] = $time;
                $data['wtf_end_time'] = $endTime;
                $wo->created_at = $wo->start_date.' '.$time;
                $created_at = $wo->start_date.' '.$time;
            } else {
                $created_at = $wo->created_at;
                if (Carbon::parse($wo->start_date)->isSameDay(Carbon::parse($wo->created_at))) {
                    // Use created_at if both dates are the same
                    $created_at = $wo->created_at;
                } else {
                    // For future dates, use start_date with the specified time
                    $wo->created_at = $wo->start_date . ' ' . $time;
                    $created_at = $wo->start_date . ' ' . $time;
                }
                $isReactive = true;
                $isScheduled  = $wo->status == 7;
            }
            //$created_at = $wo->created_at;
            //dd($created_at);
            $t1 = Carbon::parse($created_at);
            $t2 = Carbon::parse(date('Y-m-d H:i:s'));
            $diff = $t1->diff($t2);
            $response_time = $diff->i;

            $tdate = date('Y-m-d H:i:s');
            $datetime1 = strtotime($created_at);
            $datetime2 = strtotime($tdate);
            $interval  = abs($datetime2 - $datetime1);
            $minutes   = round($interval / 60);
            //dd($minutes);
            $response_time = 0;
            $service_window = 0;
            $response_time_type = 'hours';
            $service_window_type = 'minutes';
            
            if($wo->work_order_type == "preventive" && $wo->priority_id != 0) //If work order type is preventive
            {
                $sla_asset_categories = ContractAssetCategories::where('asset_category_id', $wo->asset_category_id)
                ->where('contract_number', $wo->contract_number)
                ->orderBy('id', 'desc')
                ->first();
                if(!empty($sla_asset_categories->priority_id) || !empty($wo->sla_service_window_priority))
                {
                    $priorityId = $wo->sla_service_window_priority != 0 ? $wo->sla_service_window_priority : $sla_asset_categories->priority_id;            
                }
                else
                {
                    $priorityId = $wo->workOrderPriorityId ? $wo->workOrderPriorityId : $wo->priority_id;
                }

                $contract = Contracts::where('id', $wo->contract_id)
                ->where('is_deleted', 'no')
                ->select('contract_number', 'contract_type_id')
                ->first();

                $contractTypeId = $contract->contract_type_id;

                if(in_array($contractTypeId, ContractTypes::advanced())) {
                    $priorityId = $wo->workOrderPriorityId ? $wo->workOrderPriorityId : $wo->priority_id;
                }
                
                $frequency = DB::table('contract_priorities')
                ->where('priority_id', $priorityId)
                ->where('contract_number', $wo->contract_number)
                ->orderBy('id', 'desc')
                ->first();
                if(isset($frequency)) //If has frequency and response time less than reponse time
                {
                    $response_time = $frequency->response_time;
                    $response_time_type = $frequency->response_time_type;
                    $data['sla_response_time'] = $response_time;
                    $data['response_time_type'] = $response_time_type;
                    $data['sla_response_time_priority'] = $frequency->priority_id;
                    if($response_time_type == "days")
                    {
                        $response_time = $response_time * 1440;
                    }
                    elseif($response_time_type == "hours")
                    {
                        $response_time = $response_time * 60;
                    }
                    $time_left = $response_time - $minutes;
                    if($time_left >= 0) //If the time left is greater than equal to 0
                    {
                        $data['response_time'] = 'On time';
                    }
                    else
                    {
                        $data['response_time'] = 'Late';
                    }
                    $data['target_date'] = date('Y-m-d H:i:s', strtotime('+'.$frequency->service_window.' '.$frequency->service_window_type));
                }
            }
            elseif($wo->work_order_type == "preventive") //If work order type is preventive
            {
                $frequency = DB::table('frequencies_master')->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')->where('frequencies_master.id', $wo->frequency_id)->first();
                $contract = Contracts::where('id', $wo->contract_id)
                ->where('is_deleted', 'no')
                ->select('contract_number', 'contract_type_id')
                ->first();

                $contractTypeId = $contract->contract_type_id;

                if(in_array($contractTypeId, ContractTypes::advanced())) {
                    $data['sla_response_time_priority'] = $wo->workOrderPriorityId ? $wo->workOrderPriorityId : $wo->priority_id;
                }
                else
                {
                    $data['sla_response_time_priority'] = $wo->priority_id;
                }
                $response_time = $frequency->response_time;
                $response_time_type = $frequency->response_time_type;
                $data['sla_response_time'] = $response_time;
                $data['response_time_type'] = $response_time_type;
                //$data['sla_response_time_priority'] = $wo->priority_id;
                if($response_time_type == "days")
                {
                    $response_time = $response_time * 1440;
                }
                elseif($response_time_type == "hours")
                {
                    $response_time = $response_time * 60;
                }
                $time_left = $response_time - $minutes;
                if($time_left >= 0) //If the time left is greater than equal to 0
                {
                    $data['response_time'] = 'On time';
                }
                else
                {
                    $data['response_time'] = 'Late';
                }
                $data['target_date'] = date('Y-m-d H:i:s', strtotime('+'.$frequency->service_window.' '.$frequency->service_window_type));
            }
            else
            {
                $sla_asset_categories = ContractAssetCategories::where('asset_category_id', $wo->asset_category_id)
                ->where('contract_number', $wo->contract_number)
                ->orderBy('id', 'desc')
                ->first();
                if(!empty($sla_asset_categories->priority_id) || !empty($wo->sla_service_window_priority))
                {
                    $priorityId = $wo->sla_service_window_priority != 0 ? $wo->sla_service_window_priority : $sla_asset_categories->priority_id;            
                }
                else
                {
                    $priorityId = $wo->workOrderPriorityId ? $wo->workOrderPriorityId : $wo->priority_id;
                }
                
                $priorityId = $wo->workOrderPriorityId ? $wo->workOrderPriorityId : $priorityId;

                $priorities = DB::table('contract_priorities')
                      ->where('priority_id', $priorityId)
                      ->where('contract_number', $wo->contract_number)
                      ->orderBy('id', 'desc')
                      ->first();
            
                
                $response_time = $priorities->response_time;
                $response_time_type = $priorities->response_time_type;
                $data['sla_response_time'] = $response_time;
                $data['response_time_type'] = $response_time_type;
                $data['sla_response_time_priority'] = $priorities->priority_id;
                if($response_time_type == "days")
                {
                    $response_time = $response_time * 1440;
                }
                elseif($response_time_type == "hours")
                {
                    $response_time = $response_time * 60;
                }
                $time_left = $response_time - $minutes;
                if($time_left >= 0) //If the time left is greater than equal to 0
                {
                    $data['response_time'] = 'On time';
                }
                else
                {
                    $data['response_time'] = 'Late';
                }
                $data['target_date'] = date('Y-m-d H:i:s', strtotime('+'.$priorities->service_window.' '.$priorities->service_window_type));
            }

            // Send notification to worker or all workers
            $notification_type = 'new_wo_assigned_to_worker';
            $registration_ids = [];
            if ($worker_id && $worker = Helper::userDetail($worker_id)) {
                // If a specific worker is provided and exists
                if(!is_null($worker->device_token) && !empty($worker->device_token))
                {
                    $registration_ids[] = $worker->device_token;
                }

                // Notification title based on language
                if ($worker->selected_app_langugage == 'ar' || $worker->selected_app_langugage == 'ur') {
                    $notification_title = ($worker->selected_app_langugage == 'ar') ? 'أمر عمل جديد تم تعيينه' : 'نیا ورک آرڈر دیا گیا ہے';
                } else {
                    $notification_title = 'A new work order has been assigned';
                }

                // Message array
                $message = [
                    'title' => $notification_title,
                    'body' => $wo->description,
                    'section_id' => $wo->id,
                    'notification_type' => $notification_type,
                ];

                // Send notifications
                $res = ApiHelper::send_notification_worker_FCM($worker_id, $registration_ids, $message, $wo->property_id, $notification_type, $wo->id);
				if($isReactive && $isScheduled){
                    ApiHelper::send_notification_worker_FCM($worker_id, $registration_ids, [
                        'title' => trans('notifications.work_order_scheduled_message',[],$worker->selected_app_langugage),
                        'body' => $wo->description,
                        'section_id' => $wo->id,
                        'notification_type' => $notification_type,
                    ], $wo->property_id, $notification_type, $wo->id);
                }
            } elseif ($request->selectedWorkers && is_array($request->selectedWorkers)) {
                // If worker_id is 0 or not provided, and there are selectedWorkers
                $workers = User::whereIn('id', $request->selectedWorkers)->get();

                foreach ($workers as $worker) {
                    if (!empty($worker->device_token)) {
                        $registration_ids[] = $worker->device_token;

                        // Notification title based on language
                        if ($worker->selected_app_langugage == 'ar' || $worker->selected_app_langugage == 'ur') {
                            $notification_title = ($worker->selected_app_langugage == 'ar') ? 'أمر عمل جديد تم تعيينه' : 'نیا ورک آرڈر دیا گیا ہے';
                        } else {
                            $notification_title = 'A new work order has been assigned';
                        }

                        // Message array
                        $message = [
                            'title' => $notification_title,
                            'body' => $wo->description,
                            'section_id' => $wo->id,
                            'notification_type' => $notification_type,
                        ];

                        // Send notifications
                        $res = ApiHelper::send_notification_worker_FCM($worker->id, $registration_ids, $message, $wo->property_id, $notification_type, $wo->id);
                    }
                }
            }
        }
        }
        elseif($start_job == 1) //If assigning the worker
        {
            $proposed_new_date = $request->proposed_new_date;
            $workorder_journey = 'submitted';
            $worker_id = $request->worker_id;
            $work_order_id = $request->work_order_id;
            $data = [];
            $data['worker_id'] = $worker_id;
            $data['status'] = $start_job;
            $data['workorder_journey'] = $workorder_journey;
            $data['reason'] = $request->reason;
            $data['proposed_new_date'] = isset($proposed_new_date)? date('Y-m-d H:i:s', strtotime($proposed_new_date)) : null;
            if($data['reason'] == __('work_order.forms.label.I_don_t_have_the_required_parts') && isset($request->sp_missing_items_list)){

                // Update the examine_button_clicked_at column with the current timestamp
                $data['examine_button_clicked_at'] = now();

                $requestData = [
                                'work_order_id '=>$work_order_id,
                                'user_id '=>Auth::user()->id,
                                'status '=>'requested'
                ];
                $createdRequest=new ServiceProviderMissingItemRequest;
                $createdRequest->work_order_id =$work_order_id;
                $createdRequest->user_id =Auth::user()->id;
                $createdRequest->status ='requested';
                $createdRequest->save();
                foreach(json_decode($request->sp_missing_items_list) as $missingItem){
                    if($missingItem->isChecked){
                        $createdRequestItem=new ServiceProvieRequestedMissingItem;
                        $createdRequestItem->item_id =$missingItem->id;
                        $createdRequestItem->request_id =$createdRequest->id;
                        $createdRequestItem->quantity =$missingItem->missingQuantity;
                        $createdRequestItem->save();
                    }

                }
                $data['proposed_new_date'] = null;
            }
            if(trim($reject_reason) != '')
            {
                $data['reject_reason'] = $reject_reason;
            }

        }
        $updatedRow = DB::table('work_orders')
            ->where('id', $work_order_id)
            ->update($data);

            if(!$updatedRow){
                Log::info('update_response_action error: The selected work order with ID: '.$work_order_id." cannot updated for unknown reason!");
            }

        /**
        * Sending Notifications
        **/
        $notification_sub_type = '';

        $wo = DB::table('work_orders')->select('property_buildings.building_name', 'work_orders.id','work_orders.work_order_id','work_orders.work_order_type','work_orders.property_id','work_orders.service_provider_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.id', $work_order_id)->first();
        if(isset($wo)) //If has work order
        {
            if($start_job == 2) //If worker is assigned
            {
                if($worker_id)
                {
                    $wr = DB::table('users')->select('users.name')->where('users.id', $worker_id)->first();
                    // @flip1@  input field to weight bold
                    $message = 'Worker <strong>'.$wr->name.'</strong> has been assigned to the work order by <strong>'.Auth::user()->name.'</strong>';
                    $message_ar = 'تم تعيين العامل <strong>'.$wr->name.'</strong> لأداء أمر العمل من قبل <strong>'.Auth::user()->name.'</strong>';
                    $notification_sub_type = 'worker_assigned_by_bm';
                }
                elseif (is_array($request->selectedWorkers)) {
                    $message = 'Service Provider <strong>'.Auth::user()->name.'</strong> has assigned <strong>'.count($request->selectedWorkers).'</strong> workers to the work order';
                    $message_ar = 'مقدم الخدمة <strong>'.Auth::user()->name.'</strong> قام بتعيين <strong>'.count($request->selectedWorkers).'</strong> عامل لأداء أمر العمل';
                    $notification_sub_type = 'worker_assigned_by_bm';
                }
                else
                {
                    $message = 'Service provider <strong>'.Auth::user()->name.'</strong> has started the work on behalf of worker';
                    $message_ar = 'مقدم الخدمة <strong>'.Auth::user()->name.'</strong> بدأ العمل بالنيابة عن العامل';
                    $notification_sub_type = 'worker_assigned_by_bm';
                }
            }
            elseif($start_job == 1) //If other option is selected
            {
                $notification_sub_type = 'sp_has_an_issue';
                if($request->reason =="I don't have the required parts" || $request->reason =="ليس لدي قطع الغيار"){
                    $reason_en =  "[Required Spare Parts]";
                    $reason_ar = "[قطع الغيار مطلوبة]";
                    $message =  __('work_order.notifications.sp_doesnt_have_required_parts', ['name' => Auth::user()->name,'reason' => $reason_en], 'en');
                    $message_ar =  __('work_order.notifications.sp_doesnt_have_required_parts', ['name' => Auth::user()->name,'reason' => $reason_ar], 'ar');
                    $notification_sub_type = 'sent_to_project_owner';

                    WorkOrders::where('id', $work_order_id)->update(['status' => 3,'pause_start_time' => now(),'pause_time_spent' => "",'pause_time_spent_minutes' => "",'pause_end_time' => NULL]);
                }
                else if($request->reason =="No permission to enter facility" || $request->reason =="لا يوجد إذن لدخول المنشأة"){
                    $reason_en = "No Permission to enter facility";
                    $reason_ar = "لا يوجد إذن لدخول المنشأة";

                    $message =  __('work_order.notifications.other_reason_notification', ['name' => Auth::user()->name,'reason' => $reason_en, 'proposed_new_date' => $proposed_new_date], 'en');
                    $message_ar =  __('work_order.notifications.other_reason_notification', ['name' => Auth::user()->name,'reason' => $reason_ar, 'proposed_new_date' => $proposed_new_date], 'ar');
                }
                else if($request->reason =="Other Reason" || $request->reason =="سبب آخر"){
                    $reason_en = "Other Reason";
                    $reason_ar = "سبب آخر";

                    $message =  __('work_order.notifications.other_reason_notification', ['name' => Auth::user()->name,'reason' => $reason_en, 'proposed_new_date' => $proposed_new_date], 'en');
                    $message_ar =  __('work_order.notifications.other_reason_notification', ['name' => Auth::user()->name,'reason' => $reason_ar, 'proposed_new_date' => $proposed_new_date], 'ar');
                }
                else{
                    $reason_en = $request->reason;
                    $reason_ar = $request->reason;

                    $message =  __('work_order.notifications.other_reason_notification', ['name' => Auth::user()->name,'reason' => $reason_en, 'proposed_new_date' => $proposed_new_date], 'en');
                    $message_ar =  __('work_order.notifications.other_reason_notification', ['name' => Auth::user()->name,'reason' => $reason_ar, 'proposed_new_date' => $proposed_new_date], 'ar');
                }
            }
            //Insert for notifications
            DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order','building_ids' => $wo->property_id,'notification_sub_type'=>$notification_sub_type, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));
            //Insert for Timeline
            DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order','building_ids' => $wo->property_id,'notification_sub_type'=>$notification_sub_type, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));

        }
        //$work_order_number = $wo->id;
        // Save in Work Order Email Table
        /*$data = array(
            'wo_id'=> $work_order_number,
            'work_order_type'=>$wo->work_order_type,
            'status'=>'WO-Status-Updated',
            'created_at'=>date('Y-m-d H:i:s'),
        );
        DB::table('work_orders_email_jobs')->insert($data);*/

        $workOrdersArray = [
            'work_order_id' => $wo->work_order_id,
            'wo_id' => $wo->id,
            'work_order_type' => $wo->work_order_type,
            'status' => 'WO-Status-Updated',
            'created_at' => $this->getCurrentDateTime()
        ];
        
       $saveEmailJob = $this->saveWorkOrderEmailJob($workOrdersArray);

        if(!$saveEmailJob){
            Log::info('update_response_action error: Issue when saving data inside work_orders_email_jobs table!');
        }

        return "success";
    }

    public function assignWorkerForScheduled($id, Request $request)
    {
        $user = auth()->user();
        $worker_id = $request->worker_id;

        // Fetch Work Order with Related Data
        $work_order = WorkOrders::with(['contract:id,contract_number', 'propertyBuilding:id,building_name'])
            ->select('id', 'description', 'property_id', 'contract_id')
            ->findOrFail($id);

            WorkOrders::where('id',$id)->update([
                'worker_id'             => $worker_id,
                'assigned_to'           => AssignedTo::SP_WORKER->value,
                'worker_for_responsible'=> $user->id,
                'pass_fail'             => PassFail::PENDING->value,
                'workorder_journey' => 'job_execution' // new addition for API issue
            ]);

        // Fetch Worker Details
        $worker = User::select('id', 'name', 'device_token', 'selected_app_langugage')
            ->find($worker_id);

        // Send Push Notification
        if ($worker && !empty($worker->device_token)) {
            $notification_title = trans('notifications.new_scheduled_wo_assigned_title', [], $worker->selected_app_langugage);
            $notification_body = trans('notifications.new_wo_assigned_body', ['description' => $work_order->description], $worker->selected_app_langugage);

            $worker->notify(new WorkerAssignedNotification(
                $notification_title,
                $notification_body,
                $work_order->id,
                $work_order->property_id,
                'new_wo_assigned_to_worker'
            ));
        }

        // Get Work Order Details for Notification
        $worker_name = $worker->name ?? 'Unknown Worker';
        $message = 'A scheduled work order has been assigned';

        $message_ar = ($worker->selected_app_langugage == 'ar') ? 'أمر عمل جديد تم تعيينه' : 'نیا ورک آرڈر دیا گیا ہے';


        $timeline_message_en = trans('notifications.worker_assigned_message', [
            'worker_name' => $worker_name,
            'assigner_name' => $user?->name ?? '',
        ], 'en');

        $timeline_message_ar = trans('notifications.worker_assigned_message', [
            'worker_name' => $worker_name,
            'assigner_name' => $user?->name ?? '',
        ], 'ar');

        $notification_sub_type = 'worker_assigned_by_bm';

        // Send Notifications to Assigner (Database Notifications)
        $user->notify(new WorkerAssignedDatabaseNotification(
            $message, 'تم تعيين أمر عمل مجدول', $notification_sub_type, $work_order->id,
            $work_order->property_id, 'no'
        ));

        // Send Timeline Notification
        $user->notify(new WorkerAssignedDatabaseNotification(
            $timeline_message_en, $timeline_message_ar, $notification_sub_type, $work_order->id,
            $work_order->property_id, 'yes'
        ));

        return response()->json(['message' => 'Worker assigned successfully'], 200);
    }
    /**
     * POST workorder/approve-reject-sp-date
     *
     * Building manager(BM) can approve or reject the new proposed date requested by the service provider. BM can also propose another date to the service provider.
     *
     * @authenticated
     * @group workorder
     *
     * @response 200
     */
    public function approve_reject_sp_date(Request $request)
    {
        $user_id = auth()->user()->id;
        $approve_proposed_date = $request->approve_proposed_date;
        if($approve_proposed_date == 2) //If proposed date is accepeted
        {
            $workorder_journey = 'job_execution';
            $work_order_id = $request->work_order_id;
            $proposed_date_sp = $request->proposed_date_sp;
            //$proposed_date_sp = date('Y-m-d H:i:s');
            $data = [];
            $data['status'] = 2;
            $data['bm_approve_issue'] = 2;
            $data['workorder_journey'] = $workorder_journey;
            $data['target_date'] = $proposed_date_sp;

            $wo_check = DB::table('work_orders')->select('id')->where('work_orders.id', $work_order_id)->where('job_started_at', NULL)->count();
            //dd($wo_check);
            if($wo_check != 0)
            {
                $data['job_started_at'] = date('Y-m-d H:i:s');
            }
            $wo = DB::table('work_orders')->select('work_orders.*', 'contract_priorities.priority_id', 'contracts.contract_number')
            ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id');
            $wo = $wo->leftjoin('contract_asset_categories', function($join)
            {
                $join->on('contract_asset_categories.asset_category_id', '=', 'work_orders.asset_category_id');
                $join->on('contract_asset_categories.contract_number','=', 'contracts.contract_number');
            });
            $wo = $wo->leftjoin('contract_priorities', function($join)
            {
                $join->on('contract_priorities.priority_id', '=', 'contract_asset_categories.priority_id');
                $join->on('contract_priorities.contract_number','=', 'contracts.contract_number');
            });
            $wo = $wo->where('work_orders.id', $work_order_id);
            $wo = $wo->first();

            // if(!empty(Auth::user()->user_type == 'sp_admin')) //If user type is sp_admin
            // {
            //     $sup_ids = explode(',', $wo->supervisor_id);
            //     $data['supervisor_id'] = $sup_ids[0];
            // }
            // elseif(!empty(Auth::user()->user_type == 'supervisor')) //If user type is supervisor
            // {
            //     $data['supervisor_id'] = $user_id;
            // }
            //dd(DB::getQueryLog());
            $wtfs = DB::table('work_time_frame')
                    ->select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
                    ->where('user_id', $wo->project_user_id)
                    ->first();
            if($wo->wtf_start_time == ''){
                if(!isset($wtfs)) //If work order time frame has added
                {
                    $time = "00:00:00";
                }
                else {
                    $time = $wtfs->start_time;
                }
            }
            else{
                $time = $wo->wtf_start_time;
            }
            $created_at = $wo->created_at;
            if($wo->work_order_type == "preventive") //If the work order type is Preventive
            {
                $wo->created_at = $wo->start_date.' '.$time;
                $created_at = $wo->start_date.' '.$time;
            }
            //$created_at = $wo->created_at;
            //dd($created_at);
            $t1 = Carbon::parse($created_at);
            $t2 = Carbon::parse(date('Y-m-d H:i:s'));
            $diff = $t1->diff($t2);
            $response_time = $diff->i;

            $tdate = date('Y-m-d H:i:s');
            $datetime1 = strtotime($created_at);
            $datetime2 = strtotime($tdate);
            $interval  = abs($datetime2 - $datetime1);
            $minutes   = round($interval / 60);
            //dd($minutes);
            $response_time = 0;
            $service_window = 0;
            $response_time_type = 'hours';
            $service_window_type = 'minutes';
            if($wo->work_order_type == "preventive" && $wo->priority_id != 0) //If work order type is preventive
            {
                $frequency = DB::table('contract_priorities')
                ->where('priority_id', $wo->priority_id)
                ->where('contract_number', $wo->contract_number)
                ->orderBy('id', 'desc')
                ->first();

                if(isset($frequency)) //If has frequency and response time less than reponse time
                {
                    $response_time = $frequency->response_time;
                    $response_time_type = $frequency->response_time_type;
                    $data['sla_response_time'] = $response_time;
                    $data['response_time_type'] = $response_time_type;
                    $data['sla_response_time_priority'] = $frequency->priority_id;
                    if($response_time_type == "days")
                    {
                        $response_time = $response_time * 1440;
                    }
                    elseif($response_time_type == "hours")
                    {
                        $response_time = $response_time * 60;
                    }
                    $time_left = $response_time - $minutes;
                    if($time_left >= 0) //If the time left is greater than equal to 0
                    {
                        $data['response_time'] = 'On time';
                    }
                    else
                    {
                        $data['response_time'] = 'Late';
                    }
                    $data['target_date'] = date('Y-m-d H:i:s', strtotime('+'.$frequency->service_window.' '.$frequency->service_window_type));
                }
            }
            elseif($wo->work_order_type == "preventive") //If work order type is preventive
            {
                $frequency = DB::table('frequencies_master')->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')->where('frequencies_master.id', $wo->frequency_id)->first();

                $response_time = $frequency->response_time;
                $response_time_type = $frequency->response_time_type;
                $data['sla_response_time'] = $response_time;
                $data['response_time_type'] = $response_time_type;
                $data['sla_response_time_priority'] = $wo->priority_id;
                if($response_time_type == "days")
                {
                    $response_time = $response_time * 1440;
                }
                elseif($response_time_type == "hours")
                {
                    $response_time = $response_time * 60;
                }
                $time_left = $response_time - $minutes;
                if($time_left >= 0) //If the time left is greater than equal to 0
                {
                    $data['response_time'] = 'On time';
                }
                else
                {
                    $data['response_time'] = 'Late';
                }
                $data['target_date'] = date('Y-m-d H:i:s', strtotime('+'.$frequency->service_window.' '.$frequency->service_window_type));
            }
            else
            {
                $priorities = DB::table('contract_priorities')
                      ->where('priority_id', $wo->priority_id)
                      ->where('contract_number', $wo->contract_number)
                      ->orderBy('id', 'desc');
                //$query = str_replace(array('?'), array('\'%s\''), $priorities->toSql());
                //$query = vsprintf($query, $priorities->getBindings());
                //dump($query);
                //die;

                $priorities = $priorities->first();
                $response_time = $priorities->response_time;
                $response_time_type = $priorities->response_time_type;
                $data['sla_response_time'] = $response_time;
                $data['response_time_type'] = $response_time_type;
                $data['sla_response_time_priority'] = $priorities->priority_id;
                if($response_time_type == "days")
                {
                    $response_time = $response_time * 1440;
                }
                elseif($response_time_type == "hours")
                {
                    $response_time = $response_time * 60;
                }
                $time_left = $response_time - $minutes;
                //dd($time_left);
                if($time_left >= 0) //If the time left is greater than equal to 0
                {
                    $data['response_time'] = 'On time';
                }
                else
                {
                    $data['response_time'] = 'Late';
                }
                $data['target_date'] = date('Y-m-d H:i:s', strtotime('+'.$priorities->service_window.' '.$priorities->service_window_type));
            }
            $data['target_date'] = $proposed_date_sp;
        }
        elseif($approve_proposed_date == 1) //If proposed date is not accepted
        {
            $proposed_new_date = $request->new_suggested_Date;
            $worker_id = $request->worker_id3;
            $work_order_id = $request->work_order_id;
            $data = [];
            $data['reason'] = $request->reject_reason;
            if(!empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'supervisor'))
            {
                $data['bm_approve_issue'] = 0;
            }
            else
            {
                $data['bm_approve_issue'] = 1;
            }
            $data['proposed_new_date'] = date('Y-m-d H:i:s', strtotime($proposed_new_date));
            if($request->worker_id3) //If has worker id
            {
                $data['bm_approve_issue'] = 0;
                $data['worker_id'] = $worker_id;
            }
        }
        DB::table('work_orders')
                ->where('id', $work_order_id)
                ->update($data);
        /**
        * Sending Notifications
        **/
        $notification_sub_type = '';

        $wo = DB::table('work_orders')->select('property_buildings.building_name', 'work_orders.id', 'work_orders.worker_id','work_orders.work_order_id','work_orders.work_order_type','work_orders.property_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.id', $work_order_id)->first();
        if(isset($wo)) //If has work order
        {
            if($approve_proposed_date == 2) //If proposed date is accepted
            {
                $wr = DB::table('users')->select('users.name')->where('users.id', $wo->worker_id)->first();
                if(!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee')) //If user type is building_manager or building_manager_employee
                {
                    // @flip1@ due to change messages and bold input || BM accepts suggested date
                    if(isset($wr))
                    {
                        $message = 'Building Manager <strong>'.Auth::user()->name.'</strong> has agreed on work order proposed date, Worker <strong>'.$wr->name.'</strong> has been assigned';
                        $message_ar = 'مدير المبنى <strong>'.Auth::user()->name.'</strong> وافق على تغيير تاريخ أمر العمل، تم تعيين العامل <strong>'.$wr->name.'</strong> لأداء العمل';
                    }
                    else
                    {
                        $message = 'Building Manager <strong>'.Auth::user()->name.'</strong> has agreed on work order proposed date';
                        $message_ar = 'مدير المبنى <strong>'.Auth::user()->name.'</strong> وافق على تغيير تاريخ أمر العمل';
                    }
                
                    $notification_sub_type = 'bm_has_agreed_on_workorder';
                }
                elseif(!empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'supervisor')) //If user type is sp_admin
                {
                    // @flip1@ due to change the message in field bold || SP accepts suggested date
                     $message = 'Service provider <strong>'.Auth::user()->name.'</strong> has agreed on work order proposed date, Worker <strong>'.$wr->name.'</strong> has been assigned';
                     $message_ar = 'مقدم الخدمة <strong>'.Auth::user()->name.'</strong> وافق على تغيير تاريخ أمر العمل، تم تعيين العامل <strong>'.$wr->name.'</strong> لأداء العمل';

                    $notification_sub_type = 'sp_has_agreed_on_workorder';
                }

            }
            elseif($approve_proposed_date == 1) //If proposed date is not accepted
            {
                //dd('yes');
                if(!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee')) //If user type is building_manager or building_manager_employee
                {
                    // @flip1@ due to change input field || BM rejects suggested date
                    $message = "Building Manager <strong>".Auth::user()->name."</strong> has rejected the proposed date due to <strong>".$request->reject_reason."</strong>, work order has been sent back to Service Provider with this  suggested date <strong>".$request->new_suggested_Date."</strong>";
                    $message_ar = 'مدير المبنى <strong>'.Auth::user()->name.'</strong> رفض تغيير التاريخ بسبب <strong>'.$request->reject_reason.'</strong>، تم إعادة تعيين أمر العمل إلى مقدم الخدمة مع التاريخ المقترح التالي <strong>'.$request->new_suggested_Date.'</strong>';
                    $notification_sub_type = 'bm_has_did_not_agreed_on_workorder';
                }
                elseif(!empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'supervisor')) //If user type is sp_admin
                {
                    // @flip1@ due to change message and input field || SP rejects suggested date [Change of content, and was missing BOLD]
                    $message = 'Service Provider <strong>'.Auth::user()->name.'</strong> did not agree on the proposed target date due to <strong>'.$request->reject_reason.'</strong>, new target date has been suggested <strong>'.$proposed_new_date.'</strong>';
                    $message_ar = 'مقدم الخدمة <strong>'.Auth::user()->name.'</strong> لم يوافق على التاريخ المستهدف بسبب <strong>'.$request->reject_reason.'</strong>, تم وضع تاريخ مقترح جديد <strong>'.$proposed_new_date.'</strong>';

                    $notification_sub_type = 'sp_has_did_not_agreed_on_workorder';
                }
            }
            if(isset($message))
            {
                //Insert for notification
            DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id,'notification_sub_type'=>$notification_sub_type, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));
            //Insert for Timeline
            DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id,'notification_sub_type'=>$notification_sub_type, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'), 'is_timeline'=>'yes'));
            } 
            
        }
       // $work_order_number = $wo->id;
        // Save in Work Order Email Table
        /*$data = array(
            'wo_id'=> $work_order_number,
            'work_order_type'=>$wo->work_order_type,
            'status'=>'WO-Status-Updated-Second',
            'created_at'=>date('Y-m-d H:i:s'),
        );
        DB::table('work_orders_email_jobs')->insert($data);*/

        $workOrdersArray = [
            'work_order_id' => $wo->work_order_id,
            'wo_id' => $wo->id,
            'work_order_type' => $wo->work_order_type,
            'status' => 'WO-Status-Updated-Second',
            'created_at' => $this->getCurrentDateTime()
        ];

        $saveEmailJob = $this->saveWorkOrderEmailJob($workOrdersArray);

        if(!$saveEmailJob){
            Log::info('approve_reject_sp_date error: Issue when saving data inside work_orders_email_jobs table!');
        }

        return "success";
    }

    /**
     * POST workorder/complete-job-by-sp
     *
     * Service provider(SP) can complete the job on behalf of the worker when for some reason worker is not available.
     *
     * @authenticated
     * @group workorder
     *
     * @response 200
     */
    public function complete_job_by_sp(Request $request)
    {
        $user_id = auth()->user()->id;
        $complete_job = $request->complete_job;

        $data = [];

        if($complete_job == "job_started_on_behalf"){
            $work_order_id = $request->work_order_id;

            $array = [
                'worker_started_at' => $this->getCurrentDateTime()
            ];

            $updateStartedBehalf = $this->updateWorkOrderDetailsByValues($work_order_id, $array);

            if(!$updateStartedBehalf){
                Log::error("complete_job_by_sp error: We cannot update the job_started_at column : Satrted on Behalf!");
            }
        }

        elseif($complete_job == "job_completed"){
            // Retrieve and parse stored item ids and quantities from hidden inputs
            $storedItemIds = explode(',', $request->input('stored_item_id'));
            $storedQuantities = explode(',', $request->input('stored_quantity'));

            // Check if storedItemIds is an array with only one element containing "undefined"
            if (count($storedItemIds) === 1 && ($storedItemIds[0] === "undefined" || $storedItemIds[0] == "")) {
                $status = 'not_requested';
            } else {
                // Determine the status based on the presence of items
                $status = empty($storedItemIds) ? 'not_requested' : 'accepted';
            }
            // Store work order requested items only if items are present
            if ($status === 'accepted') {
                // Create a work order item request
                $workOrderItemRequest = WorkOrderItemRequest::create([
                    'work_order_id' => $request->work_order_id,
                    'worker_id' => isset($request->worker_id) && $request->worker_id != 'undefined' ? $request->worker_id : $user_id,
                    'status' => $status
                ]);

                // Store each requested item in the WorkOrderRequestedItem table
                foreach ($storedItemIds as $index => $itemId) {
                    WorkOrderRequestedItem::create([
                        'request_id' => $workOrderItemRequest->id,
                        'item_id' => $itemId,
                        'quantity' => $storedQuantities[$index],
                    ]);
                }
            }

            //  After Worker Assigned by SP
            // 1. Job Complete karna by Worker or SP
            // 2. SP aprrove 0 or 1 -> if 1 then it wil ask sp for approval -> here you will check if bm approive is o or 1 if its 0 then  you will finish if not them the normal flow
            // 3. Sp approve 0 then approve the sp approve logic
            if(isset($request->sp_approove) && $request->sp_approove == 1  )
            {
                $workorder_journey = 'job_evaluation';
                $work_order_id = $request->work_order_id;
                $data['status'] = 2;
                $data['workorder_journey'] = $workorder_journey;
                $data['job_completed_by'] = "SP";
                $data['sp_approve_job'] = "0";
                $data['bm_approve_job'] = "0";
                $data['job_submitted_at'] = date('Y-m-d H:i:s');
                $data['assigned_to'] = auth()->user()->user_type;

                $wo = DB::table('work_orders')->select('work_orders.*', 'contract_priorities.priority_id', 'work_orders.priority_id as work_order_priority_id', 'contracts.contract_number')->leftjoin('contract_asset_categories', 'contract_asset_categories.asset_category_id', '=', 'work_orders.asset_category_id')->leftjoin('contract_priorities', 'contract_priorities.priority_id', '=', 'contract_asset_categories.priority_id')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->where('work_orders.id', $work_order_id)->first();

                $sla_asset_categories = DB::table('contract_asset_categories')
                      ->where('asset_category_id', $wo->asset_category_id)
                      ->where('contract_number', $wo->contract_number)
                      ->orderBy('id', 'desc')
                      ->first();

                $response_time = 0;
                $service_window = 0;
                $response_time_type = 'hours';
                $service_window_type = 'minutes';
                $priority_id = $wo->priority_id ?? $wo->work_order_priority_id;

                $contract = Contracts::where('contract_number', $wo->contract_number)
                        ->where('is_deleted', 'no')
                        ->select('contract_number', 'contract_type_id')
                        ->first();  

                $contractTypeId = $contract->contract_type_id;
                
                if($wo->work_order_type == "reactive")
                {
                    if(!empty($sla_asset_categories->priority_id) || (in_array($contractTypeId, ContractTypes::advanced()))){
                        if(in_array($contractTypeId, ContractTypes::advanced()))
                        {
                            $wo_priority_id = $wo->work_order_priority_id;
                        }
                        else
                        {
                            $wo_priority_id = $wo->sla_service_window_priority != 0 ? $wo->sla_service_window_priority : $sla_asset_categories->priority_id;
                        
                            $wo_priority_id = $wo->work_order_priority_id ? $wo->work_order_priority_id : $wo_priority_id;
                        }
                        
                        
                        $contract_priorities = DB::table('contract_priorities')
                            ->where('priority_id', $wo_priority_id)
                            ->where('contract_number', $wo->contract_number)
                            ->orderBy('id', 'desc')
                            ->first();
                        
                        if($contract_priorities) {
                            $response_time = $contract_priorities->response_time;
                            $service_window = $contract_priorities->service_window;
                            $response_time_type = $contract_priorities->response_time_type;
                            $service_window_type = $contract_priorities->service_window_type;
                            $priority_id = $contract_priorities->priority_id;
                        } else {
                            $contract_priorities = DB::table('priorities')
                                ->where('id', $wo_priority_id)
                                ->orderBy('id', 'desc')
                                ->first();
                            $response_time = $contract_priorities->response_time;
                            $service_window = $contract_priorities->service_window;
                            $response_time_type = $contract_priorities->response_time_type;
                            $service_window_type = $contract_priorities->service_window_type;
                            $priority_id = $contract_priorities->id;
                        }
                    } else {
                        //$wo_priority_id = $wo->sla_service_window_priority != 0 ? $wo->sla_service_window_priority : $priority_id;
                        $wo_priority_id = $wo->work_order_priority_id ? $wo->work_order_priority_id : $wo->priority_id;
                        $contract_priorities = DB::table('priorities')
                            ->where('id', $wo_priority_id)
                            ->orderBy('id', 'desc')
                            ->first();
                        $response_time = $contract_priorities->response_time;
                        $service_window = $contract_priorities->service_window;
                        $response_time_type = $contract_priorities->response_time_type;
                        $service_window_type = $contract_priorities->service_window_type;
                        $priority_id = $contract_priorities->id;
                    }
                }
                elseif($wo->work_order_type == "preventive" && $wo->priority_id != 0)
                {
                    $contract_priorities = DB::table('contract_priorities')
                        ->where('priority_id', $wo->priority_id)
                        ->where('contract_number', $wo->contract_number)
                        ->orderBy('id', 'desc')
                        ->first();

                    $response_time = 0;
                    $service_window = 0;
                    if($contract_priorities)
                    {
                        $response_time = $contract_priorities->response_time;
                        $service_window = $contract_priorities->service_window;
                        $response_time_type = $contract_priorities->response_time_type;
                        $service_window_type = $contract_priorities->service_window_type;
                        $priority_id = $contract_priorities->priority_id;
                    }
                }
                else
                {
                    $contract_frequencies = DB::table('frequencies_master')
                            ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                            ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                            ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                            ->where('frequencies_master.id', $wo->frequency_id)
                            ->where('contract_frequencies.contract_number', $wo->contract_number)
                            ->first();
                        $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
                        $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : 0;
                        $response_time_type = isset($contract_priorities->response_time_type)?$contract_priorities->response_time_type:'hours';
                        $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
                }
                $created_at = $wo->created_at;
                $tdate = date('Y-m-d H:i:s');
                $datetime1 = strtotime($created_at);
                $datetime2 = strtotime($tdate);
                $interval  = abs($datetime2 - $datetime1);
                $minutes   = round($interval / 60);
                if($response_time_type == "days")
                {
                    $response_time = $response_time * 1440;
                }
                elseif($response_time_type == "hours")
                {
                    $response_time = $response_time * 60;
                }
                $time_left = $response_time - $minutes;

                if($wo->job_started_at == NULL || $wo->bm_approve_issue == 2)
                {
                    $target_date = $wo->target_date;
                }
                else
                {
                    $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($wo->job_started_at)));

                }
                //dd($service_window);
                if($wo->job_started_at != '' && $wo->status != 4 && $wo->pass_fail == 'pending')
                {
                    if(isset($wo->pause_time_spent_minutes) && trim($wo->pause_time_spent_minutes) != "" && $wo->pause_time_spent_minutes > 0)
                    {
                        $newDateTime = Carbon::now()->subMinutes($wo->pause_time_spent_minutes);
                        $newDateTime = $newDateTime->format('Y-m-d H:i:s');
                    }
                    else
                    {
                        $newDateTime = date('Y-m-d H:i:s');
                    }
                    if(strtotime($target_date) >= strtotime($newDateTime))
                    {
                        $data['pass_fail'] = 'pass';
                    }
                    else
                    {
                        $data['pass_fail'] = 'fail';
                    }
                    $data['sla_service_window'] = $service_window;
                    $data['service_window_type'] = $service_window_type;
                    $data['sla_service_window_priority'] = $priority_id;
                }
                //dd($data);
            }
            // If Building manager does not want to approve :: bm_approve = 0 and sp admin does not wants to approve sp_approove =0
            elseif($request->bm_approove == 0 && $request->sp_approove == 0 )
            {
                $workorder_journey = 'finished';
                $work_order_id = $request->work_order_id;
                $data['status'] = 4;
                $data['workorder_journey'] = $workorder_journey;
                $data['job_completed_by'] = "SP";
                $data['rating'] = '';
                $data['score'] = '';
                $data['building_manager_comment']='';
                $data['job_completion_date'] = date('Y-m-d H:i:s');
                $data['sp_approve_job'] = "0";
                $data['bm_approve_job'] = "0";
                $data['job_submitted_at'] = date('Y-m-d H:i:s');

                $wo = DB::table('work_orders')->select('work_orders.*', 'contract_priorities.priority_id', 'work_orders.priority_id as work_order_priority_id', 'contracts.contract_number')->leftjoin('contract_asset_categories', 'contract_asset_categories.asset_category_id', '=', 'work_orders.asset_category_id')->leftjoin('contract_priorities', 'contract_priorities.priority_id', '=', 'contract_asset_categories.priority_id')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->where('work_orders.id', $work_order_id)->first();

                $sla_asset_categories = DB::table('contract_asset_categories')
                      ->where('asset_category_id', $wo->asset_category_id)
                      ->where('contract_number', $wo->contract_number)
                      ->orderBy('id', 'desc')
                      ->first();

                $response_time = 0;
                $service_window = 0;
                $response_time_type = 'hours';
                $service_window_type = 'minutes';
                $priority_id = $wo->priority_id ?? $wo->work_order_priority_id;
                if($wo->work_order_type == "reactive")
                {
                    if(!empty($sla_asset_categories->priority_id)){

                        $wo_priority_id = $wo->sla_service_window_priority != 0 ? $wo->sla_service_window_priority : $sla_asset_categories->priority_id;
                        $wo_priority_id = $wo->work_order_priority_id ? $wo->work_order_priority_id : $wo_priority_id;
                        $contract_priorities = DB::table('contract_priorities')
                            ->where('priority_id', $wo_priority_id)
                            ->where('contract_number', $wo->contract_number)
                            ->orderBy('id', 'desc')
                            ->first();
                        if($contract_priorities) {
                            $response_time = $contract_priorities->response_time;
                            $service_window = $contract_priorities->service_window;
                            $response_time_type = $contract_priorities->response_time_type;
                            $service_window_type = $contract_priorities->service_window_type;
                            $priority_id = $contract_priorities->priority_id;
                        } else {
                            //$wo_priority_id = $wo->sla_service_window_priority != 0 ? $wo->sla_service_window_priority : $sla_asset_categories->priority_id;
                            $contract_priorities = DB::table('priorities')
                                ->where('id', $wo_priority_id)
                                ->orderBy('id', 'desc')
                                ->first();
                            $response_time = $contract_priorities->response_time;
                            $service_window = $contract_priorities->service_window;
                            $response_time_type = $contract_priorities->response_time_type;
                            $service_window_type = $contract_priorities->service_window_type;
                            $priority_id = $contract_priorities->id;
                        }
                    } else {
                        $wo_priority_id = $wo->sla_service_window_priority != 0 ? $wo->sla_service_window_priority : $priority_id;
                        $wo_priority_id = $wo->work_order_priority_id ? $wo->work_order_priority_id : $wo_priority_id;
                        $contract_priorities = DB::table('priorities')
                            ->where('id', $wo_priority_id)
                            ->orderBy('id', 'desc')
                            ->first();
                        $response_time = $contract_priorities->response_time;
                        $service_window = $contract_priorities->service_window;
                        $response_time_type = $contract_priorities->response_time_type;
                        $service_window_type = $contract_priorities->service_window_type;
                        $priority_id = $contract_priorities->id;
                    }
                }
                elseif($wo->work_order_type == "preventive" && $wo->priority_id != 0)
                {
                    $contract_priorities = DB::table('contract_priorities')
                        ->where('priority_id', $wo->priority_id)
                        ->where('contract_number', $wo->contract_number)
                        ->orderBy('id', 'desc')
                        ->first();
                    $response_time = $contract_priorities->response_time;
                    $service_window = $contract_priorities->service_window;
                    $response_time_type = $contract_priorities->response_time_type;
                    $service_window_type = $contract_priorities->service_window_type;
                    $priority_id = $contract_priorities->priority_id;
                }
                else
                {
                    $contract_frequencies = DB::table('frequencies_master')
                            ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                            ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                            ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                            ->where('frequencies_master.id', $wo->frequency_id)
                            ->where('contract_frequencies.contract_number', $wo->contract_number)
                            ->first();
                        $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
                        $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : 0;
                        $response_time_type = isset($contract_priorities->response_time_type)?$contract_priorities->response_time_type:'hours';
                        $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
                }
                $created_at = $wo->created_at;
                $tdate = date('Y-m-d H:i:s');
                $datetime1 = strtotime($created_at);
                $datetime2 = strtotime($tdate);
                $interval  = abs($datetime2 - $datetime1);
                $minutes   = round($interval / 60);
                if($response_time_type == "days")
                {
                    $response_time = $response_time * 1440;
                }
                elseif($response_time_type == "hours")
                {
                    $response_time = $response_time * 60;
                }
                $time_left = $response_time - $minutes;

                if($wo->job_started_at == NULL || $wo->bm_approve_issue == 2)
                {
                    $target_date = $wo->target_date;
                }
                else
                {
                    $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($wo->job_started_at)));

                }
                if($wo->job_started_at != '' && $wo->status != 4 && $wo->pass_fail == 'pending')
                {
                    if(trim($wo->pause_time_spent_minutes) != "" && $wo->pause_time_spent_minutes > 0)
                    {
                        $newDateTime = Carbon::now()->subMinutes($wo->pause_time_spent_minutes);
                        $newDateTime = $newDateTime->format('Y-m-d H:i:s');
                    }
                    else
                    {
                        $newDateTime = date('Y-m-d H:i:s');
                    }

                    if(strtotime($target_date) >= strtotime($newDateTime))
                    {
                        $data['pass_fail'] = 'pass';
                    }
                    else
                    {
                        $data['pass_fail'] = 'fail';
                    }
                    $data['sla_service_window'] = $service_window;
                    $data['service_window_type'] = $service_window_type;
                    $data['sla_service_window_priority'] = $priority_id;
                }
            }
            // If Building manager  want to approve :: bm_approve = 1 and sp admin does not wants to approve sp_approove = 0
            elseif($request->bm_approove == 1 && $request->sp_approove == 0 )
            {
                $work_order_id = $request->work_order_id;
                $data['bm_approve_job'] = 0;
                $data['sp_approve_job'] = 2;
                $data['workorder_journey'] = 'job_approval';
                $data['reason'] = '';
                $data['job_submitted_at'] = date('Y-m-d H:i:s');
            }
        } elseif($complete_job == "missing_spare_parts") {
            $work_order_id = $request->work_order_id;
            // Retrieve and parse stored item ids and quantities from hidden inputs
            $storedItemIds = explode(',', $request->input('stored_missing_item_id'));
            $storedQuantities = explode(',', $request->input('stored_missing_quantity'));

            // Create a work order item request
            $workOrderItemRequest = ServiceProviderMissingItemRequest::create([
                'work_order_id' => $request->work_order_id,
                'user_id' => Auth::user()->id,
                'status' => 'requested'
            ]);

            // Store each requested item in the ServiceProvieRequestedMissingItem table
            foreach ($storedItemIds as $index => $itemId) {
                ServiceProvieRequestedMissingItem::create([
                    'request_id' => $workOrderItemRequest->id,
                    'item_id' => $itemId,
                    'quantity' => $storedQuantities[$index],
                ]);
            }

            $data['requested_missing_spare_parts'] = 1;
            $data['examine_button_clicked_at'] = now();
        }
        else
        {
            $worker_id = $request->worker_id;
            $proposed_new_date = $request->proposed_new_date;
            //$proposed_new_date = date('Y-m-d H:i:s');
            $work_order_id = $request->work_order_id;
            if($complete_job == "propose_new_date") //If there is a proposed date
            {
                $data['proposed_new_date'] = date('Y-m-d H:i:s', strtotime($proposed_new_date));
                $data['bm_approve_issue'] = 0;
                $data['status'] = 1;
                $data['workorder_journey'] = 'submitted';
                $data['job_completed_by'] = "";
                $data['reason'] = $request->reason;
            }
            else
            {
                // re assign worker
                $data['worker_id'] = $worker_id;
                $data['assign_type'] = AssignType::Normal->value;
                $data['assigned_to'] = 'sp_worker';// @flip1@ update assigned to
                $data['worker_for_responsible'] = auth()->user()->id;// @flip1@ update assigned to
            }

        }
        $notification_sub_type = '';
        $additional_param = '';
        $work_order=DB::table('work_orders')->where('id', $work_order_id)->first();
        $data['old_worker_id']=$work_order->worker_id??"";
        DB::table('work_orders')
                ->where('id', $work_order_id)
                ->update($data);

        $wo = DB::table('work_orders')->select('work_orders.unique_id','work_orders.service_provider_id','property_buildings.building_name', 'work_orders.id','work_orders.work_order_id','work_orders.work_order_type','work_orders.property_id', 'work_orders.old_worker_id','work_orders.created_by')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.id', $work_order_id)->first();
        if($complete_job == "job_completed")
        {
            $image_array = [];
            if(($request->TotalFiles) && $request->TotalFiles > 0) //If Total Files and Total Files greater than zero
            {
                // if(!Storage::disk('public')->exists('actions')) {
                //     Storage::disk('public')->makeDirectory('actions');
                // }
                for ($x = 0; $x < $request->TotalFiles; $x++)
                {
                   if ($request->hasFile('file_upload'.$x))
                    {
                        $file = $request->file('file_upload'.$x);
                        $mimeType = $file->getClientMimeType();
                        if (str_contains($mimeType, 'image/')) {
                            //compressed file code

                            $imageName = ImagesUploadHelper::compressedImage($file, 'actions');
                            array_push($image_array,$imageName);
                        }
                        else {
                            array_push($image_array, Storage::disk('public')->put('actions', $file));
                        }
                    }
                }
                $nochecklistdata = array(
                    'work_order_id' =>$work_order_id,
                    'worker_id' => auth::user()->id,
                    'photos' => json_encode($image_array),
                    'comment' => $request->sp_comment,
                    'feedback_options' => '',
                    'checklist_task_id' => 0,
                    'is_reopen_wo' => 0,
                    'created_at' => date('Y-m-d H:i:s')
                );
                DB::table('no_checklist_actions')->where(['work_order_id' => $work_order_id, 'checklist_task_id' => 0, 'is_reopen_wo' => 0])->delete();
                DB::table('no_checklist_actions')->insert($nochecklistdata);
            }
            // @flip@ change timeline message
            // $message = 'Service Provider marked workorder '.$wo->building_name.' '.$wo->work_order_id.' as completed on behalf of worker';
            // $message_ar = 'مقدم الخدمة وضع حالة أمر العمل '.$wo->building_name.' '.$wo->work_order_id.'  مكتمل بالنيابة عن العامل';
            $message = 'Service Provider <strong>'.Auth::user()->name.'</strong> marked work order as completed on behalf of Worker';
            $message_ar = 'مقدم الخدمة <strong>'.Auth::user()->name.'</strong> غيّر حالة أمر العمل إلى مكتمل بالنيابة عن العامل';
            $notification_sub_type = 'sp_has_marked_wo_completed';
            $additional_param = Auth::user()->name;
        }
        elseif($complete_job == "missing_spare_parts") {
            // Notifications will be added

            $message = __('work_order.notifications.SP_doesnt_own_warehouse_and_waiting_project_owner', ['name' => auth()->user()->name], 'en');
            $message_ar = __('work_order.notifications.SP_doesnt_own_warehouse_and_waiting_project_owner', ['name' => auth()->user()->name], 'ar');

            $notification_sub_type = 'sent_to_project_owner';
        }
        else
        {
            if($complete_job == "propose_new_date") //Has proposed date
            {
                $message = "Service Provider <strong>".Auth::user()->name."</strong> has suggested new target date of the work order to <strong>".date('d/m/Y h:i A', strtotime($proposed_new_date))."</strong> due to <strong>{$request->reason}</strong>, pending for building manager approval";
                $message_ar = "مقدم الخدمة <strong>".Auth::user()->name."</strong> قام باقتراح تاريخ مستهدف جديد لأمر العمل إلى <strong>".date('d/m/Y h:i A', strtotime($proposed_new_date))."</strong> بسبب <strong>{$request->reason}</strong>، بانتظار اعتماد مدير المبنى";

                $notification_sub_type = 'sp_has_edited_target_date';
            }
            else
            {
                // $wr = DB::table('users')->where('users.id', $request->worker_id)->first();
                // $old_worker=DB::table('users')->where('id',$wo->old_worker_id??'')->first();;
                // dd($wr);
                // $message = 'Service Provider changed workorder '.$wo->building_name.' '.$wo->work_order_id.' to '.$wr->name;
                // $message_ar = 'مقدم الخدمة عين  العامل '.$wr->name.' بدلاً من '.$wr->name.' على أمر العمل '.$wo->building_name.' '.$wo->work_order_id;

                // // @flip@ change timeline | 6-SP changes the worker (Job execution)
                // if(isset($old_worker) && $old_worker->id!=$wr->id){
                //     $message = "Service Provider assigned the work order to the worker <strong>{$wr->name}</strong> in place of the worker <strong>".($old_worker->name??'')."</strong>";
                // $message_ar = "مقدم الخدمة عين العامل <strong>{$wr->name}</strong> بدلاً من <strong>".($old_worker->name??'')."</strong> على أمر العمل";
                // }else{
                //     $message = "Service Provider assigned the work order to the worker <strong>{$wr->name}</strong>";
                //     $message_ar = "مقدم الخدمة عين العامل <strong>{$wr->name}</strong> على أمر العمل";
                // }
                // notification for start on behalf
                if($request->complete_job == "job_started_on_behalf"){
                    $message = 'Service Provider <strong>'.Auth::user()->name.'</strong> marked work order as started on behalf of Worker';
                    $message_ar = 'مقدم الخدمة <strong>'.Auth::user()->name.'</strong> تم وضع علامة على أمر العمل على أنه بدأ نيابة عن العامل';
                }

                // @flip1@ change message for new worker assign || 6-SP changes the worker (Job execution)
               else if(intval($request->worker_id) != intval($wo->old_worker_id)){
                    $wr = DB::table('users')->where('users.id', $request->worker_id)->first();
                    $old_worker=DB::table('users')->where('users.id',$wo->old_worker_id)->first();
                    // @flip1@ change message if new worker and old worker are same|| 6-SP changes the worker (Job execution)
                    if(isset($old_worker))
                    {
                        $message = "Service Provider <strong>".Auth::user()->name."</strong> assigned the work order to the worker <strong>".$wr->name."</strong> in place of the worker <strong>".$old_worker->name."</strong>";
                        $message_ar = "مقدم الخدمة <strong>".Auth::user()->name."</strong> عين العامل <strong>".$wr->name."</strong> بدلاً من العامل <strong>".$old_worker->name."</strong> على أمر العمل";
                    }
                    else
                    {
                        $message = "Service Provider assigned the work order to the worker <strong>".$wr->name."</strong>";
                        $message_ar = "مقدم الخدمة عين العامل <strong>".$wr->name."</strong> على أمر العمل";
                    }
                }else{
                    $wr = DB::table('users')->where('users.id', $request->worker_id)->first();
                    // @flip1@ change message if new worker and old worker are same|| 6-SP changes the worker (Job execution)
                    $message = "Service Provider assigned the work order to the worker <strong>".$wr->name."</strong>";
                    $message_ar = "مقدم الخدمة عين العامل <strong>".$wr->name."</strong> على أمر العمل";
                }

                $notification_sub_type = 'sp_has_changed_workorder';
            }
        }
        //Insert for notification
        DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id, 'notification_sub_type'=>$notification_sub_type,'additional_param'=>$additional_param, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));
        //Insert for Timeline
        DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id, 'notification_sub_type'=>$notification_sub_type,'additional_param'=>$additional_param, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));

        if($complete_job == "missing_spare_parts") {
            //Move Workorder on pause status due to pending spare parts request
            WorkOrders::where('id', $work_order_id)->update(['status' => 3,'pause_start_time' => now(),'pause_time_spent' => "",'pause_time_spent_minutes' => "",'pause_end_time' => NULL]);
            $pause_message = __('work_order.notifications.pause_due_to_approval_of_spare_part_request', ['workorder_id' => $wo->work_order_id], 'en');
            $pause_message_ar = __('work_order.notifications.pause_due_to_approval_of_spare_part_request', ['workorder_id' => $wo->work_order_id], 'ar');
            NotificationHelper::sendPauseWorkOrderNotificationplatform(Auth::user(), $wo, $pause_message, $pause_message_ar, 'pause_workorder');
        }

        $bmdetails = User::select('name')->where('id',$wo->created_by)->first();
        if(isset($bmdetails))
        {
            $bm_name = $bmdetails->name;
        }
        else
        {
            $bm_name = '';
        }

        $spdetails = User::select('name')->where('id',$wo->service_provider_id)->first();
        if(isset($spdetails))
        {
            $sp_name = $spdetails->name;
        }
        else
        {
            $sp_name = '';
        }

        if($complete_job == "job_completed")
        {
            if($request->bm_approove == 0 && $request->sp_approove == 0)
            {
                // @flip1@ change to field bold | 21-SP skip approval [PM]
                $message = 'Service Provider <strong>'.$sp_name.'</strong> has <strong><i>automatically</i></strong> approved the work order, pending for Building Manager approval';
                $message_ar = 'مقدم الخدمة <strong>'.$sp_name.'</strong> اعتمد أمر العمل <strong><i>تلقائيا،</i></strong> بانتظار اعتماد مدير المبنى';
                //Insert for notification
                DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order','building_ids' => $wo->property_id, 'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s'),'is_timeline' => 'no'));

                //Insert for timeline
                DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order','building_ids' => $wo->property_id, 'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s'),'is_timeline' => 'yes'));


                // @flip1@ change to field bold | 22-BM skip approval [PM]
                $message = 'Building Manager <strong>'.$bm_name.'</strong> has <strong><i>automatically</i></strong> approved the work order';
                $message_ar = 'مدير المبنى <strong>'.$bm_name.'</strong> اعتمد أمر العمل <strong><i>تلقائيا</i></strong>';
                $notification_sub_type = 'bm_has_automatically_approved_wo';
                 //Insert for notification
                DB::table('notifications')->insert(array('user_id' => $wo->created_by, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id,'notification_sub_type'=> $notification_sub_type ,'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));

                 //Insert for timeline
                 DB::table('notifications')->insert(array('user_id' => $wo->created_by, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id,'notification_sub_type'=> $notification_sub_type ,'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));

            }elseif($request->bm_approove == 1 && $request->sp_approove == 0 ){
                //flip 2 @ input field to bold
                $message = 'Service Provider <strong>'.$sp_name.'</strong> has <strong><i>automatically</i></strong> approved the work order, pending for Building Manager approval';
                $message_ar = 'مقدم الخدمة <strong>'.$sp_name.'</strong> اعتمد أمر العمل <strong><i>تلقائيا،</i></strong> بانتظار اعتماد مدير المبنى';

                $notification_sub_type = 'sp_has_automatically_approved_wo';
                 //Insert for notification
                DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id,'notification_sub_type'=> $notification_sub_type ,'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));
                //Insert for timeline
                DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id,'notification_sub_type'=> $notification_sub_type ,'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));

            }
            elseif($request->bm_approove == 0 && $request->sp_approove == 1)
            {
                // $message = 'Building Manager <strong>'.$bm_name.'</strong> has <strong><i>automatically</i></strong> approved the work order';
                // $message_ar = 'مدير المبنى <strong>'.$bm_name.'</strong> اعتمد أمر العمل <strong><i>تلقائيا</i></strong>';
                // $notification_sub_type = 'bm_has_automatically_approved_wo';
                // //dd($message_ar);
                // DB::table('notifications')->insert(array('user_id' => $wo->created_by, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id,'notification_sub_type'=> $notification_sub_type ,'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s')));
            }
        }

        //$work_order_number = $wo->id;
        // Save in Work Order Email Table
       /* $data = array(
            'wo_id'=> $work_order_number,
            'work_order_type'=>$wo->work_order_type,
            'status'=>'WO-Status-Updated-Third',
            'created_at'=>date('Y-m-d H:i:s'),
        );
        DB::table('work_orders_email_jobs')->insert($data);*/

        $workOrdersArray = [
            'work_order_id' => $wo->work_order_id,
            'wo_id' => $wo->id,
            'work_order_type' => $wo->work_order_type,
            'status' => 'WO-Status-Updated-Third',
            'created_at' => $this->getCurrentDateTime()
        ];

        $saveEmailJob = $this->saveWorkOrderEmailJob($workOrdersArray);		
						
        if(!$saveEmailJob){
            Log::info('complete_job_by_sp error: Issue when saving data inside work_orders_email_jobs table!');
        }

        return $complete_job;
    }

    /**
     * POST workorder/approve-job-by-sp
     *
     * Service provider(SP) will approve the job completed by the worker also he can reject the work done.
     *
     * @authenticated
     * @group workorder
     *
     * @response 200
     */
    public function approve_job_by_sp(Request $request)
    {
        $work_order_id = $request->work_order_id;
        $user_id = auth()->user()->id;
        $approve_job = $request->approve_job;
        $work_order_id = $request->work_order_id;
        $worker_id = $request->worker_id;
        $mail_triggered = isset($request->mail_triggered) ? $request->mail_triggered : false;
        $data = [];
        $data['cost']=$request->cost;
        $old_wo = DB::table('work_orders')->select('property_buildings.building_name', 'work_orders.property_id','work_orders.created_by','work_orders.service_provider_id','work_orders.description','work_orders.id', 'work_orders.work_order_id', 'work_orders.worker_id','work_orders.closed_by','work_orders.old_worker_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.id', $work_order_id)->first();
        if($approve_job == "2") //If job is approved
        {
            
            // If Building manager does not want to approve :: bm_approve = 0
            if($request->bm_approove == 0 )
            {

                $fetchMaterialCostCalculation = WorkorderHelper::calculateMaterialTotalCost($work_order_id);
            
            $data['total_item_price'] = $fetchMaterialCostCalculation['total_sale_price'];
            $data['total_item_price_with_vat'] = $fetchMaterialCostCalculation['total_sale_price_with_vat'];
                $workorder_journey = 'finished';
                $work_order_id = $request->work_order_id;
                $data['status'] = 4;
                $data['workorder_journey'] = $workorder_journey;
                $data['job_completed_by'] = "SP";
                $data['rating'] = '';
                $data['score'] = '';
                $data['building_manager_comment']='';
                $data['job_completion_date'] = date('Y-m-d H:i:s');
                $data['sp_approve_job'] = "0";
                $data['bm_approve_job'] = "0";
                $data['Job_approved_by_sp_at'] = date('Y-m-d H:i:s');

                $wo = DB::table('work_orders')->select('work_orders.*', 'contract_priorities.priority_id', 'work_orders.priority_id as work_order_priority_id', 'contracts.contract_number')->leftjoin('contract_asset_categories', 'contract_asset_categories.asset_category_id', '=', 'work_orders.asset_category_id')->leftjoin('contract_priorities', 'contract_priorities.priority_id', '=', 'contract_asset_categories.priority_id')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->where('work_orders.id', $work_order_id)->first();
                $sla_asset_categories = DB::table('contract_asset_categories')
                      ->where('asset_category_id', $wo->asset_category_id)
                      ->where('contract_number', $wo->contract_number)
                      ->orderBy('id', 'desc')
                      ->first();

                $contract = Contracts::where('id', $wo->contract_id)
                    ->where('is_deleted', 'no')
                    ->select('contract_number', 'contract_type_id')
                    ->first();

                 $contractTypeId = $contract->contract_type_id;

                $response_time = 0;
                $service_window = 0;
                $response_time_type = 'hours';
                $service_window_type = 'minutes';
                $priority_id = $wo->priority_id ?? $wo->work_order_priority_id;
                if($wo->work_order_type == "reactive")
                {
                    if(in_array($contractTypeId, ContractTypes::advanced()))
                    {
                        $pId = $priority_id;
                    }
                    else
                    {
                        $pId = $sla_asset_categories->priority_id;
                    }
                    if(!empty($sla_asset_categories->priority_id)){
                        $contract_priorities = DB::table('contract_priorities')
                            ->where('priority_id', $pId)
                            ->where('contract_number', $wo->contract_number)
                            ->orderBy('id', 'desc')
                            ->first();
                        if($contract_priorities) {
                            $response_time = $contract_priorities->response_time;
                            $service_window = $contract_priorities->service_window;
                            $response_time_type = $contract_priorities->response_time_type;
                            $service_window_type = $contract_priorities->service_window_type;
                            $priority_id = $contract_priorities->priority_id;
                        } else {
                            $contract_priorities = DB::table('priorities')
                                ->where('id', $sla_asset_categories->priority_id)
                                ->orderBy('id', 'desc')
                                ->first();
                            $response_time = $contract_priorities->response_time;
                            $service_window = $contract_priorities->service_window;
                            $response_time_type = $contract_priorities->response_time_type;
                            $service_window_type = $contract_priorities->service_window_type;
                            $priority_id = $contract_priorities->id;
                        }
                    } else {
                        $contract_priorities = DB::table('priorities')
                            ->where('id', $priority_id)
                            ->orderBy('id', 'desc')
                            ->first();
                        $response_time = $contract_priorities->response_time;
                        $service_window = $contract_priorities->service_window;
                        $response_time_type = $contract_priorities->response_time_type;
                        $service_window_type = $contract_priorities->service_window_type;
                        $priority_id = $contract_priorities->id;
                    }
                }
                elseif($wo->work_order_type == "preventive" && $wo->priority_id != 0)
                {
                    $contract_priorities = DB::table('contract_priorities')
                        ->where('priority_id', $wo->priority_id)
                        ->where('contract_number', $wo->contract_number)
                        ->orderBy('id', 'desc')
                        ->first();
                    if($contract_priorities) {
                        $response_time = $contract_priorities->response_time;
                        $service_window = $contract_priorities->service_window;
                        $response_time_type = $contract_priorities->response_time_type;
                        $service_window_type = $contract_priorities->service_window_type;
                        $priority_id = $contract_priorities->priority_id;
                    } else {
                        $contract_priorities = DB::table('priorities')
                            ->where('id', $priority_id)
                            ->orderBy('id', 'desc')
                            ->first();
                        $response_time = $contract_priorities->response_time;
                        $service_window = $contract_priorities->service_window;
                        $response_time_type = $contract_priorities->response_time_type;
                        $service_window_type = $contract_priorities->service_window_type;
                        $priority_id = $contract_priorities->id;
                    }
                }
                else
                {
                    $contract_frequencies = DB::table('frequencies_master')
                            ->select('contract_frequencies.response_time', 'contract_frequencies.service_window','contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                            ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                            ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                            ->where('frequencies_master.id', $wo->frequency_id)
                            ->where('contract_frequencies.contract_number', $wo->contract_number)
                            ->first();
                        $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
                        $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : 0;
                        $response_time_type = isset($contract_priorities->response_time_type)?$contract_priorities->response_time_type:'hours';
                        $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
                }
                $created_at = $wo->created_at;
                $tdate = date('Y-m-d H:i:s');
                $datetime1 = strtotime($created_at);
                $datetime2 = strtotime($tdate);
                $interval  = abs($datetime2 - $datetime1);
                $minutes   = round($interval / 60);
                if($response_time_type == "days")
                {
                    $response_time = $response_time * 1440;
                }
                elseif($response_time_type == "hours")
                {
                    $response_time = $response_time * 60;
                }
                $time_left = $response_time - $minutes;

                if($wo->job_started_at == NULL || $wo->bm_approve_issue == 2)
                {
                    $target_date = $wo->target_date;
                }
                else
                {
                    $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($wo->job_started_at)));
                }
                if($wo->job_started_at != '' && $wo->status != 4 && $wo->pass_fail == 'pending')
                {
                    if(trim($wo->pause_time_spent_minutes) != "" && $wo->pause_time_spent_minutes > 0)
                    {
                        $newDateTime = Carbon::now()->subMinutes($wo->pause_time_spent_minutes);
                        $newDateTime = $newDateTime->format('Y-m-d H:i:s');
                    }
                    else
                    {
                        $newDateTime = date('Y-m-d H:i:s');
                    }

                    if(strtotime($target_date) >= strtotime($newDateTime))
                    {
                        $data['pass_fail'] = 'pass';
                        $data['mark_penalty_candidate'] = 0;
                    }
                    else
                    {
                        $data['pass_fail'] = 'fail';
                        $data['mark_penalty_candidate'] = 1;
                    }
                    $data['sla_service_window'] = $service_window;
                    $data['service_window_type'] = $service_window_type;
                    $data['sla_service_window_priority'] = $priority_id;
                }
            }
            else
            {
                $data['bm_approve_job'] = 0;
                $data['sp_approve_job'] = $approve_job;
                $data['workorder_journey'] = 'job_approval';
                $data['reason'] = $request->back_reason;
                if(!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee'))
                {
                    //If user type is building_manager or building_manager_employee
                    //dd('stop');
                    $fetchMaterialCostCalculation = WorkorderHelper::calculateMaterialTotalCost($work_order_id);
            
                    $data['total_item_price'] = $fetchMaterialCostCalculation['total_sale_price'];
                    $data['total_item_price_with_vat'] = $fetchMaterialCostCalculation['total_sale_price_with_vat'];
                    $data['bm_approve_job'] = $approve_job;
                    $data['rating'] = $request->rating;
                    $data['uniform_specified_by_authority'] = $request->uniform_specified_by_authority;
                    $data['extent_of_cleanliness'] = $request->extent_of_cleanliness;
                    $data['safety_procedure'] = $request->safety_procedure;
                    $data['estimated_price'] = $request->estimated_price;
                    $data['status'] = 4;
                    $data['workorder_journey'] = 'finished';
                    $data['closed_by'] = $user_id;
                    $data['job_completion_date'] = date('Y-m-d H:i:s');
                    $data['building_manager_comment']=!empty($request->building_manager_feedback) ? $request->building_manager_feedback:'';
                    $wo = DB::table('work_orders')->select('property_buildings.building_name', 'work_orders.*')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.id', $work_order_id)->first();

                    if(!empty($wo->maintanance_request_id)) { //If has maintanance_request_id
                        $maintanance_data = [
                            'status' => 'Completed'
                        ];
                        Helper::updateMaintenanceRequest($maintanance_data,$wo->maintanance_request_id);
                        $maintanance_id = $wo->maintanance_request_id;
                        $details = MaintenanceRequest::where('id',$wo->maintanance_request_id)->first();
                        $total_emails[]               = $details->email;
                        //$total_emails[]               = Helper::getTheBuildingManager($user->id);
                        //@flip1@ use try catch
                        try {
                            event(new MaintananceRequestCreated($total_emails,$maintanance_id));
                        } catch (\Throwable $th) {
                            //throw $th;
                        }
                    }
                    $score = 0;
                    if($wo->response_time == "On time") //If response_time is On time
                    {
                        $score = $score + 0.025;
                    }
                    elseif($wo->response_time == "Late") //If response_time is Late
                    {
                        $score = $score + 0;
                    }
                    if($wo->pass_fail == "pass") //If pass_fail is pass
                    {
                        $score = $score + 0.1;
                    }
                    elseif($wo->pass_fail == "fail") //If pass_fail is fail
                    {
                        $score = $score - 0.01;
                    }
                    if($wo->reopen == "no") //If reopen is no
                    {
                        $score = $score + 0;
                    }
                    else
                    {
                        $score = $score - 0.025;
                    }
                    //echo $request->rating;exit;
                    switch ($request->rating){
                        case 5:
                            $score = $score + 0.025;
                        break;
                        case 4:
                            $score = $score + 0.02;
                        break;
                        case 3:
                            $score = $score + 0.015;
                        break;
                        case 2:
                            $score = $score + 0.01;
                        break;
                        case 1:
                            $score = $score + 0.005;
                        break;
                    }
                    $score = $score/0.015;
                    //echo round($score, 2);exit;
                    $score = round($score, 2);
                    if($score < 0)
                    {
                        $score = 0;
                    }
                    $data['score'] = $score;
                }
                else
                {
                    $data['Job_approved_by_sp_at'] = date('Y-m-d H:i:s');

                    $wo = DB::table('work_orders')->select('work_orders.*', 'contract_priorities.priority_id', 'work_orders.priority_id as work_order_priority_id', 'contracts.contract_number')->leftjoin('contract_asset_categories', 'contract_asset_categories.asset_category_id', '=', 'work_orders.asset_category_id')->leftjoin('contract_priorities', 'contract_priorities.priority_id', '=', 'contract_asset_categories.priority_id')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->where('work_orders.id', $work_order_id)->first();

                    
                    $contract = Contracts::where('id', $wo->contract_id)
                        ->where('is_deleted', 'no')
                        ->select('contract_number', 'contract_type_id')
                        ->first();

                    $contractTypeId = $contract->contract_type_id;
                    
                    $sla_asset_categories = DB::table('contract_asset_categories')
                        ->where('asset_category_id', $wo->asset_category_id)
                        ->where('contract_number', $wo->contract_number)
                        ->orderBy('id', 'desc')
                        ->first();

                    $response_time = 0;
                    $service_window = 0;
                    $response_time_type = 'hours';
                    $service_window_type = 'minutes';
                    $priority_id = $wo->priority_id ?? $wo->work_order_priority_id;
                    if($wo->work_order_type == "reactive")
                    {
                        if(!empty($sla_asset_categories->priority_id) || (in_array($contractTypeId, ContractTypes::advanced()))){
                            if(in_array($contractTypeId, ContractTypes::advanced()))
                            {
                                $pId = $priority_id;
                            }
                            else
                            {
                                $pId = $sla_asset_categories->priority_id;
                            }
                            $contract_priorities = DB::table('contract_priorities')
                                ->where('priority_id', $pId)
                                ->where('contract_number', $wo->contract_number)
                                ->orderBy('id', 'desc')
                                ->first();
                            if($contract_priorities) {
                                $response_time = $contract_priorities->response_time;
                                $service_window = $contract_priorities->service_window;
                                $response_time_type = $contract_priorities->response_time_type;
                                $service_window_type = $contract_priorities->service_window_type;
                                $priority_id = $contract_priorities->priority_id;
                            } else {
                                $contract_priorities = DB::table('priorities')
                                    ->where('id', $pId)
                                    ->orderBy('id', 'desc')
                                    ->first();
                                $response_time = $contract_priorities->response_time;
                                $service_window = $contract_priorities->service_window;
                                $response_time_type = $contract_priorities->response_time_type;
                                $service_window_type = $contract_priorities->service_window_type;
                                $priority_id = $contract_priorities->id;
                            }
                        } else {
                            $contract_priorities = DB::table('priorities')
                                ->where('id', $priority_id)
                                ->orderBy('id', 'desc')
                                ->first();
                            $response_time = $contract_priorities->response_time;
                            $service_window = $contract_priorities->service_window;
                            $response_time_type = $contract_priorities->response_time_type;
                            $service_window_type = $contract_priorities->service_window_type;
                            $priority_id = $contract_priorities->id;
                        }
                    }
                    elseif($wo->work_order_type == "preventive" && $wo->priority_id != 0)
                    {
                        $contract_priorities = DB::table('contract_priorities')
                            ->where('priority_id', $wo->priority_id)
                            ->where('contract_number', $wo->contract_number)
                            ->orderBy('id', 'desc')
                            ->first();
                        if(isset($contract_priorities))
                        {
                            $response_time = $contract_priorities->response_time;
                            $service_window = $contract_priorities->service_window;
                            $response_time_type = $contract_priorities->response_time_type;
                            $service_window_type = $contract_priorities->service_window_type;
                            $priority_id = $contract_priorities->priority_id;
                        }
                    }
                    else
                    {
                        $contract_frequencies = DB::table('frequencies_master')
                                ->select('contract_frequencies.response_time', 'contract_frequencies.service_window','contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                                ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                                ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                                ->where('frequencies_master.id', $wo->frequency_id)
                                ->where('contract_frequencies.contract_number', $wo->contract_number)
                                ->first();
                            $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
                            $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : 0;
                            $response_time_type = isset($contract_priorities->response_time_type)?$contract_priorities->response_time_type:'hours';
                            $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
                    }
                    $created_at = $wo->created_at;
                    $tdate = date('Y-m-d H:i:s');
                    $datetime1 = strtotime($created_at);
                    $datetime2 = strtotime($tdate);
                    $interval  = abs($datetime2 - $datetime1);
                    $minutes   = round($interval / 60);
                    if($response_time_type == "days")
                    {
                        $response_time = $response_time * 1440;
                    }
                    elseif($response_time_type == "hours")
                    {
                        $response_time = $response_time * 60;
                    }
                    $time_left = $response_time - $minutes;

                    if($wo->job_started_at == NULL || $wo->bm_approve_issue == 2)
                    {
                        $target_date = $wo->target_date;
                    }
                    else
                    {
                        $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($wo->job_started_at)));
                    }
                    if($wo->job_started_at != '' && $wo->status != 4 && $wo->pass_fail == 'pending')
                    {
                        if(trim($wo->pause_time_spent_minutes) != "" && $wo->pause_time_spent_minutes > 0)
                        {
                            $newDateTime = Carbon::now()->subMinutes($wo->pause_time_spent_minutes);
                            $newDateTime = $newDateTime->format('Y-m-d H:i:s');
                        }
                        else
                        {
                            $newDateTime = date('Y-m-d H:i:s');
                        }
                        if(strtotime($target_date) >= strtotime($newDateTime))
                        {
                            $data['pass_fail'] = 'pass';
                            $data['mark_penalty_candidate'] = 0;
                        }
                        else
                        {
                            $data['pass_fail'] = 'fail';
                            $data['mark_penalty_candidate'] = 1;
                        }
                        $data['sla_service_window'] = $service_window;
                        $data['service_window_type'] = $service_window_type;
                        $data['sla_service_window_priority'] = $priority_id;
                    }
                }
            }
        }
        else
        {
            // Reject case by BM
            if(!isset($worker_id))
            {
                $worker_id = $request->old_worker_id;
            }
            $work_order_id = $request->work_order_id;
            $data['reason'] = $request->reason;
            $data['workorder_journey'] = 'job_execution';
            $data['worker_id'] = $worker_id;
            $data['sp_approve_job'] = 1;
            if(!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee'))
            { //If user type is building_manager or building_manager_employee
                $data['workorder_journey'] = 'job_evaluation';
                $data['reason'] = $request->reason;
                $data['bm_approve_job'] = 1;
                $data['sp_approve_job'] = 0;
            }
            $data['worker_started_at'] = NULL;
            $data['job_submitted_at'] = NULL;
            DB::table('no_checklist_actions')
                ->where('work_order_id', $work_order_id)
                ->where('is_reopen_wo', 0)
                ->delete();
        }
        DB::table('work_orders')
                ->where('id', $work_order_id)
                ->where('status', '!=', 4)
                ->update($data);
                if (isset($data['workorder_journey']) && $data['workorder_journey'] == 'finished') {
               $WOTasksTrait = (new class { use \App\Http\Traits\CRMProjects\WOTasksTrait; });
                $checkTangibleData= $WOTasksTrait->markAsCompleted($work_order_id);
                }
        $notification_sub_type = '';

        $wo = DB::table('work_orders')->select('property_buildings.building_name', 'work_orders.property_id','work_orders.created_by','work_orders.service_provider_id','work_orders.description','work_orders.id', 'work_orders.work_order_id', 'work_orders.worker_id','work_orders.closed_by','work_orders.old_worker_id','work_orders.is_handle_by_team_leader','work_orders.team_leader_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.id', $work_order_id)->first();
        $closedBMId = $wo->created_by;

        if (!empty(Auth::user()->user_type) && in_array(Auth::user()->user_type, ['building_manager', 'building_manager_employee'])) {
            $closedBMId = Auth::user()->id;
        }

        if($wo->closed_by != 0) {
            $closedBMId = $wo->closed_by;
        }
        $bmdetails = User::select('name')->where('id',$closedBMId)->first();
        if(isset($bmdetails))
        {
            $bm_name = $bmdetails->name;
        }
        else
        {
            $bm_name = '';
        }

        $spdetails = User::select('name')->where('id',$wo->service_provider_id)->first();
        if(isset($spdetails))
        {
            $sp_name = $spdetails->name;
        }
        else
        {
            $sp_name = '';
        }
        if($approve_job == "2") //If work order is approved
        {
            $message = 'Service provider <strong>'.Auth::user()->name.'</strong> approved the work order, pending for Building Manager approval';
            $message_ar = 'مقدم الخدمة <strong>'.Auth::user()->name.'</strong> اعتمد أمر العمل، بانتظار اعتماد مدير المبنى';
            $notification_sub_type = 'sp_has_approved_wo';
            if(!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee'))
            { //If user type is building_manager or building_manager_employee
                // $message = ''.auth()->user()->name.' has approved and evaluated workorder '.$wo->building_name.' '.$wo->work_order_id;
                // $message_ar = ''.auth()->user()->name.' قيم وإعتمد هذا العمل '.$wo->building_name.' '.$wo->work_order_id;

                // @flip1@ due to change message
                $message = 'Building Manager <strong>'.$bm_name.'</strong> has approved and evaluated the work order';
                $message_ar = 'مدير المبنى <strong>'.$bm_name.'</strong> قيم واعتمد أمر العمل';
                    if($wo->closed_by != null){
                        $message ='Building Manager <strong>'. $bm_name.'</strong> has approved and evaluated the work order';
                        $message_ar = 'مدير المبنى ' .'<strong>'.$bm_name.'</strong>   قيم واعتمد أمر العمل';
                    }


                $notification_sub_type = 'bm_has_approved_and_evaluated_wo';
            }
        }
        else
        {
            if(!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee')) //If user type is building_manager or building_manager_employee
            {
                $message = 'Building Manager <strong>'.$bm_name.'</strong> has rejected the work order due to <strong>'.$request->reason.'</strong>, work order has been sent back to Service Provider';
                $message_ar = 'مدير المبنى <strong>'.$bm_name.'</strong> رفض أمر العمل بسبب <strong>'.$request->reason.'</strong>، تم إعادة تعيين أمر العمل إلى مقدم الخدمة';
                $notification_type = 'bm_work_order_rejected';
                $notification_sub_type = 'bm_work_order_rejected';
            }
            else
            {
                // $wr = DB::table('users')->select('users.name')->where('users.id', $request->old_worker_id)->first();
                // // $message = 'Service provider rejected '.$wr->name.' work, work order has sent back to the worker';
                // // $message_ar = 'مقدم الخدمة رفض عمل '.$wr->name.' تم إعادة تعيين العمل للعامل';

                // // @flip@ change little line in message
                // $message = "Service provider rejected <strong>{$wr->name}</strong> work, work order has sent back to the worker";
                // $message_ar = "مقدم الخدمة رفض عمل <strong>{$wr->name}</strong>، تم إعادة تعيين أمر العمل للعامل";

                // @flip1@ there is 2 worker. old/ new || 9-SP rejected worker’s work
                if($request->old_worker_id != $wo->worker_id){
                    // @flip1@ change little line in message || 9-SP rejected worker’s work
                    $nwr = DB::table('users')->select('users.name')->where('users.id', $request->old_worker_id)->first();
                    $wr = DB::table('users')->select('users.name')->where('users.id', $wo->worker_id)->first();

                    $message = "Service provider <strong>".Auth::user()->name."</strong> rejected worker <strong>{$nwr->name}</strong> work, work order has sent back to the worker <strong>{$wr->name}</strong>";
                    $message_ar = "مقدم الخدمة <strong>".Auth::user()->name."</strong> رفض عمل العامل <strong>{$nwr->name}</strong>، تم إعادة تعيين أمر العمل للعامل <strong>{$wr->name}</strong>";

                }else{
                    $wr = DB::table('users')->select('users.name')->where('users.id', $request->old_worker_id)->first();
                     // @flip1@ change little line in message || 9-SP rejected worker’s work
                     $message = "Service provider <strong>".Auth::user()->name."</strong> rejected worker <strong>{$wr->name}</strong> work, work order has sent back to the worker";
                     $message_ar = "مقدم الخدمة <strong>".Auth::user()->name."</strong> رفض عمل العامل <strong>{$wr->name}</strong>، تم إعادة تعيين أمر العمل للعامل";
                }

                $notification_type = 'sp_work_order_rejected';
                $notification_sub_type = 'sp_work_order_rejected';
                $WorkerId = $wo->is_handle_by_team_leader == 1 ? $wo->team_leader_id : $worker_id;
                $user_details = Helper::userDetail($WorkerId);
                $registration_ids = [];
                if(!empty($user_details)) {
                    $registration_ids[] = $user_details->device_token;
                    if($wo->worker_id == $old_wo->worker_id || $wo->is_handle_by_team_leader == 1)
                    {
                        if($user_details->selected_app_langugage == 'ar' || $user_details->selected_app_langugage == "ar")
                        {
                            $notification_title = "تم رفض أمر العمل";
                        }
                        elseif($user_details->selected_app_langugage == 'ur' || $user_details->selected_app_langugage == "ur")
                        {
                            $notification_title = "ورک آرڈر مسترد کر دیا گیا ہے";
                        }
                        else
                        {
                            $notification_title = "A work order has been rejected";
                        }

                        $notification_type = 'sp_work_order_rejected';
                        $notification_sub_type = 'sp_work_order_rejected';
                    }
                    else
                    {
                        // if work order assigning to new worker
                        if($user_details->selected_app_langugage == 'ar' || $user_details->selected_app_langugage == "ar")
                        {
                            $notification_title = "أمر عمل جديد تم تعيينه";
                        }
                        elseif($user_details->selected_app_langugage == 'ur' || $user_details->selected_app_langugage == "ur")
                        {
                            $notification_title = "نیا ورک آرڈر دیا گیا ہے";
                        }
                        else
                        {
                            $notification_title = "A new work order has been assigned";
                        }

                        $notification_type = 'new_wo_assigned_to_worker';
                        $notification_sub_type = 'new_wo_assigned_to_worker';
                    }

                    $messgae = array(
                        "title" => $notification_title,
                        "body" => $wo->description,
                        'section_id' => $wo->id,
                        'notification_type' => $notification_type
                    );
                    //if(count($registration_ids)>0) {
                        $res = ApiHelper::send_notification_worker_FCM($WorkerId, $registration_ids, $messgae, $wo->property_id,$notification_type,$wo->id);
                    //}
                }
            }

        }
        //$notification_sub_type = '';
        //Insert for notification

        DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids'=>$wo->property_id, 'notification_sub_type'=> $notification_sub_type, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));
        //Insert for Timeline
        DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids'=>$wo->property_id, 'notification_sub_type'=> $notification_sub_type, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));

        if($approve_job == "2") //If job is approved
        {
            // If Building manager does not want to approve :: bm_approve = 0
            if($request->bm_approove == 0 )
            {
                // flip 2 @ input fields to the bold
                $message = 'Building Manager <strong>'.$bm_name.'</strong> has <strong><i>automatically</i></strong> approved the work order';
                $message_ar = 'مدير المبنى <strong>'.$bm_name.'</strong> اعتمد أمر العمل <strong><i>تلقائيا</i></strong>';
                $notification_sub_type = 'bm_has_automatically_approved_wo';
                //Insert for notification
                DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order',
                'building_ids'=>$wo->property_id,'notification_sub_type'=>$notification_sub_type, 'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));
                //Insert for timeline
                 DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order',
                 'building_ids'=>$wo->property_id,'notification_sub_type'=>$notification_sub_type, 'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));

            }
        }
        $wo = WorkOrders::where('id',$work_order_id)->first();
        if(Auth::user()->user_type == 'building_manager' || Auth::user()->user_type == 'building_manager_employee') { //If user type is building_manager
            /*$data = array(
                'wo_id'=> $wo->id,
                'work_order_type'=>$wo->work_order_type,
                'status'=>'WO-Closed',
                'created_at'=>date('Y-m-d H:i:s'),
            );
            DB::table('work_orders_email_jobs')->insert($data);*/

            $workOrdersArray = [
                'work_order_id' => $wo->work_order_id,
                'wo_id' => $wo->id,
                'work_order_type' => $wo->work_order_type,
                'status' => 'WO-Closed',
                'created_at' => $this->getCurrentDateTime()
            ];

            $saveEmailJob = $this->saveWorkOrderEmailJob($workOrdersArray);
							
            if(!$saveEmailJob){
                Log::info('approve_job_by_sp error: Issue when saving data inside work_orders_email_jobs table!');
            }

            if($approve_job == "2")
            {
                //dd($wo->maintanance_request_id);
                $maintanance_id = $wo->maintanance_request_id;
                if(isset($maintanance_id) && $maintanance_id>0)
                {
                    $mr = DB::table('maintanance_request')->select('*')->where('id', $maintanance_id)->first();

                    if($mr->app_type == 'tenant' || $mr->generated_from == 'tenant')
                    {
                        //dont send message for tenant
                    }
                    else
                    {
                        $link = route('maintenance.task.details', $maintanance_id);
                        if (App::getLocale()=='en')
                        {
                            $message = "Hello $mr->name, your maintenance #$maintanance_id is completed, click link to see result and add your feedback $link";
                        }
                        else
                        {
                            $message = "مرحبا $mr->name, طلب الصيانة #$maintanance_id تم إكماله، لمتابعة النتيجة واضافة تقييم اضغط هنا $link";
                        }
                        // @flip1@ use try catch
                        try {
                            Helper::sendSms($message, '966'.ltrim($mr->phone, 0));
                        } catch (\Throwable $th) {
                            //throw $th;
                        }
                    }
                }
            }

        } else {
            if($mail_triggered == false) {
                /*$data = array(
                    'wo_id'=> $wo->id,
                    'work_order_type'=>$wo->work_order_type,
                    'status'=>'WO-Status-Updated-Fourth',
                    'created_at'=>date('Y-m-d H:i:s'),
                );
                DB::table('work_orders_email_jobs')->insert($data);*/

                $workOrdersArray = [
                    'work_order_id' => $wo->work_order_id,
                    'wo_id' => $wo->id,
                    'work_order_type' => $wo->work_order_type,
                    'status' => 'WO-Status-Updated-Fourth',
                    'created_at' => $this->getCurrentDateTime()
                ];

                $saveEmailJob = $this->saveWorkOrderEmailJob($workOrdersArray);
						
                if(!$saveEmailJob){
                    Log::info('approve_job_by_sp error: Issue when saving data inside work_orders_email_jobs table!');
                }
            }
        }
        $sup_ids = explode(',',($wo->supervisor_id));
        $work_order_number = $wo->work_order_id;
        $user = Auth::user();
        if ($user->user_type == 'building_manager_employee') { //If user type is building_manager_employee
            $user_ids[]               = $user->id;
            $user_ids[]               = $wo->service_provider_id;
            $user_ids[]               = Helper::getTheBuildingManager($user->id);
            $total_email_recivers_ids = array_merge($user_ids, $sup_ids);
        } else {
            $user_ids[]               = $user->id;
            $user_ids[]               = $wo->service_provider_id;
            $total_email_recivers_ids = array_merge($user_ids, $sup_ids);
        }
        if(Auth::user()->user_type == 'building_manager') { //If user type is building_manager
            // Send FCM Notification
            //$chat_created_token = ApiHelper::getAllTenantsDeviceTokensByBuilding($wo->property_id);
            $registration_ids = ApiHelper::getTenantDeviceTokensRaisedMaintenanceRequestByuserid($wo->maintanance_request_id,$wo->property_id,'tenant');
            $tenant_user_id = ApiHelper::getTenantIdRaisedMaintenanceRequestByuserid($wo->maintanance_request_id,$wo->property_id,'tenant');
            $registration_ids = array_unique($registration_ids);

            $messgae = array(
                "title" =>'تم إنهاء طلب صيانة في مجتمعك !',
                "body" => $wo->description
            );
            //if(count($registration_ids)>0) {
                $notification_type = 'work_order_closed';
                $res = ApiHelper::send_maintenance_notification_FCM($tenant_user_id, $registration_ids, $messgae, $wo->property_id,$notification_type,$wo->id,Auth::user()->id);
            //}
        }
        return "success";
    }

    /**
     * POST workorder/workorder-edit-image
     *
     * Update the workorder image
     *
     * @authenticated
     * @group workorder
     *
     * @response 200
     */
    public function workorder_edit_image(Request $request)
    {
      $logedin_user=auth()->user();
      $id=$logedin_user->id;
      $data=array();
      $work_order_id=$request->work_order_id;
      $result = '';

      /***********************for uploading profile img ***********************/
      if($request->hasFile('upload_receipt')) //If there is file upload_receipt
      {
        $image = $request->file('upload_receipt');
        $mimeType = $image->getClientMimeType();

        if (str_contains($mimeType, 'image/')) {
            $data['upload_receipt'] = ImagesUploadHelper::compressedImage($image, 'workorder');
            $result = ImagesUploadHelper::displayImage($data['upload_receipt'], 'uploads/workorder');

        } else {
            //storing files name in a variable
            $image_name = time().'.'.$image->getClientOriginalExtension();

            $destinationPath = public_path('/uploads/workorder');
            if($image->move($destinationPath, $image_name))
            {
                $data['upload_receipt']=$image_name;
                $result = 'uploads/workorder/'.$image_name;
                $result = asset($result);
            }
        }


      }
      WorkOrders::where('id', $work_order_id)->update($data);
      return response()->json(['success'=>' success','link'=>$result]);
    }

    /**
     * POST workorder/workorder-delete-image
     *
     * Deleting the workorder image
     *
     * @authenticated
     * @group workorder
     *
     * @response 200
     */
    public function workorder_delete_image2(Request $request)
    {
        $logedin_user=auth()->user();
        $id=$logedin_user->id;
        $data=array();
        $work_order_id=$request->work_order_id;
        /***********************for uploading profile img ***********************/
        $data['upload_receipt']=null;
        WorkOrders::where('id', $work_order_id)->update($data);
        return true;
    }

    /**
     * POST workorder/bm-accept-reject-reopen
     *
     * The Service provider(SP) can reject the reopen workorder reopened by the Building manager(BM). This rejection of SP again can be accepted or rejected by the BM.
     *
     * @authenticated
     * @group workorder
     *
     * @response 200
     */
    public function bm_accept_reject_reopen(Request $request)
    {
        $user_id = auth()->user()->id;
        //dd($user_id);
        $reason = $request->reason;
        // dd($reason, $request->all());
        $accept_reject_bm = $request->accept_reject_bm;
        $work_order_id = $request->work_order_id;
        $data = [];
        if($accept_reject_bm == 2) //If work order is closed
        {
            $data['status'] = 4;
            $data['workorder_journey'] = "finished";

            $wo = DB::table('work_orders')->select('property_buildings.building_name', 'work_orders.*')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.id', $work_order_id)->first();
                $score = 0;
                if($wo->response_time == "On time") //If response_time is On time
                {
                    $score = $score + 0.025;
                }
                elseif($wo->response_time == "Late") //If response_time is Late
                {
                    $score = $score + 0;
                }
                if($wo->pass_fail == "pass") //If response_time is pass
                {
                    $score = $score + 0.1;
                }
                elseif($wo->pass_fail == "fail") //If response_time is fail
                {
                    $score = $score - 0.01;
                }
                if($wo->reopen == "no") //If reopen is no
                {
                    $score = $score + 0;
                }
                else
                {
                    $score = $score - 0.025;
                }
                switch ($wo->rating){
                    case 5:
                        $score = $score + 0.025;
                    break;
                    case 4:
                        $score = $score + 0.02;
                    break;
                    case 3:
                        $score = $score + 0.015;
                    break;
                    case 2:
                        $score = $score + 0.01;
                    break;
                    case 1:
                        $score = $score + 0.005;
                    break;
                $score = $score/0.015;
                //$score = round($score, 2);
                if($score < 0)
                {
                    $score = 0;
                }
                $data['score'] = $score;
            }
            $data['closed_by'] = $user_id;

        }
        else
        {
            $data['workorder_journey'] = "submitted";
            $data['reason'] = $reason;
            $data['sp_reopen_status'] = 0;
        }
        DB::table('work_orders')
                ->where('id', $work_order_id)
                ->update($data);
                 if (isset($data['workorder_journey']) && $data['workorder_journey'] == 'finished') {
               $WOTasksTrait = (new class { use \App\Http\Traits\CRMProjects\WOTasksTrait; });
                $checkTangibleData= $WOTasksTrait->markAsCompleted($work_order_id);
                }
        /**
        * Sending Notifications
        **/
        $notification_sub_type = '';
        $wo = DB::table('work_orders')->select('property_buildings.building_name', 'work_orders.id','work_orders.work_order_id','work_orders.work_order_type','work_orders.property_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.id', $work_order_id)->first();
        if(isset($wo)) //If there is work order
        {
            if($accept_reject_bm == 2) //If the work order is closed
            {
                $message = 'Building manager <strong>'.Auth::user()->name.'</strong> agrees with service provider for not reopening, work order has been closed again';
                $message_ar = 'مدير المبنى <strong>'.Auth::user()->name.'</strong> يوافق مقدم الخدمة على عدم الحاجة لإعادة فتح أمر العمل وتم إغلاقه مجدداً';

                $notification_sub_type = 'bm_has_agrees_with_sp_wo_has_closed';
            }
            else
            {
                $message = "Building Manager <strong>".Auth::user()->name."</strong> did not agree with Service Provider rejection of reopening the work order due to <strong>".$reason."</strong>, pending for Service Provider action";
                $message_ar ="مدير المبنى <strong>".Auth::user()->name."</strong> لم يوافق مقدم الخدمة على رفض إعادة فتح أمر العمل بسبب <strong>".$reason."</strong>، بانتظار رد مقدم الخدمة";

                $notification_sub_type = 'bm_has_not_agrees_with_sp_wo';
            }
            //Insert for notification
            DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order',
            'building_ids'=>$wo->property_id,'notification_sub_type'=> $notification_sub_type, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));
            //Insert for timeline
            DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order',
            'building_ids'=>$wo->property_id,'notification_sub_type'=> $notification_sub_type, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));

        }

        //$work_order_number = $wo->id;
        // Save in Work Order Email Table
       /* $data = array(
            'wo_id'=> $work_order_number,
            'work_order_type'=>$wo->work_order_type,
            'status'=>'WO-Status-Updated-Fifth',
            'created_at'=>date('Y-m-d H:i:s'),
        );
        DB::table('work_orders_email_jobs')->insert($data);*/

        $workOrdersArray = [
            'work_order_id' => $wo->work_order_id,
            'wo_id' => $wo->id,
            'work_order_type' => $wo->work_order_type,
            'status' => 'WO-Status-Updated-Fifth',
            'created_at' => $this->getCurrentDateTime()
        ];

        $saveEmailJob = $this->saveWorkOrderEmailJob($workOrdersArray);			
						
        if(!$saveEmailJob){
            Log::info('bm_accept_reject_reopen error: Issue when saving data inside work_orders_email_jobs table!');
        }

        return "success";
    }

    /**
     * POST workorder/sp-action-on-rejects-wo-by-bm
     *
     * The Service provider(SP) can accept or reject the rejction made by building manager(BM). If the SP accepts the rejection he will send back the workorder to the worker if not he will request the BM to re evaluate the workorder.
     *
     * @authenticated
     * @group workorder
     *
     * @response 200
     */
    public function sp_action_on_rejects_wo_by_bm(Request $request)
    {
        $work_order_id = $request->work_order_id;
        //dd($request->reject_bm);
        if($request->reject_bm == 2) { // Resent to worker
            $workorder_journey = 'job_execution';
            $worker_id = $request->worker_id;
            $assigned_to = $request->assigned_to;
            $data = [];
            $data['worker_id'] = $worker_id;
            $data['assigned_to'] = $worker_id?'sp_worker':$request->assigned_to;
            $data['status'] = 2;
            $data['sp_approve_job'] = 0;
            $data['bm_approve_job'] = 0;
            $data['workorder_journey'] = $workorder_journey;
            //$data['job_started_at'] = date('Y-m-d H:i:s');
            $data['pass_fail'] = "pending";
            $data['reason'] = $request->reason;

            if(!isset($worker_id))
            {
                $message = 'The service provider <strong>'.Auth::user()->name.'</strong> restarted the work on behalf of the worker';
                $message_ar = 'قام مقدم الخدمة <strong>'.Auth::user()->name.'</strong> بإعادة العمل على أمر العمل بالنيابة عن العامل';
            }
            else
            {
                // @flip1@ add workername on message ||  12-SP sends WO back to Worker after rejection
                $worker_name = DB::table('users')->where('id',  $worker_id)->value('name');
                // @flip1@ due to message change and input field change || 12-SP sends WO back to Worker after rejection
                $message = 'Service Provider <strong>'.Auth::user()->name.'</strong> sent the work order back to the worker <strong>'.$worker_name.'</strong>, due to <strong>'.$request->reason.'</strong>';
                $message_ar = 'مقدم الخدمة <strong>'.Auth::user()->name.'</strong> أعاد إرسال أمر العمل إلى العامل <strong>'.$worker_name.'</strong>، بسبب <strong>'.$request->reason.'</strong>';

                //Send notifiation to worker
                $wo = DB::table('work_orders')->select('property_buildings.building_name', 'work_orders.property_id','work_orders.description','work_orders.id', 'work_orders.work_order_id', 'work_orders.worker_id','work_orders.closed_by')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.id', $work_order_id)->first();
                if($worker_id == $wo->worker_id)
                {
                    //if its same then reject notification will be send

                    $notification_type = 'sp_work_order_rejected';
                    $user_details = Helper::userDetail($worker_id);
                    $registration_ids = [];
                    if(!empty($user_details)) {

                        $registration_ids[] = $user_details->device_token;
                        if($user_details->selected_app_langugage == 'ar' || $user_details->selected_app_langugage == "ar")
                        {
                            $notification_title = "تم رفض أمر العمل";
                        }
                        elseif($user_details->selected_app_langugage == 'ur' || $user_details->selected_app_langugage == "ur")
                        {
                            $notification_title = "ورک آرڈر مسترد کر دیا گیا ہے";
                        }
                        else
                        {
                            $notification_title = "A work order has been rejected";
                        }
                        $messgae = array(
                            "title" => $notification_title,
                            "body" => $wo->description,
                            'section_id' => $wo->id,
                            'notification_type' => $notification_type
                        );
                        //if(count($registration_ids)>0) {
                            $res = ApiHelper::send_notification_worker_FCM($worker_id, $registration_ids, $messgae, $wo->property_id,$notification_type,$wo->id);
                        //}
                    }
                }

            }
        } else { // send to bm
            $workorder_journey = 'job_approval';
            $worker_id = $request->worker_id;
            $data = [];
            $data['worker_id'] = $worker_id;
            $data['status'] = 2;
            $data['sp_approve_job'] = 3;
            $data['bm_approve_job'] = 0;
            $data['workorder_journey'] = $workorder_journey;
            //$data['job_started_at'] = date('Y-m-d H:i:s');
            //$data['pass_fail'] = "pending";
            $data['reason'] = $request->reason;

            //  // @flip1@ SP respont to BM rejection
            //  $message = 'Service Provider has responded with '.$request->reason.' to Building Manager rejection';
            //  $message_ar = 'مقدم الخدمة رد على الرفض المقدم من مدير المبنى بـ '.$request->reason.'';

             // @flip1@ due to change input field || SP rejects and resend to Building manger
             $message = 'Service Provider <strong>'.Auth::user()->name.'</strong> has responded with <strong>'.$request->reason.'</strong> to Building Manager rejection';
             $message_ar = 'مقدم الخدمة <strong>'.Auth::user()->name.'</strong> رد على الرفض المقدم من مدير المبنى بـ<strong>'.$request->reason.'</strong>';
        }
        DB::table('work_orders')
        ->where('id', $work_order_id)
        ->update($data);

        $wo = DB::table('work_orders')
        ->where('id', $work_order_id)->first();

        //$message = 'SP respond to rejection done by BM';
        // @flip1@ comment add different message respond to bm or worker.|| 12-SP sends WO back to Worker after rejection
        // $message = 'Service Provider has responded with '.$request->reason.' to Building Manager rejection';
        // $message_ar = 'مقدم الخدمة رد على الرفض المقدم من مدير المبنى بـ '.$request->reason.'';
        $user_id = auth()->user()->id;
        //Insert for notifications
        DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids'=>$wo->property_id, 'notification_sub_type'=> 'sp_respond_to_bm_rejection', 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));

        //Insert for timeline
        DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids'=>$wo->property_id, 'notification_sub_type'=> 'sp_respond_to_bm_rejection', 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));

        return "success";
    }

    /**
     * POST workorder/bm-action-on_rejects-wo-by-sp
     *
     * The Building Manager(BM) can accept the Service Provider(SP) rejection or he can reject and send back to SP.
     *
     * @authenticated
     * @group workorder
     *
     * @response 200
     */
    public function bm_action_on_rejects_wo_by_sp(Request $request)
    {
        $work_order_id = $request->work_order_id;
        $data = [];
        if($request->reject_bm == 2) { // Approve the workorder
            $approve_job = $request->reject_bm;
            $data['bm_approve_job'] = 0;
            $data['sp_approve_job'] = $approve_job;
            $data['workorder_journey'] = 'job_approval';
            $data['reason'] = $request->reason;
            if(!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee')) //If user type is building_manager or building_manager_employee
            {
                $data['bm_approve_job'] = $approve_job;
                $data['rating'] = $request->rating;
                $data['estimated_price'] = $request->estimated_price;
                $data['status'] = 4;
                $data['workorder_journey'] = 'finished';
                $data['job_completion_date'] = date('Y-m-d H:i:s');

                $wo = DB::table('work_orders')->select('property_buildings.building_name', 'work_orders.*')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.id', $work_order_id)->first();
                $score = 0;
                if($wo->response_time == "On time") //If response_time is On time
                {
                    $score = $score + 0.025;
                }
                elseif($wo->response_time == "Late") //If response_time is Late
                {
                    $score = $score + 0;
                }
                if($wo->pass_fail == "pass") //If response_time is pass
                {
                    $score = $score + 0.1;
                }
                elseif($wo->pass_fail == "fail") //If response_time is fail
                {
                    $score = $score - 0.01;
                }
                if($wo->reopen == "no") //If reopen is no
                {
                    $score = $score + 0;
                }
                else
                {
                    $score = $score - 0.025;
                }
                //echo $request->rating;exit;
                switch ($request->rating){
                    case 5:
                        $score = $score + 0.025;
                    break;
                    case 4:
                        $score = $score + 0.02;
                    break;
                    case 3:
                        $score = $score + 0.015;
                    break;
                    case 2:
                        $score = $score + 0.01;
                    break;
                    case 1:
                        $score = $score + 0.005;
                    break;
                }
                $score = $score/0.015;
                //echo round($score, 2);exit;
                $score = round($score, 2);
                if($score < 0)
                {
                    $score = 0;
                }
                $data['score'] = $score;
            }
        } else { // respond to SP
            //$worker_id = $request->old_worker_id;
            $work_order_id = $request->work_order_id;
            $data['reason'] = $request->reason;
            $data['workorder_journey'] = 'job_execution';

            if(!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee'))
            { //If user type is building_manager or building_manager_employee
                $data['workorder_journey'] = 'job_evaluation';
                $data['reason'] = $request->reason;
                $data['bm_approve_job'] = 1;
                $data['sp_approve_job'] = 0;
            }
            $data['worker_started_at'] = NULL;
            $data['job_submitted_at'] = NULL;
            DB::table('no_checklist_actions')
                ->where('work_order_id', $work_order_id)
                ->where('is_reopen_wo', 0)
                ->delete();
        }


        DB::table('work_orders')
        ->where('id', $work_order_id)
        ->update($data);

        $wo = DB::table('work_orders')
        ->select('work_orders.*', 'property_buildings.building_name', 'work_orders.id', 'work_orders.work_order_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
        ->where('work_orders.id', $work_order_id)->first();

        if($request->reject_bm == 2) {
            if(!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee')) //If user type is building_manager or building_manager_employee
            {
                $message = 'Building Manager <strong>'.Auth::user()->name.'</strong> has approved and evaluated the work order';
                $message_ar = 'مدير المبنى <strong>'.Auth::user()->name.'</strong> قيم واعتمد أمر العمل';

                if($wo->closed_by != null){
                    $bm_name = User::where('id',$wo->closed_by)->value('name');
                    $message ='Building Manager <strong>'. $bm_name.'</strong> has approved and evaluated the work order';
                    $message_ar = 'مدير المبنى '.'<strong>'.$bm_name.'</strong>   قيم واعتمد أمر العمل';
                }
                $notification_sub_type = 'bm_has_approved_and_evaluated_wo';

            }
            else
            {
                $message = 'Building Manager has responded with <strong>'.$request->reason.'</strong> to Service Provider rejection';
                $message_ar = 'مدير المبنى رد على الرفض المقدم من مقدم الخدمة بـ <strong>'.$request->reason.'</strong>';
                $notification_sub_type = 'bm_respond_to_sp_rejection';
            }
        }
        else
        {
            //$message = 'Building Manager respond to rejection done by SP';
            $message = 'Building Manager has responded with <strong>'.$request->reason.'</strong> to Service Provider rejection';
            $message_ar = 'مدير المبنى رد على الرفض المقدم من مقدم الخدمة بـ <strong>'.$request->reason.'</strong>';
            $notification_sub_type = 'bm_respond_to_sp_rejection';
        }
        $user_id = auth()->user()->id;
        //Insert for notification
        DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids'=>$wo->property_id, 'notification_sub_type'=> $notification_sub_type, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));

        //Insert for timeline
        DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids'=>$wo->property_id, 'notification_sub_type'=> $notification_sub_type, 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'), 'is_timeline'=>'yes'));

        return "success";

    }

    /**
     * GET workorder/soft-service/{id}
     *
     * Return view with data
     * Return a view with data. I think we can ignore this function.
     *
     * @authenticated
     * @group Workorder
     *
     * @responseField data array
     * @response 200 {"data": []}
     */
    public function softServiceShow($id)
    {
        $this->data['pageTitle']='Work Order Details | soft service details';
        $this->data['id']="AWS4-HMAC-SHA256&XSS";
        return view($this->view_path.'.soft-service-show',['data'=>$this->data]);
    }

    /**
     * GET workorder/close-order/{id}
     *
     * Return view with data
     * Return a view with data. I think we can ignore this function.
     *
     * @authenticated
     * @group Workorder
     *
     * @responseField data array
     * @response 200 {"data": []}
     */
    public function closeOrderShow()
    {
        $this->data['pageTitle']='Work Order Details | Close Orders';
        $this->data['id']="AWS4-HMAC-SHA256&XSC";
        return view($this->view_path.'.work-order-close',['data'=>$this->data]);
    }

    /**
     * GET workorder/work-order-result/{id}
     *
     * Return view with workorder details
     * Return a view with workorder details. This will have all the calculations of the workorders, it will have final result of the workorder.
     *
     * @authenticated
     * @group Workorder
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function OrderResult($id)
    {
        $id = Crypt::decryptString($id);
        $this->data['pageTitle']='Work Order| Result Details';
        if($id && $this->getWorkOrderResult($id)){
            $this->data['data_record'] = json_decode($this->getWorkOrderResult($id));
            // @flip1@ get mantanance data
            $this->data['maintanance_request'] = DB::table('maintanance_request')->where('id', $this->data['data_record']->maintanance_request_id)->first();

            return view($this->view_path.'.work-order-result', $this->data);
        }
        return abort(404);
    }

    /**
     * GET workorder/-
     *
     * Return JSON collection of workorder details
     * Lets ignore this function for now
     *
     * @authenticated
     * @group Workorder
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function getWorkOrderResult($id)
    {
        //$id = Crypt::decryptString($id);
        //dd($id);
        $data_set=[];
        $data_query=DB::table('work_orders')
                 ->select('work_orders.*','users.name as raised_by','contracts.contract_number', 'maintanance_request.feedback_value as feedback')
                 ->join('users','users.id','=','work_orders.created_by')
                 ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                 ->leftjoin('maintanance_request','maintanance_request.id','=','work_orders.maintanance_request_id')
                 ->where([['work_orders.id',$id], ['work_orders.status','=',4]])
                 ->first();
        $data_query=json_decode(json_encode($data_query), true);

        $sla_asset_categories = DB::table('contract_asset_categories')
                      ->where('asset_category_id', $data_query['asset_category_id'])
                      ->where('contract_number', $data_query['contract_number'])
                      ->orderBy('id', 'desc')
                      ->first();
        $response_time = 0;
            $service_window = 0;
            $response_time_type = 'hours';
            $service_window_type = 'minutes';
            if($data_query['work_order_type'] == "reactive")
            {
              if(!empty($sla_asset_categories->priority_id)){
                $contract_priorities = DB::table('contract_priorities')
                      ->where('priority_id', $sla_asset_categories->priority_id)
                      ->where('contract_number', $data_query['contract_number'])
                      ->orderBy('id', 'desc')
                      ->first();

                if (!$contract_priorities) 
                {
                                    // Fallback query if no record found in contract_priorities
                    $contract_priorities = DB::table('priorities')
                        //->select('id', 'service_window', 'service_window_type')
                        ->where('id', $sla_asset_categories->priority_id)
                        ->orderBy('id', 'desc')
                        ->first();
                }

                $response_time = $contract_priorities->response_time;
                $service_window = $contract_priorities->service_window;
                $response_time_type = $contract_priorities->response_time_type;
                $service_window_type = $contract_priorities->service_window_type;
              }
            }
            elseif($data_query['work_order_type'] == "preventive" && $data_query['priority_id'] != 0)
            {
                $contract_priorities = DB::table('contract_priorities')
                    ->where('priority_id', $data_query['priority_id'])
                    ->where('contract_number', $data_query['contract_number'])
                    ->orderBy('id', 'desc')
                    ->first();
                if(isset($contract_priorities))
                {
                    $response_time = $contract_priorities->response_time;
                    $service_window = $contract_priorities->service_window;
                    $response_time_type = $contract_priorities->response_time_type;
                    $service_window_type = $contract_priorities->service_window_type;
                }
            }
            else
            {
              $contract_frequencies = DB::table('frequencies_master')
                        ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                        ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                        ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                        ->where('frequencies_master.id', $data_query['frequency_id'])
                        ->where('contract_frequencies.contract_number', $data_query['contract_number'])
                        ->first();
              $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
              $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : '';
              $response_time_type = isset($contract_priorities->response_time_type)?$contract_priorities->response_time_type:'hours';
              $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
            }

        $created_at = $data_query['created_at'];
            $tdate = date('Y-m-d H:i:s');
            $datetime1 = strtotime($created_at);
            $datetime2 = strtotime($tdate);
            $interval  = abs($datetime2 - $datetime1);
            $minutes   = round($interval / 60);
            if($response_time_type == "days")
            {
              $response_time = $response_time * 1440;
            }
            elseif($response_time_type == "hours")
            {
              $response_time = $response_time * 60;
            }
            $time_left = $response_time - $minutes;

        if($data_query['response_time'] == 'On time' || $data_query['response_time'] == 'Late')
            {
              $data_query['response_time'] = $data_query['response_time'];
            }
            else
            {
              if($time_left >= 0)
              {
                $data_query['response_time'] = 'On time'; // on time
              }
              else
              {
                $data_query['response_time'] = 'Late';
              }
            }
        //dd($data_query);
        if(isset($data_query['checklist_id'])){
            if($data_query['checklist_id'] > 0) //If has checklist_id and is greater than 0
            {
                $ben_data = WorkorderHelper::getCheckListTask($data_query['checklist_id'], $id);
                $data_query['checklist_task']=$ben_data;
            }
            $data_query['images'] = Workorders::get_checklist_images($id);
            return json_encode($data_query);
        }
        if($data_query['score'] < 0)
        {
            $data_query['score'] = 0;
        }
    }

    /**
     * GET workorder/taskCheckListDetails
     *
     * Return JSON collection of workorder checklist details
     * This will have the details of the workorder checklist details. Check list is basically user in worker app where worker will select those checklist and submit answers. while creating a workorder we have to select a specific checklist to display for the worker.
     *
     * @authenticated
     * @group Workorder
     *
     * @responseField data array
     * @response 200 {"data": []}
     */
    public function taskCheckListDetails(Request $request)
    {
        $db_query=ChecklistTasks::with('checklist_subtasks')// @flip1@ add with for get subtasks deta
                 ->join('checklists','checklist_tasks.checklist_id','=','checklists.list_id')
                 ->Leftjoin('no_checklist_actions','no_checklist_actions.checklist_task_id','=','checklist_tasks.id')
                 ->select('checklist_tasks.*','checklists.*','no_checklist_actions.comment as comment1','no_checklist_actions.photos','no_checklist_actions.feedback_options', 'checklist_tasks.id as id', 'no_checklist_actions.checklist_subtask_id')
                ->where('no_checklist_actions.id',$request->taskId)
                ->where('no_checklist_actions.work_order_id',$request->wo_id)

                ->groupBy('checklist_tasks.id')
                ->orderBy('no_checklist_actions.id', 'asc')
                ->get();
                // dd($db_query, $request->taskId, $request->wo_id);
        foreach($db_query as $dq){
            $dq->image = json_decode($dq->photos);
        }
        return response()->json(['data'=>$db_query]);
    }

    /**
     * GET workorder/check-list-data/{id?}
     *
     * Return view with workorder checklist details
     * This will have the details of the workorder checklist details. This will return the data which is filled by the worker.
     *
     * @authenticated
     * @group Workorder
     *
     * @responseField data array
     * @response 200 {"data": []}
     */
    public function checkListDetails(Request $request, $id=null)
    {
        $this->data['pageTitle']='Work Order List';
        $id = Crypt::decryptString($id);
        //dd($id);
        $user_id = auth()->user()->id;
        $this->data['work_order_id']=$id;
        if(request()->ajax()) //If has ajax call
        {

            $work_orders = WorkOrders::getChecklistListDataSet($id);
            //dd($work_orders);
            return view($this->view_path.'.checklist.list-ajax')->with(['work_orders' => $work_orders]);
        }
        return view($this->view_path.'.checklist.list', ['data'=>$this->data]);
    }


    /**
     * POST workorder/list_details
     *
     * Return array of workorder checklist details for popup
     * This will have the details of the workorder checklists. This will return the main checklist details according to the id passed.
     *
     * @authenticated
     * @group Workorder
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function WorkorderChecklistDetails(Request $request)
    {
        if($request->ajax())
        {
            $request_id = $request->mr_id;
            $mr_data = WorkOrders::WorkorderChecklistDetails($request_id);
            return response()->json(['data'=>$mr_data]);
        }
    }

    /**
     * GET workorder/view-checklist-details/{id}
     *
     * Return array of workorder checklist details
     * This will have the details of the workorder checklists. This will return the main checklist details according to the id passed.
     *
     * @authenticated
     * @group Workorder
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function view_checklist_details($id)
    {
        $this->data['pageTitle']='Add new check list';
        $this->data['id']="STATIC001";
        //$id = Crypt::decryptString($id);
        $checklists = DB::table('checklists')->where('id',$id)->first();
        $checklists = json_decode(json_encode($checklists), true);
        $checklist_cities = DB::table('cities')->whereIn('id', explode(',', $checklists['city_id']))->get();
        $checklists['cities'] = json_decode(json_encode($checklist_cities), true);
        $checklist_regions = DB::table('regions')->whereIn('id', explode(',', $checklists['country_id']))->get();
        $checklists['regions'] = json_decode(json_encode($checklist_regions), true);
        $checklists['asset_category'] = DB::table('asset_categories')->where('id', $checklists['asset_category_id'])->value('asset_category');
        $checklists['asset_name'] = DB::table('asset_names')->where('id', $checklists['asset_id'])->value('asset_name');
        $checklist_properties = DB::table('properties')->whereIn('id', explode(',', $checklists['properties']))->get();
        $checklists['properties'] = json_decode(json_encode($checklist_properties), true);
        // $checklist_tasks = DB::table('checklist_tasks')->where('checklist_id',$checklists['list_id'])->get();

        // @flip1@ changes in query for get subtasks data
        $checklist_tasks = ChecklistTasks::with('checklist_subtasks')->where('checklist_id',$checklists['list_id'])->where('is_deleted', 'no')->get();
        $checklists['tasks'] = json_decode(json_encode($checklist_tasks), true);
        //dd($checklists);
        $this->data['checklists'] = $checklists;
        return $this->data;
    }

    /**
     * GET workorder/checkListDetailsAjax
     *
     * Return array of workorder checklist details
     * This will have the details of the workorder checklist details. Check list is basically user in worker app where worker will select those checklist and submit answers. while creating a workorder we have to select a specific checklist to display for the worker. This will return the HTML data to be appended.
     *
     * @authenticated
     * @group Workorder
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function checkListDetailsAjax(Request $request)
    {
        $checklists = $this->view_checklist_details($request->checklist_id);

        $i = 1;
        $html = '';
        foreach($checklists['checklists']['tasks'] as $key =>$task){
            $html .= '<div class="atbd-collapse atbd-collapse-custom"><div class="atbd-collapse-item mb-0">';

            $html .= '<div class="atbd-collapse-item__header';

            if($key == 0){
                $html .= ' active ';
            }
            $html .= '"><a href="#" class="item-link';
            if($key == 0){
                $html .= ' border-bottom ';
            }else{
                $html .= ' collapsed ';

            }
            $html .= '" data-toggle="collapse" data-target="#collapse-body-c-'.$key.'" ';
            if($key == 0){
                $html .= ' aria-expanded="true" ';
            }else{
                $html .= ' aria-expanded="false" ';

            }
            $html .= ' aria-controls="collapse-body-c-'.$key.'"><i class="la la-angle-right"></i><h6>'.$task['task_title'].'</h6></a></div>';

            $html .= '<div id="collapse-body-c-'.$key.'" class="collapse bg-white pt-3 atbd-collapse-item__body ';
            if($key == 0){
                $html .= ' show ';
            }
            $html .= '">';
            $html .= '<div class="collapse-body-text"><div class="row">';

            $html .= '<div class="col-6"><div class="form-group">
                <label for="closed_at" class="text-dark">'.__("configration_checklist.checklist_table.task_number").'</label>
                <p>'. __("general_sentence.breadcrumbs.task").' '.$i++.'</p></div></div>';

            $html .= '<div class="col-6"><div class="form-group"><label for="closed_at" class="text-dark">'.__("configration_checklist.checklist_table.task_title").'</label><p> '.$task['task_title'].'</p></div></div>';
            $html .= '<div class="col-12"><div class="form-group"><label for="closed_at" class="text-dark">'.__("configration_checklist.checklist_table.description").'</label><p> '.$task['description'].' </p></div></div>';
            $html .= '<div class="col-12"><h6 class="mb-3">'.__("configration_checklist.common.selected_actions").'</h6></div>';



            if($task['photos'] == 'yes' || $task['comment'] == 'yes' || $task['multiple_options'] != NULL || $task['checklist_subtasks'] != []){
                if($task['photos'] == 'yes'){
                    $html .= '<div class="col-12">
                                <div class=""> <i class="las la-check"></i><label class="text-dark ml-2"> <span data-feather="check"></span>'.__('configration_checklist.checklist_table.photos').'</label></div>
                            </div>';
                }
                if($task['comment'] == 'yes'){
                    $html .= '<div class="col-12">
                                <div class="">
                                <i class="las la-check"></i>
                                    <label for="closed_at" class="text-dark ml-1"><span data-feather="check"></span>'.__('configration_checklist.checklist_table.comment').' </label>
                                </div>
                            </div>';
                }
                if($task['multiple_options'] != NULL){
                    $html .= '<div class="col-12">
                    <div class="form-group"><i class="las la-check"></i>
                        <label for="closed_at" class="text-dark ml-1"><span data-feather="check"></span> '.__('configration_checklist.checklist_forms.label.multiple_options').' </label>
                        <div class="pl-sm-4">
                            <div class="row">';
                    $mo_arr = explode(',', $task['multiple_options']);
                    foreach($mo_arr as $k => $mo){
                        $html .= '<div class="col-6 mb-3">
                        <label class="text-dark">'.__('configration_checklist.checklist_forms.label.option').' '. ++$k .'</label><p>'.$mo.'</p></div>';
                    }
                    $html .= '</div></div></div></div>';
                }
                if($task['checklist_subtasks'] != []){
                    $html .= '<div class="col-12">
                    <div class="form-group"><i class="las la-check"></i>
                        <label for="closed_at" class="text-dark ml-1"><span data-feather="check"></span> '.__('configration_checklist.checklist_forms.label.sub_tasks').' </label>
                        <div class="pl-sm-4">
                            <div class="row">';

                    foreach($task['checklist_subtasks'] as $k => $st){
                        $html .= '<div class="col-6 mb-3">
                        <label class="text-dark">'.__('configration_checklist.checklist_forms.label.sub_task').' '. ++$k .'</label><p>'.$st['name'].'</p></div>';
                    }
                    $html .= '</div></div></div></div>';
                }
            }else{
                $html .= '<div class="col-12"><div class=""><label class="">'.__('configration_checklist.common.no_actions_added').'</label></div></div>';
            }
            $html .= '</div></div></div></div></div>';
        }

        $this->data['heading'] = $checklists['checklists']['checklist_title'];
        $this->data['html'] = $html;
        return $this->data;
    }

    /**
     * GET workorder/send-reminder/{id?}
     *
     * Return JSON response with success message
     * As a building manager I want to remind/notify SP users (SPA/SPS) for any work order that is “Submitted (Open), or still In progress”, to make expedite the action of completing.
     *
     * @authenticated
     * @group Workorder
     *
     * @response 200
     */
    public function sendReminder(Request $request, $id){
        $ipaddress=Helper::getIPAddr();
        $user_id = auth()->user()->id;
        //Check wo exist or not with given time
        $check_wo = DB::table('work_orders')->where('id','=',$request->id)->first();
        if(isset($check_wo) && !empty($check_wo))
        {
            $project_user_id = $check_wo->project_user_id;
            if(isset($project_user_id) && $project_user_id != 0) //If has project_user_id and project_user_id not equal to 0
            {
                $userDetails = DB::table('users')->where('id', $project_user_id)->first();
                $project_id = $userDetails->project_id;
            }
            else
            {
                $userDetails = DB::table('users')->where('id', $check_wo->created_by)->first();
                $project_id = $userDetails->project_id;
            }
            $projectSetting = DB::table('project_settings')
                            ->select('workorder_reminder_periods')
                            ->where('project_id',$project_id)->first();
            if(isset($projectSetting) && !empty($projectSetting))
            {
                $workorder_reminder_periods = trim($projectSetting->workorder_reminder_periods) != "" ? trim($projectSetting->workorder_reminder_periods) : '7';
            }
            else
            {
                $workorder_reminder_periods = '7';
            }
            if(in_array($check_wo->status,array(1,2)))
            {
                //Check time
                if((trim($check_wo->wo_reminder_sent_on) != null) && (trim($check_wo->wo_reminder_sent_on) != ""))
                {
                    $wo_reminder_sent_on_timestamp = strtotime($check_wo->wo_reminder_sent_on);
                    $workorder_reminder_periods_in_sec = 86400*$workorder_reminder_periods;
                    if($wo_reminder_sent_on_timestamp > time() + $workorder_reminder_periods_in_sec) {
                        $show_counter = 'no';

                    } else {
                        $show_counter = 'yes';
                    }
                }
                else
                {
                    $show_counter = 'no';
                }
            }
            else
            {
                $show_counter = 'yes';
            }
        }
        else
        {
            $show_counter = 'yes';
        }

        // if($show_counter == 'no')
        // {
                    $data = array(
                        'wo_reminder_send_by'=> $user_id,
                        'wo_reminder_sent_on'=>date('Y-m-d H:i:s'),
                        'wo_reminder_period'=> $workorder_reminder_periods,

                    );
                    $response = DB::table('work_orders')->where('id','=',$request->id)->update($data);
                    if(!empty($response)) //If has response
                    {
                            $work_orders = DB::table('work_orders')->select('property_buildings.building_name', 'work_orders.id', 'work_orders.work_order_id','work_orders.property_id', 'work_orders.supervisor_id', 'work_orders.service_provider_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->where('work_orders.id', $request->id)->get();
                            $work_orders = json_decode(json_encode($work_orders), true);

                            if(!empty($work_orders)) //If has work orders
                            {
                                foreach($work_orders as $wo)
                                {
                                $work_order_id = $wo['id'];

                                $service_provider_ids = trim($wo['service_provider_id']) != "" ? $wo['service_provider_id'] : "";
                                $supervisor_ids = trim($wo['supervisor_id']) != "" ? $wo['supervisor_id'] : "";
                                if(auth()->user()->user_type == 'sp_admin')
                                {
                                    $final_user_ids = explode(",",$supervisor_ids);
                                }
                                else
                                {
                                    $final_user_ids = array_merge(explode(",",$service_provider_ids),explode(",",$supervisor_ids));
                                }

                                if(count(array_values($final_user_ids)) > 0)
                                {
                                    $send_remimnders = WorkOrders::getSendWoReminderemail($final_user_ids,$work_order_id);
                                }


                                $message = 'Reminder: '.$wo['work_order_id'].' is not closed, kindly complete process of closing.';
                                $message_ar = 'تذكير: أمر العمل '.$wo['work_order_id'].' لم يتم إغلاقه، يرجى إكمال إجراءات الإغلاق';

                                //Insert For notification
                                DB::table('notifications')->insert(array('user_id' => $user_id, 'building_ids'=>$wo['property_id'], 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'notification_type' => 'send_wo_reminder_to_sp', 'notification_sub_type' => 'send_wo_reminder_to_sp', 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'), 'is_timeline'=>'no'));
                                //Insert For timeline
                                DB::table('notifications')->insert(array('user_id' => $user_id, 'building_ids'=>$wo['property_id'], 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'notification_type' => 'send_wo_reminder_to_sp', 'notification_sub_type' => 'send_wo_reminder_to_sp', 'section_id' => $work_order_id, 'created_at' => date('Y-m-d H:i:s'), 'is_timeline'=>'yes'));


                                }
                            }
                            return response()->json(['status'=> true,'message'=>'Reminder sent successfully']);
                    }
                    else
                    {
                        return response()->json(['status'=> false,'message'=> __('general_sentence.s_global.auth.something_wrong_try_again')]);
                    }
        // }
        // else
        // {
        //     dd('here2');

        //     return response()->json(['status'=> false,'message'=> __('general_sentence.s_global.auth.something_wrong_try_again')]);
        // }

    }

    /**
     * POST workorder/assign-supervisor
     *
     * Return view with Success or Failure response
     * We are assigning or re-assigning a supervisor form the work order details page.
     *
     * @authenticated
     * @group Workorder
     *
     * @responseField data array
     * @response 200 {"data": []}
     */
    public function assignSupervisor(Request $request)
    {
        $supervisor_id = WorkOrders::where('id', $request->work_order_id)->where('assigned_to', 'supervisor')->value('supervisor_id');
        WorkOrders::where('id', $request->work_order_id)
                ->update([
                    'supervisor_id' => $request->supervisor_id,
                    'assigned_to' => 'supervisor'
                    ]);

        if((!isset($supervisor_id) || count(explode(',', $supervisor_id))>1) && ($supervisor_id != $request->supervisor_id))
        {
            $user = User::find($request->supervisor_id);
            $message = 'Service Provider <strong>'.auth()->user()->name .'</strong> has assigned the supervisor <strong>'.$user->name.'</strong>';
            $message_ar = 'مقدم الخدمة <strong>'.auth()->user()->name .'</strong> قام بتعيين المشرف <strong>'.$user->name.'</strong>';
        }
        elseif(count(explode(',', $supervisor_id)) == 1  && ($supervisor_id != $request->supervisor_id))
        {
            $user = User::find($request->supervisor_id);
            $suser = User::find($supervisor_id);
            $message = 'Service Provider <strong>'.auth()->user()->name .'</strong> has assigned the supervisor <strong>'.$user->name.'</strong> in place of <strong>'.$suser->name.'</strong>';
            $message_ar = 'مقدم الخدمة <strong>'.auth()->user()->name .'</strong> قام بتعيين المشرف <strong>'.$user->name.'</strong> بدلاً من <strong>'.$suser->name.'</strong>';
        }
        if($supervisor_id != $request->supervisor_id)
        {
            $notification_sub_type = 'sps_has_assigned';
            DB::table('notifications')->where('section_id', $request->work_order_id)->where('notification_sub_type', $notification_sub_type)->update(['notification_sub_type' => '']);

            //Insert for Notifiaction
            DB::table('notifications')->insert(array('user_id' => auth()->user()->id, 'building_ids' => $request->building_ids, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'section_id' => $request->work_order_id,'section_id' => $request->work_order_id, 'notification_sub_type' => $notification_sub_type, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));
            //Insert for Timeline
            DB::table('notifications')->insert(array('user_id' => auth()->user()->id, 'building_ids' => $request->building_ids, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'section_id' => $request->work_order_id,'section_id' => $request->work_order_id, 'notification_sub_type' => $notification_sub_type, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));

        }

        return redirect()->route('workorder.show', Crypt::encryptString($request->work_order_id))->with('message', 'Data added Successfully');
    }


    /**
     * GET workorder/reopenworkorderDetails/{id}
     *
     * Return view with reopen workorder details
     * Return view with reopen work order history using the workorder id selected in the workorder.
     *
     * @authenticated
     * @group Workorders
     *
     * @responseField array
     * @response 200 {[]}
     */
    public function reopenworkorderDetails(Request $request, $id=null)
    {
        $this->data['pageTitle']='Reopen Work Order Details';
        $id = Crypt::decryptString($id);
        $user_id = auth()->user()->id;
        $this->data['work_order_id']=$id;

        $reopen_work_order_data = ReopenWorkOrderDetail::query()
                                ->with(['users:id,user_type', 'workOrder:id,work_order_id,job_completed_by','checklistimages'])
                                ->where('wo_id', $id)
                                ->orderBy('id', 'desc')
                                ->get()->toArray();

        if(isset($reopen_work_order_data))
        {
            foreach($reopen_work_order_data as $key => $row)
            {
                    $images = [];
                            if(!empty($row['checklistimages']))
                            {
                                foreach($row['checklistimages'] as $r)
                                {
                                    $json = json_decode($r['photos']);
                                    foreach($json as $prow)
                                    {
                                        $images[] = $prow;
                                    }
                                }
                            }
                $reopen_work_order_data[$key]['images'] = $images;

                if($row['work_order']['job_completed_by'] == 'SP' || $row['work_order']['job_completed_by'] == "SP")
                {
                    $reopen_work_order_data[$key]['job_completed_user_type'] = Helper::getReopenwoChecklistUserdata($row['id']);
                }
                else
                {
                    $reopen_work_order_data[$key]['job_completed_user_type'] = 'sp_worker';
                }

                $reopen_work_order_data[$key]['count_checklist'] = NoChecklistAction::query()
                ->with(['checklistTask'])
                ->where('reopen_wo_id', '=', $row['id'])
                ->where('checklist_task_id','!=',0)->count();
            }
            $this->data['reopen_work_order_data'] = $reopen_work_order_data;
            return view($this->view_path.'.reopen_wo_details', ['data'=>$this->data]);
        }

    }

    /**
     * GET workorder/reopencheckListDetails/{id?}
     *
     * Return view with reopen history workorder submitted checklist details.
     *
     * @authenticated
     * @group Workorder
     *
     * @responseField data array
     * @response 200 {"data": []}
     */
    public function reopencheckListDetails(Request $request, $id=null)
    {
        $this->data['pageTitle']='Work Order Checklist';
        $id = Crypt::decryptString($id);
        $user_id = auth()->user()->id;
        $this->data['work_order_id']=$id;
        if(request()->ajax()) //If has ajax call
        {
            $work_orders = Helper::getReopenchecklistListDataSet($id);
            return view($this->view_path.'.checklist.reopen_wo_list-ajax')->with(['work_orders' => $work_orders]);
        }
        return view($this->view_path.'.checklist.reopen_wo_list', ['data'=>$this->data]);
    }

    /**
     * Change the status of a work order item request by a worker.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    public function changeItemRequestByWorkerStatus(Request $request)
    {
        try {
            $akaunting = new Akaunting();
            $user = Auth::user();
            $wo =  WorkOrders::find($request->work_order_id);

            /** @var App\Models\Contracts $contract */
            $contract = $wo->contract;
            $warehouse_owner = $wo->contract->warehouse_owner;
            
            if ($warehouse_owner !== 'admin') {
                    if($warehouse_owner == 'no_inventory')
                    {
                            $company_id = null;
                    }
                    else
                    {
                        $company_id = $wo->contract->serviceProvider->serviceProviderAdmin->userCompany->company_id;
                    }    
            }else{
                $company_id = null;
            }

            
            $defaultWarehouse = $contract->getDefaultWarehouse();

            $worker_name = User::where('id', $wo->worker_id)->value('name');

            // Initialize $data variable
            $data = [];

            // Determine the data based on the provided status
            switch ($request->status) {
                case 'rejected':
                    $message = ($user->user_type == 'admin') ? __('work_order.notifications.po_rejected_item_request_generated_by_worker', ['name' => $user->name,'reason' => $request->rejection_reason], 'en') : __('work_order.notifications.sp_rejected_item_request_generated_by_worker', ['name' => $user->name,'reason' => $request->rejection_reason, 'worker_name' => $worker_name], 'en');

                    $message_ar = ($user->user_type == 'admin') ? __('work_order.notifications.po_rejected_item_request_generated_by_worker', ['name' => $user->name,'reason' => $request->rejection_reason], 'ar') : __('work_order.notifications.sp_rejected_item_request_generated_by_worker', ['name' => $user->name,'reason' => $request->rejection_reason, 'worker_name' => $worker_name], 'ar');
                    $data = ['reject_reason' => $request->rejection_reason, 'status' => 'rejected'];

                    $notification_sub_type = 'rejected';
                    $worker_notification_sub_type = 'wo_item_request_rejected';
                    break;
                case 'partially_given':
                    $message = ($user->user_type == 'admin') ? __('work_order.notifications.po_partially_given_item_request_generated_by_worker', ['name' => $user->name], 'en') : __('work_order.notifications.sp_partially_given_item_request_generated_by_worker', ['name' => $user->name], 'en');

                    $message_ar = ($user->user_type == 'admin') ? __('work_order.notifications.po_partially_given_item_request_generated_by_worker', ['name' => $user->name], 'ar') : __('work_order.notifications.sp_partially_given_item_request_generated_by_worker', ['name' => $user->name], 'ar');
                    $data = ['note' => $request->note, 'status' => 'partially_given'];

                    $notification_sub_type = 'sent_to_project_owner';
                    $worker_notification_sub_type = 'wo_item_request_partially_given';
                    break;
                case 'fully_given':
                    $message = ($user->user_type == 'admin') ? __('work_order.notifications.po_fully_given_item_request_generated_by_worker', ['name' => $user->name], 'en') : __('work_order.notifications.sp_fully_given_item_request_generated_by_worker', ['name' => $user->name], 'en');

                    $message_ar =  ($user->user_type == 'admin') ? __('work_order.notifications.po_fully_given_item_request_generated_by_worker', ['name' => $user->name], 'ar') : __('work_order.notifications.sp_fully_given_item_request_generated_by_worker', ['name' => $user->name], 'ar') ;
                    $data = ['note' => $request->note, 'status' => 'accepted'];

                    $notification_sub_type = 'accepted';
                    $worker_notification_sub_type = 'wo_item_request_accepted';
                    break;
            }

            $data['approved_by'] = $user->id;

            $akaunting = new Akaunting();

            $stockEnchants = [];

            // Start a database transaction
            DB::beginTransaction();

            // Update the WorkOrderItemRequest based on the determined data
            $WorkOrderItemRequest = WorkOrderItemRequest::where('work_order_id', $request->work_order_id)->first();
            $updateWorkOrderItemRequest =  $WorkOrderItemRequest->update($data);
            // Update the WorkOrderRequestedItem records if items are provided
            $items = $request->items;
            if($updateWorkOrderItemRequest && isset($WorkOrderItemRequest->id) && $request->status != 'rejected'){
                if(isset($items)) {
                    foreach (json_decode($items) as $item) {

                        $defaultWarehouseId = $defaultWarehouse ? $defaultWarehouse->id : null;

                        /** @var WorkOrderRequestedItem $requestedItem */
                        $requestedItem = WorkOrderRequestedItem::where('id', $item->id)->where('request_id',$WorkOrderItemRequest->id);
                        $requestedItem->update([
                            'missing_quantity' => isset($item->missing_quantity) ? $item->missing_quantity : 0,
                            'quantity_accepted' => isset($item->quantity_accepted) ? $item->quantity_accepted : 0,
                            'warehouse_id' => isset($item->warehouse_id) ? $item->warehouse_id : 0,
                        ]);
                        $stockEnchants[] = [
                            'item_id' => $item->item_id,
                            'quantity' => $item->quantity_accepted,
                            'warehouse_id' => ($item->warehouse_id ?? $defaultWarehouseId),
                        ];
                    }
                }
            }

            NotificationHelper::sendWorkOrderNotification($user, $wo, $message, $message_ar, $notification_sub_type);
            // Update the work order status of pause
            WorkorderHelper::updatePauseWorkorderdata($wo->id);
            //Send notifcation to worker

            $WorkerId = $wo->is_handle_by_team_leader == 1 ? $wo->team_leader_id : $wo->worker_id;
            $registration_ids = [];
            if ($WorkerId && $worker = Helper::userDetail($WorkerId)) {
                // If a specific worker is provided and exists
                $registration_ids[] = $worker->device_token;

                // Notification title based on language
                if ($worker->selected_app_langugage == 'ar' || $worker->selected_app_langugage == 'ur') {
                    if($worker_notification_sub_type == 'wo_item_request_rejected')
                    {
                        $notification_title = ($worker->selected_app_langugage == 'ar') ? 'تمت الموافقة على طلب قطع الغيار' : 'اسپیئر پارٹ کی درخواست مسترد کر دی گئی ہے';
                    }
                    else
                    {
                        $notification_title = ($worker->selected_app_langugage == 'ar') ? 'تم رفض طلب قطع الغيار' : 'اسپیئر پارٹ کی درخواست منظور کر لی گئی ہے';
                    }
                } else {
                    $notification_title = $worker_notification_sub_type == 'wo_item_request_rejected' ? 'Spare part request has been Rejected' : 'Spare part request has been Approved';
                }

                // Message array
                $message = [
                    'title' => $notification_title,
                    'body' => $wo->description,
                    'section_id' => $wo->id,
                    'notification_type' => $worker_notification_sub_type,
                ];
                // Send notifications
                $res = ApiHelper::send_notification_worker_FCM($WorkerId, $registration_ids, $message, $wo->property_id, $worker_notification_sub_type, $wo->id);
            }
                // Commit the transaction
            DB::commit();

            $responses = [];
            foreach ($stockEnchants as $key => $stockEnchant)
            {
                $response = $akaunting->inventory()->items()->enchantItem($stockEnchant['item_id'], $stockEnchant['warehouse_id'], $stockEnchant['quantity'], 'subtract', $company_id);
                $responses[] = $response->json();
            }

            // Return true to indicate success
            return true;
        } catch (\Exception $e) {
            // Rollback the transaction on exception
            DB::rollBack();

            // Log or handle the exception as needed
            return $e;
        }
    }

        /**
     * Change the status of a work order item request by a worker.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    public function changeItemRequestBySpStatus(Request $request)
    {
        try {
            $user = Auth::user();
            /** @var App\Models\Contracts $wo */
            $wo =  WorkOrders::find($request->work_order_id);
            $warehouse_owner = $wo->contract->warehouse_owner;
            if ($warehouse_owner !== 'admin') {
                if($warehouse_owner == 'no_inventory')
                    {
                            $company_id = null;
                    }
                else
                {
                    $company_id = $wo->contract->serviceProvider->serviceProviderAdmin->userCompany->company_id;
                }
            }else{
                $company_id = null;
            }

            // Initialize $data variable
            $data = [];

            // Determine the data based on the provided status
            switch ($request->status) {
                case 'rejected':
                    $data = ['reject_reason' => $request->rejection_reason, 'status' => 'rejected'];
                    $message = __('work_order.notifications.po_rejected_item_request_generated_by_sp', ['name' => $user->name,'reason' => $request->rejection_reason], 'en');
                    $message_ar = __('work_order.notifications.po_rejected_item_request_generated_by_sp', ['name' => $user->name,'reason' => $request->rejection_reason], 'ar');
                    break;

                case 'partially_given':
                    $data = ['note' => $request->note, 'status' => $request->status];
                    $message = __('work_order.notifications.po_partially_given_item_request_generated_by_worker', ['name' => $user->name], 'en');
                    $message_ar =  __('work_order.notifications.po_partially_given_item_request_generated_by_worker', ['name' => $user->name], 'ar');
                    break;

                case 'fully_given':
                    $message = __('work_order.notifications.po_fully_given_item_request_generated_by_sp', ['name' => $user->name], 'en');
                    $message_ar = __('work_order.notifications.po_fully_given_item_request_generated_by_sp', ['name' => $user->name], 'ar');
                    $data = ['note' => $request->note, 'status' => 'accepted'];
                    break;
            }

            $data['approved_by'] = $user->id;


            $akaunting = new Akaunting();
            $stockEnchants = [];

            // Start a database transaction
            DB::beginTransaction();

            // Update the WorkOrderItemRequest based on the determined data
            $WorkOrderItemRequest = ServiceProviderMissingItemRequest::where('work_order_id', $request->work_order_id)->first();
            $updateWorkOrderItemRequest =  $WorkOrderItemRequest->update($data);

            // Update the WorkOrderRequestedItem records if items are provided
            $mark_delay_material = 0;
            $items = $request->items;
            if($updateWorkOrderItemRequest && isset($WorkOrderItemRequest->id) && $request->status != 'rejected'){
                if(isset($items)) {
                    foreach (json_decode($items) as $item) {
                        $missingItemQty = isset($item->missing_quantity) ? $item->missing_quantity : 0;
                        if($missingItemQty > 0) {
                            $mark_delay_material = 1;
                        }
                        $update = ServiceProvieRequestedMissingItem::where('id', $item->id)->where('request_id',$WorkOrderItemRequest->id)->first();
                        $update->update([
                            'missing_quantity' => isset($item->missing_quantity) ? $item->missing_quantity : 0,
                            'warehouse_id' => isset($item->warehouse_id) ? $item->warehouse_id : 0,
                        ]);
                        $stockEnchants[] = [
                            'item_id' => $update->item_id,
                            'quantity' => $item->quantity_accepted,
                            'warehouse_id' => $item->warehouse_id,
                        ];
                    }
                }
            }
            // Add Timeline and notification
            
            NotificationHelper::sendWorkOrderNotification($user, $wo, $message, $message_ar);

            WorkOrders::where('id','=',$request->work_order_id)
            ->update(['mark_delay_material' => $mark_delay_material]);
            if($wo->pause_start_time != NULL && $wo->pause_start_time != "" && $wo->pause_end_time == NULL)
            {
                $pause_time_spent_minutes = Carbon::parse($wo->pause_start_time)->diffInMinutes(Carbon::now());
                $pause_time_spent = WorkorderHelper::fetchClockInHrs($wo->pause_start_time);

                $workOrder = WorkOrders::find($request->work_order_id);

                if ($workOrder) {
                    $status = $workOrder->workorder_journey === 'submitted' ? 1 : 2;

                    $workOrder->update([
                        'status' => $status,
                        'pause_time_spent' => $pause_time_spent,
                        'pause_time_spent_minutes' => $pause_time_spent_minutes,
                        'pause_end_time' => now(),
                    ]);
                }

            }

            // Commit the transaction
            DB::commit();

            $responses = [];
            foreach ($stockEnchants as $key => $stockEnchant)
            {
                $response = $akaunting->inventory()->items()->enchantItem($stockEnchant['item_id'], $stockEnchant['warehouse_id'], $stockEnchant['quantity'], 'subtract', $company_id);
                $responses[] = $response->json();
            }

            // Return true to indicate success
            return true;
        } catch (\Exception $e) {
            // Rollback the transaction on exception
            DB::rollBack();

            // Log or handle the exception as needed
            return $e;
        }

    }


    /**
     * Mark a work order item request as sent to the project owner.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    public function sendToProjectOwner(Request $request)
    {
        try {
            // The logged in user details
            $loggedInUser = Auth::user();

            // Update the WorkOrderItemRequest based on the determined data
            WorkOrderItemRequest::where('work_order_id', $request->work_order_id)->update(['sent_to_project_owner' => 1]);

            // Notification messages
            $message = __('work_order.notifications.sp_sent_to_po_request_generated_by_worker', ['name' => $loggedInUser->name], 'en') ;
            $message_ar =  __('work_order.notifications.sp_sent_to_po_request_generated_by_worker', ['name' => $loggedInUser->name], 'ar') ;

            // Retrieve the work order
            $workOrder = WorkOrders::find($request->work_order_id);

            // Send th notification and timeline message
            NotificationHelper::sendWorkOrderNotification($loggedInUser, $workOrder, $message, $message_ar, 'sent_to_project_owner');

            // Return true to indicate success
            return true;
        } catch (\Exception $e) {
            // Log or handle the exception as needed
            return false;
        }
    }

    /**
     * Retrieve submitted checklist data by the worker.
     *
     * This function fetches the comments and photos submitted by the worker for a specific checklist task.
     * If the checklist task ID is provided, it retrieves the submitted data for that task.
     * If the checklist task ID is 0, it only retrieves the comments and photos without specific task association.
     *
     * @param int $checklistTaskId The ID of the checklist task (optional, default: 0).
     * @return array The submitted checklist data including comments and photos.
     */
    public function getSubmittedChecklistData(Request $request)
    {
        $checklistId = $request->checklistId;
        $workOrderId = $request->workOrderId;
        $workerId = $request->workerId;

        // Initialize an array to store the checklist data and submitted data
        $checklistData = [];
        $submittedData = [];

        // If the checklist task ID is provided
        if ($checklistId != 0) {
            // Retrieve checklist data for the specified task
            $checklistData = WorkorderHelper::getChecklistData($checklistId);

            // If checklist data is found
            if ($checklistData) {
                // Retrieve submitted data for the checklist task
                $submittedData = WorkorderHelper::getSubmittedDataForTask($checklistData->list_id, $workOrderId, $workerId);
            }
        } else {
            // If no specific task is provided, retrieve general submitted data (without task association)
            $submittedData = WorkorderHelper::getGeneralSubmittedData($workOrderId, $workerId);
        }

        $result = [
            'checklistData' => $checklistData,
            'submittedData' => $submittedData
        ];

        // Return the submitted checklist data
        return view($this->view_path.'.worker-checklist-summary-modal', $result);
    }


     /** New work-order-list optimized
     * GET workorder/work-order-list/{id}
     *
     * Return view with workorders list
     * Return a view with list of workorders. The Filters also can be applied. For the user types such as Super admin, Osool admin, POA and POE the work orders will listed according to the service providers selected.
     *
     * @authenticated
     * @group Workorders
     *
     * @responseField data array
     * @response 200 {"data": []}
     */

     public function new_list(Request $request) {
        try {
            $crypted_string = $request->route('id');

            if($this->valueIsRequired($crypted_string)){
                return redirect('admin/dashboards.404');
            }

            else{
                $serviceProviderId = $this->decryptCryptedString($crypted_string);
                $privilegeCheck = Helper::checkLoggedinUserPrivileges('no_view', 'workorder', false);

                if (!$privilegeCheck['success']) {
                    return $privilegeCheck['redirect_url'];
                }

                $page = explode('/', request()->route()->uri)[1];
                $pagePrivileges = json_decode(auth()->user()->user_privileges);
                return view('applications.admin.new-work-orders.list', compact('serviceProviderId'));
            }
        }

        catch (\Throwable $th) {
            Log::error("new_list Error: ".$th);
            return redirect('admin/dashboards.404');
        }
    }

    /**
     * POST workorder/store-selected-sp-session
     *
     * Store selcted service provider in session
     *
     * @authenticated
     * @group Workorders
     *
     * @responseField count
     * @response 200
     */
    public function storeServiceproviderSelected(Request $request)
    {
        if(isset($request->selected_multiple_sp) && count($request->selected_multiple_sp) > 0)
        {
            if(Session::has('selected_multiple_sp_id'))
            {
                Session::put('selected_multiple_sp_id', $request->selected_multiple_sp);
            }
            else
            {
                Session::put('selected_multiple_sp_id', $request->selected_multiple_sp);
            }
        }

        return response()->json(['success'=>'success','']);
    }

    public function getFinalWorkerId($contractId, $propertyId, $assetId, $decision, $assignedWorker, $newDate, $workOrderId, $uniqueId, $selectedPermanentWorker) {
        try {
            $workerId = 0;

            if(isset($decision)){
                if($decision == WorkerAssigningDescision::SmartAssign->value){
                    $user = $this->getAuthenticatedUser();
                    $workerId = $this->manageWorkersSelection($contractId, $propertyId, $assetId, $newDate, $user, $workOrderId);

                    $newArray = [
                        'assign_type' => $workerId > 0 ? AssignType::Smart->value : AssignType::Normal->value
                    ];

                    $workOrdersList = $this->getWorkOrdersDetailsByValues('unique_id', $uniqueId);
                    $firstcriteria = $this->getSmartAssignCriteriaDescriptionDetailsByValues('work_order_id', $workOrderId);

                    if(!is_null($firstcriteria)){
                        if(isset($workOrdersList)){
                            foreach($workOrdersList as $data){
                                $searchedcriteria = $this->getSmartAssignCriteriaDescriptionDetailsByValues('work_order_id', $data);

                                $array = [
                                    'worker_id' => $workerId,
                                    'availability' => $firstcriteria->availability,
                                    'rating' => $firstcriteria->rating,
                                    'location' => $firstcriteria->location,
                                    'experience' => $firstcriteria->experience,
                                    'old_user' => $firstcriteria->old_user,
                                    'contract_id' => $contractId,
                                    'service_id' => $assetId,
                                    'property_building_id' => $propertyId,
                                    'log_id' => $firstcriteria->log_id,
                                    'work_order_id' => $data,
                                    'wo_date' => $firstcriteria->wo_date,
                                    'rating_value' => $firstcriteria->rating_value,
                                    'distance' => $firstcriteria->distance,
                                    'wo_count' => $firstcriteria->wo_count,
                                    'created_user_date_time' => $firstcriteria->created_user_date_time,
                                    'created_by' => $firstcriteria->created_by,
                                    'updated_by' => $firstcriteria->updated_by
                                ];

                                if(is_null($searchedcriteria)){
                                    $saveCriteria = $this->saveSmartAssignCriteriaDescription($array);

                                    if(!$saveCriteria){
                                        Log::info("getFinalWorkerId error: We cannot save the used criteria in smart assign worker with this Work Order: ".$data);
                                    }
                                }

                                else{
                                    $updateCriteria = $this->updateSmartAssignCriteriaDescriptionByValues('work_order_id', $data, $array);

                                    if(!$updateCriteria){
                                        Log::info("getFinalWorkerId error: We cannot update the used criteria in smart assign worker with this Work Order: ".$data);
                                    }
                                }

                                $updateWorkOrder = $this->updateWorkOrderDetailsByValues($data, $newArray);

                                if(!$updateWorkOrder){
                                    Log::info("getFinalWorkerId error: We cannot update this Work Order: ".$data);
                                }
                            }
                        }
                    }
                }

                elseif($decision == WorkerAssigningDescision::ManualSelectWorker->value){
                    $workerId = isset($selectedPermanentWorker) ? $selectedPermanentWorker : 0;
                }

                else{
                    $workerId = $assignedWorker;
                }
            }

            else{
                $workerId = isset($assignedWorker) ? $assignedWorker : $workerId;
            }

            return $workerId;
        }

        catch (\Throwable $th) {
            Log::error("getFinalWorkerId error: ".$th);
        }
    }
}

