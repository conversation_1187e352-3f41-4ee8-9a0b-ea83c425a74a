<?php

namespace App\Http\Controllers;
use Mail;

use Illuminate\Http\Request;
use App\Models\User;
use Helper;
use Response;
use AUTH;
use DB;
use Storage;
use ArPHP\I18N\Arabic;
use App;
use Session;
use App\Jobs\SendAdvancedContractReportJob;
use PDF;
use PDF2;
use DateTime;
use stdClass;
use App\Models\ServiceProvider;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use FilesystemIterator;
use App\Http\Controllers\Controller;
use App\Models\Contracts;
use Log;
use Mccarlosen\LaravelMpdf\Facades\LaravelMpdf;
use App\Http\Helpers\ReportQueryHelper;
use App\Http\Helpers\ImagesUploadHelper;


class AdvancedContractReportController extends Controller
{
    private $userType;

    public function __construct(){
        $this->userType = \AUTH::user()?->user_type;
    }




    public function advancedContractReport(Request $request)
    {
        $redirectinactiveuser = ReportQueryHelper::redirectinactiveuser();
        if ($redirectinactiveuser != "active user") {
            return $redirectinactiveuser;
        }
        $redirect_page=$request->redirect_page ?? 1;
        $report=$request->report_type;
        $months=$request->months;
        if (strpos($months, ' to ') !== false) {
            list($startStr, $endStr) = explode(' to ', $months);
        } else {
            $startStr = $endStr = $months;
        }
        $start_date = date('Y-m-d', strtotime("first day of $startStr"));
        $end_date   = date('Y-m-d', strtotime("last day of $endStr"));

        $report_type=$request->type ?? 'csv';
        $contract_ids = $request->contract_ids;
        $logedin_user = auth()->user();
        $user_id = $logedin_user->id;
        $project_user_id = Auth::user()->project_user_id;
        $lang = App::getLocale();
        $user = Auth::user();
        $link_start = asset('uploads/');
        $report_number = 'RPT' . rand(1000, 9999);
        $filename = rand(10000000, 99999999) . '.xlsx';
        $project_image = Helper::getProjectImage($user->project_id);
        $report_details = ['user_id' => $user_id, 'filename' => $filename, 'report_no' => $report_number, 'report_type' => $report_type, 'requested_at' => date('Y-m-d H:i:s'), 'status' => 'pending', 'start_date' => $start_date, 'end_date' => $end_date, 'project_user_id' => $project_user_id];
        $report_id =  ReportQueryHelper::createReport($report_details);
        if ($report_id) {
            $project_image_link = asset('img/OSOOL_logo_svg.svg'); 
            if (!empty($project_image) || $project_image != null) {
                $project_image_link = ImagesUploadHelper::displayImage($project_image, 'uploads/project_images', 1);
                if (empty($project_image_link)) { 
                $project_image_link = asset('img/OSOOL_logo_svg.svg'); 
                }
            }
           SendAdvancedContractReportJob::dispatch($request->all(), Auth::user(), $report_number, $filename, $lang, $_SERVER["DOCUMENT_ROOT"], $project_image_link, $link_start, $report_id,$project_user_id,$report);
        }
        App::setLocale($lang);
        $request->session()->put('generated', ['report_no' => $report_number]);
        if($redirect_page){
            return view('applications.admin.reports.downloadConfirmation', ['report_no' => $report_number]);
        }
        return json_encode(['msg'=>'Success']);
    }


    // public function uploadExcel($report_id,$file_name,Request $request)
    // {
    //     if ($request->hasFile('file')) {
    //         $file = $request->file('file');
    //         $filename = 'Full_Report_' . now()->format('Ymd_His') . '.xlsx';
    //         $path = $file->storeAs('public/reports', $file_name);
    //         $rpt = ['generated_at'=>date('Y-m-d H:i:s'), 'status'=>'generated', 'oci_link' => asset(Storage::url($path))];
    //         ReportQueryHelper::editReport($report_id, $rpt);
    //         return response()->json(['status' => 'ok', 'message' => 'File saved']);
    //         //return response()->json(['path' => asset(Storage::url($path))]);
    //     }
    //     //return response()->json(['error' => 'No file uploaded'], 400);
    // }



   
}
