<?php
    namespace App\Http\Controllers\CRMProjects;
    use App\Http\Controllers\Controller;
    use Illuminate\Http\Request;
    use App\Http\Traits\FunctionsTrait;

    class GantChartController extends Controller{
        use FunctionsTrait;

        public function openGantChart($id) {
            try {
                $user = $this->getAuthenticatedUser();

                if(is_null($user)){
                    
                    return redirect('admin/dashboards.404');
                }

                else{
                    $projectId = $this->decryptCryptedString($id);
                   
                    if(!isset($projectId)){
                        
                        return redirect()->route('CRMProjects.list');
                    }

                    else{
                        return view('CRMProjects.charts.gaint', ['projectID' => $projectId]);
                    }
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("gantChartDetails error: ".$th);
                return redirect('admin/dashboards.404');
            }
        }
    }
?>