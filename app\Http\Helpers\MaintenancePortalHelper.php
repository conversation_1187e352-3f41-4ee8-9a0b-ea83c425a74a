<?php
namespace App\Http\Helpers;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Session;
use Ya<PERSON>ra\DataTables\DataTables;
use Carbon\Carbon;
use App\Models\Property;
use App\Models\AssetCategory;
use App\Models\User;
use App\Models\WorkOrders;
use App\Models\Notification;
use App\Models\MaintenanceRequest;
use App\Models\ChecklistTasks;
use App\Http\Helpers\PropertyHelper;
use App\Http\Helpers\ImagesUploadHelper;
use Illuminate\Support\Facades\Cache;
use App\Enums\WorkOrderStatus;

class MaintenancePortalHelper
{
    /**
     * Read a maintenance request notification and fetch associated details.
     *
     * @param string|null $notification_read
     * @param string|null $notification_id
     * @param string|null $maintenance_request_id
     * @return array
     */
    public static function readMaintenanceRequestNotification($notification_read = null, $notification_id = null, $maintenance_request_id = null, $loggedInUser)
    {
        // Get the current user's ID
        $user_id = $loggedInUser->id;

        // Decrypt the notification ID
        $notification_id = Crypt::decryptString($notification_id);

        // Retrieve the notification by its ID
        $notification = Notification::find($notification_id);

        // Create an array of users who have read the notification
        $read_user_arr = !empty($notification->is_read_by_users) ? explode(',', $notification->is_read_by_users) : [];

        // Add the current user to the list if not already present
        if (!in_array($user_id, $read_user_arr)) {
            $read_user_arr[] = $user_id;
        }

        // Update the list of users who have read the notification
        $notification->update(['is_read_by_users' => implode(',', $read_user_arr)]);

        // Determine the maintenance request ID from the notification
        $request_id = trim($notification->maintenance_section_id) ?: '0';

        // Retrieve maintenance request details
        $mr_details = MaintenanceRequest::find($request_id);

        // Fetch and assign the asset category name using a relationship (assuming 'assetCategory' is the relationship)
        $mr_details->load('assetCategory');

        // Initialize the data array with maintenance request details
        $data['mr_details'] = $mr_details;

        // Check if a related work order exists
        $work_order = WorkOrder::where('maintenance_request_id', $request_id)->first();

        // Assign the work order ID to data if it exists, otherwise set it to 0
        $data['work_order_id'] = isset($work_order) ? $work_order->id : 0;

        return $data;
    }

    /**
     * Retrieve and format maintenance request details.
     *
     * @param string $maintenance_request_id
     * @return array
     */
    public static function fetchMaintenanceRequestDetails($maintenance_request_id)
    {
        $data = [];
        if ($maintenance_request_id != '') {
            // Retrieve maintenance request details
            $mrDetails = MaintenanceRequest::with('building', 'assetCategory')->find($maintenance_request_id);

            if ($mrDetails) {
                // Load the asset category relationship
                $mrDetails->load('assetCategory');

                // Cache images locally
                $image1Key = "image_{$maintenance_request_id}_1";
                $mrDetails->image1 = Cache::remember($image1Key, 60, function () use ($mrDetails) {
                    return empty(trim($mrDetails->image1)) ? '' : ImagesUploadHelper::displayImage($mrDetails->image1, 'uploads/maintanance_request');
                });

                $image2Key = "image_{$maintenance_request_id}_2";
                $mrDetails->image2 = Cache::remember($image2Key, 60, function () use ($mrDetails) {
                    return empty(trim($mrDetails->image2)) ? '' : ImagesUploadHelper::displayImage($mrDetails->image2, 'uploads/maintanance_request');
                });

                $image3Key = "image_{$maintenance_request_id}_3";
                $mrDetails->image3 = Cache::remember($image3Key, 60, function () use ($mrDetails) {
                    return empty(trim($mrDetails->image3)) ? '' : ImagesUploadHelper::displayImage($mrDetails->image3, 'uploads/maintanance_request');
                });

                // Check and set apartment number based on generated_from and phone
                if (trim($mrDetails->generated_from) === 'app' && trim($mrDetails->phone) !== "") {
                    $checkUserData = User::where('phone', $mrDetails->phone)->where('user_type', 'tenant')->first();

                    if ($checkUserData) {
                        $mrDetails->apartment_no = $checkUserData->apartment;
                    } else {
                        $mrDetails->apartment_no = '';
                    }
                } else {
                    $mrDetails->apartment_no = '';
                }

                if(isset($mrDetails->building->building_name)) {
                    $mrDetails->buildingName = $mrDetails->building->building_name;
                }

                if($mrDetails->asset_category_id !=0 && $mrDetails->assetCategory->asset_category) {
                    $mrDetails->asset_category = $mrDetails->assetCategory->asset_category;
                }


                $data['mr_details'] = $mrDetails;
            }

            // Retrieve associated work order ID
            $workOrder = WorkOrders::where('maintanance_request_id', $maintenance_request_id)->first();
            $data['work_order_id'] = $workOrder ? $workOrder->id : 0;
        } else {
            $data['work_order_id'] = 0;
        }

        return $data;
    }

    /**
     * Retrieve filtered maintenance request data for AJAX requests.
     *
     * @param array $buildingIds
     * @param array $requestStatuses
     * @param array $searchCriteria
     * @return \Illuminate\Support\Collection
     */
    public static function retrieveFilteredMaintenanceRequests($buildings_arr, $request_status, $search, $page)
    {
        // Initialize the query builder
        $sqlList = MaintenanceRequest::select('id', 'name', 'building_id', 'property_id', 'floor', 'space_no', 'description', 'status', 'created_at', 'generated_from', 'user_id', 'phone', 'place', 'group_id','app_type')->with('building_details','userDetailsById')
            ->whereIn('building_id', $buildings_arr);
        if($page == "grouped") {
            $sqlList->where('generated_from', 'tenant')
            ->where('group_id', '!=', 0)
            ->groupBy('group_id');
        } else {
            $sqlList->where('generated_from', '!=', 'tenant');
        }

        if (!empty($request_status)) {
            if (in_array('Completed', $request_status)) {
                // If 'Completed' status is included, map it to 'Finished'
                $new_status = 'Finished';
                $status_arr = $request_status;
                array_push($status_arr, $new_status);
                $sqlList = $sqlList->whereIn('status', $status_arr);
            } else {
                $sqlList = $sqlList->whereIn('status', $request_status);
            }
        }

        if (isset($search['buildings'])) {
            // Filter by selected buildings
            $sqlList = $sqlList->whereIn('building_id', $search['buildings']);
        }

        if ($search['dateRange'] && !empty($search['dateRange']['startd'])) {
            // Filter by date range
            $sqlList = $sqlList->whereBetween('created_at', [$search['dateRange']['startd'] . ' 00:00:00', $search['dateRange']['endd'] . ' 23:59:59']);
        }

        if ($search['search'] && !empty($search['search'])) {
            // Search by ID or name
            $sqlList = $sqlList->where(function ($query) use ($search) {
                $query->where('maintanance_request.group_id', 'LIKE', "%{$search['search']}%")
                    ->orWhere('maintanance_request.name', 'LIKE', "%{$search['search']}%")
                    ->orWhere('maintanance_request.id', 'LIKE', "%{$search['search']}%");
            });
        }

        // Get the count
        $count = $sqlList->count();

        if (isset($search['start']) && isset($search['length'])) {
            $sqlList->skip($search['start'])->take($search['length']);
        }

        // Order the results by ID in descending order and get the results as a collection
        $sqlList = $sqlList->orderBy('id', 'desc')->get();

        return [
            'count' => $count,
            'result' => $sqlList
        ];
    }

    /**
     * Generate DataTables response for maintenance request list with custom actions.
     *
     * @param \Illuminate\Support\Collection $dataList
     * @return \Yajra\DataTables\DataTables
     */
    public static function generateMaintenanceRequestDataTable($dataList, $data)
    {
        return Datatables::of($dataList)
            ->addColumn('action', function ($data) {
                // Create action buttons for each row
                $actionButtons = '<ul class="orderDatatable_actions mb-0 min-w-0 max-w-50">'
                    . '<li><a class="view showdetails" data-id="' . $data->id . '" title="View" href="javascript:void(0)"><i class="la la-eye"></i></i></a></li>'
                    . '</ul>';
                return $actionButtons;
            })
            ->addColumn('group_action', function ($data) {
                // Create action buttons for each row
                $actionButtons = '<ul class="orderDatatable_actions mb-0  mb-0 min-w-0 max-w-50">'
                    . '<li><a class="view show_group" data-id="' . $data->id . '" data-group-id="' . $data->group_id . '" title="View"  href="javascript:void(0);" data-target="#modal-basic-group-details" data-toggle="modal"><i class="la la-eye"></i></i></a></li>'
                    . '</ul>';
                return $actionButtons;
            })
            ->editColumn('floor', function ($data) {
                return $data->floor ? $data->floor : '-';
            })
            ->editColumn('space_no', function ($data) {
                return $data->space_no ? $data->space_no : '-';
            })
            ->editColumn('place', function ($data) {
                $places = Self::getPlacesByGroupId($data->group_id);
                return $places ? implode(', ', $places) : '-';
            })
            ->editColumn('description', function ($data) {
                // Edit the 'description' column to display a message if it's empty
                if ($data->description == NULL) {
                    return '<span class="max-td">' . __('user_management_module.user_forms.label.no_description_added') . ' </span>';
                } else {
                    return '<span class="max-td">' . $data->description . ' </span>';
                }
            })
            ->editColumn('status', function ($data) {
                // Edit the 'status' column to display a formatted status
                $status = Self::getMaintenanceRequestStatus($data->id,$data->status);
                return $status;
            })
            ->editColumn('building_id', function ($data) {
                // Edit the 'building_id' column to display the building name
                return PropertyHelper::getPropertyBuildingName($data->building_id, $data->property_id);
            })
            ->editColumn('created_at', function ($data) {
                // Edit the 'created_at' column to display a formatted date
                return date('d/m/Y h:i A', strtotime($data->created_at));
            })
            ->editColumn('name', function ($data) {
                // Edit the 'name' column to display the maintenance request creator's name
                return Self::getMaintenanceCreatedName($data->generated_from, $data->name, $data->phone, $data->user_id);
            })
            ->editColumn('generated_from', function ($data) {
                // Edit the 'generated_from' column to display 'worker_app' if user_id is not 0
                if ($data->app_type == 'worker' ) {
                    return "worker_app";
                } else {
                    return $data->generated_from;
                }
            })
            ->with([
                'building_list' =>$dataList->pluck('building_details')->unique()->toArray(),
                'all_mrs' =>$dataList->pluck('id')->unique()->toArray()
            ])
            ->rawColumns(['id', 'name', 'building_id', 'floor', 'space_no', 'description', 'status', 'created_at', 'action',
            'group_action'])
            ->setTotalRecords($data['total_row_count'])
            ->skipPaging()
            ->make(true);
    }
    public static function getPlacesByGroupId($groupId)
{
    try {

        $places = MaintenanceRequest::where('group_id', $groupId)->pluck('place')->toArray();

        if (empty($places)) {
            return ['No places found'];
        }
        return array_unique($places);
    } catch (\Exception $e) {
         \Log::error("Error fetching places for group_id $groupId: " . $e->getMessage());
        return ['Error fetching places'];
    }
}


    /**
     * Get the human-readable maintenance request status.
     *
     * @param int $maintenance_request_id
     * @param string $mrStatus
     * @return string
     */
    public static function getMaintenanceRequestStatus($maintenance_request_id,$mrStatus)
    {
        // Default status is empty
        $status = '';

        $workOrder = WorkOrders::where('maintanance_request_id', $maintenance_request_id)->first();

        if ($workOrder && $workOrder->status == 4 && $mrStatus == 'In Progress') {
            $mrStatus = 'Completed';
        }

        // Map the provided status to a human-readable status
        $status = in_array($mrStatus, ['Pending', 'Rejected', 'In Progress', 'Completed']) ? $mrStatus : ($mrStatus == 'Finished' ? 'Completed' : $status);

        return $status;
    }

    public static function newGetMaintenanceRequestStatus($workOrder,$mrStatus)
    {
        // Default status is empty
        $status = '';


        if ($workOrder && $workOrder->status == 4 && $mrStatus == 'In Progress') {
            $mrStatus = 'Completed';
        }

        // Map the provided status to a human-readable status
        $status = in_array($mrStatus, ['Pending', 'Rejected', 'In Progress', 'Completed']) ? $mrStatus : ($mrStatus == 'Finished' ? 'Completed' : $status);

        return $status;
    }


    /**
     * Get the name of the user who created the maintenance request.
     *
     * @param int $id
     * @return string
     */
    public static function getMaintenanceCreatedName($generated_from, $name, $phone, $userID = NULL)
    {
        if ($generated_from === 'web') {
            // If the request was generated from the web, return the maintenance request name
            return $name ?? __('user_management_module.user_forms.label.notAdded');
        } else {
            if($generated_from == "app" && $name != NULL) {
                return $name;
            } elseif(isset($phone) && $phone != NUll) {
                // If not, check if there is a user associated with the phone number
                $user = User::where('phone', $phone)->first();

                if (isset($userID) && $userID != NUll && $userID != 0) {
                    $user = User::where('id', $userID)->first();
                }

                if ($user) {
                    return $user->name;
                }
            } elseif (isset($userID) && $userID != NUll) {
                $user = User::where('id', $userID)->first();

                if ($user) {
                    return $user->name;
                }
            }
            return $name ?? __('user_management_module.user_forms.label.notAdded');
        }
    }


    public static function clearSessionByKey(string $sessionKey): void
    {
        //check if session has key and forget it if exists
        if (Session::has($sessionKey))
            Session::forget($sessionKey);
    }

    public static function retrieveFilteredMaintenanceRequestsWithPagination($buildings_arr, $request_status, $search, $page)
    {
        // Initialize the query builder
        $sqlList = MaintenanceRequest::query()
            ->select('id', 'name', 'building_id', 'property_id', 'floor', 'space_no', 'description', 'status', 'created_at', 'generated_from', 'user_id', 'phone', 'place', 'group_id', 'app_type')
            ->with(['building_details', 'userDetailsById','singleWorkOrder','reschedule'])
//            ->with(['singleWorkOrder'])
            ->whereIn('building_id', $buildings_arr);
        if ($page == "grouped") {
            $sqlList
                ->selectRaw('group_id as group_id2, COUNT(*) as request_count')
                ->where('generated_from', '=','tenant')
                ->where('group_id', '!=', 0)
                ->groupBy('group_id');
        } else {
            $sqlList->where('generated_from', '!=', 'tenant');
        }

        if (!empty($request_status)) {
            if (in_array('Completed', $request_status)) {
                // If 'Completed' status is included, map it to 'Finished'
                $new_status = 'Finished';
                $status_arr = $request_status;
                array_push($status_arr, $new_status);
                $sqlList = $sqlList->whereIn('status', $status_arr);
            } 
            
            else {
                //$sqlList = $sqlList->whereIn('status', $request_status);

                if (in_array('In progress', $request_status)) {
                    $sqlList = $sqlList->whereHas('singleWorkOrder', function ($query) {
                        $query->where('status', '<>', WorkOrderStatus::Closed->value);
                    });
                }

                $sqlList = $sqlList->whereIn('status', $request_status);
            }
        }

        if (!empty($search['buildings'])) {
            // Filter by selected buildings
            $sqlList = $sqlList->whereIn('building_id', $search['buildings']);
        }

        if ($search['dateRange'] && !empty($search['dateRange']['startd'])) {
            // Filter by date range
            $sqlList = $sqlList->whereBetween('created_at', [$search['dateRange']['startd'] . ' 00:00:00', $search['dateRange']['endd'] . ' 23:59:59']);
        }

        if ($search['search'] && !empty($search['search'])) {
            // Search by ID or name
            $sqlList = $sqlList->where(function ($query) use ($search) {
                $query->where('maintanance_request.group_id', 'LIKE', "%{$search['search']}%")
                    ->orWhere('maintanance_request.name', 'LIKE', "%{$search['search']}%")
                    ->orWhere('maintanance_request.id', 'LIKE', "%{$search['search']}%");
            });
        }

        return $sqlList;
    }


    public static function getBuildingNameByBuildingDetails($building_details)
    {
        if ($building_details) {
            $property = $building_details->property;

            if ($property->property_type == 'complex') {
                // If the property is of type 'complex', return complex name + building name
                return $property->complex_name . ' ' . $property->building_name;
            } else {
                // Otherwise, return just the building name
                return $building_details->building_name;
            }
        } else {
            return '';
        }
    }

}



