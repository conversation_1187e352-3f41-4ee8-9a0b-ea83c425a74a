<?php

namespace App\Http\Livewire\Accounting\Report\Account;

use App\Http\Helpers\Helper;
use App\Services\Finance\TransactionReportService;
use Carbon\Carbon;
use Livewire\Component;
use Barryvdh\Snappy\Facades\SnappyPdf as PDF;

class Account extends Component
{

    public $accounts = [];
    public $currency;
    public $start_date;
    public $end_date;
    public $account;
    public $type;

    protected $listeners = ['getAccounts', 'downloadPdf'];

    public function getAccounts($data)
    {
        $this->account = $data['account'];
        $this->type = $data['type'];
        $this->start_date = Carbon::parse($data['from_date'])->format('Y-m-d');
        $this->end_date = Carbon::parse($data['to_date'])->format('Y-m-d');
        $this->loadData();
    }


    public function mount($data = [])
    {
        $this->type = 'payment';
        $this->accounts = $data['accounts'] ?? [];
        $this->currency = Helper::currency();
    }

    public function loadData()
    {
        $service =  app(TransactionReportService::class);
        $data = $service->accounts(['start_date' => $this->start_date, 'end_date' => $this->end_date, 'account' => $this->account, 'type' => $this->type]);

        if (@$data['status'] == "success") {
            $this->accounts = $data['data'] ?? [];
        }
    }

    public function downloadPdf()
    {
        $html = view('livewire.accounting.report.account.pdf', ['from_date' => $this->start_date, 'to_date' => $this->end_date, 'accounts' => $this->accounts, 'currency' => $this->currency, 'type' => $this->type])->render();

        $pdf = PDF::loadHTML($html)->setPaper('a4', 'landscape');

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->inline(); // use inline to output PDF content
        }, 'account_summary.pdf');
    }

    public function render()
    {
        return view('livewire.accounting.report.account.accounts');
    }
}
