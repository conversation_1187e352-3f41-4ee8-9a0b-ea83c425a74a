<?php

namespace App\Http\Livewire\Accounting\Report\Transaction;

use App\Http\Helpers\Helper;
use App\Services\Finance\TransactionReportService;
use Carbon\Carbon;
use Livewire\Component;
use Barryvdh\Snappy\Facades\SnappyPdf as PDF;

class Account extends Component
{

    public $accounts = [];
    public $currency;
    public $start_date;
    public $end_date;
    public $account;
    public $category;

    protected $listeners = ['getAccounts', 'downloadPdf'];

    public function getAccounts($data)
    {
        $this->account = $data['account'];
        $this->category = $data['category'];
        $this->start_date = Carbon::parse($data['from_date'])->format('Y-m-d');
        $this->end_date = Carbon::parse($data['to_date'])->format('Y-m-d');
        $this->loadData();
    }


    public function mount($data = [])
    {
        $this->accounts = $data['accounts'] ?? [];
        $this->currency = Helper::currency();
    }

    public function loadData()
    {
        $service =  app(TransactionReportService::class);
        $data = $service->accounts(['start_date' => $this->start_date, 'end_date' => $this->end_date, 'account' => $this->account, 'category' => $this->category]);

        if (@$data['status'] == "success") {
            $this->accounts = $data['data'] ?? [];
        }
    }

    public function downloadPdf()
    {
        $html = view('livewire.accounting.report.transaction.pdf', ['from_date' => $this->start_date, 'to_date' => $this->end_date, 'accounts' => $this->accounts, 'currency' => $this->currency])->render();

        $pdf = PDF::loadHTML($html)->setPaper('a4', 'landscape');

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->inline(); // use inline to output PDF content
        }, 'transaction_summary.pdf');
    }

    public function render()
    {
        return view('livewire.accounting.report.transaction.accounts');
    }
}
