<?php
      namespace App\Http\Livewire\BulkImport;
    use Livewire\Component;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\TempBulkImportTrait;

    class ShowBulkProjectImportProgress extends Component{
        use FunctionsTrait, TempBulkImportTrait;

        public $token;
        public $decryptedToken;
        public $progressPercentage;

        public function render(){
            $this->calculeProgressBar();
            return view('livewire.bulk-import.show-bulk-project-import-progress');
        }

        public function mount() {
            try {
                $this->initDecryptedToken();
                $this->setProgressPercentage(0);
            } 
            
            catch (\Throwable $th) {
                Log::error("mount error: ".$th);
            }
        }

        public function initDecryptedToken() {
            try {
                $this->decryptedToken = $this->decryptToken($this->token);
            } 
            
            catch (\Throwable $th) {
                Log::error("initDecryptedToken error: ".$th);
            }
        }

        public function setProgressPercentage($value) {
            try {
                $this->progressPercentage = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setProgressPercentage error: ".$th);
            }
        }

        public function calculeProgressBar() {
            try {
                $batch = $this->findDispatch($this->decryptedToken['batch']);

                if(is_null($batch)){
                    Log::error("calculeProgressBar error: No batch found with this batchId: ".$this->decryptedToken['batch']);
                    return redirect('admin/dashboards.404');
                }

                else{
                    $totalJobs = $this->convertValueToInt($batch->totalJobs);
                    $pendingJobs = $this->convertValueToInt($batch->pendingJobs);
                    $completedJobs = $this->substrictionOperation($totalJobs, $pendingJobs);

                    if($totalJobs == $completedJobs){
                        $this->setProgressPercentage(100);
                    }

                    else{
                        $divid = $this->divisionOperation($completedJobs, $totalJobs);
                        $multip = $this->multiplicationOperation($divid, 100);
                        $result = $this->convertValueToInt($multip);
                        $this->setProgressPercentage($result);
                    }
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("calculeProgressBar error: ".$th);
            }
        }

        public function backToPrevious() {
            try {
                $this->clearBulkImportFileSession();
                $delete = $this->deleteTempBulkImportByProjectId($this->decryptedToken['projectId']);

                if($delete){
                    return $this->goToRouteName('bulk-import.openBulkImport');
                }

                else{
                    Log::error("backToPrevious error: We cannot delete the bulk import row");
                    return redirect('admin/dashboards.404');
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("backToPrevious error: ".$th);
            }
        }

        public function openImportationResult() {
            try {
                $this->clearBulkImportFileSession();
                return redirect()->route("bulk-import.openResultBulkImport", ['token' => $this->token]);
            } 
            
            catch (\Throwable $th) {
                Log::error("openImportationResult error: ".$th);
            }
        }
    }
?>