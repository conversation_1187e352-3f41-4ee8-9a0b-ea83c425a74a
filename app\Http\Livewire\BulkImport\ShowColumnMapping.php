<?php
    namespace App\Http\Livewire\BulkImport;
    use Livewire\Component;
    use Illuminate\Support\Facades\Log;
    use App\Http\Helpers\ReportQueryHelper;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\UserTrait;
    use App\Http\Traits\TempBulkImportTrait;
    use App\Http\Traits\PriorityTrait;
    use App\Http\Traits\ServiceTrait;
    use App\Http\Traits\PropertyTrait;
    use App\Http\Traits\PropertyBuildingTrait;
    use App\Http\Traits\AssetTrait;
    use App\Http\Traits\RegionTrait; 
    use App\Http\Traits\CityTrait;  
    use App\Http\Traits\ConfigurationTrait;
    use App\Http\Traits\ServiceProviderProjectMappingTrait;
    use App\Jobs\UsersImportJob;
    use App\Jobs\PrioritiesImportJob;
    use App\Jobs\ServicesImportJob;
    use App\Jobs\PropertiesImportJob;
    use App\Jobs\PropertiesBuildingImportJob;
    use App\Jobs\AssetImportJob;
    use App\Enums\ResultType;
    use App\Enums\Identifier;

    class ShowColumnMapping extends Component{
        use FunctionsTrait, UserTrait, TempBulkImportTrait, PriorityTrait, ServiceTrait, PropertyTrait, PropertyBuildingTrait, AssetTrait, RegionTrait, CityTrait, ConfigurationTrait, ServiceProviderProjectMappingTrait;

        public $token;
        public $projectId;
        public $project;
        public $usersList;
        public $prioritiesList;
        public $servicesList;
        public $propertiesList;
        public $propertiesBuildingList;
        public $assetsList;
        public $requiredUserType;
        public $validUserType;
        public $requiredEmail;
        public $validEmail;
        public $validTypeMobileNumber;
        public $validMobileNumberLength;
        public $requiredName;
        public $requiredPriorityName;
        public $requiredServiceWindow;
        public $validServiceWindowNumber;
        public $requiredServiceWindowType;
        public $validTypeServieceWindowType;
        public $requiredResponseTime;
        public $validTypeResponseTime;
        public $requiredResponseTimeType;
        public $validTypeResponseTimeType;
        public $requiredServiceName;
        public $requiredServicePriority;
        public $validServicePriority;
        public $requiredServiceType;
        public $validServiceType;
        public $requiredRegion;
        public $validRegion;
        public $requiredCity;
        public $validCity; 
        public $requiredPropertyType;
        public $validPropertyType;
        public $requiredPropertyName;
        public $validPropertyName;
        public $requiredBuildingCount;
        public $validBuildingCount;
        public $validBuildingCountNull;
        public $requiredLatitude;
        public $validLatitude;
        public $requiredLongitude;
        public $validLongtitude;
        public $requiredPropertyNameBuilding;
        public $validPropertyNameBuilding;
        public $requiredBuildingName;
        public $requiredBuildingZone;
        public $requiredBuildingUnit;
        public $requiredBuildingUnitType;
        public $requiredPropertyNameAsset;
        public $validPropertyNameAsset;
        public $requiredBuildingNameAsset;
        public $requiredServiceTypeAsset;
        public $validServiceTypeAsset;
        public $requiredZoneAsset;
        public $requiredUnitAsset;
        public $requiredAssetName;
        public $requiredAssetSymbole;
        public $requiredAssetNumber;
        public $validAssetNumber;
        public $validPurchaseDate;
        public $validAssetStatus;
        public $validDamageDate;
        public $selectedType;
        public $userIdentifier;
        public $priorityIdentifier;
        public $serviceIdentifier;
        public $propertyIdentifier;
        public $validZone;
        public $assetIdentifier;
        protected $listeners = ['importFile'];
        public $user;
        public $selectedLanguage;
        public $projectName;
        public $serviceProviderId;

        public function render(){
            $serviceProviderList = $this->getServiceProviderListByValues('project_id', $this->projectId) ?? null;
            return view('livewire.bulk-import.show-column-mapping', compact('serviceProviderList'));
        }

        public function mount() {
            try {
                $this->initProjectId();
                $this->initUsersList();
                $this->initPrioritiesList();
                $this->initServicesList();
                $this->initPropertiesList();
                $this->initPropertiesBuildingList();
                $this->initAssetsList();
                $this->initProject();
                $this->setRequiredUserType(false);
                $this->setValidUserType(false);
                $this->setRequiredName(false);
                $this->setRequiredEmail(false);
                $this->setValidEmail(false);
                $this->setValidTypeMobileNumber(false);
                $this->setValidMobileNumberLength(false);
                $this->setRequiredPriorityName(false);
                $this->setRequiredServiceWindow(false);
                $this->setValidServiceWindowNumber(false);
                $this->setRequiredServiceWindowType(false);
                $this->setValidTypeServieceWindowType(false);
                $this->setRequiredResponseTime(false);
                $this->setValidTypeResponseTime(false);
                $this->setRequiredResponseTimeType(false);
                $this->setValidTypeResponseTimeType(false);
                $this->setRequiredServiceName(false);
                $this->setRequiredServicePriority(false);
                $this->setValidServicePriority(false);
                $this->setRequiredServiceType(false);
                $this->setValidServiceType(false);
                $this->setRequiredRegion(false);
                $this->setValidRegion(false);
                $this->setRequiredCity(false);
                $this->setValidCity(false);
                $this->setRequiredPropertyType(false);
                $this->setValidPropertyType(false);
                $this->setRequiredPropertyName(false);
                $this->setValidPropertyName(false);
                $this->setRequiredBuildingCount(false);
                $this->setValidBuildingCount(false);
                $this->setValidBuildingCountNull(false);
                $this->setRequiredLatitude(false);
                $this->setValidLatitude(false);
                $this->setRequiredLongitude(false);
                $this->setValidLongtitude(false);
                $this->setRequiredPropertyNameBuilding(false);
                $this->setValidPropertyNameBuilding(false);
                $this->setRequiredBuildingName(false);
                $this->setRequiredBuildingZone(false);
                $this->setRequiredBuildingUnit(false);
                $this->setRequiredBuildingUnitType(false);
                $this->setRequiredPropertyNameAsset(false);
                $this->setValidPropertyNameAsset(false);
                $this->setRequiredBuildingNameAsset(false);
                $this->setRequiredServiceTypeAsset(false);
                $this->setValidServiceTypeAsset(false);
                $this->setRequiredZoneAsset(false);
                $this->setRequiredUnitAsset(false);
                $this->setRequiredAssetName(false);
                $this->setRequiredAssetSymbole(false);
                $this->setRequiredAssetNumber(false);
                $this->setValidAssetNumber(false);
                $this->setValidPurchaseDate(false);
                $this->setValidAssetStatus(false);
                $this->setValidDamageDate(false);
                $this->setSelectedType([]);
                $this->setUserIdentifier(Identifier::Email->value);
                $this->setPriorityIdentifier(Identifier::PriorityLevel->value);
                $this->setServiceIdentifier(Identifier::Service->value);
                $this->setPropertyIdentifier(Identifier::PropertyName->value);
                $this->setAssetIdentifier(Identifier::Asset->value);
                $this->initUser();
                $this->initSelectedLanguage();
                $this->initProjectName();
                $this->setServiceProviderId("");
            } 
            
            catch (\Throwable $th) {
                Log::error("mount error: ".$th);
            }
        }

        public function initProjectId() {
            try {
                $this->projectId = $this->decryptCryptedString($this->token);
            } 
            
            catch (\Throwable $th) {
                Log::error("initProjectId error: ".$th);
            }
        }

        public function initUsersList() {
            try {
                $this->usersList = $this->getDataSession("users");
            } 
            
            catch (\Throwable $th) {
                Log::error("initUsersList error: ".$th);
            }
        }

        public function initPrioritiesList() {
            try {
                $this->prioritiesList = $this->getDataSession("priorities");
            } 
            
            catch (\Throwable $th) {
                Log::error("initPrioritiesList error: ".$th);
            }
        }

        public function initServicesList() {
            try {
                $this->servicesList = $this->getDataSession("services");
            } 
            
            catch (\Throwable $th) {
                Log::error("initServicesList error: ".$th);
            }
        }

        public function initPropertiesList() {
            try {
                $this->propertiesList = $this->getDataSession("properties");
            } 
            
            catch (\Throwable $th) {
                Log::error("initPropertiesList error: ".$th);
            }
        }

        public function initPropertiesBuildingList() {
            try {
                $this->propertiesBuildingList = $this->getDataSession("propertiesBuilding");
            } 
            
            catch (\Throwable $th) {
                Log::error("initPropertiesBuildingList error: ".$th);
            }
        }

        public function initAssetsList() {
            try {
                $this->assetsList = $this->getDataSession("assets");
            } 
            
            catch (\Throwable $th) {
                Log::error("initAssetsList error: ".$th);
            }
        }

        public function initProject() {
            try {
                $this->project = ReportQueryHelper::getProjectByID($this->projectId);
            } 
            
            catch (\Throwable $th) {
                Log::error("initProject error: ".$th);
            }
        }

        public function setRequiredUserType($value) {
            try {
                $this->requiredUserType = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredUserType error: ".$th);
            }
        }

        public function setValidUserType($value) {
            try {
                $this->validUserType = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidUserType error: ".$th);
            }
        }

        public function setRequiredName($value) {
            try {
                $this->requiredName = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredName error: ".$th);
            }
        }

        public function setRequiredEmail($value) {
            try {
                $this->requiredEmail = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredEmail error: ".$th);
            }
        }

        public function setValidEmail($value) {
            try {
                $this->validEmail = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidEmail error: ".$th);
            }
        }

        public function setValidTypeMobileNumber($value) {
            try {
                $this->validTypeMobileNumber = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidTypeMobileNumber error: ".$th);
            }
        }

        public function setValidMobileNumberLength($value) {
            try {
                $this->validMobileNumberLength = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidMobileNumberLength error: ".$th);
            }
        }

        public function setRequiredPriorityName($value) {
            try {
                $this->requiredPriorityName = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredPriorityName error: ".$th);
            }
        }

        public function setRequiredServiceWindow($value) {
            try {
                $this->requiredServiceWindow = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredServiceWindow error: ".$th);
            }
        }
        
        public function setValidServiceWindowNumber($value) {
            try {
                $this->validServiceWindowNumber = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidServiceWindowNumber error: ".$th);
            }
        }
        
        public function setRequiredServiceWindowType($value) {
            try {
                $this->requiredServiceWindowType = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredServiceWindowType error: ".$th);
            }
        }

        public function setValidTypeServieceWindowType($value) {
            try {
                $this->validTypeServieceWindowType = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidTypeServieceWindowType error: ".$th);
            }
        }
        
        public function setRequiredResponseTime($value) {
            try {
                $this->requiredResponseTime = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredResponseTime error: ".$th);
            }
        }

        public function setValidTypeResponseTime($value) {
            try {
                $this->validTypeResponseTime = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidResponseTime error: ".$th);
            }
        }

        public function setRequiredResponseTimeType($value) {
            try {
                $this->requiredResponseTimeType = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredResponseTimeType error: ".$th);
            }
        }

        public function setValidTypeResponseTimeType($value) {
            try {
                $this->validTypeResponseTimeType = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidTypeResponseTimeType error: ".$th);
            }
        }

        public function setRequiredServiceName($value) {
            try {
                $this->requiredServiceName = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredServiceName error: ".$th);
            }
        }

        public function setRequiredServicePriority($value) {
            try {
                $this->requiredServicePriority = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredServicePriority error: ".$th);
            }
        }

        public function setValidServicePriority($value) {
            try {
                $this->validServicePriority = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidServicePriority error: ".$th);
            }
        }

        public function setRequiredServiceType($value) {
            try {
                $this->requiredServiceType = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredServiceType error: ".$th);
            }
        }

        public function setValidServiceType($value) {
            try {
                $this->validServiceType = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidServiceType error: ".$th);
            }
        }

        public function setRequiredRegion($value) {
            try {
                $this->requiredRegion = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredRegion error: ".$th);
            }
        }

        public function setValidRegion($value) {
            try {
                $this->validRegion = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidRegion error: ".$th);
            }
        }

        public function setRequiredCity($value) {
            try {
                $this->requiredCity = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredCity error: ".$th);
            }
        }

        public function setValidCity($value) {
            try {
                $this->validCity = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidCity error: ".$th);
            }
        }

        public function setRequiredPropertyType($value) {
            try {
                $this->requiredPropertyType = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredPropertyType error: ".$th);
            }
        }

        public function setValidPropertyType($value) {
            try {
                $this->validPropertyType = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidPropertyType error: ".$th);
            }
        }

        public function setRequiredPropertyName($value) {
            try {
                $this->requiredPropertyName = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredPropertyName error: ".$th);
            }
        }

        public function setValidPropertyName($value) {
            try {
                $this->validPropertyName = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidPropertyName error: ".$th);
            }
        }

        public function setRequiredBuildingCount($value) {
            try {
                $this->requiredBuildingCount = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredBuildingCount error: ".$th);
            }
        }

        public function setValidBuildingCount($value) {
            try {
                $this->validBuildingCount = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidBuildingCount error: ".$th);
            }
        }

        public function setValidBuildingCountNull($value) {
            try {
                $this->validBuildingCountNull = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidBuildingCountNull error: ".$th);
            }
        }

        public function setRequiredLatitude($value) {
            try {
                $this->requiredLatitude = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredLatitude error: ".$th);
            }
        }

        public function setValidLatitude($value) {
            try {
                $this->validLatitude = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidLatitude error: ".$th);
            }
        }

        public function setRequiredLongitude($value) {
            try {
                $this->requiredLongitude = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredLongitude error: ".$th);
            }
        }

        public function setValidLongtitude($value) {
            try {
                $this->validLongtitude = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidLongtitude error: ".$th);
            }
        }

        public function setRequiredPropertyNameBuilding($value) {
            try {
                $this->requiredPropertyNameBuilding = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredPropertyNameBuilding error: ".$th);
            }
        }

        public function setValidPropertyNameBuilding($value) {
            try {
                $this->validPropertyNameBuilding = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidPropertyNameBuilding error: ".$th);
            }
        }
        
        public function setRequiredBuildingName($value) {
            try {
                $this->requiredBuildingName = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredBuildingName error: ".$th);
            }
        }
        
        public function setRequiredBuildingZone($value) {
            try {
                $this->requiredBuildingZone = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredBuildingZone error: ".$th);
            }
        }
        
        public function setRequiredBuildingUnit($value) {
            try {
                $this->requiredBuildingUnit = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredBuildingUnit error: ".$th);
            }
        }

        public function setRequiredBuildingUnitType($value) {
            try {
                $this->requiredBuildingUnitType = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredBuildingUnitType error: ".$th);
            }
        }

        public function setRequiredPropertyNameAsset($value) {
            try {
                $this->requiredPropertyNameAsset = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredPropertyNameAsset error: ".$th);
            }
        }

        public function setValidPropertyNameAsset($value) {
            try {
                $this->validPropertyNameAsset = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidPropertyNameAsset error: ".$th);
            }
        }

        public function setRequiredBuildingNameAsset($value) {
            try {
                $this->requiredBuildingNameAsset = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredBuildingNameAsset error: ".$th);
            }
        }

        public function setRequiredServiceTypeAsset($value) {
            try {
                $this->requiredServiceTypeAsset = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredServiceTypeAsset error: ".$th);
            }
        }

        public function setValidServiceTypeAsset($value) {
            try {
                $this->validServiceTypeAsset = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidServiceTypeAsset error: ".$th);
            }
        }

        public function setRequiredZoneAsset($value) {
            try {
                $this->requiredZoneAsset = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredZoneAsset error: ".$th);
            }
        }

        public function setRequiredUnitAsset($value) {
            try {
                $this->requiredUnitAsset = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredUnitAsset error: ".$th);
            }
        }

        public function setRequiredAssetName($value) {
            try {
                $this->requiredAssetName = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredAssetName error: ".$th);
            }
        }

        public function setRequiredAssetSymbole($value) {
            try {
                $this->requiredAssetSymbole = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredAssetSymbole error: ".$th);
            }
        }

        public function setRequiredAssetNumber($value) {
            try {
                $this->requiredAssetNumber = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setRequiredAssetNumber error: ".$th);
            }
        }

        public function setValidAssetNumber($value) {
            try {
                $this->validAssetNumber = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidAssetNumber error: ".$th);
            }
        }
        
        public function setValidPurchaseDate($value) {
            try {
                $this->validPurchaseDate = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidPurchaseDate error: ".$th);
            }
        }

        public function setValidAssetStatus($value) {
            try {
                $this->validAssetStatus = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidAssetStatus error: ".$th);
            }
        }
        
        public function setValidDamageDate($value) {
            try {
                $this->validDamageDate = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setValidDamageDate error: ".$th);
            }
        }     

        public function getCheckSearchedProperty($value) {
            try {
                return $this->checkIfDataExistInArray($this->propertiesBuildingList, $value, 'property_name') ?? 0;
            } 
            
            catch (\Throwable $th) {
                Log::error("getCheckSearchedProperty error: ".$th);
            }
        }

        public function getCheckSearchedServiceTypeAsset($value) {
            try {
                return $this->checkIfDataExistInArray($this->servicesList, $value, 'service_name') ?? 0;
            } 
            
            catch (\Throwable $th) {
                Log::error("getCheckSearchedServiceTypeAsset error: ".$th);
            }
        }

        public function getCheckSearchedPriorityInPriorityList($value) {
            try {
                return $this->checkIfDataExistInArray($this->prioritiesList, $value, 'priority_name') ?? 0;
            } 
            
            catch (\Throwable $th) {
                Log::error("getCheckSearchedPriorityInPriorityList error: ".$th);
            }
        }

        public function getCheckSearchedPropertyNameInPropertiesList($value) {
            try {
                return $this->checkIfDataExistInArray($this->propertiesList, $value, 'property_name') ?? 0;
            } 
            
            catch (\Throwable $th) {
                Log::error("getCheckSearchedPropertyNameInPropertiesList error: ".$th);
            }
        }

        public function verifUserData($value) {
            try {
                $this->setRequiredUserType(false);
                $this->setValidUserType(false);
                $this->setRequiredName(false);
                $this->setRequiredEmail(false);
                $this->setValidEmail(false);
                $this->setValidTypeMobileNumber(false);
                $this->setValidMobileNumberLength(false); 

                if(!isset($value['user_type'])){
                    $this->setRequiredUserType(true);
                }

                else{
                    $this->setRequiredUserType(false);
                }

                if(isset($value['user_type']) && !in_array($value['user_type'], array('Project Owner Admin', 'Project Owner Employee', 'Building Manager Admin', 'Building Manager Employee', 'Service Provider Admin', 'Service Provider Supervisor', 'Service Provider Worker', 'Tenant'))){
                    $this->setValidUserType(true);
                }

                else{
                    $this->setValidUserType(false);
                }

                if(!isset($value['user_name'])){
                    $this->setRequiredName(true);
                }

                else{
                    $this->setRequiredName(false);
                } 

                if(!isset($value['email'])){
                    $this->setRequiredEmail(true);
                }

                else{
                    $this->setRequiredEmail(false);
                }

                if(isset($value['email']) && !$this->isValidEmail($value['email'])){
                    $this->setValidEmail(true);
                }

                else{
                    $this->setValidEmail(false);
                }

                if(isset($value['phone_number']) && !$this->isNumericValue($value['phone_number'])){
                    $this->setValidTypeMobileNumber(true);
                }

                else{
                    $this->setValidTypeMobileNumber(false);
                }

                if(isset($value['phone_number']) && !$this->validatePhoneNumber($value['phone_number'])){
                    $this->setValidMobileNumberLength(true);
                }

                else{
                    $this->setValidMobileNumberLength(false);
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("verifUserData error: ".$th);
            }
        }

        public function verifPrioritiesLevelData($value) {
            try {
                $this->setRequiredPriorityName(false);
                $this->setRequiredServiceWindow(false);
                $this->setValidServiceWindowNumber(false);
                $this->setRequiredServiceWindowType(false);
                $this->setValidTypeServieceWindowType(false);
                $this->setRequiredResponseTime(false);
                $this->setValidTypeResponseTime(false);
                $this->setRequiredResponseTimeType(false);
                $this->setValidTypeResponseTimeType(false);

                if(!isset($value['priority_name'])){
                    $this->setRequiredPriorityName(true);
                }

                else{
                    $this->setRequiredPriorityName(false);
                }

                if(!isset($value['service_window'])){
                    $this->setRequiredServiceWindow(true);
                }

                else{
                    $this->setRequiredServiceWindow(false);
                }

                if(isset($value['service_window']) && !$this->checkIfNumber($value['service_window'])){
                    $this->setValidServiceWindowNumber(true);
                }

                else{
                    $this->setValidServiceWindowNumber(false);
                }

                if(!isset($value['service_window_type'])){
                    $this->setRequiredServiceWindowType(true);
                }

                else{
                    $this->setRequiredServiceWindowType(false);
                }

                if(isset($value['service_window_type']) && !in_array(strtolower($value['service_window_type']), array('minutes', 'hours', 'days'))){
                    $this->setValidTypeServieceWindowType(true);
                }

                else{
                    $this->setValidTypeServieceWindowType(false);
                }

                if(!isset($value['response_time'])){
                    $this->setRequiredResponseTime(true);
                }

                else{
                    $this->setRequiredResponseTime(false);
                }

                if(isset($value['response_time']) && !$this->checkIfNumber($value['response_time'])){
                    $this->setValidTypeResponseTime(true);
                }

                else{
                    $this->setValidTypeResponseTime(false);
                }

                if(!isset($value['response_time_type'])){
                    $this->setRequiredResponseTimeType(true);
                }

                else{
                    $this->setRequiredResponseTimeType(false);
                }

                if(isset($value['response_time_type']) && !in_array(strtolower($value['response_time_type']), array('minutes', 'hours', 'days'))){
                    $this->setValidTypeResponseTimeType(true);
                }

                else{
                    $this->setValidTypeResponseTimeType(false);
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("verifPrioritiesLevelData error: ".$th);
            }
        }

        public function verifServicesData($value) {
            try {
                $this->setRequiredServiceName(false);
                $this->setRequiredServicePriority(false);
                $this->setValidServicePriority(false);
                $this->setRequiredServiceType(false);
                $this->setValidServiceType(false);

                if(!isset($value['service_name'])){
                    $this->setRequiredServiceName(true);
                }

                else{
                    $this->setRequiredServiceName(false);
                }

                if(!isset($value['priority'])){
                    $this->setRequiredServicePriority(true);
                }

                else{
                    $this->setRequiredServicePriority(false);
                }

                if(isset($value['priority']) && is_null($this->getPriorityDataByValues('priority_level', $value['priority'], $this->user->project_user_id)) && !$this->makeFullValidationForSearchedPriorityInPriorityList($value)){
                    $this->setValidServicePriority(true);
                }

                else{
                    $this->setValidServicePriority(false);
                }

                if(!isset($value['service_type'])){
                    $this->setRequiredServiceType(true);
                }

                else{
                    $this->setRequiredServiceType(false);
                }

                if(isset($value['service_type']) && !in_array($value['service_type'], array('Soft', 'Hard', 'soft', 'hard'))){
                    $this->setValidServiceType(true);
                }

                else{
                    $this->setValidServiceType(false);
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("verifServicesData error: ".$th);
            }
        }

        public function verifPropertiesData($value) {
            try {
                $this->setRequiredRegion(false);
                $this->setValidRegion(false);
                $this->setRequiredCity(false);
                $this->setValidCity(false);
                $this->setRequiredPropertyType(false);
                $this->setValidPropertyType(false);
                $this->setRequiredPropertyName(false);
                $this->setValidPropertyName(false);
                $this->setRequiredBuildingCount(false);
                $this->setValidBuildingCount(false);
                $this->setValidBuildingCountNull(false);
                $this->setRequiredLatitude(false);
                $this->setValidLatitude(false);
                $this->setRequiredLongitude(false);
                $this->setValidLongtitude(false);

                if(!isset($value['region'])){
                    $this->setRequiredRegion(true);
                }

                else{
                    $this->setRequiredRegion(false);
                }

                if(isset($value['region']) && is_null($this->getRegionInformationsByValues('name', $value['region']))){
                    $this->setValidRegion(true);
                }

                else{
                    $this->setValidRegion(false);
                }

                if(!isset($value['city'])){
                    $this->setRequiredCity(true);
                }

                else{
                    $this->setRequiredCity(false);
                }

                if(isset($value['city']) && is_null($this->getCityInformationsByValues('name_en', $value['city']))){
                    $this->setValidCity(true);
                }

                else{
                    $this->setValidCity(false);
                }

                if(!isset($value['property_type'])){
                    $this->setRequiredPropertyType(true);
                }

                else{
                    $this->setRequiredPropertyType(false);
                }

                if(isset($value['property_type']) && !in_array($value['property_type'], array('Complex', 'complex', 'Building', 'building'))){
                    $this->setValidPropertyType(true);
                }

                else{
                    $this->setValidPropertyType(false);
                }

                if(!isset($value['property_name'])){
                    $this->setRequiredPropertyName(true);
                }

                else{
                    $this->setRequiredPropertyName(false);
                }

                if(isset($value['property_name']) && !$this->getCheckSearchedProperty($value['property_name'])){
                    $this->setValidPropertyName(true);
                }

                else{
                    $this->setValidPropertyName(false);
                }

                if(!isset($value['building_count'])){
                    $this->setRequiredBuildingCount(true);
                }

                else{
                    $this->setRequiredBuildingCount(false);
                }

                if(isset($value['building_count']) && !$this->checkIfNumber($value['building_count'])){
                    $this->setValidBuildingCount(true);
                }

                else{
                    $this->setValidBuildingCount(false);
                }

                if(isset($value['building_count']) && $value['building_count'] == 0){
                    $this->setValidBuildingCountNull(true);
                }

                else{
                    $this->setValidBuildingCountNull(false);
                }

                if(!isset($value['gps_location_latitude'])){
                    $this->setRequiredLatitude(true);
                }

                else{
                    $this->setRequiredLatitude(false);
                }

                if(isset($value['gps_location_latitude']) && !$this->checkIfFloat($value['gps_location_latitude'])){
                    $this->setValidLatitude(true);
                }

                else{
                    $this->setValidLatitude(false);
                }

                if(!isset($value['gps_location_longitude'])){
                    $this->setRequiredLongitude(true);
                }

                else{
                    $this->setRequiredLongitude(false);
                }

                if(isset($value['gps_location_longitude']) && !$this->checkIfFloat($value['gps_location_longitude'])){
                    $this->setValidLongtitude(true);
                }

                else{
                    $this->setValidLongtitude(false);
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("verifPropertiesData error: ".$th);
            }
        }

        public function verifPropertiesBuildingData($value) {
            try {
                $this->setRequiredPropertyNameBuilding(false);
                $this->setValidPropertyNameBuilding(false);
                $this->setRequiredBuildingName(false);
                $this->setRequiredBuildingZone(false);
                $this->setRequiredBuildingUnit(false);
                $this->setRequiredBuildingUnitType(false);

                if(!isset($value['property_name'])){
                    $this->setRequiredPropertyNameBuilding(true);
                }

                else{
                    $this->setRequiredPropertyNameBuilding(false);
                }

                if(isset($value['property_name']) && !$this->makeFullValidationForSearchedProperty($value) && is_null($this->getPropertyInformationsByValues("property_tag", $value['property_name'], $this->user->project_user_id))){
                    $this->setValidPropertyNameBuilding(true);
                }

                else{
                    $this->setValidPropertyNameBuilding(false);
                }

                if(!isset($value['building_name'])){
                    $this->setRequiredBuildingName(true);
                }

                else{
                    $this->setRequiredBuildingName(false);
                }

                if(!isset($value['zone'])){
                    $this->setRequiredBuildingZone(true);
                }

                else{
                    $this->setRequiredBuildingZone(false);
                }

                if(!isset($value['unit'])){
                    $this->setRequiredBuildingUnit(true);
                }
                
                else{
                    $this->setRequiredBuildingUnit(false);
                }

                if(!isset($value['unit_type'])){
                    $this->setRequiredBuildingUnitType(true);
                }
                
                else{
                    $this->setRequiredBuildingUnitType(false);
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("verifPropertiesBuildingData error: ".$th);
            }
        }

        public function verifAssetsData($value) {
            try {
                $this->setRequiredPropertyNameAsset(false);
                $this->setValidPropertyNameAsset(false);
                $this->setRequiredBuildingNameAsset(false);
                $this->setRequiredServiceTypeAsset(false);
                $this->setValidServiceTypeAsset(false);
                $this->setRequiredZoneAsset(false);
                $this->setRequiredUnitAsset(false);
                $this->setRequiredAssetName(false);
                $this->setRequiredAssetSymbole(false);
                $this->setRequiredAssetNumber(false);
                $this->setValidAssetNumber(false);
                $this->setValidPurchaseDate(false);
                $this->setValidAssetStatus(false);
                $this->setValidDamageDate(false);

                if(!isset($value['property_name'])){
                    $this->setRequiredPropertyNameAsset(true);
                }

                else{
                    $this->setRequiredPropertyNameAsset(false);
                }

                if(isset($value['property_name']) && !$this->makeFullValidationForSearchedProperty($value) && is_null($this->getPropertyInformationsByValues("property_tag", $value['property_name'], $this->user->project_user_id))){
                    $this->setValidPropertyNameAsset(true);
                }

                else{
                    $this->setValidPropertyNameAsset(false);
                }

                if(!isset($value['building_name'])){
                    $this->setRequiredBuildingNameAsset(true);
                }

                else{
                    $this->setRequiredBuildingNameAsset(false);
                }

                if(!isset($value['service_type'])){
                    $this->setRequiredServiceTypeAsset(true);
                }

                else{
                    $this->setRequiredServiceTypeAsset(false);
                }

                if(isset($value['service_type']) && !$this->makeFullValidationForSearchedServiceTypeInAssets($value) && is_null($this->getSpeceficServiceInformationsByValues('asset_category', $value['service_type'], $this->user->project_user_id))){
                    $this->setValidServiceTypeAsset(true);
                }

                else{
                    $this->setValidServiceTypeAsset(false);
                }

                if(!isset($value['zone'])){
                    $this->setRequiredZoneAsset(true);
                }

                else{
                    $this->setRequiredZoneAsset(false);
                }

                if(!isset($value['unit'])){
                    $this->setRequiredUnitAsset(true);
                }

                else{
                    $this->setRequiredUnitAsset(false);
                }

                if(!isset($value['asset_name'])){
                    $this->setRequiredAssetName(true);
                }

                else{
                    $this->setRequiredAssetName(false);
                }

                if(!isset($value['asset_symbol'])){
                    $this->setRequiredAssetSymbole(true);
                }

                else{
                    $this->setRequiredAssetSymbole(false);
                }

                if(!isset($value['asset_number'])){
                    $this->setRequiredAssetNumber(true);
                }

                else{
                    $this->setRequiredAssetNumber(false);
                }

                if(isset($value['asset_number']) && $this->getStringLength($value['asset_number']) < 3 || $this->getStringLength($value['asset_number']) > 45){
                    $this->setValidAssetNumber(true);
                }

                else{
                    $this->setValidAssetNumber(false);
                } 

                if(isset($value['date_of_purchase']) && !$this->checkIfValidDate($value['date_of_purchase'])){
                    $this->setValidPurchaseDate(true);
                }               

                else{
                    $this->setValidPurchaseDate(false);
                }

                if(isset($value['damage_status']) && !in_array(strtolower($value['damage_status']), array('new', 'used', 'damaged', 'abandoned', 'disposed', 'donated', 'escrowed', 'for sale', 'in repair', 'in service', 'in storage', 'in use', 'leased', 'expired', 'missing', 'n/a', 'out of service', 'owned', 'owned and leased', 'pipeline', 'salvaged', 'sold', 'stolen', 'sub-leased', 'sub left', 'under contract', 'unknown'))){
                    $this->setValidAssetStatus(true);
                }

                else{
                    $this->setValidAssetStatus(false);
                }

                if(isset($value['damage_date']) && !$this->checkIfValidDate($value['damage_date'])){
                    $this->setValidDamageDate(true);
                }

                else{
                    $this->setValidDamageDate(false);
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("verifAssetsData error: ".$th);
            }
        }

        public function setSelectedType($value) {
            try {
                $this->selectedType = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setSelectedType error: ".$th);
            }
        }

        public function setUserIdentifier($value) {
            try {
                $this->userIdentifier = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setUserIdentifier error: ".$th);
            }
        }

        public function setPriorityIdentifier($value) {
            try {
                $this->priorityIdentifier = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setPriorityIdentifier error: ".$th);
            }
        }

        public function setServiceIdentifier($value) {
            try {
                $this->serviceIdentifier = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setServiceIdentifier error: ".$th);
            }
        }

        public function setPropertyIdentifier($value) {
            try {
                $this->propertyIdentifier = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setPropertyIdentifier error: ".$th);
            }
        }

        public function setAssetIdentifier($value) {
            try {
                $this->assetIdentifier = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setAssetIdentifier error: ".$th);
            }
        }

        public function getManagedEntredUser($value) {
            try {
                $decodedJson = json_decode($value, true);
                return $this->manageEntredUser($decodedJson['email'], $this->user->project_user_id);
            } 
            
            catch (\Throwable $th) {
                Log::error("getManagedEntredUser error: ".$th);
            }
        }

        public function getManagedEntredPrioritiesLevel($value) {
            try {
                $decodedJson = json_decode($value, true);
                return $this->manageEntredPriorityLevel($decodedJson['priority_name'], $this->user->project_user_id);
            } 
            
            catch (\Throwable $th) {
                Log::error("getManagedEntredPrioritiesLevel error: ".$th);
            }
        }

        public function getManageEntredService($value) {
            try {
                $decodedJson = json_decode($value, true);
                return $this->manageEntredService($decodedJson['service_name'] , $this->user->project_user_id);
            } 
            
            catch (\Throwable $th) {
                Log::error("getManageEntredService error: ".$th);
            }
        }

        public function getManagedEntredProperties($value) {
            try {
                $decodedJson = json_decode($value, true);
                return $this->manageEntredProperty($decodedJson['property_name'] , $this->user->project_user_id);
            } 
            
            catch (\Throwable $th) {
                Log::error("getManagedEntredPrioritiesLevel error: ".$th);
            }
        }

        public function getManagedEntredPropertiesBuilding($value) {
            try {
                $decodedJson = json_decode($value, true);
                return $this->manageEntredPropertyBuilding($decodedJson['property_name'], $decodedJson['building_name'], $this->user->project_user_id, $decodedJson['zone'], $decodedJson['unit']);
            } 
            
            catch (\Throwable $th) {
                Log::error("getManagedEntredPropertiesBuilding error: ".$th);
            }
        }

        public function getManagedEntredAssets($value) {
            try {
                $decodedJson = json_decode($value, true);
                return $this->manageEntredAsset($decodedJson['asset_name'], $decodedJson['asset_number'], $this->user->project_user_id);
            } 
            
            catch (\Throwable $th) {
                Log::error("getManagedEntredPrioritiesLevel error: ".$th);
            }
        }

        public function getCountSelectedType() {
            try {
                return $this->getCountOfItemsInsideArray($this->selectedType);
            } 
            
            catch (\Throwable $th) {
                Log::error("getCountSelectedType error: ".$th);
            }
        }

        public function initUser() {
            try {
                $this->user = $this->getAuthenticatedUser();
            } 
            
            catch (\Throwable $th) {
                Log::error("initUser error: ".$th);
            }
        }

        public function initSelectedLanguage() {
            try {
                $this->selectedLanguage = $this->getLocalLanguage();
            } 
            
            catch (\Throwable $th) {
                Log::error("initSelectedLanguage error: ".$th);
            }
        }

        public function initProjectName() {
            try {
                $this->projectName = $this->selectedLanguage == 'en' ? $this->project->project_name : $this->project->project_name_ar;
            } 
            
            catch (\Throwable $th) {
                Log::error("initProjectName error: ".$th);
            }
        }

        public function setServiceProviderId($value) {
            try {
                $this->serviceProviderId = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setServiceProviderId error: ".$th);
            }
        }

        public function importFile() {
            try {
                if($this->getCountSelectedType() > 0){
                    $usersJob = in_array(ResultType::Users->value, $this->selectedType) ? new UsersImportJob($this->projectId, $this->usersList, $this->user->id, $this->user->project_user_id, $this->serviceProviderId) : null;
                    $prioritiesJob = in_array(ResultType::PrioritiesLevels->value, $this->selectedType) ? new PrioritiesImportJob($this->projectId, $this->prioritiesList, $this->user->project_user_id) : null;
                    $servicesJob = in_array(ResultType::Services->value, $this->selectedType) ? new ServicesImportJob($this->projectId, $this->servicesList, $this->user->project_user_id) : null;
                    $propertiesJob = in_array(ResultType::Properties->value, $this->selectedType) ? new PropertiesImportJob($this->projectId, $this->propertiesList, $this->user->project_user_id, $this->propertiesBuildingList) : null;
                    $propertiesBuildingJob = in_array(ResultType::PropertyBuilding->value, $this->selectedType) ? new PropertiesBuildingImportJob($this->projectId, $this->propertiesBuildingList, $this->user->project_user_id) : null;
                    $assetsJob = in_array(ResultType::Assets->value, $this->selectedType) ? new AssetImportJob($this->projectId, $this->assetsList, $this->user->project_user_id, $this->user->id) : null;
                    $jobs = array_filter([$usersJob, $prioritiesJob, $servicesJob, $propertiesJob, $propertiesBuildingJob, $assetsJob]);
                    $batch = $this->createDispatch($jobs);
                    
                    if($batch->id){
                        $idTempBulk = $this->saveTempBulkImport(null, null, null, null, null, null, $this->projectId, $batch->id, $this->user->id);

                        if($idTempBulk){
                            $newCryptedToken = $this->cryptTempBulkToken($this->projectId, $batch->id, $idTempBulk);
                            return redirect()->route('bulk-import.openShowBulkImport', ["token" => $newCryptedToken]);
                        }

                        else{
                            Log::error("manageProjectBulkImport error: Cannot save the temp bulk row");
                            return redirect('admin/dashboards.404');
                        }
                    }

                    else{
                        Log::error("importFile error: No batch found with ID: ".$batch->id);
                        return redirect('admin/dashboards.404');
                    }
                }

                else{
                    Log::error('importFile error: No selected type found');
                    return back();
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("importFile error: ".$th);
            }
        }

        public function makeFullValidationForSearchedPriorityInPriorityList($value) {
            try {
                $getSearchedValue = $this->getCheckSearchedPriorityInPriorityList($value['priority']);

                if(!$getSearchedValue){
                    return false;
                }

                else{
                    $row = $this->prioritiesList->FirstWhere('priority_name', $value['priority']);

                    if(is_null($row)){
                        return false;
                    }

                    elseif(!$this->checkIfNumber($row['service_window']) || !$this->checkIfNumber($row['response_time']) || !in_array($row['service_window_type'], array('Minutes', 'Hours', 'Days', 'minutes', 'hours', 'days')) || !in_array($row['response_time_type'], array('Minutes', 'Hours', 'Days', 'minutes', 'hours', 'days'))){
                        return false;
                    }

                    else{
                        return true;
                    }
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("makeFullValidationForSearchedPriorityInPriorityList error: ".$th);
            }
        }

        public function makeFullValidationForSearchedProperty($value) {
            try {
                $getSearchedValue = $this->getCheckSearchedPropertyNameInPropertiesList($value['property_name']);

                if(!$getSearchedValue){
                    return false;
                }

                else{
                    $row = $this->propertiesList->FirstWhere('property_name', $value['property_name']);

                    if(is_null($row)){
                        return false;
                    }

                    else{
                        $region = $this->getRegionInformationsByValues('name', $row['region']); 
                        $city = $this->getCityInformationsByValues('name_en', $row['city']); 

                        if(is_null($row) || is_null($row) || !$this->checkIfFloat($row['gps_location_latitude']) || !$this->checkIfFloat($row['gps_location_longitude']) || !$this->getCheckSearchedProperty($row['property_name']) || !in_array($row['property_type'], array('Complex', 'complex', 'Building', 'building')) || !$this->checkIfNumber($row['building_count']) || $row['building_count'] == 0){
                            return false;
                        }

                        else{
                            return true;
                        }
                    }
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("makeFullValidationForSearchedProperty error: ".$th);
            }
        }

        public function makeFullValidationForSearchedServiceTypeInAssets($value) {
            try {
                $getSearchedValue = $this->getCheckSearchedServiceTypeAsset($value['service_type']);

                if(!$getSearchedValue){
                    return false;
                }

                else{
                    $row = $this->servicesList->FirstWhere('service_name', $value['service_type']);

                    if(is_null($row)){
                        return false;
                    }

                    elseif(!in_array($row['service_type'], array('Soft', 'Hard', 'soft', 'hard')) || (!$this->makeFullValidationForSearchedPriorityInPriorityList($row) && is_null($this->getPriorityDataByValues('priority_level', $row['priority'], $this->user->project_user_id))) ){
                        return false;
                    }

                    else{
                        return true;
                    }
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("makeFullValidationForSearchedServiceTypeInAssets error: ".$th);
            }
        }
    }
?>