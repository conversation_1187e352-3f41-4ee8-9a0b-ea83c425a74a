<?php
    namespace App\Http\Livewire\BulkImport;
    use Livewire\Component;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\TempBulkImportTrait;
    use App\Http\Traits\UserTrait;
    use App\Http\Traits\PriorityTrait;
    use App\Http\Traits\ServiceTrait;
    use App\Http\Traits\PropertyTrait;
    use App\Http\Traits\PropertyBuildingTrait; 
    use App\Http\Traits\AssetTrait;
    use App\Http\Traits\RoomsTypeFloorTrait; 
    use App\Http\Traits\AssetNameTrait;
    use App\Http\Traits\UserCompanyTrait;
    use App\Http\Traits\ServiceProviderTrait;
    use App\Enums\ResultType;
    use App\Enums\SwalType;

    class ShowResultBulkProjectImport extends Component{
        use FunctionsTrait, TempBulkImportTrait, UserTrait, PriorityTrait, ServiceTrait, PropertyTrait, PropertyBuildingTrait, AssetTrait, RoomsTypeFloorTrait, AssetNameTrait, UserCompanyTrait, ServiceProviderTrait;

        public $token;
        public $decryptedToken;
        public $tempId;
        public $projectId;
        public $temp;
        protected $listeners = ['openViewSelectedData', 'deleteDataList'];
        public $showConfirmModal;
        public $selectedLanguage;
        public $sericeProvider;
        public $user;

        public function render(){
            $this->initDecryptedToken();
            $this->initTempId();
            $this->initProjectId();
            $this->setTemp(); 
            return view('livewire.bulk-import.show-result-bulk-project-import');
        }

        public function mount() {
            try {
                $this->setShowConfirmModal(false);
                $this->initSelectedLanguage();
                $this->initUser();
                $this->initServiceProvider();
            } 
            
            catch (\Throwable $th) {
                Log::error("mount error: ".$th);
            }
        }

        public function initDecryptedToken() {
            try {
                $this->decryptedToken = $this->decryptToken($this->token);
            } 
            
            catch (\Throwable $th) {
                Log::error("initDecryptedToken error: ".$th);
            }
        }

        public function initTempId() {
            try {
                $this->tempId = $this->decryptedToken['tempId'];
            } 
            
            catch (\Throwable $th) {
                Log::error("initTempId error: ".$th);
            }
        }

        public function initProjectId() {
            try {
                $this->projectId = $this->decryptedToken['projectId'];
            } 
            
            catch (\Throwable $th) {
                Log::error("initTempId error: ".$th);
            }
        }

        public function setTemp() {
            try {
                $this->temp = $this->getTempBulkImportInformationsWithTrashedByTempId($this->tempId);
            } 
            
            catch (\Throwable $th) {
                Log::error("initTemp error: ".$th);
            }
        }

        public function setShowConfirmModal($value) {
            try {
                $this->showConfirmModal = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setShowConfirmModal error: ".$th);
            }
        }

        public function checkDataCount($type) {
            try {
                $count = 0;

                switch ($type) {
                    case ResultType::Users->value:
                        $oldData = isset($this->temp) ? $this->getDecodedJson($this->temp->users) : null;
                        $count = count($oldData ?? []);
                    break;

                    case ResultType::PrioritiesLevels->value:
                        $oldData = isset($this->temp) ? $this->getDecodedJson($this->temp->priorities_levels) : null;
                        $count = count($oldData ?? []);
                    break;

                    case ResultType::Services->value:
                        $oldData = isset($this->temp) ? $this->getDecodedJson($this->temp->assets_categories) : null;
                        $count = count($oldData ?? []);
                    break;

                    case ResultType::Properties->value:
                        $oldData = isset($this->temp) ? $this->getDecodedJson($this->temp->properties) : null;
                        $count = count($oldData ?? []);
                    break;

                    case ResultType::PropertyBuilding->value:
                        $oldData = isset($this->temp) ? $this->getDecodedJson($this->temp->properties_building) : null;
                        $count = count($oldData ?? []);
                    break;

                    case ResultType::Assets->value:
                        $oldData = isset($this->temp) ? $this->getDecodedJson($this->temp->assets) : null;
                        $count = count($oldData ?? []);
                    break;
                }

                return $count;
            } 
            
            catch (\Throwable $th) {
                Log::error("checkDataCount error: ".$th);
            }
        }

        public function openViewSelectedData($type) {
            try {
                $newToken = $this->cryptViewData($this->tempId, $type, $this->projectId);
                return redirect()->route('bulk-import.openResultSpeceficData', ['token' => $newToken]);
            } 
            
            catch (\Throwable $th) {
                Log::error("openViewSelectedData error: ".$th);
            }
        } 

        public function openConfirmModal() {
            try {
                $this->dispatchBrowserEvent('openModal');
            } 
            
            catch (\Throwable $th) {
                Log::error("openConfirmModal error: ".$th);
            }
        }

        public function deleteDataList($type) {
            try {
                $this->setTemp();

                switch ($type) {
                    case ResultType::Users->value:
                        $users = isset($this->temp) ? $this->getDecodedJson($this->temp->users) : null;

                        if(!is_null($users)){
                            $userIds = array_column($users, 'userId');
                            $deleteUser = $this->deleteUserByValue('id', $userIds);

                            if($deleteUser){
                                $update = $this->updateTempBulkImportByProjectId($this->projectId, 'users', null, $this->tempId);

                                if($update){
                                    $this->callSwalByType(SwalType::Success->value);
                                }

                                else{
                                    $this->callSwalByType(SwalType::Error->value); 
                                }
                            }

                            else{
                                $this->callSwalByType(SwalType::Error->value);  
                            }
                        }

                        else{
                            $this->callSwalByType(SwalType::Error->value);
                        }
                    break;

                    case ResultType::PrioritiesLevels->value:
                        $priorities = isset($this->temp) ? $this->getDecodedJson($this->temp->priorities_levels) : null;

                        if(!is_null($priorities)){
                            $priorityIds = array_column($priorities, 'priorityId');
                            $deletePriorities = $this->deletePriorityByValue('id', $priorityIds);

                            if($deletePriorities){
                                $update = $this->updateTempBulkImportByProjectId($this->projectId, 'priorities_levels', null, $this->tempId);

                                if($update){
                                    $this->callSwalByType(SwalType::Success->value);
                                }

                                else{
                                    $this->callSwalByType(SwalType::Error->value); 
                                }
                            }

                            else{
                                $this->callSwalByType(SwalType::Error->value); 
                            }
                        }

                        else{
                            $this->callSwalByType(SwalType::Error->value);
                        }
                    break;

                    case ResultType::Services->value:
                        $services = isset($this->temp) ? $this->getDecodedJson($this->temp->assets_categories) : null;

                        if(!is_null($services)){
                            $serviceIds = array_column($services, 'serviceId');
                            $deleteAssetName = $this->deleteAssetNameByValue('asset_category_id', $serviceIds);
                            $deleteAsset = $this->deleteAssetByValue('asset_category_id', $serviceIds);
                            $deleteService = $this->deleteServiceByValues('id', $serviceIds);

                            if($deleteService){
                                $update = $this->updateTempBulkImportByValues($this->projectId, 'assets_categories', 'assets', null, $this->tempId);

                                if($update){
                                    $this->callSwalByType(SwalType::Success->value);
                                }

                                else{
                                    $this->callSwalByType(SwalType::Error->value); 
                                }
                            }

                            else{
                                $this->callSwalByType(SwalType::Error->value);
                            }
                        }

                        else{
                            $this->callSwalByType(SwalType::Error->value);
                        }
                    break;

                    case ResultType::Properties->value:
                        $properties = isset($this->temp) ? $this->getDecodedJson($this->temp->properties) : null;

                        if(!is_null($properties)){
                            $propertyIds = array_column($properties, 'propertyId');
                            $deleteAsset = $this->deleteAssetByValue('property_id', $propertyIds);
                            $deleteProperty = $this->deleteProperyByValue('id', $propertyIds);
                            $update = $this->updateTempBulkImportByValues($this->projectId, 'properties', 'assets', null, $this->tempId);

                            if($update){
                                $this->callSwalByType(SwalType::Success->value);
                            }

                            else{
                                $this->callSwalByType(SwalType::Error->value); 
                            }
                        }

                        else{
                            $this->callSwalByType(SwalType::Error->value);
                        }
                    break;

                    case ResultType::PropertyBuilding->value:
                        $propertiesBulding = isset($this->temp) ? $this->getDecodedJson($this->temp->properties_building) : null;
                        
                        if(!is_null($propertiesBulding)){
                            $propertyBuildingIds = array_column($propertiesBulding, 'propertyBuildingId');
                            $roomTypesFloorArray = [];
                            $roomTypesArray = [];
                            $propertiesArray = [];

                            foreach ($propertiesBulding as $data) {
                                $roomTypeFloorList = $this->getRoomTypeFloorListByKeyValues('building_id', $data->propertyBuildingId);

                                if(!is_null($roomTypeFloorList)){
                                    foreach($roomTypeFloorList as $item){
                                        $roomTypesFloorArray[] = isset($item) ? $item->id : null;
                                        $roomTypesArray[] = isset($item) ? $item->room_type : null;
                                        $propertiesArray[] = isset($item) ? $item->property_id : null;
                                    }
                                }
                            }

                            $deleteRommType = $this->deleteRoomTypeByValue('id', $roomTypesArray);
                            $deleteRommTypeFloor = $this->deleteRoomTypeFloorByValue('id', $roomTypesFloorArray);
                            $deleteProperty = $this->deleteProperyByValue('id', $propertiesArray);
                            $deletePropertyBuilding = $this->deletePropertyBuildingByValue('id', $propertyBuildingIds);

                            if($deletePropertyBuilding){
                                $update = $this->updateTempBulkImportByProjectId($this->projectId, 'properties_building', null, $this->tempId);

                                if($update){
                                    $this->callSwalByType(SwalType::Success->value);
                                }
    
                                else{
                                    $this->callSwalByType(SwalType::Error->value); 
                                }
                            }

                            else{
                                $this->callSwalByType(SwalType::Error->value);
                            }
                        }

                        else{
                            $this->callSwalByType(SwalType::Error->value);
                        }
                    break;

                    case ResultType::Assets->value:
                        $assets = isset($this->temp) ? $this->getDecodedJson($this->temp->assets) : null;
                        $assetNamesArray = [];

                        if(!is_null($assets)){
                            foreach ($assets as $data) {
                                $assetRow = $this->getAssetInformationsById($data->assetId);
                                $assetNamesArray[] = isset($assetRow) ? $assetRow->asset_name_id : null;
                            }

                            $assetIds = array_column($assets, 'assetId');
                            $deleteAssetName = $this->deleteAssetNameByValue('id', $assetNamesArray);
                            $deleteAsset = $this->deleteAssetByValue('id', $assetIds);
                            
                            if($deleteAsset){
                                $update = $this->updateTempBulkImportByProjectId($this->projectId, 'assets', null, $this->tempId, null);

                                if($update){
                                    $this->callSwalByType(SwalType::Success->value);
                                }
    
                                else{
                                    $this->callSwalByType(SwalType::Error->value); 
                                }
                            }

                            else{
                                $this->callSwalByType(SwalType::Error->value);
                            }
                        }

                        else{
                            $this->callSwalByType(SwalType::Error->value);
                        }                        
                    break;
                    
                    default:
                        $this->callSwalByType(SwalType::Error->value);
                    break;
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("deleteDataList error: ".$th);
            }
        }

        public function callSwalByType($type) {
            try {
                $this->dispatchBrowserEvent('result', $type);
            } 
            
            catch (\Throwable $th) {
                Log::error("callSwalByType error: ".$th);
            }
        }

        public function initSelectedLanguage() {
            try {
                $this->selectedLanguage = $this->getLocalLanguage();
            } 
            
            catch (\Throwable $th) {
                Log::error("initSelectedLanguage error: ".$th);
            }
        }

        public function initServiceProvider() {
            try {
                $this->sericeProvider = isset($this->temp->service_provider_id) ? $this->getServiceProviderInformationByValue('id', $this->temp->service_provider_id) : null;
            } 
            
            catch (\Throwable $th) {
                Log::error("initServiceProvider error: ".$th);
            }
        }

        public function initUser() {
            try {
                $this->user = $this->getAuthenticatedUser();
            } 
            
            catch (\Throwable $th) {
                Log::error("initUser error: ".$th);
            }
        }
    }
?>