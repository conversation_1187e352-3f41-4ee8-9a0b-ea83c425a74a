<?php
    namespace App\Http\Livewire\BulkImport;
    use Livewire\Component;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\UserTrait;
    use App\Http\Traits\PriorityTrait;
    use App\Http\Traits\ServiceTrait;
    use App\Http\Traits\PropertyTrait;
    use App\Http\Traits\PropertyBuildingTrait;
    use App\Http\Traits\AssetTrait;
    use App\Http\Traits\ServiceProviderTrait;
    use App\Enums\ResultType;

    class ShowSpeceficDataList extends Component{
        use FunctionsTrait, UserTrait, PriorityTrait, ServiceTrait, PropertyTrait, PropertyBuildingTrait, AssetTrait, ServiceProviderTrait;

        public $token; 
        public $decryptedToken;
        public $type;
        public $tempId;
        public $temp;
        public $collectionData;
        public $selectedLanguage;
        public $sericeProvider;
        
        public function render(){
            $newCollection = $this->addGroupedData();
            return view('livewire.bulk-import.show-specefic-data-list', compact('newCollection'));
        }

        public function mount() {
            try {
                $this->initDecryptedToken();
                $this->initType();
                $this->initTempId();
                $this->setTemp();
                $this->initSelectedLanguage();
                $this->initServiceProvider();
                $this->initCollectionData();
            } 
            
            catch (\Throwable $th) {
                Log::error("mount error: ".$th);
            }
        }

        public function initDecryptedToken() {
            try {
                $this->decryptedToken = $this->decryptToken($this->token);
            } 
            
            catch (\Throwable $th) {
                Log::error("initDecryptedToken error: ".$th);
            }
        }

        public function initType() {
            try {
                $this->type = $this->decryptedToken['type'];
            } 
            
            catch (\Throwable $th) {
                Log::error("initType error: ".$th);
            }
        }

        public function initTempId() {
            try {
                $this->tempId = $this->decryptedToken['tempId'];
            } 
            
            catch (\Throwable $th) {
                Log::error("initTempId error: ".$th);
            }
        }

        public function setTemp() {
            try {
                $this->temp = $this->getTempBulkImportInformationsWithTrashedByTempId($this->tempId);
            } 
            
            catch (\Throwable $th) {
                Log::error("initTemp error: ".$th);
            }
        }

        public function initServiceProvider() {
            try {
                $this->sericeProvider = $this->getServiceProviderInformationByValue('id', $this->temp->service_provider_id);
            } 
            
            catch (\Throwable $th) {
                Log::error("initServiceProvider error: ".$th);
            }
        }

        public function manageNewData() {
            try {
                $data = collect([]);
                $newData = [];

                switch ($this->type) {
                    case ResultType::Users->value:
                        $oldData = isset($this->temp) ? $this->getDecodedJson($this->temp->users) : null;

                        if(!empty($oldData) && !is_null($oldData)){
                            foreach ($oldData as $row) {
                                $user = $this->getUserInformationsById($row->userId);
                                $newData = [
                                    'userId' => $row->userId,
                                    'email' => $user->email,
                                    'name' => $user->name,
                                    'phone' => $user->phone,
                                    'user_type' => $user->user_type,
                                    'emp_dept' => $user->emp_dept,
                                    'service_provider_id' => isset($this->sericeProvider) ? $this->sericeProvider->service_provider_id: null,
                                    'status' => $user->status
                                ];
                                $data->push($newData);
                            }
                        }

                        else{
                            $data->push(null);
                        }
                    break;

                    case ResultType::PrioritiesLevels->value:
                        $oldData = isset($this->temp) ? $this->getDecodedJson($this->temp->priorities_levels) : null;
                        
                        if(!empty($oldData) && !is_null($oldData)){
                            foreach ($oldData as $row) {
                                $priority = $this->getPriorityInformationsByKeyValues('id', $row->priorityId);

                                $newData = [
                                    'priorityId' => $row->priorityId,
                                    'priority_name' => $priority->priority_level,
                                    'service_window' => $priority->service_window,
                                    'service_window_type' => $priority->service_window_type,
                                    'response_time' => $priority->response_time,
                                    'response_time_type' => $priority->response_time_type,
                                ];
                                $data->push($newData);
                            }
                        }

                        else{
                            $data->push(null);
                        }
                    break;

                    case ResultType::Services->value:
                        $oldData = isset($this->temp) ? $this->getDecodedJson($this->temp->assets_categories) : null;

                        if(!empty($oldData) && !is_null($oldData)){
                            foreach ($oldData as $row) {
                                $service = $this->getServiceInformationsById($row->serviceId);

                                $newData = [
                                    'serviceId' => $row->serviceId,
                                    'service_name' => $service->asset_category,
                                    'service_type' => $service->service_type,
                                    'priority' => $service->priority_level
                                ];
                                $data->push($newData);
                            }
                        }

                        else{
                            $data->push(null);
                        }
                    break;

                    case ResultType::Properties->value:
                        $oldData = isset($this->temp) ? $this->getDecodedJson($this->temp->properties) : null;

                        if(!empty($oldData) && !is_null($oldData)){
                            foreach ($oldData as $row) {
                                $property = $this->getPropertyInformationsById($row->propertyId);

                                $newData = [
                                    'propertyId' => $row->propertyId,
                                    'region' => $this->selectedLanguage == "en" ? $property->region->name : $property->region->name_ar,
                                    'city' => $this->selectedLanguage == "en" ? $property->city->name_en : $property->region->name_ar,
                                    'property_name' => $property->property_tag,
                                    'latitude' => $property->latitude,
                                    'longitude' => $property->longitude,
                                    'property_type' => $property->property_type,
                                    'complex_name' => $property->complex_name,
                                    'building_count' => $property->buildings_count
                                ];
                                $data->push($newData);
                            }
                        }

                        else{
                            $data->push(null);
                        }
                    break;

                    case ResultType::PropertyBuilding->value:
                        $oldData = isset($this->temp) ? $this->getDecodedJson($this->temp->properties_building) : null;

                        if(!empty($oldData) && !is_null($oldData)){
                            foreach ($oldData as $row) {
                                $propertyBuilding = $this->getPropertyBuildingInformationsById($row->propertyBuildingId);
                                $roomsData = $propertyBuilding->rooms;

                                $newData = [
                                    'property_name' => $propertyBuilding->property->property_tag,
                                    'propertyBuildingId' => $row->propertyBuildingId ?? null,
                                    'rooms' => $propertyBuilding->rooms ?? null
                                ];
                                $data->push($newData);
                            }
                        }

                        else{
                            $data->push(null);
                        }
                    break;

                    case ResultType::Assets->value:
                        $oldData = isset($this->temp) ? $this->getDecodedJson($this->temp->assets) : null;

                        if(!empty($oldData) && !is_null($oldData)){
                            foreach ($oldData as $row) {
                                $asset = $this->getAssetInformationsById($row->assetId);

                                $newData = [
                                    'assetId' => $row->assetId,
                                    'building_name' => $asset->building->building_name,
                                    'property_name' => $asset->property->property_type == "complex" ? $asset->property->complex_name : $asset->property->property_tag,
                                    'service_type' => $asset->assetCategory->asset_category,
                                    'asset_name' => $asset->assetName->asset_name,
                                    'asset_symbol' => $asset->assetName->asset_symbol,
                                    'asset_number' => $asset->asset_number,
                                    'purchase_date' => $asset->purchase_date,
                                    'model_number' => $asset->model_number,
                                    'manufacturer_name' => $asset->manufacturer_name,
                                    'asset_status' => $asset->asset_status,
                                    'asset_damage_date' => $asset->asset_damage_date,
                                    'zone' => $asset->floor,
                                    'unit' => $asset->room,
                                ];
                                $data->push($newData);
                            }
                        }

                        else{
                            $data->push(null);
                        }
                    break;
                }
                return $data;
            } 
            
            catch (\Throwable $th) {
                Log::error("manageNewData error: ".$th);
            }
        }

        public function addGroupedData() {
            try {
                $collection = collect($this->collectionData);
                $groupedData = null;

                switch ($this->type) {
                    case ResultType::Users->value:
                        $groupedData = $collection->groupBy('user_type') ?? null;
                    break;

                    case ResultType::PrioritiesLevels->value:
                        $groupedData = $collection->groupBy('priority_name') ?? null;
                    break;

                    case ResultType::Services->value:
                        $groupedData = $collection->groupBy('priority') ?? null;
                    break;

                    case ResultType::Properties->value:
                        $groupedData = $collection->groupBy('property_name') ?? null;
                    break;

                    case ResultType::PropertyBuilding->value:
                        $groupedData = $collection->flatMap(function ($item) {
                            return $item['rooms'];
                        })->unique()->groupBy('floor') ?? null;
                    break;

                    case ResultType::Assets->value:
                        $groupedData = $collection->groupBy('asset_name') ?? null;
                    break;
                }

                return $groupedData;
            } 

            catch (\Throwable $th) {
                Log::error("addGroupedData error: ".$th);
            }
        }

        public function initSelectedLanguage() {
            try {
                $this->selectedLanguage = $this->getLocalLanguage();
            } 
            
            catch (\Throwable $th) {
                Log::error("initSelectedLanguage error: ".$th);
            }
        }

        public function initCollectionData() {
            try {
                $this->collectionData = $this->manageNewData();
            } 
            
            catch (\Throwable $th) {
                Log::error("initCollectionData error: ".$th);
            }
        }
    }
?>