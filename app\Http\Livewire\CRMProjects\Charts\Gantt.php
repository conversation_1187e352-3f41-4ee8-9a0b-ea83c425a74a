<?php
    namespace App\Http\Livewire\CRMProjects\Charts;
    use Livewire\Component;
    use Illuminate\Support\Facades\Log;
    use App\Services\CRM\Projects\ProjectGanttChartService;
    use App\Services\CRM\Projects\FinanceServices;
    use App\Http\Traits\FunctionsTrait;

    class Gantt extends Component{
        use FunctionsTrait;

        public $projectID;
        public $projectDetails;
        public $duration;
        public $selectedId;
        public $status;
        public $statusFilter;
        public $priority;
        public $search;
        public $showHideColumns;
        public $tempShowHideColumns;
        protected $listeners = ['setSelectedId', 'setStatus', 'updateStatusFilter', 'updatePriority', 'updateSearch', 'setDuration', 'setTempShowHideColumns'];

        public function render(){
            if (isset($this->projectDetails) && $this->projectDetails['status'] == 'success') {
                $data = $this->manageGanttResponse();
                return view('livewire.c-r-m-projects.charts.gantt', compact('data'));
            }

            else {
                return view('livewire.c-r-m-projects.empty-project');
            }
        }

        public function mount($projectID = null){
             $this->projectID =$projectID;
            try {
                $this->initProjectDetails();
                $this->setDuration('Week');
                $this->initShowHideColumns();
                $this->initTempShowHideColumns();
            } 
            
            catch (\Throwable $th) {
                Log::error('mount error: '.$th);
            }
        }

        public function initProjectDetails() {
            try {
                $financeService = app(FinanceServices::class);
                $this->projectDetails = $financeService->getProjectDetails($this->projectID);
            } 
            
            catch (\Throwable $th) {
                Log::error('initProjectDetails error: '.$th);
            }
        }

        public function setDuration($value) {
            try {
                $this->duration = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error('setDuration error: '.$th);
            }
        }

        public function initGanttChart() {
            try {
                $data = [
                    'status' => $this->statusFilter,
                    'priority' => $this->priority,
                    'name' => $this->search
                ];

                $ganttService = app(ProjectGanttChartService::class);
                return $ganttService->getProjectGanttChartDetails($this->duration, $this->projectID, $data);
            } 
            
            catch (\Throwable $th) {
                Log::error('initGanttChart error: '.$th);
            }
        }

        public function setSelectedId($value) {
            try {
                $this->selectedId = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error('setSelectedId error: '.$th);
            }
        }

        public function setStatus($value) {
            try {
                $this->status = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error('setStatus error: '.$th);
            }
        }

        public function initUpdateStatus() {
            try {
                $data = [
                    'task_id' => $this->selectedId,
                    'status' => $this->status
                ];

                $ganttService = app(ProjectGanttChartService::class);
                return $ganttService->updateTaskStatusByValues($this->projectID, $data);
            } 
            
             catch (\Throwable $th) {
                Log::error('initUpdateStatus error: '.$th);
            }
        }

        public function submitEditStatusForm() {
            $validatedData = $this->validate([
                'status' => 'required'
            ],
            [
                'item.required' => __( 'CRMProjects.status_required' )
            ]);

            try {
                if($validatedData){
                    $response = $this->initUpdateStatus();

                    if(isset($response) && $response['status'] == 'success'){
                        $this->callJsFunctionByValues('show-toastr', 'success', __('CRMProjects.status_updated_successfully'));
                        $this->initGanttChart($this->duration);
                        $this->closeModalByValue('closeUpdateStatusModal');
                    }

                    else{
                        $this->callJsFunctionByValues('show-toastr', 'error', __('CRMProjects.status_not_updated'));
                        $this->closeModalByValue('closeUpdateStatusModal');
                    }
                }
            } 
            
            catch (\Throwable $th) {
                Log::error('submitEditStatusForm error: '.$th);
            }
        }

        public function callJsFunctionByValues($key, $type, $message) {
            try {
                return $this->dispatchBrowserEvent($key, [
                    'type' => $type,
                    'message' => $message
                ]);
            } 
            
            catch (\Throwable $th) {
                Log::error('callJsFunctionByValues error: '.$th);
            }
        }

        public function closeModalByValue($key) {
            try {
                return $this->dispatchBrowserEvent($key);
            } 
            
            catch (\Throwable $th) {
                Log::error('closeModalByValue error: '.$th);
            }
        }

        public function setStatusFilter($value) {
            try {
                $this->statusFilter = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error('setStatusFilter error: '.$th);
            }
        }

        public function setPriority($value) {
            try {
                $this->priority = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error('setPriority error: '.$th);
            }
        }

        public function setSearch($value) {
            try {
                $this->search = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error('setSearch error: '.$th);
            }
        }

        public function initShowHideColumns() {
            try {
                $array = ['progress', 'comments', 'duration', 'owner', 'milestone'];
                $this->setShowHideColumns($array);
            } 
            
            catch (\Throwable $th) {
                Log::error('initShowHideColumns error: '.$th);
            }
        }

        public function setShowHideColumns($value) {
            try {
                $this->showHideColumns = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error('setShowHideColumns error: '.$th);
            }
        }

        public function updateStatusFilter($value) {
            try {
                $this->setStatusFilter($value);
                $data = $this->manageGanttResponse();
                $this->sendDataToJsByValues('chart-gantt-data', json_encode($data));
            } 
            
            catch (\Throwable $th) {
                Log::error('updateStatusFilter error: '.$th);
            }
        }

        public function manageGanttResponse() {
            try {
                $response = $this->initGanttChart();
                return isset($response) && isset($response['data']) ? $response['data']['tasks'] : null;
            } 
            
            catch (\Throwable $th) {
                Log::error('manageGanttResponse error: '.$th);
            }
        }

        public function sendDataToJsByValues($key, $value) {
            try {
                return $this->dispatchBrowserEvent($key, [
                    'tasks' => $value,
                    'showHideColumns' => json_encode($this->showHideColumns)
                ]);
            } 
            
            catch (\Throwable $th) {
                Log::error('sendDataToJsByValues error: '.$th);
            }
        }

        public function updatePriority($value) {
            try {
                $this->setPriority($value);
                $data = $this->manageGanttResponse();
                $this->sendDataToJsByValues('chart-gantt-data', json_encode($data));
            } 
            
            catch (\Throwable $th) {
                Log::error('updatePriority error: '.$th);
            }
        }

        public function updateSearch($value) {
            try {
                $this->setSearch($value);
                $data = $this->manageGanttResponse();
                $this->sendDataToJsByValues('chart-gantt-data', json_encode($data));
            } 
            
            catch (\Throwable $th) {
                Log::error('updateSearch error: '.$th);
            }
        }

        public function setTempShowHideColumns($value) {
            try {
                $this->tempShowHideColumns = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error('setTempShowHideColumns error: '.$th);
            }
        }

        public function updateShowHideColumns() {
            try {
                $this->setShowHideColumns($this->tempShowHideColumns);
                $data = $this->manageGanttResponse();
                $this->sendDataToJsByValues('chart-gantt-data', json_encode($data));
            } 
            
            catch (\Throwable $th) {
                Log::error('updateShowHideColumns error: '.$th);
            }
        }

        public function resetShowHideColumns() {
            try {
                $this->initShowHideColumns();
                $data = $this->manageGanttResponse();
                $this->sendDataToJsByValues('chart-gantt-data', json_encode($data));
            } 
            
            catch (\Throwable $th) {
                Log::error('resetShowHideColumns error: '.$th);
            }
        }

        public function initTempShowHideColumns() {
            try {
                $array = ['progress', 'comments', 'duration', 'owner'];
                $this->setTempShowHideColumns($array);
            } 
            
            catch (\Throwable $th) {
                Log::error('initTempShowHideColumns error: '.$th);
            }
        }

        public function resetAllFilters() {
            try {
                $this->setStatusFilter(null);
                $this->setPriority(null);
                $data = $this->manageGanttResponse();
                $this->sendDataToJsByValues('chart-gantt-data', json_encode($data));
                $this->callJsFunction('resetAllFilters');
            } 
            
            catch (\Throwable $th) {
                Log::error('resetAllFilters error: '.$th);
            }
        }

        public function callJsFunction($key) {
            try {
                $this->dispatchBrowserEvent($key);
            } 
            
            catch (\Throwable $th) {
                Log::error("callJsFunction error: ".$th);
            }
        }
    }
?>