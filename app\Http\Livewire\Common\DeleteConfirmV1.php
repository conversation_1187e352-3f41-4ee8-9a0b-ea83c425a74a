<?php

namespace App\Http\Livewire\Common;

use Livewire\Component;

class DeleteConfirmV1 extends Component
{
    public $itemId;
    public $itemName;
    public $deleteRoute;
    public $deleteFunctionName;
    public $data = [];

    protected $listeners = ['confirmDelete' => 'setItem'];

    public function setItem($id, $name, $deleteFunctionName = null, $data = [])
    {
        $this->itemId = $id;
        $this->itemName = $name;
        $this->deleteFunctionName = $deleteFunctionName;
        $this->data = $data;
        $this->dispatchBrowserEvent('open-delete-modal');
    }

    public function delete($delete = true)
    {
        if ($this->itemId) {

            $this->emit($this->deleteFunctionName ?? 'deleteItem', $this->itemId, $delete, $this->data);
            $this->dispatchBrowserEvent('close-delete-modal');
        }
    }

    public function render()
    {
        return view('livewire.common.delete-confirm-v1');
    }
}
