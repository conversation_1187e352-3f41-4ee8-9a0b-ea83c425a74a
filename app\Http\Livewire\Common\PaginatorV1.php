<?php

namespace App\Http\Livewire\Common;

use Livewire\Component;

class PaginatorV1 extends Component
{
    public $paginatorId;

    public $currentPage;
    public $perPage = 10;
    public $totalRecords = 0;
    public $totalPages = 1;
    public $startIndex = 1;
    public $endIndex = 1;
    public $functionName = 'fetchData';
    public $showPerPage = false;
    public $perPageValues = [10, 20, 40];
    public $updateUrl = false;

    // Dynamically register scoped listener for this paginator instance
    protected function getListeners()
    {
        return [
            "refreshPagination:{$this->paginatorId}" => 'initialize',
        ];
    }

    public function mount(
        $currentPage = null,
        $functionName = 'fetchData',
        $totalRecords = 0,
        $perPage = null,
        $perPageValues = null,
        $updateUrl = false
    ) {
        $this->paginatorId = $functionName;

        $this->initialize(
            $currentPage,
            $functionName,
            $totalRecords,
            $perPage,
            $perPageValues,
            $updateUrl
        );
    }

    public function initialize(
        $currentPage = null,
        $functionName = 'fetchData',
        $totalRecords = 0,
        $perPage = null,
        $perPageValues = null,
        $updateUrl = true
    ) {
        $this->functionName = $functionName;
        $this->totalRecords = $totalRecords;
        $this->perPage = $perPage ?? $this->perPage;
        $this->showPerPage = $perPage !== null;
        $this->perPageValues = $perPageValues ?? $this->perPageValues;
        $this->updateUrl = $updateUrl;

        $this->totalPages = $this->calculateTotalPages();
        $this->currentPage = $currentPage ?? request()->query('page', 1);
        $this->updateIndices();
    }

    public function updatedPerPage($value)
    {
        $this->emit("perPageUpdated_$this->functionName", $value);
    }

    public function goToPage($page)
    {
        if ($page < 1 || $page > $this->totalPages) {
            return;
        }

        $this->currentPage = $page;

        if ($this->updateUrl) {
            $this->emit('updateUrl', ['page' => $page, 'functionName' => $this->functionName]);
        }

        if (is_array($this->functionName)) {
            $args = array_merge($this->functionName['arg'], [$page]);
            $this->emit($this->functionName['name'], ...$args);
        } else {
            $this->emit($this->functionName, $page);
        }

        $this->updateIndices();
    }

    protected function updateIndices()
    {
        $this->startIndex = $this->getStartIndex();
        $this->endIndex = $this->getEndIndex();
    }

    protected function getStartIndex()
    {
        return ($this->currentPage - 1) * $this->perPage + 1;
    }

    protected function getEndIndex()
    {
        return min($this->currentPage * $this->perPage, $this->totalRecords);
    }

    protected function calculateTotalPages()
    {
        return ($this->perPage > 0) ? (int) ceil($this->totalRecords / $this->perPage) : 1;
    }

    public function render()
    {
        return view('livewire.common.pagination-v1');
    }
}
