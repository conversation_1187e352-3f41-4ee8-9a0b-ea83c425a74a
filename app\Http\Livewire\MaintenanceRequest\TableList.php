<?php

namespace App\Http\Livewire\MaintenanceRequest;

use App\Enums\ConfigurationCode;
use App\Http\Helpers\MaintenancePortalHelper;
use App\Http\Helpers\PropertyHelper;
use App\Http\Helpers\WorkorderHelper;
use App\Http\Traits\ConfigurationTrait;
use App\Http\Traits\FunctionsTrait;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;
use Livewire\Component;
use Livewire\WithPagination;
use App\Http\Traits\WorkOrdersTrait;

class TableList extends Component
{
    use WithPagination, FunctionsTrait, ConfigurationTrait, WorkOrdersTrait;

    public $perPage = 10;
    public $search = '';
    public $loggedInUser = null;

    public $notification_read = null;
    public $maintenance_request_id = null;
    public $notification_id = null;

//    public $requestSegments;
//    public $requestQuery;
    protected $requestObject = null;
    public $data = [];
    public $buildings = [];
    public $filter = [
        'status' => [],
//        'buildings' => null,
        'date_range' => [],
    ];

    protected $listeners = ['setPropertyBuildings'];
    public $selectedMaintenanceRequest;

    public function render()
    {

        $this->initRequestParams();

        $this->notification_read = $this->requestObject->query('notification_read') ?? null;
        $this->notification_read = $this->requestObject->query('notification_id') ?? null;
        $this->maintenance_request_id = $this->requestObject->query('maintenance_request_id') ?? null;

        // Check if the 'notification_read' parameter is provided and equal to 'notification-read'
        if (!empty($this->notification_read) && $this->notification_read == 'notification-read') {
            // When user reads a notification, fetch details of the maintenance request
            $read_mr_notification = MaintenancePortalHelper::readMaintenanceRequestNotification($this->notification_read, $this->notification_id, $this->maintenance_request_id, $this->loggedInUser);
            $this->data['mr_details'] = $read_mr_notification['mr_details'];
            $this->data['work_order_id'] = $read_mr_notification['work_order_id'];
            $this->data['mr_details']->buldingName = PropertyHelper::getPropertyBuildingName($this->data['mr_details']->building_id, $this->data['mr_details']->property_id);
        }

        // Determine the maintenance request ID based on the request parameter or decrypted 'maintenance_request_id'
        $maintenance_request_id = $this->requestObject->id ?? (trim($this->maintenance_request_id) != "" ? Crypt::decryptString($this->maintenance_request_id) : '');

        // Fetch details of the maintenance request
        $notification_data = MaintenancePortalHelper::fetchMaintenanceRequestDetails($maintenance_request_id);

        // Check if maintenance request details are available
        if (!empty($notification_data['mr_details'])) {
            $this->data['mr_details'] = $notification_data['mr_details'];
            $this->data['work_order_id'] = $notification_data['work_order_id'];
        } else {
            $this->data['work_order_id'] = 0;
        }

        // Split the building IDs into an array
        $buildings_arr = !empty($this->loggedInUser->building_ids) ? explode(',', $this->loggedInUser->building_ids) : [];

        // Get filtered properties based on the user
        $this->data['properties'] = WorkorderHelper::getFilteredProperties($this->loggedInUser, $this->loggedInUser->id);

        // Count the number of maintenance request details
        $this->data['total_row_count'] = 0;

        // Create a search array based on request parameters
        $search = array(
//            'status' => $this->filter['status'],
            'buildings' => $this->buildings,
            'dateRange' => array('startd' => @$this->filter['date_range'][0] ?? '', 'endd' => @$this->filter['date_range'][1] ?? ''),
            'search' => $this->search ?? ''
        );


        // Retrieve maintenance request data using AJAX
        $sqlList = MaintenancePortalHelper::retrieveFilteredMaintenanceRequestsWithPagination($buildings_arr, $this->filter['status'] ?? [], $search, 'list');
        $all_mrs = $sqlList->
        orderBy('id', 'desc')->pluck('id')->unique()->toArray();

        // Count the number of maintenance request details
        $this->data['total_row_count'] = $sqlList->count();

        // pass the query result of maintenance request details
        $maintenanceRequest = $sqlList->
        orderBy('id', 'desc')
            ->paginate($this->perPage);
        $this->data['maintenance_requests'] = $maintenanceRequest;
        $this->data['building_list'] = $maintenanceRequest->pluck('building_details')->unique()->toArray();
        $this->data['all_mrs'] = $all_mrs;

        //new code added by ghayth
        $workOrdersList = isset($this->selectedMaintenanceRequest) ? $this->getWorkOrdersListByValues('maintanance_request_id', $this->selectedMaintenanceRequest) : null;
        
//        dd($maintenanceRequest);
        return view('livewire.maintenance-request.table-list', [
            'data' => $this->data,
            'maintenanceRequests' => $maintenanceRequest,
            'loggedInUser' => $this->loggedInUser,
            'workOrdersList' => $workOrdersList
        ]);
    }

    //Runs once, immediately after the component is instantiated, but before render() is called.
    //This is only called once on initial page load and never called again, even on component refreshes
    public function mount()
    {
        $this->initUser();
        $this->initPerPage();
    }


    public function initPerPage()
    {
        try {
            $this->perPage = 10;//$this->getConfigurationByValue('code', ConfigurationCode::PerPage->value)->value;
        } catch (\Throwable $th) {
            Log::error("initPerPage error: " . $th);
        }
    }

    public function initUser()
    {
        try {
            $this->loggedInUser = $this->getAuthenticatedUser();
        } catch (\Throwable $th) {
            Log::error("initUser error: " . $th);
        }
    }

    public function initRequestParams()
    {
        $this->requestObject = \request();
    }

    public function updatingPerPage()
    {
        try {
            $this->resetPage();
        } catch (\Throwable $th) {
            Log::error("updatingPerPage error: " . $th);
        }
    }

    public function propertyFilterClicked()
    {
//        dd($this->buildings);
        $this->resetPage();
        $this->render();
    }

    public function propertyFilterReset()
    {
        $this->buildings = [];
        $this->resetPage();
        $this->render();
    }

    public function updatingFilterStatus()
    {
        try {
//            dd($this->filter);
            $this->resetPage();
        } catch (\Throwable $th) {
            Log::error("updatingPerPage error: " . $th);
        }
    }

    public function updatingFilterDateRange()
    {
        try {
            $this->resetPage();
        } catch (\Throwable $th) {
            Log::error("updatingPerPage error: " . $th);
        }
    }


    public function updateSearch()
    {
        try {
            $this->resetPage();
        } catch (\Throwable $th) {
            Log::error("updatingPerPage error: " . $th);
        }
    }

    public function updatingSearch()
    {
        try {
            $this->resetPage();
        } catch (\Throwable $th) {
            Log::error("updatingPerPage error: " . $th);
        }
    }

    public function setPropertyBuildings($value) {
        try {
            $this->buildings = $value;
        } 
        
        catch (\Throwable $th) {
            Log::error("setPropertyBuildings error: " . $th);
        }
    }
    
    //new code added by ghayth
    public function setSelectedMaintenanceRequest($value) {
        try {
            $this->selectedMaintenanceRequest = isset($value) ? $value : null;
        } 
        
        catch (\Throwable $th) {
            Log::error("setSelectedMaintenanceRequest error: " . $th);
        }
    }
}
