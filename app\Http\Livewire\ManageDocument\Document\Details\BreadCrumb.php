<?php

namespace App\Http\Livewire\ManageDocument\Document\Details;

use App\Services\ManageDocument\DocumentService;
use Livewire\Component;


class BreadCrumb extends Component
{
    public $itemId, $statuses = [], $status;

    public function mount($id, $statuses, $status)
    {
        $this->itemId = $id;
        $this->statuses = $statuses;
        $this->status = $status;
    }

    public function updatedStatus()
    {
        $this->updateStatus();
    }

    public function updateStatus()
    {
        $service =  app(DocumentService::class);
        $response = $service->updateStatus($this->itemId, [
            'status' => $this->status,
        ]);
        if (@$response['status'] == "success") {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => __("document_module.status_updated_success")
            ]);
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $response['errors']
            ]);
        }
    }

    public function render()
    {
        return view('livewire.manage-document.document.details.breadcrumb');
    }
}
