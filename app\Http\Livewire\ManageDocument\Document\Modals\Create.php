<?php

namespace App\Http\Livewire\ManageDocument\Document\Modals;

use App\Services\ManageDocument\DocumentService;
use Livewire\Component;

class Create extends Component
{

    public $modalId = 'createDocument';
    public $projects = [], $types = [], $users = [];

    public $subject, $project, $type, $user, $description;

    protected $listeners = ['showCreateDocument', 'resetForm_createDocument'];

    protected $rules = [
        'subject' => 'required',
        'type' => 'required',
        'user' => 'required',
    ];

    public function resetForm_createDocument()
    {
        $this->reset([
            'subject',
            'project',
            'type',
            'user',
            'description',
        ]);
        $this->resetErrorBag();
    }

    public function loadData()
    {
        $service =  app(DocumentService::class);
        $data = $service->create();
        if (@$data['status'] == "success") {
            $this->projects = $data['data']['projects'];
            $this->types = $data['data']['types'];
            $this->users = $data['data']['users'];
        }
    }

    public function showCreateDocument()
    {
        $this->loadData();
        $this->dispatchBrowserEvent('open-modal', ['modalId' => $this->modalId]);
        $this->dispatchBrowserEvent('hideLoader');
    }

    public function save()
    {
        $this->validate();
        $service =  app(DocumentService::class);
        $response = $service->store([
            'subject' => $this->subject,
            'project_id' => $this->project,
            'document_type_id' => $this->type,
            'user_id' => $this->user,
            'description' => $this->description,
        ]);
        if (@$response['status'] == "success") {
            $this->emit('newDocumentAdded', $response['data']['document']);
            $this->dispatchBrowserEvent('close-modal', ['modalId' => $this->modalId]);

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => __("document_module.doc_created_success")
            ]);
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $response['errors']
            ]);
        }
    }

    public function render()
    {
        return view('livewire.manage-document.document.modals.create');
    }
}
