<?php

namespace App\Http\Livewire\ManageDocument\Document\Modals;

use App\Services\ManageDocument\DocumentService;
use Livewire\Component;

class Duplicate extends Component
{
    public $modalId = 'duplicateDocument';
    public $itemId;
    public $projects = [], $types = [], $users = [];

    public $subject, $project, $type, $user, $description;

    protected $listeners = ['showDuplicateDocument', 'resetForm_duplicateDocument'];

    protected $rules = [
        'subject' => 'required',
        'type' => 'required',
        'user' => 'required',
    ];

    public function resetForm_duplicateDocument()
    {
        $this->reset([
            'subject',
            'project',
            'type',
            'user',
            'description',
        ]);
        $this->resetErrorBag();
    }

    public function loadData()
    {
        $service =  app(DocumentService::class);
        $data = $service->edit($this->itemId);
        if (@$data['status'] == "success") {
            $this->projects = $data['data']['projects'];
            $this->types = $data['data']['types'];
            $this->users = $data['data']['users'];
            $document = $data['data']['document'];
            $this->subject = $document['subject'];
            $this->user = $document['user_id'] ? $document['user_id'] : null;
            $this->project = $document['project_id'] ? $document['project_id'] : null;
            $this->type = $document['type'];
            $this->description = $document['description'];
        }
    }

    public function showDuplicateDocument($id)
    {
        $this->itemId = $id;
        $this->loadData();
        $this->dispatchBrowserEvent('open-modal', ['modalId' => $this->modalId]);
        $this->dispatchBrowserEvent('hideLoader');
    }

    public function save()
    {
        $this->validate();
        $service =  app(DocumentService::class);
        $response = $service->store([
            'subject' => $this->subject,
            'project_id' => $this->project,
            'document_type_id' => $this->type,
            'user_id' => $this->user,
            'description' => $this->description,
        ]);
        if (@$response['status'] == "success") {
            $this->emit('newDocumentAdded', $response['data']['document']);
            $this->dispatchBrowserEvent('close-modal', ['modalId' => $this->modalId]);

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => __("document_module.doc_created_success")
            ]);
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $response['errors']
            ]);
        }
    }

    public function render()
    {
        return view('livewire.manage-document.document.modals.duplicate');
    }
}
