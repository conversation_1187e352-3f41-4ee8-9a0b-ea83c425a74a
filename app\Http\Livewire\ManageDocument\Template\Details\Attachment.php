<?php

namespace App\Http\Livewire\ManageDocument\Template\Details;

use App\Services\ManageDocument\DocumentService;
use Livewire\Component;
use Livewire\WithFileUploads;


class Attachment extends Component
{
    use WithFileUploads;
    public $attachments = [];
    public $attachment;
    public $itemId, $deletingId;

    protected $listeners = ['deleteAttachment' => 'delete'];

    public function mount($id, $data)
    {
        $this->itemId = $id;
        $this->attachments = $data;
    }

    public function updatedAttachment()
    {
        $this->save();
    }

    public function save()
    {
        $filename = null;
        $absolutePath = null;

        if ($this->attachment) {
            $filename = $this->attachment->getClientOriginalName();
            $filePath = 'uploads/document-attachment/' . $filename;
            $this->attachment->storeAs('uploads/document-attachment', $filename, 'local');
            $absolutePath = storage_path('app/' . $filePath);
        }
        $service =  app(DocumentService::class);
        $response = $service->uploadAttachment($this->itemId, $absolutePath, $filename);

        if ($response['status'] === 'success') {
            // Delete the stored file
            if (file_exists($absolutePath)) {
                unlink($absolutePath);
            }
            $this->reset('attachment');
            array_unshift($this->attachments, $response['data']['attachment']);
            $this->emit('attachmentCount', count($this->attachments));

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => __("document_module.attachment_created_success")
            ]);
            $this->dispatchBrowserEvent('resetFileInput');
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $response['errors']
            ]);
        }
    }

    public function confirmAttachmentDeletion($id)
    {
        $this->deletingId = $id;
        $this->emit('confirmDelete', $id, __('document_module.file'), 'deleteAttachment');
    }

    public function delete($id, $delete)
    {
        if ($id && $delete) {
            $service =  app(DocumentService::class);
            $response = $service->deleteAttachment($id);

            if ($response['status'] === 'success') {
                foreach ($this->attachments as $index => $item) {
                    if ($item['id'] == $id) {
                        unset($this->attachments[$index]);
                        break;
                    }
                }
                $this->attachments = array_values($this->attachments); // reset keys and reassign
                $this->emit('attachmentCount', count($this->attachments));
                $this->dispatchBrowserEvent('close-confirm-modal');

                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'success',
                    'message' => __("document_module.attachment_deleted_success")
                ]);
            } else {
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'error',
                    'message' => $response['errors']
                ]);
            }
        }

        $this->deletingId = null;
    }

    public function render()
    {
        return view('livewire.manage-document.template.details.attachments');
    }
}
