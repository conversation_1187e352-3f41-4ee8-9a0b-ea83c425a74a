<?php

namespace App\Http\Livewire\ManageDocument\Template\Details;

use App\Services\ManageDocument\DocumentService;
use Livewire\Component;


class Comment extends Component
{
    public $details = [];
    public $comment;
    public $itemId, $deletingId;

    protected $listeners = ['deleteComment' => 'delete'];

    protected $rules = [
        'comment' => 'required',
    ];

    public function mount($id, $data)
    {
        $this->itemId = $id;
        $this->details = $data;
    }

    public function save()
    {
        $this->validate();
        $service =  app(DocumentService::class);
        $response = $service->storeComment($this->itemId, [
            'comment' => $this->comment,
        ]);
        if (@$response['status'] == "success") {
            array_unshift($this->details, $response['data']['comment']);
            $this->emit('commentCount', count($this->details));

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => __("document_module.comment_created_success")
            ]);

            $this->resetErrorBag();
            $this->reset('comment');
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $response['errors']
            ]);
        }
    }

    public function confirmCommentDeletion($id)
    {
        $this->deletingId = $id;
        $this->emit('confirmDelete', $id, __('document_module.comment'), 'deleteComment');
    }

    public function delete($id, $delete)
    {
        if ($id && $delete) {
            $service =  app(DocumentService::class);
            $response = $service->deleteComment($id);

            if ($response['status'] === 'success') {
                foreach ($this->details as $index => $item) {
                    if ($item['id'] == $id) {
                        unset($this->details[$index]);
                        break;
                    }
                }
                $this->emit('commentCount', count($this->details));
                $this->dispatchBrowserEvent('close-confirm-modal');

                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'success',
                    'message' => __("document_module.comment_deleted_success")
                ]);
            } else {
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'error',
                    'message' => $response['errors']
                ]);
            }
        }

        $this->deletingId = null;
    }

    public function render()
    {
        return view('livewire.manage-document.template.details.comments');
    }
}
