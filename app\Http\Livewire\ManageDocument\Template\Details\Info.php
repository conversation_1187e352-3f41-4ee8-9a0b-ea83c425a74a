<?php

namespace App\Http\Livewire\ManageDocument\Template\Details;

use App\Services\ManageDocument\DocumentService;
use Livewire\Component;


class Info extends Component
{
    public $details = [];
    public $description, $additional_description, $attachments_count, $comments_count, $notes_count, $itemId;

    protected $listeners = ['attachmentCount', 'commentCount', 'noteCount'];

    public function attachmentCount($data)
    {
        $this->attachments_count = $data;
    }

    public function commentCount($data)
    {
        $this->comments_count = $data;
    }

    public function noteCount($data)
    {
        $this->notes_count = $data;
    }

    public function mount($id, $data, $counts)
    {
        $this->itemId = $id;
        $this->details = $data;
        $this->description = $data['description'];
        $this->additional_description = $data['additional_description'];
        $this->attachments_count = $counts['attachments'];
        $this->comments_count = $counts['comments'];
        $this->notes_count = $counts['notes'];
    }

    public function save()
    {
        $service =  app(DocumentService::class);
        $response = $service->updateDescription($this->itemId, [
            'description' => $this->description,
            'additional_description' => $this->additional_description,
        ]);
        if (@$response['status'] == "success") {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => __("document_module.desc_updated_success")
            ]);
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $response['errors']
            ]);
        }
        $this->dispatchBrowserEvent('hideLoader');
    }

    public function render()
    {
        return view('livewire.manage-document.template.details.info');
    }
}
