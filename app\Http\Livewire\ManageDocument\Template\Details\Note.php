<?php

namespace App\Http\Livewire\ManageDocument\Template\Details;

use App\Services\ManageDocument\DocumentService;
use Livewire\Component;


class Note extends Component
{
    public $details = [];
    public $note;
    public $itemId, $deletingId;

    protected $rules = [
        'note' => 'required',
    ];

    protected $listeners = ['deleteNote' => 'delete'];

    public function mount($id, $data)
    {
        $this->itemId = $id;
        $this->details = $data;
    }

    public function save()
    {
        $this->validate();
        $service =  app(DocumentService::class);
        $response = $service->storeNote($this->itemId, [
            'note' => $this->note,
        ]);
        if (@$response['status'] == "success") {
            array_unshift($this->details, $response['data']['note']);
            $this->emit('noteCount', count($this->details));
            $this->reset('note');

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => __("document_module.note_created_success")
            ]);
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $response['errors']
            ]);
        }
    }

    public function confirmNotetDeletion($id)
    {
        $this->deletingId = $id;
        $this->emit('confirmDelete', $id, __('document_module.note'), 'deleteNote');
    }

    public function delete($id, $delete)
    {
        if ($id && $delete) {
            $service =  app(DocumentService::class);
            $response = $service->deleteNote($id);

            if ($response['status'] === 'success') {
                foreach ($this->details as $index => $item) {
                    if ($item['id'] == $id) {
                        unset($this->details[$index]);
                        break;
                    }
                }
                $this->emit('noteCount', count($this->details));
                $this->dispatchBrowserEvent('close-confirm-modal');

                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'success',
                    'message' => __("document_module.note_deleted_success")
                ]);

                $this->resetErrorBag();
                $this->reset('note');
            } else {
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'error',
                    'message' => $response['errors']
                ]);
            }
        }

        $this->deletingId = null;
    }

    public function render()
    {
        return view('livewire.manage-document.template.details.notes');
    }
}
