<?php

namespace App\Http\Livewire\ManageDocument\Template\Modals;

use App\Services\ManageDocument\DocumentService;
use App\Services\ManageDocument\TemplateService;
use Livewire\Component;

class Convert extends Component
{
    public $modalId = 'convertDocument';
    public $itemId;
    public $types = [];

    public $subject;

    protected $listeners = ['showConvertDocument', 'resetForm_convertDocument'];

    protected $rules = [
        'subject' => 'required',
    ];

    public function resetForm_convertDocument()
    {
        $this->reset([
            'subject',
        ]);
        $this->resetErrorBag();
    }

    public function showConvertDocument($id, $subject)
    {
        $this->itemId = $id;
        $this->subject = $subject;
        $this->dispatchBrowserEvent('open-modal', ['modalId' => $this->modalId]);
        $this->dispatchBrowserEvent('hideLoader');
    }

    public function save()
    {
        $this->validate();
        $service =  app(TemplateService::class);
        $response = $service->convert($this->itemId, [
            'subject' => $this->subject,
        ]);
        if (@$response['status'] == "success") {
            $this->dispatchBrowserEvent('close-modal', ['modalId' => $this->modalId]);

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => __("document_module.doc_temp_converted_success")
            ]);
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $response['errors']
            ]);
        }
    }

    public function render()
    {
        return view('livewire.manage-document.template.modals.convert');
    }
}
