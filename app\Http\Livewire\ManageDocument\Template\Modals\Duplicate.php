<?php

namespace App\Http\Livewire\ManageDocument\Template\Modals;

use App\Services\ManageDocument\DocumentService;
use App\Services\ManageDocument\TemplateService;
use Livewire\Component;

class Duplicate extends Component
{
    public $modalId = 'duplicateDocument';
    public $itemId;
    public $types = [];

    public $subject, $type, $description;

    protected $listeners = ['showDuplicateDocument', 'resetForm_duplicateDocument'];

    protected $rules = [
        'subject' => 'required',
        'type' => 'required',
    ];

    public function resetForm_duplicateDocument()
    {
        $this->reset([
            'subject',
            'type',
            'description',
        ]);
        $this->resetErrorBag();
    }

    public function loadData()
    {
        $service =  app(TemplateService::class);
        $data = $service->edit($this->itemId);
        if (@$data['status'] == "success") {
            $this->types = $data['data']['types'];
            $document = $data['data']['template'];
            $this->subject = $document['subject'];
            $this->type = $document['type'];
            $this->description = $document['description'];
        }
    }

    public function showDuplicateDocument($id)
    {
        $this->itemId = $id;
        $this->loadData();
        $this->dispatchBrowserEvent('open-modal', ['modalId' => $this->modalId]);
        $this->dispatchBrowserEvent('hideLoader');
    }

    public function save()
    {
        $this->validate();
        $service =  app(TemplateService::class);
        $response = $service->store([
            'subject' => $this->subject,
            'document_type_id' => $this->type,
            'description' => $this->description,
        ]);
        if (@$response['status'] == "success") {
            $this->emit('newDocumentAdded', $response['data']['document']);
            $this->dispatchBrowserEvent('close-modal', ['modalId' => $this->modalId]);

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => __("document_module.doc_temp_created_success")
            ]);
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $response['errors']
            ]);
        }
    }

    public function render()
    {
        return view('livewire.manage-document.template.modals.duplicate');
    }
}
