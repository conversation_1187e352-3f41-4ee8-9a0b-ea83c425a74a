<?php

namespace App\Http\Livewire\ManageDocument\Type;

use App\Services\ManageDocument\DocumentService;
use App\Services\ManageDocument\TemplateService;
use App\Services\ManageDocument\TypeService;
use Illuminate\Support\Facades\Http;
use Livewire\Component;


class Index extends Component
{
    public $items = [];
    public $total = 0, $search;
    public $page = 1;
    public $perPage = 10;

    protected $listeners = ['newDocumentTypeAdded', 'deleteItem' => 'delete', 'documentTypeUpdated', 'fetchData', 'perPageUpdated_fetchData'];

    protected $queryString = [
        'page'   => ['except' => 1],
    ];

    public function mount()
    {
        $this->fetchData();
    }

    public function perPageUpdated_fetchData($val)
    {
        $this->perPage = $val;
        $this->fetchData();
    }

    public function newDocumentTypeAdded($data)
    {
        array_unshift($this->items, $data);
        $this->total += 1;
        $this->emit('refreshPagination:fetchData', $this->page, 'fetchData', $this->total, $this->perPage);
    }

    public function documentTypeUpdated($data)
    {
        foreach ($this->items as $index => $item) {
            if ($item['id'] == $data['id']) {
                unset($this->items[$index]);
                array_unshift($this->items, $data);
                break;
            }
        }
    }

    public function fetchData($page = 1)
    {
        $page = request()->query('page', $page);;
        $service =  app(TypeService::class);
        $data = $service->list(['page' => $page, 'per_page' => $this->perPage]);

        if (@$data['status'] == "success") {
            $this->items = $data['data']['items'];
            $this->total = $data['data']['total'];
            $this->page = $data['data']['current_page'];
            $this->emit('refreshPagination:fetchData', $page, 'fetchData', $this->total, $this->perPage);
        }
    }

    public function delete($id, $delete)
    {
        if ($id && $delete) {
            $service =  app(TypeService::class);
            $response = $service->delete($id);

            if ($response['status'] === 'success') {
                foreach ($this->items as $index => $item) {
                    if ($item['id'] == $id) {
                        unset($this->items[$index]);
                        $this->total -= 1;
                        $this->emit('refreshPagination:fetchData', $this->page, 'fetchData', $this->total, $this->perPage);
                        break;
                    }
                }
                $this->dispatchBrowserEvent('close-confirm-modal');

                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'success',
                    'message' => __("document_module.doc_type_deleted_success")
                ]);
            } else {
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'error',
                    'message' => $response['errors']
                ]);
            }
        }
    }

    public function render()
    {
        return view('livewire.manage-document.type.index');
    }
}
