<?php

namespace App\Http\Livewire\ManageDocument\Type\Modals;

use App\Services\ManageDocument\DocumentService;
use App\Services\ManageDocument\TemplateService;
use App\Services\ManageDocument\TypeService;
use Livewire\Component;

class Create extends Component
{

    public $modalId = 'createDocumentType';
    public $types = [];

    public $name;

    protected $listeners = ['showCreateDocumentType', 'resetForm_createDocumentType'];

    protected $rules = [
        'name' => 'required',
    ];

    public function resetForm_createDocumentType()
    {
        $this->reset([
            'name',
        ]);
        $this->resetErrorBag();
    }

    public function showCreateDocumentType()
    {
        $this->dispatchBrowserEvent('open-modal', ['modalId' => $this->modalId]);
        $this->dispatchBrowserEvent('hideLoader');
    }

    public function save()
    {
        $this->validate();
        $service =  app(TypeService::class);
        $response = $service->store([
            'name' => $this->name,
        ]);
        if (@$response['status'] == "success") {
            $this->emit('newDocumentTypeAdded', $response['data']['type']);
            $this->dispatchBrowserEvent('close-modal', ['modalId' => $this->modalId]);

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => __("document_module.doc_type_created_success")
            ]);
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $response['errors']
            ]);
        }
    }

    public function render()
    {
        return view('livewire.manage-document.type.modals.create');
    }
}
