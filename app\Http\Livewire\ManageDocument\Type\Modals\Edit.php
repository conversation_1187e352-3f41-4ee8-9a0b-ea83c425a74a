<?php

namespace App\Http\Livewire\ManageDocument\Type\Modals;

use App\Services\ManageDocument\DocumentService;
use App\Services\ManageDocument\TemplateService;
use App\Services\ManageDocument\TypeService;
use Livewire\Component;

class Edit extends Component
{
    public $modalId = 'editDocumentType';
    public $itemId;
    public $types = [];

    public $name;

    protected $listeners = ['showEditDocumentType', 'resetForm_editDocumentType'];

    protected $rules = [
        'name' => 'required',
    ];

    public function resetForm_editDocumentType()
    {
        $this->reset([
            'name',
        ]);
        $this->resetErrorBag();
    }

    public function showEditDocumentType($id, $name)
    {
        $this->itemId = $id;
        $this->name = $name;
        $this->dispatchBrowserEvent('open-modal', ['modalId' => $this->modalId]);
        $this->dispatchBrowserEvent('hideLoader');
    }

    public function save()
    {
        $this->validate();
        $service =  app(TypeService::class);
        $response = $service->update($this->itemId, [
            'name' => $this->name,
        ]);
        if (@$response['status'] == "success") {
            $this->emit('documentTypeUpdated', $response['data']['type']);
            $this->dispatchBrowserEvent('close-modal', ['modalId' => $this->modalId]);

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => __("document_module.doc_type_updated_success")
            ]);
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $response['errors']
            ]);
        }
    }

    public function render()
    {
        return view('livewire.manage-document.type.modals.edit');
    }
}
