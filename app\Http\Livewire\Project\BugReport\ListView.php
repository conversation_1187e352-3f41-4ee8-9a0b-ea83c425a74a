<?php

namespace App\Http\Livewire\Project\BugReport;

use App\Http\Helpers\Helper;
use App\Services\CRM\Sales\ProjectService;
use Livewire\Component;

class ListView extends Component
{
    public $records;
    public $itemId;
    public $viewType ;
    protected $listeners = ['fetchData', 'deleteIncident', 'currentPage'];

    public $currentPage = [];
    public function currentPage($data)
    {
        $this->currentPage[$data['functionName']] = $data['page'];
    }

    public function mount($id)
    {
        $this->itemId = decrypt($id);

        
        $this->fetchData();
    }

    public function fetchData($page = 1)
    {
        $page = $this->currentPage['fetchData'] ??  request()->query('page', $page);
        $this->dispatchBrowserEvent('showLoader');
        $service =  app(ProjectService::class);
        $data = $service->bugReportListView($this->itemId, $page);
        // dd($page);
        if (@$data['status'] == "success") {
            $this->records = $data['data'] ?? [];
            $this->emit('refreshPagination', ceil(@$this->records['total'] / @$this->records['per_page']), $page, 'fetchData', @$this->records['total']);
            $this->emit('updateUrl', ['page' => $page, 'functionName' => 'fetchData']);
        }
        $this->dispatchBrowserEvent('hideLoader');
    }

    public function deleteIncident($id)
    {
        if ($id) {
            $service =  app(ProjectService::class);
            $response = $service->deleteIncident($this->itemId, $id);

            if ($response['status'] === 'success') {
                $this->dispatchBrowserEvent('close-confirm-modal');
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'success',
                    'message' => $response['message']
                ]);
                $this->fetchData();
            } else {
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'error',
                    'message' =>  $response['message']
                ]);
            }
        }
    }

    public function render()
    {
        return view('livewire.project.bug-report.list-view');
    }
}
