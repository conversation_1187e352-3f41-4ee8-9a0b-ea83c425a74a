<?php

namespace App\Http\Livewire\Project\BugReport\Partials;

use Livewire\Component;

class BreadcrumbActions extends Component
{
    public $encryptedid;

    public function mount($encryptedid = null): void
    {
        $this->encryptedid = $encryptedid;
    }

    public function render()
    {
        return view('livewire.project.task-board.partials.breadcrumb-actions', [
            'encryptedId' => $this->encryptedid,
        ]);
    }
    // public $startDate;
    // public $endDate;

    // public $viewType = 'kanban';

    // public function updatedViewType()
    // {
    //     $this->dispatchBrowserEvent('showLoader');
    //     $this->emitTo('project.task-board.index', 'changeView', $this->viewType);
    // }

    // public function submit()
    // {
    //     // Add logic to handle date filter submission
    //     $this->emit('filter-submitted', [
    //         'startDate' => $this->startDate,
    //         'endDate' => $this->endDate
    //     ]);
    // }
}

