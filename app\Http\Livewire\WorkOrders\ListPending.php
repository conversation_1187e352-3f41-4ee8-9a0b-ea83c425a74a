<?php
    namespace App\Http\Livewire\WorkOrders;
    use Livewire\Component;
    use Livewire\WithPagination;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\UserTrait;
    use App\Http\Traits\ContractsTrait;
    use App\Http\Traits\ProjectDetailTrait;
    use App\Http\Traits\ConfigurationTrait;
    use App\Http\Traits\WorkOrdersTrait;
    use App\Http\Traits\ExportTrait;
    use App\Jobs\NewGenerateExportWorkOrders;
    use App\Http\Helpers\Helper;
    use App\Enums\ConfigurationCode;
    use App\Enums\FileType;
    use App\Enums\Language;
    use App\Enums\PdfType;
    use App\Enums\ExportStatus;
    use App\Enums\ExportType;
    use App\Enums\SwalType;

    class ListPending extends Component{
        use WithPagination, FunctionsTrait, UserTrait, ProjectDetailTrait, ContractsTrait, ConfigurationTrait, WorkOrdersTrait, ExportTrait;

        public $showProviderFilter;
        public $propertiesList;
        public $buildingManagersList;
        public $servicesList;
        public $hiddenColumns;
        public $workersList;
        public $assets;
        public $supervisorList;
        public $checkCreateButton;
        public $user;
        public $projectId;
        public $projectDetails;
        public $project;
        public $decryptedServiceProviderId;
        public $countServiceProviders;
        public $serviceprovidersList;
        public $currentDate;
        public $selectedLanguage;
        public $dataList;
        public $contractsList;
        public $configPerPage;
        public $perPage;
        public $explodedServiceProviders;
        public $explodedBuildings;
        public $explodedAssetCategories;
        public $search;
        public $dateRange;
        public $sortBy;
        public $selectedRows;
        public $showDiv;
        public $showCloseText;
        public $showSelectedRows;
        public $fileType;
        public $fileLanguage;
        public $pdfType;
        public $filters;
        public $selectedAllRows;
        protected $listeners = ['setDateRange', 'setSortBy', 'setShowSelectedRows', 'setExplodedServiceProviders', 'setFilters', 'updatedSelectedAllRows'];

        public function render(){
            $userPrivileges = $this->initUserPrivileges();
            $list = $this->getFiltredWorkOrdersList($this->perPage, $this->dataList, 'pending', $this->user->user_type, $this->user->id, $this->contractsList, $this->explodedBuildings, $this->explodedAssetCategories, $this->explodedServiceProviders, $this->user->project_user_id, $this->search, $this->currentDate, $this->dateRange, $this->sortBy, $this->selectedRows, $this->showSelectedRows, $this->filters, $this->user->service_provider);
            $this->callJsFunction('feather');
            return view('livewire.work-orders.list-pending', compact('userPrivileges', 'list'));
        }

        public function mount() {
            $this->initUser();
            $this->initProjectId();
            $this->initProjectDetails();
            $this->initProject();
            $this->initCurrentDate();
            $this->initSelectedLanguage();
            $this->initDataList();
            $this->initConfigPerPage();
            $this->initPerPage();
            $this->initExplodedServiceProviders();
            $this->initContractsList();
            $this->initExplodedBuildings();
            $this->initExplodedAssetCategories();
            $this->setDateRange([]);
            $this->setSortBy(['submission_date', 'DESC']);
            $this->setSelectedRows([]);
            $this->setShowDiv(false);
            $this->setShowCloseText(false);
            $this->setShowSelectedRows(false);
            $this->setFileType(FileType::PDF->value);
            $this->setFileLanguage(Language::English->value);
            $this->setPdfType(PdfType::Osool->value);
			$this->hiddenColumns = json_decode(request()->cookie('hiddenColumns_5'), true) ?? [];
        }

        public function initUser() {
            try {
                if(!isset($this->user)){
                    $this->user = $this->getAuthenticatedUser();
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("initUser error: ".$th);
            }
        }

        public function initProjectId() {
            try {
                if(!isset($this->projectId)){
                    $this->projectId = isset($this->user) ? $this->getProjectIdForWorkOrdersList($this->user->user_type, $this->user->project_id, $this->user->project_user_id) : 0;
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("initProjectId error: ".$th);
            }
        }

        public function initProjectDetails() {
            try {
                if(!isset($this->projectDetails)){
                    $this->projectDetails = isset($this->projectId) ? $this->getUserByvalues($this->projectId) : null;
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("initProjectDetails error: ".$th);
            }
        }

        public function initProject() {
            try {
                if(!isset($this->project)){
                    $this->project = isset($this->projectId) ? $this->getProjectDetailInformationByProjectId($this->projectId) : null;
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("initProject error: ".$th);
            }
        }

        public function initUserPrivileges() {
            try {
                return isset($this->user) ? $this->getDecodedJson($this->user->user_privileges) : null;
            } 
            
            catch (\Throwable $th) {
                Log::error("initUserPrivileges error: ".$th);
            }
        }

        public function initCurrentDate() {
            try {
                $this->currentDate = $this->getCurrentDate();
            } 
            
            catch (\Throwable $th) {
                Log::error("initCurrentDate error: ".$th);
            }
        }

        public function initSelectedLanguage() {
            try {
                $this->selectedLanguage = $this->getLocalLanguage();
            } 
            
            catch (\Throwable $th) {
                Log::error("initSelectedLanguage error: ".$th);
            }
        }

        public function initDataList() {
            try {
                $this->dataList = [
                    'no_worker_assigned' => $this->selectedLanguage == 'en' ? 'No worker assigned' : 'لم يتم تعيين عامل',
                    'started_on_behalf' => $this->selectedLanguage == 'en' ? 'Started on behalf' : 'تم البدء بالنيابة عن العامل',
                    'status_open' => $this->selectedLanguage == 'en' ? 'Open' : 'مفتوح',
                    'status_in_progress' => $this->selectedLanguage == 'en' ? 'In Progress' : 'قيد التنفيذ',
                    'status_on_hold' => $this->selectedLanguage == 'en' ? 'On Hold' : 'توقف مؤقتا',
                    'status_closed' => $this->selectedLanguage == 'en' ? 'Closed' : 'مغلق',
                    'status_deleted' => $this->selectedLanguage == 'en' ? 'Deleted' : 'محذوف',
                    'status_reopen' => $this->selectedLanguage == 'en' ? 'Re Open' : 'تمت إعادة فتحه',
                    'status_scheduled' => $this->selectedLanguage == 'en' ? 'Scheduled' : 'مجدول',
                    'status_warrantly' => $this->selectedLanguage == 'en' ? 'Warranty' : 'ضمان'
                ];
            } 
            
            catch (\Throwable $th) {
                Log::error("initDataList error: ".$th);
            }
        }

        public function initExplodedServiceProviders() {
            try {
                $this->explodedServiceProviders = $this->hasDataSession('selected_multiple_sp_id') ? $this->getDataSession('selected_multiple_sp_id') : $this->explodeDataFromField($this->decryptedServiceProviderId);
            } 
            
            catch (\Throwable $th) {
                Log::error("initExplodedServiceProviders error: ".$th);
            }
        }

        public function initContractsList() {
            try {
                switch ($this->user->user_type) {
                    case 'building_manager':
                        $this->contractsList = !is_null($this->explodedServiceProviders) && is_array($this->explodedServiceProviders) ? $this->getContractInformationByValues('service_provider_id', $this->explodedServiceProviders) : null;
                    break;

                    case 'building_manager_employee':
                        $this->contractsList = !is_null($this->explodedServiceProviders) && is_array($this->explodedServiceProviders) ? $this->getContractInformationByValues('service_provider_id', $this->explodedServiceProviders) : null;
                    break;

                    case 'sp_admin':
                        $this->contractsList = $this->getContractInformationByServiceProvider($this->user->service_provider);
                    break;

                    default:
                        $this->contractsList = null;    
                    break;
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("initContractsList error: ".$th);
            }
        }

        public function initConfigPerPage() {
            try {
                if(!isset($this->configPerPage)){
                    $this->configPerPage = isset($this->user) ? $this->getConfigurationByValue('code', ConfigurationCode::PerPage->value) : null;
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("initConfigPerPage error: ".$th);
            }
        }

        public function initPerPage() {
            try {
                if(!isset($this->perPage)){
                    $this->perPage = isset($this->configPerPage) ? $this->configPerPage->value : 5;
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("initPerPage error: ".$th);
            }
        }

        public function updatingPerPage(){
            try {
                $this->resetPage();
            } 
            
            catch (\Throwable $th) {
                Log::error("updatingPerPage error: ".$th);
            }
        }

        public function initExplodedBuildings() {
            try {
                if(isset($this->user) && in_array($this->user->user_type, array('building_manager', 'building_manager_employee'))){
                    $this->explodedBuildings = !empty($this->user->building_ids) ? $this->explodeDataFromField($this->user->building_ids) : [];
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("initExplodedBuildings error: ".$th);
            }
        }

        public function initExplodedAssetCategories() {
            try {
                if(isset($this->user) && in_array($this->user->user_type, array('building_manager', 'building_manager_employee'))){
                    $this->explodedAssetCategories = !empty($this->user->asset_categories) ? $this->explodeDataFromField($this->user->asset_categories) : [];
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("initExplodedAssetCategories error: ".$th);
            }
        }

        public function callJsFunction($key) {
            try {
                $this->dispatchBrowserEvent($key);
            } 
            
            catch (\Throwable $th) {
                Log::error("callJsFunction error: ".$th);
            }
        }

        public function setDateRange($value) {
            try {
                $this->dateRange = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setDateRange error: ".$th);
            }
        }

        public function setSortBy($value) {
            try {
                if($value){
                    $this->sortBy = $value;
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("setSortBy error: ".$th);
            }
        }

        public function setSelectedRows($value) {
            try {
                $this->selectedRows = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setSelectedRows error: ".$th);
            }
        }

        public function setShowDiv($value) {
            try {
                $this->showDiv = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setShowDiv error: ".$th);
            }
        }

        public function setShowCloseText($value) {
            try {
                $this->showCloseText = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setShowCloseText error: ".$th);
            }
        }

        public function setShowSelectedRows($value) {
            try {
                $this->showSelectedRows = $value;

                if($value){
                    $this->setShowCloseText($value);
                }

                else{
                    $this->setShowCloseText(false);
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("setShowSelectedRows error: ".$th);
            }
        }

        public function updatedSelectedRows($value) {
            try {
                if($value){
                    $this->setShowDiv(true);
                    $this->setSelectedRows($value);
                }

                else{
                    $this->setShowDiv(false);
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("updatedSelectedRows error: ".$th);
            }
        }

        public function setFileType($value) {
            try {
                $this->fileType = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setFileType error: ".$th);
            }
        }

        public function setFileLanguage($value) {
            try {
                $this->fileLanguage = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setFileLanguage error: ".$th);
            }
        }

        public function setPdfType($value) {
            try {
                $this->pdfType = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setPdfType error: ".$th);
            }
        }

        public function submitExportForm() {
            try {
                $randomNumber = $this->generateRandomNumber(10000000, 99999999);
                $fileName =  $this->fileType == FileType::PDF->value ? $randomNumber.'.pdf' : $randomNumber.'.csv';
                $exportName = 'EXP'.$this->generateRandomNumber(1000, 9999);
                $projectImage = isset($this->project) ? $this->project->project_image : null;
                $translations = Helper::translateIndustryType();
                $countItems = count($this->selectedRows);
                $exportType =  $this->fileType == FileType::PDF->value ? 'pdf' : 'csv';
                $requestedAt = $this->getCurrentDateTime();
                $startDate = $this->valueIsRequired($this->dateRange) ? $this->changeDateFormat('Y-m-d h:i:s', $this->projectDetails->created_at) : $this->dateRange[0];
                $endDate = $this->valueIsRequired($this->dateRange) ? $this->changeDateFormat('Y-m-d h:i:s', $this->currentDate) : $this->dateRange[1];
                $root = $_SERVER["DOCUMENT_ROOT"];
                $exportId = $this->saveExports($this->user->id, $fileName, $countItems, $exportName, $exportType, $requestedAt, $startDate, $endDate, ExportStatus::Pending->value, $this->user->project_id, ExportType::WorkOrder->value);
                
                if($exportId){
                    $job = NewGenerateExportWorkOrders::dispatch($exportId, $this->user, $root, $startDate, $endDate, $this->selectedRows, $this->fileType, $this->pdfType, $this->fileLanguage, $fileName, $translations, $countItems);
                    
                    if($job){
                        $this->callSwalByType(SwalType::Success->value);
                    }

                    else{
                        $this->callSwalByType(SwalType::Error->value);
                    }
                }

                else{
                    $this->callSwalByType(SwalType::Error->value);
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("submitExportForm error: ".$th);
            }
        }

        public function callSwalByType($type) {
            try {
                $this->dispatchBrowserEvent('result', $type);
            } 
            
            catch (\Throwable $th) {
                Log::error("callSwalByType error: ".$th);
            }
        }

        public function setExplodedServiceProviders($value) {
            try {
                if(isset($value) && count($value) > 0){
                    $this->explodedServiceProviders = $value; 
                }

                else{
                    $this->initExplodedServiceProviders();
                }

                $this->createDataSession('selected_multiple_sp_id', $this->explodedServiceProviders);
            } 
            
            catch (\Throwable $th) {
                Log::error("setExplodedServiceProviders error: ".$th);
            }
        }

        public function setFilters($value) {
            try {
                $this->filters = $value;

                if(isset($value['columns'])){
                    $this->setHiddenColumns($value['columns']);
					$this->updateHiddenColumns($value['columns']);
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("setFilters error: ".$th);
            }
        }

        public function setHiddenColumns($value) {
            try {
                $this->hiddenColumns = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setHiddenColumns error: ".$th);
            }
        }

        public function updatingSearch(){
            try {
                $this->resetPage();
            } 
            
            catch (\Throwable $th) {
                Log::error("updatingSearch error: ".$th);
            }
        }

		public function updateHiddenColumns($hiddenColumns){
            cookie()->queue('hiddenColumns_5', json_encode($hiddenColumns), 60 * 24 * 30); // 30 days
        }

        public function updatedSelectedAllRows($value) {
            try {
                if($value){
                    $data = json_decode($value, true);
                    $this->setSelectedRows($data);
                    $this->setShowDiv(true);
                }

                else{
                    $this->setShowDiv(false);
                    $this->setSelectedRows([]);
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("updatedSelectedAllRows error: ".$th);
            }
        }
    }
?>