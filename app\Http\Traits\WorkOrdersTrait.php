<?php
    namespace App\Http\Traits;
    use Illuminate\Support\Facades\Log;
    use App\Http\Helpers\WorkorderHelper;
    use App\Http\Helpers\Helper;
    use App\Http\Traits\ContractsTrait;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\RatingWorkerTrait;
    use App\Http\Traits\WorkerLocationTrait;
    use App\Http\Traits\PropertyBuildingTrait;
    use App\Http\Traits\ManageWorkerAvailabilityStatusTrait;
    use App\Http\Traits\SmartAssigningContractTrait;
    use App\Http\Traits\ConfigurationTrait;
    use App\Http\Traits\ProjectSettingsTrait;
    use App\Http\Traits\SmartAssigningLogTrait;
    use App\Http\Traits\UserTrait;
    use App\Http\Traits\SmartAssignCriteriaDescriptionTrait;
    use App\Models\WorkOrders;
    use App\Enums\ConfigurationCode;
    use App\Enums\WorkOrderStatus;
    use DB;
    use Carbon\Carbon;

    trait WorkOrdersTrait{
        use ContractsTrait, FunctionsTrait, RatingWorkerTrait, PropertyBuildingTrait, ManageWorkerAvailabilityStatusTrait, SmartAssigningContractTrait, ConfigurationTrait, ProjectSettingsTrait, WorkerLocationTrait, SmartAssigningLogTrait, UserTrait, SmartAssignCriteriaDescriptionTrait;

        public function getFiltredWorkOrdersByWorkOrderId() {
            try {
                $result = WorkOrders::when(!empty($this->search), function ($query) {
                    $query->where('work_order_id', 'LIKE', '%' . $this->search . '%');
                })
                ->select(
                    'id', 'work_order_id', 'work_order_type', 'status', 'description', 'property_id', 'floor', 'room', 'workorder_journey', 'contract_type', 'supervisor_id',
                    'assigned_to', 'worker_id', 'service_provider_id', 'job_started_at', 'priority_id', 'contract_id', 'frequency_id', 'project_user_id', 'wtf_start_time',
                    'created_at', 'start_date', 'end_date', 'rating', 'asset_category_id', 'response_time', 'sp_approove', 'pass_fail', 'target_date', 'bm_approve_issue'
                )
                ->where('is_deleted', '!=', 'yes')
                ->where('start_date', '<=', $this->currentDateWithCarbon)
                ->when(!empty($this->dateRange), function ($query) {
                    $query->where('start_date', '>=', $this->dateRange['0'])
                    ->where('end_date', '<=', $this->dateRange['1']);
                })
                ->when(!empty($this->type), function ($query) {
                    $query->where('work_order_type', '=', $this->type);
                })
                ->when(!empty($this->viewBy), function ($query) {
                    $query->where('response_time', '=', $this->viewBy);
                })
                ->when(!empty($this->status), function ($query) {
                    $query->whereIn('status', $this->status);
                })
                ->when(!empty($this->rating), function ($query) {
                    $query->where('rating', '=', $this->rating);
                })
                ->when(!empty($this->property), function ($query) {
                    $query->whereIn('property_id', $this->property);
                })
                ->when(!empty($this->supervisor), function ($query) {
                    $supp = $this->implodeDataFromField($this->supervisor);

                    $query->where(function($innerQuery) use($supp) {
                        $innerQuery->whereRaw("(((supervisor_id IS NOT NULL and assigned_to = 'supervisor') || (assigned_to = 'sp_worker' AND (CHAR_LENGTH(supervisor_id) - CHAR_LENGTH(REPLACE(supervisor_id, ',', '')) + 1) = 1)) AND supervisor_id IN ($supp)AND (assigned_to != 'sp_worker' || worker_id != 0)) OR (service_provider_id IN ($supp) AND (assigned_to != 'sp_worker' || worker_id != 0) AND assigned_to != 'supervisor' AND (CHAR_LENGTH(supervisor_id) - CHAR_LENGTH(REPLACE(supervisor_id, ',', '')) + 1) > 1)");
                    });
                })
                ->when(!empty($this->service), function ($query) {
                    $query->whereIn('asset_category_id', $this->service);
                });

                if (!in_array($this->user->user_type, ['sp_admin', 'supervisor'])) {
                    $result = $result->where('project_user_id', $this->user->project_user_id);
                }

                elseif (in_array($this->user->user_type, ['building_manager', 'building_manager_employee'])) {
                    $user = $this->user;

                    $result = $result->when(in_array($user->user_type, ['building_manager_employee', 'building_manager']), function ($subquery) use ($user) {
                        $buildingIds = $this->explodeDataFromField($user->building_ids);
                        $assetCategories = $this->explodeDataFromField($user->asset_categories);
                        return $subquery->whereIn('property_id', $buildingIds)
                        ->whereIn('asset_category_id', $assetCategories);
                    });
                }

                elseif ($this->user->user_type == 'sp_admin') {
                    $contract_ids = $this->getContractInformationByServiceProvider($this->user->service_provider);
                    $result = $result->whereIn('contract_id', $contract_ids)
                    ->where('contract_type', 'regular');
                }

                elseif ($this->user->user_type == 'supervisor') {
                    $supervisorId = $this->user->id;
                    $result = $result->whereRaw("find_in_set($supervisorId, supervisor_id)");
                }

                else{
                    $result = $result->where('service_provider_id', $this->serviceProviderId);
                }

                return $result->orderBy($this->orderBy, $this->typeOrderBy)
                ->paginate($this->perPage, ['*'], 'page');
            }

            catch (\Throwable $th) {
                Log::error("getFiltredWorkOrdersByWorkOrderId Error: ".$th);
            }
        }

        public function getSpeceficWorkOrdersByWorkOrderId() {
            try {
                $result = WorkOrders::when(!empty($this->search), function ($query) {
                    $query->where('work_order_id', 'LIKE', '%' . $this->search . '%');
                })
                ->when(!empty($this->selectedRows), function ($query) {
                    $query->whereIn('id', $this->selectedRows);
                })
                ->select(
                    'id', 'work_order_id', 'work_order_type', 'status', 'description', 'property_id', 'floor', 'room', 'workorder_journey', 'contract_type', 'supervisor_id',
                    'assigned_to', 'worker_id', 'service_provider_id', 'job_started_at', 'priority_id', 'contract_id', 'frequency_id', 'project_user_id', 'wtf_start_time',
                    'created_at', 'start_date', 'end_date', 'rating', 'asset_category_id', 'response_time', 'sp_approove', 'pass_fail', 'target_date', 'bm_approve_issue'
                )
                ->where('is_deleted', '!=', 'yes')
                ->where('start_date', '<=', $this->currentDateWithCarbon)
                ->when(!empty($this->dateRange), function ($query) {
                    $query->where('start_date', '>=', $this->dateRange['0'])
                    ->where('end_date', '<=', $this->dateRange['1']);
                })
                ->when(!empty($this->type), function ($query) {
                    $query->where('work_order_type', '=', $this->type);
                })
                ->when(!empty($this->viewBy), function ($query) {
                    $query->where('response_time', '=', $this->viewBy);
                })
                ->when(!empty($this->status), function ($query) {
                    $query->whereIn('status', $this->status);
                })
                ->when(!empty($this->rating), function ($query) {
                    $query->where('rating', '=', $this->rating);
                })
                ->when(!empty($this->property), function ($query) {
                    $query->whereIn('property_id', $this->property);
                })
                ->when(!empty($this->supervisor), function ($query) {
                    $supp = $this->implodeDataFromField($this->supervisor);

                    $query->where(function($innerQuery) use($supp) {
                        $innerQuery->whereRaw("(((supervisor_id IS NOT NULL and assigned_to = 'supervisor') || (assigned_to = 'sp_worker' AND (CHAR_LENGTH(supervisor_id) - CHAR_LENGTH(REPLACE(supervisor_id, ',', '')) + 1) = 1)) AND supervisor_id IN ($supp)AND (assigned_to != 'sp_worker' || worker_id != 0)) OR (service_provider_id IN ($supp) AND (assigned_to != 'sp_worker' || worker_id != 0) AND assigned_to != 'supervisor' AND (CHAR_LENGTH(supervisor_id) - CHAR_LENGTH(REPLACE(supervisor_id, ',', '')) + 1) > 1)");
                    });
                })
                ->when(!empty($this->service), function ($query) {
                    $query->whereIn('asset_category_id', $this->service);
                });

                if (!in_array($this->user->user_type, ['sp_admin', 'supervisor'])) {
                    $result = $result->where('project_user_id', $this->user->project_user_id);
                }

                elseif (in_array($this->user->user_type, ['building_manager', 'building_manager_employee'])) {
                    $user = $this->user;

                    $result = $result->when(in_array($user->user_type, ['building_manager_employee', 'building_manager']), function ($subquery) use ($user) {
                        $buildingIds = $this->explodeDataFromField($user->building_ids);
                        $assetCategories = $this->explodeDataFromField($user->asset_categories);
                        return $subquery->whereIn('property_id', $buildingIds)
                        ->whereIn('asset_category_id', $assetCategories);
                    });
                }

                elseif ($this->user->user_type == 'sp_admin') {
                    $contract_ids = $this->getContractInformationByServiceProvider($this->user->service_provider);
                    $result = $result->whereIn('contract_id', $contract_ids)
                    ->where('contract_type', 'regular');
                }

                elseif ($this->user->user_type == 'supervisor') {
                    $supervisorId = $this->user->id;
                    $result = $result->whereRaw("find_in_set($supervisorId, supervisor_id)");
                }

                else{
                    $result = $result->where('service_provider_id', $this->serviceProviderId);
                }

                return $result->orderBy($this->orderBy, $this->typeOrderBy)
                ->paginate($this->perPage, ['*'], 'page');
            }

            catch (\Throwable $th) {
                Log::error("getSpeceficWorkOrdersByWorkOrderId Error: ".$th);
            }
        }

        public function getPluckWorkOrdersByWorkOrderId() {
            try {
                $result = WorkOrders::when(!empty($this->search), function ($query) {
                    $query->where('work_order_id', 'LIKE', '%' . $this->search . '%');
                })
                ->select('id')
                ->where('is_deleted', '!=', 'yes')
                ->where('start_date', '<=', $this->currentDateWithCarbon)
                ->when(!empty($this->dateRange), function ($query) {
                    $query->where('start_date', '>=', $this->dateRange['0'])
                    ->where('end_date', '<=', $this->dateRange['1']);
                })
                ->when(!empty($this->type), function ($query) {
                    $query->where('work_order_type', '=', $this->type);
                })
                ->when(!empty($this->viewBy), function ($query) {
                    $query->where('response_time', '=', $this->viewBy);
                })
                ->when(!empty($this->status), function ($query) {
                    $query->whereIn('status', $this->status);
                })
                ->when(!empty($this->rating), function ($query) {
                    $query->where('rating', '=', $this->rating);
                })
                ->when(!empty($this->property), function ($query) {
                    $query->whereIn('property_id', $this->property);
                })
                ->when(!empty($this->supervisor), function ($query) {
                    $supp = $this->implodeDataFromField($this->supervisor);

                    $query->where(function($innerQuery) use($supp) {
                        $innerQuery->whereRaw("(((supervisor_id IS NOT NULL and assigned_to = 'supervisor') || (assigned_to = 'sp_worker' AND (CHAR_LENGTH(supervisor_id) - CHAR_LENGTH(REPLACE(supervisor_id, ',', '')) + 1) = 1)) AND supervisor_id IN ($supp)AND (assigned_to != 'sp_worker' || worker_id != 0)) OR (service_provider_id IN ($supp) AND (assigned_to != 'sp_worker' || worker_id != 0) AND assigned_to != 'supervisor' AND (CHAR_LENGTH(supervisor_id) - CHAR_LENGTH(REPLACE(supervisor_id, ',', '')) + 1) > 1)");
                    });
                })
                ->when(!empty($this->service), function ($query) {
                    $query->whereIn('asset_category_id', $this->service);
                });

                if (!in_array($this->user->user_type, ['sp_admin', 'supervisor'])) {
                    $result = $result->where('project_user_id', $this->user->project_user_id);
                }

                elseif (in_array($this->user->user_type, ['building_manager', 'building_manager_employee'])) {
                    $user = $this->user;

                    $result = $result->when(in_array($user->user_type, ['building_manager_employee', 'building_manager']), function ($subquery) use ($user) {
                        $buildingIds = $this->explodeDataFromField($user->building_ids);
                        $assetCategories = $this->explodeDataFromField($user->asset_categories);
                        return $subquery->whereIn('property_id', $buildingIds)
                        ->whereIn('asset_category_id', $assetCategories);
                    });
                }

                elseif ($this->user->user_type == 'sp_admin') {
                    $contract_ids = $this->getContractInformationByServiceProvider($this->user->service_provider);
                    $result = $result->whereIn('contract_id', $contract_ids)
                    ->where('contract_type', 'regular');
                }

                elseif ($this->user->user_type == 'supervisor') {
                    $supervisorId = $this->user->id;
                    $result = $result->whereRaw("find_in_set($supervisorId, supervisor_id)");
                }

                else{
                    $result = $result->where('service_provider_id', $this->serviceProviderId);
                }

                return $result->orderBy($this->orderBy, $this->typeOrderBy)
                ->each(function ($order) {
                    $this->selectedRows[] = $order->id;
                });
            }

            catch (\Throwable $th) {
                Log::error("getPluckWorkOrdersByWorkOrderId Error: ".$th);
            }
        }

        public function getFiltredCountWorkOrdersByStatus($status) {
            try {
                $result = WorkOrders::when(!empty($status), function ($query) use($status) {
                    $query->where('status', '=', $status);
                })
                ->where('is_deleted', '!=', 'yes')
                ->where('start_date', '<=', $this->currentDateWithCarbon);

                if (!in_array($this->user->user_type, ['sp_admin', 'supervisor'])) {
                    $result = $result->where('project_user_id', $this->user->project_user_id);
                }

                elseif ($this->user->user_type == 'sp_admin') {
                    $contract_ids = $this->getContractInformationByServiceProvider($this->user->service_provider);
                    $result = $result->whereIn('contract_id', $contract_ids)
                    ->where('contract_type', 'regular');
                }

                elseif ($this->user->user_type == 'supervisor') {
                    $supervisorId = $this->user->id;
                    $result = $result->whereRaw("find_in_set($supervisorId, supervisor_id)");
                }

                else{
                    $result = $result->where('service_provider_id', $this->serviceProviderId);
                }

                return $result->count();
            }

            catch (\Throwable $th) {
                Log::error("getFiltredCountWorkOrdersByStatus error: ".$th);
            }
        }

        public function getProjectStartDate() {
            try {
                return Helper::getProjectDetails(Helper::getProjectID())->created_at;
            }

            catch (\Throwable $th) {
                Log::error("getProjectStartDate Error: ".$th);
            }
        }

        public function getFormatPropertyName($property) {
            try {
                return WorkorderHelper::getFormattedPropertyName($property);
            }

            catch (\Throwable $th) {
                Log::error("getFormatPropertyName error: ".$th);
            }
        }

        public function getFormatSupervisor($supervisor, $assigned, $worker, $service_provider) {
            try {
                return WorkorderHelper::getFormattedSupervisorsOrServiceProviderName($supervisor, $assigned, $worker, $service_provider);
            }

            catch (\Throwable $th) {
                Log::error("getFormatSupervisor error: ".$th);
            }
        }

        public function getFormatWorker($data) {
            try {
                return WorkorderHelper::generateAssignedWorkerColumnContent($data);
            }

            catch (\Throwable $th) {
                Log::error("getFormatWorker error: ".$th);
            }
        }

        public function getFormatJourney($journey) {
            try {
                return WorkorderHelper::generateWorkorderJourneyColumnContent($journey);
            }

            catch (\Throwable $th) {
                Log::error("getFormatJourney error: ".$th);
            }
        }

        public function getFormatResponseTime($data) {
            try {
                return WorkorderHelper::generateResponseTimeColumnContent($data);
            }

            catch (\Throwable $th) {
                Log::error("getFormatResponseTime error: ".$th);
            }
        }

        public function getFormatExecutionTime($data) {
            try {
                return WorkorderHelper::generatePassFailColumnContent($data);
            }

            catch (\Throwable $th) {
                Log::error("getFormatExecutionTime error: ".$th);
            }
        }

        public function getFormatReportedTime($data) {
            try {
                return WorkorderHelper::formatSubmissionDate($data);
            }

            catch (\Throwable $th) {
                Log::error("getFormatReportedTime error: ".$th);
            }
        }

        public function getFormatExpectedDate($data) {
            try {
                return WorkorderHelper::generateTargetDateColumnContent($data);
            }

            catch (\Throwable $th) {
                Log::error("getFormatExpectedDate error: ".$th);
            }
        }

        public function getExistAssetCategories($loggedInUser) {
            try {
                return WorkorderHelper::getFilteredAssetCategories($loggedInUser);
            }

            catch (\Throwable $th) {
                Log::error("getExistAssetCategories error: ".$th);
            }
        }

        public function getExistProperties($loggedInUser, $userId, $serviceProviderId) {
            try {
                return WorkorderHelper::getFilteredProperties($loggedInUser, $userId, $serviceProviderId);
            }

            catch (\Throwable $th) {
                Log::error("getExistProperties error: ".$th);
            }
        }

        public function getExistSupervisors($loggedInUser, $serviceProviderId) {
            try {
                return WorkorderHelper::getFilteredSupervisors($loggedInUser, $serviceProviderId);
            }

            catch (\Throwable $th) {
                Log::error("getExistSupervisors error: ".$th);
            }
        }

        public function getExistWorkers() {
            try {
                return WorkorderHelper::getFilteredWorkers();
            }

            catch (\Throwable $th) {
                Log::error("getExistWorkers error: ".$th);
            }
        }

        public function getWorkOrdersListById($id) {
            try {
                return WorkOrders::with([
                    'contract',
                    'propertyBuilding.property',
                    'worker',
                    'relatedWorkOrders',
                    'relatedPMWorkOrders',
                    'slaAssetCategory.priority',
                    'serviceWindowPriority',
                    'assetCategory',
                    'contractPriority.priority',
                    'frequencyMaster.frequencies.contractFrequencies',
                    'frequencyMaster',
                    'selectedAssetName',
                    'workTimeFrame',
                    'propertyBuilding.property.city',
                    'propertyBuilding.property.region',
                    'checklist.checklistTasks.noChecklistActions' ,
                    'checklist.checklistTasks.checklistSubtasks.SubtaskActionsList' ,
                    'NoChecklistSubtaskAction',
                    'closedBy',
                    'createdBy',
                    'maintenanceRequest',
                    'projectSettings',
                    'woNoChecklistActions',
                    'checklists',
                    'serviceProviderAdmin'
                  ])
                  ->whereIn('work_orders.id', $id)
                  ->get();
            }

            catch (\Throwable $th) {
                Log::error("getWorkOrdersList error: ".$th);
            }
        }

        /**
         * userNotAuthCounters
         *
         * @return void
         */
        public function userNotAuthCounters(){
            try {
                // Return zeros if the user is not authenticated
                return [
                    'work_orders_count' => 0,
                    'open_work_orders_count' => 0,
                    'inprogress_work_orders_count' => 0,
                    'under_evaluation_work_orders_count' => 0,
                    'closed_work_orders_count' => 0,
                    'bm_work_orders_count' => 0,
                    'wfa_work_orders_count' => 0
                ];
            }
            catch (\Throwable $th) {
                Log::error("userNotAuthCounters error: ".$th);
            }
        }

        /**
         * getWorkOrderCounters
         *
         * @return void
         */
        public function getWorkOrderCountersForBM_WFA(){
            try {
                $currentDateTime = Carbon::now();
                return WorkOrders::query()
                        ->select(
                            'id',
                            DB::raw('(SELECT COUNT(wo.id) FROM work_orders AS wo INNER JOIN work_time_frame ON work_time_frame.user_id = wo.project_user_id WHERE wo.work_order_type = "preventive" and wo.status = 1 AND wo.start_date = "'.$currentDateTime->toDateString().'" AND wo.id = work_orders.id AND work_time_frame.start_time >= "'.$currentDateTime->toTimeString().'") AS pm_count')
                        );
            }catch (\Throwable $th) {
                Log::error("getWorkOrderCounters error: ".$th);
            }
        }

        /**
         * getAdminUserTypes
         *
         * @return void
         */
        public function getAdminUserTypes(){
            try{
                return [
                    UserAccountType::OsoolAdmin->value,
                    UserAccountType::Admin->value,
                    UserAccountType::AdminEmployee->value,
                    UserAccountType::SuperAdmin->value
                ];
            }
            catch (\Throwable $th) {
                Log::error("getAdminUserTypes error: ".$th);
            }
        }

        /**
         * getWorkOrdersStatusList
         *
         * @return void
         */
        public function getWorkOrdersStatusList(){
            try{
                return array_column(WorkOrderRoutesStatusList::cases(), 'value');
            }
            catch (\Throwable $th) {
                Log::error("getWorkOrdersStatusList error: ".$th);
            }
        }

        /**
         * initUser
         *
         * @return void
         */
        public function initUser() {
            try {
                return auth()->user();
            }
            catch (\Throwable $th) {
                Log::error("initUser Error: ".$th);
            }
        }

        /**
         * getSPWorkOrder
         *
         * @return void
         */
        public function getSPWorkOrder($work_orders, $routesList){
            try{
                if(in_array(\Route::currentRouteName(), $routesList)  ){
                    $sp_id = \Crypt::decryptString(request()->id);
                    if(Session::has('selected_multiple_sp_id')) {
                        $sp_id = Session::get('selected_multiple_sp_id');
                    }
                    else
                    {
                        $sp_id = explode(',',$sp_id);
                    }
                    // This query run 300% faster than has() or wherehas(). previous one ;)
                    $work_orders = $work_orders->leftJoin('contract', 'contract.id', '=', 'work_orders.contract_id')->whereIn('contract.service_provider_id', $sp_id);
                    //The previous one display an error and after I fixed it's verry slow (200% slow) the best way is to use left joins instead of 'whereHas'
                    //bescause whereHas use SELECT ... FROM `work_orders` WHERE EXISTS(SELECT * FROM contract where contract.service_provider_id in($sp_id)... with * from contract without indexing ;)
                }
                return $work_orders;
            }catch (\Throwable $th) {
                Log::error("getSPWorkOrder error: ".$th);
            }
        }

        /**
         * getActiveWorkOrdersOnly
         *
         * @param  mixed $work_orders
         * @param  mixed $currentDateTime
         * @return void
         */
        public function getActiveWorkOrdersOnly($work_orders, $currentDateTime){
            try{
                return $work_orders->where('is_deleted', '=', 'no')
                        ->where('start_date', '<=', $currentDateTime->toDateString())
                        ->orderByDesc('start_date');

            }catch (\Throwable $th) {
                Log::error("getActiveWorkOrdersOnly error: ".$th);
            }
        }

        /**
         * applayWorkOrdersFilters
         *
         * @param  mixed $work_orders
         * @param  mixed $user
         * @param  mixed $currentDateTime
         * @return void
         */
        public function applayWorkOrdersFilters($work_orders, $adminUserTypes, $user, $currentDateTime){
            try{
                $currentDateTime = Carbon::now();
                $work_orders = $this->getActiveWorkOrdersOnly($work_orders, $currentDateTime);
                if(isset($adminUserTypes) && isset($user) && in_array($user->user_type, $adminUserTypes)){
                    $routesList = $this->getWorkOrdersStatusList();
                    $work_orders = $this->getSPWorkOrder($work_orders, $routesList);
                }
                return $work_orders;
            }
            catch (\Throwable $th) {
                Log::error("applayWorkOrdersFilters error: ".$th);
            }
        }

        public function wfanWorkOrderUserConditions($wfa_work_orders, $user, $user_id){
            try{
                return Helper::applyWaitingForActionWorkOrderUserConditions($wfa_work_orders, $user, $user_id);
            }
            catch (\Throwable $th) {
                Log::error("wfanWorkOrderUserConditions error: ".$th);
            }
        }
        public function workOrderConditions($work_orders, $user, $user_id){
            try{
                return Helper::applyWorkOrderUserConditions($work_orders, $user, $user_id);
            }
            catch (\Throwable $th) {
                Log::error("workOrderConditions error: ".$th);
            }
        }
        public function bmWorkOrderConditions($bm_work_orders, $user, $user_id){
            try{
                return Helper::applyBMWorkOrderUserConditions($bm_work_orders, $user, $user_id);
            }
            catch (\Throwable $th) {
                Log::error("bmWorkOrderConditions error: ".$th);
            }
        }

        /**
         * Get the counts of work orders for the authenticated user.
         *
         * @return array An associative array containing the work orders count and open work orders count.
         */
        public function workOrdersCounters()
        {
            try {
                // Check if the user is not authenticated
                if (!Auth::check()) {
                    return $this->userNotAuthCounters();
                }
                $user = $this->getAuthenticatedUser();
                $user_id = $user->project_user_id;
                $currentDateTime = $this->getCurrentDateWithCarbon();
                $work_orders = $this->getFiltredCountWorkOrdersByStatus('');
                //dd($work_orders->first());
                //
                //$work_orders = $this->applayWorkOrdersFilters($work_orders, $adminUserTypes, $user, $currentDateTime);
                ///$work_orders = $this->workOrderConditions($work_orders, $user, $user_id);
                $adminUserTypes = $this->getAdminUserTypes();
                $work_orders_bm_wfa = $this->getWorkOrderCountersForBM_WFA();
                $work_orders_bm_wfa = $this->applayWorkOrdersFilters($work_orders_bm_wfa, $adminUserTypes, $user, $currentDateTime);
                $bm_work_orders = clone $work_orders_bm_wfa;
                $wfa_work_orders = clone $work_orders_bm_wfa;

                $wfa_work_orders = $this->wfanWorkOrderUserConditions($wfa_work_orders, $user, $user_id);
                $bm_work_orders = $this->bmWorkOrderConditions($bm_work_orders, $user, $user_id);


                ///DB::enableQueryLog();

                //---This query is reduced from 7 to 3 queries only and -------------------
                //
                // Count the total work orders that match the filters
                $work_orders_count = $this->remeberFirst('work_orders_count', CachingTTL::TTL_FIVE_MIN->value, $work_orders);

                ///$queries = DB::getQueryLog();
                ///dd($queries);

                if ($user->user_type == 'sp_admin' || $user->user_type == 'supervisor' || $user->user_type == "building_manager" || $user->user_type == "building_manager_employee") {
                    // Count the total BM work orders that match the filters
                    $bm_work_orders_count = $this->remeberCount('bm_work_orders_count', CachingTTL::TTL_FIVE_MIN->value, $bm_work_orders);
                } else {
                    $bm_work_orders_count = 0;
                }

                if ($user->user_type == 'sp_admin' || $user->user_type == 'supervisor') {
                    // Count the total waiting for action work orders that match the filters
                    $wfa_work_orders_count = $this->remeberCount('wfa_work_orders_count', CachingTTL::TTL_FIVE_MIN->value, $wfa_work_orders);
                } else {
                    $wfa_work_orders_count = 0;
                }

                //--------------------------------------------------------------------
                // Return the counts in an associative array
                return [
                    'work_orders_count' => $work_orders_count->total,
                    'open_work_orders_count' => $work_orders_count->open_work_orders_count,///$open_work_orders_count,
                    'inprogress_work_orders_count' => $work_orders_count->inprogress_work_orders_count,///$inprogress_work_orders_count,
                    'under_evaluation_work_orders_count' => $work_orders_count->under_evaluation_work_orders_count,///$under_evaluation_work_orders_count,
                    'closed_work_orders_count' => $work_orders_count->closed_work_orders_count,///$closed_work_orders_count,
                    'bm_work_orders_count' => $bm_work_orders_count,
                    'wfa_work_orders_count' => $wfa_work_orders_count
                ];
            }

            catch (\Throwable $th) {
                Log::error("workOrdersCounter error: ".$th);
            }
        }

        /**
         * getWorkOrderById
         *
         * @param  mixed $woId
         * @return void
         */
        public function getWorkOrderById($woId){
            try {
                return WorkOrders::find($woId);
            }
            catch (\Throwable $th) {
                Log::error("getWorkOrderById Error: ".$th);
            }
        }

        /**
         * getItemsRequestsByWorkerApprovedOrRejected
         *
         * @param  mixed $id
         * @return void
         */
        public function getItemsRequestsByWorkerApprovedOrRejected($id){
            try {
                app()->bind(TableConfigInterface::class, WorkOrderItemFormTableConfig::class);
                return WorkorderHelper::checkItemRequestsByWorkerApprovedOrRejected($id);
            }
            catch (\Throwable $th) {
                Log::error("getItemsRequestsByWorkerApprovedOrRejected error: ".$th);
            }
        }

        public function getWorkOrderPerformanceIndicatorData($contract){
            try {
                $kpiservice = new PerformanceIndicatorService();
                $performanceIndicatorData = $kpiservice->getDataForContractOverview($contract);
                return $performanceIndicatorData;
            }
            catch (\Throwable $th) {
                Log::error("getWorkOrderPerformanceIndicatorData error: ".$th);
            }
        }

        public function getDeletedRoomTypesFloors($work_orders_details){
            try {
            return RoomsTypeFloors::where('id',$work_orders_details->unit_id )
                ->value('deleted_at');
            }
            catch (\Throwable $th) {
                Log::error("getDeletedRoomTypesFloors error: ".$th);
            }
        }

        public function checkIfGeneraSentenceIsDeleted($work_orders_details){
            if(isset($work_orders_details->unit_id) && $work_orders_details->unit_id !=0){ //If unit id not equal is 0
                $r_del = '';
                if($this->getDeletedRoomTypesFloors($work_orders_details) != ''){
                    $r_del =  __('general_sentence.modal.deleted');
                }
                $work_orders_details->floor = RoomsTypeFloors::where('id',$work_orders_details->unit_id )->value('floor');
                $work_orders_details->room = RoomsTypeFloors::where('id',$work_orders_details->unit_id )->value('room').$r_del;
                return $work_orders_details;
            }
            return null;
        }

        public function getWorkOrderType($id){
            try {
                return WorkOrders::where('id',$id)->value('work_order_type');
            }
            catch (\Throwable $th) {
                Log::error("getWorkOrderType Error: ".$th);
            }
        }
        ///kkkkk
        public function workOrderDetails($id){
            try {
                $wo_type = $this->getWorkOrderType($id);
                $work_orders_details = null;
                if(isset($wo_type) && $wo_type =='preventive'){
                    $work_orders_details_pm = WorkOrders::get_work_order_details_pm($id);
                    $work_orders_details = $this->checkIfGeneraSentenceIsDeleted($work_orders_details_pm);
                }
                else{
                    $wo_details = WorkOrders::get_work_order_details($id);
                    $work_orders_details = $this->checkIfGeneraSentenceIsDeleted($wo_details);
                }
                //dd($work_orders_details);
                $work_orders_details = $this->setCreatedAt($work_orders_details);
                //dd($work_orders_details);
                return $work_orders_details;
            }
            catch (\Throwable $th) {
                Log::error("workOrderDetails Error: ".$th);
            }
        }

        public function getWarehouseOwner($contract) {
            try {
                return $contract->warehouse_owner ?? 'admin';
            }
            catch (\Throwable $th) {
                Log::error("getWarehouseOwner Error: ".$th);
            }
        }

        public function getItemRequestsAppouved($id){
            try {
                return $this->getItemsRequestsByWorkerApprovedOrRejected($id);
            }
            catch (\Throwable $th) {
                Log::error("getItemRequestsAppouved Error: ".$th);
            }
        }

        public function getWorkOrderRequestedItemsById($id){
            try {
                $res = WorkOrderItemRequest::getWorkOrderItemRequestByWOId($id);
                return $res;
            }
            catch (\Throwable $th) {
                Log::error("getWorkOrderRequestedItemsById Error: ".$th);
            }
        }

        public function getWorkOrderRequestedItems($id){
            try {
                $workOrderRequestedItems = $this->getWorkOrderRequestedItemsById($id);
                // Check if requestedItems is set
                if (isset($workOrderRequestedItems->requestedItems)) {
                    // Access the relationship data
                    foreach ($workOrderRequestedItems->requestedItems as $key => $requestedItem) {
                        // Get the $contractUsableItem object
                        if ($requestedItem->contractUsableItem !== null) {
                            $contractUsableItem = $requestedItem->contractUsableItem->getItem();
                        } else {
                            $contractUsableItem = null; // or some default value
                        }

                        // Fetch the item data
                        $itemData = $requestedItem->getItem();

                        // Check if $itemData->items is not null and is an instance of a collection
                        $itemsArray = $itemData->items ? $itemData->items->toArray() : [];

                        // Check if there are any items
                        if (!empty($itemsArray)) {
                            // Get the first item from the array
                            $firstItem = $itemsArray[0];

                            // Get the warehouse_id and warehouse name
                            $warehouse_id = $firstItem['warehouse_id'] ?? null;
                            $getWarehouseName = $firstItem['warehouse']['name'] ?? null;
                        } else {
                            $getWarehouseName = $requestedItem->getWarehouse()->name ?? '';
                            $warehouse_id = $requestedItem->getWarehouse()->id ?? 0;
                        }

                        // Assign the name property to the requestedItem
                        $requestedItem->name = isset($contractUsableItem->name) ? $contractUsableItem->name : '';
                        $requestedItem->category_name = isset($contractUsableItem->category->name) ? $contractUsableItem->category->name : '';
                        $requestedItem->sale_price_formatted = isset($contractUsableItem->sale_price_formatted) ? $contractUsableItem->sale_price_formatted : '';
                        $requestedItem->sale_price_vat_formatted = isset($contractUsableItem->sale_price_vat_formatted) ? $contractUsableItem->sale_price_vat_formatted : '';
                        $requestedItem->country_of_origin_formatted = isset($contractUsableItem->country_of_origin_formatted) ? $contractUsableItem->country_of_origin_formatted : '';
                        $requestedItem->status = $workOrderRequestedItems->status;
                        $requestedItem->approved_by = $workOrderRequestedItems->user->name ?? '';
                        $requestedItem->warehouse_name = $getWarehouseName;
                        $requestedItem->warehouse_id = $warehouse_id;
                        $requestedItem->missing_quantity = $workOrderRequestedItems->status == 'partially_given' ? $requestedItem->quantity_accepted : $requestedItem->missing_quantity;
                    }
                }
                $requestedItems = isset($workOrderRequestedItems->requestedItems) ? $workOrderRequestedItems->requestedItems : [];
                return $requestedItems;
            }
            catch (\Throwable $th) {
                Log::error("getWorkOrderRequestedItems Error: ".$th);
            }
        }


        public function getItemsData($checkItemRequestsByWorkerApprovedOrRejected, $requestedItems, $requestedItemsBySp){
            try {
                $itemsData = [];
                if($checkItemRequestsByWorkerApprovedOrRejected == 'worker') {
                    $itemsData = $requestedItems;
                } elseif($checkItemRequestsByWorkerApprovedOrRejected == 'serviceprovider') {
                    $itemsData = $requestedItemsBySp->requestedItems ?? [];
                }
                return $itemsData;
            }
            catch (\Throwable $th) {
                Log::error("getItemsData Error: ".$th);
            }
        }

        public function getAproovedBy($checkItemRequestsByWorkerApprovedOrRejected, $work_orders_details_id){
            try {
                $aproovedBy = null;
                if($checkItemRequestsByWorkerApprovedOrRejected == 'worker') {
                    $aproovedBy = WorkOrderItemRequest::getWorkOrderItemRequestAproovedNameByByid($work_orders_details->id);
                } elseif($checkItemRequestsByWorkerApprovedOrRejected == 'serviceprovider') {
                    $aproovedBy = ServiceProviderMissingItemRequest::getServiceProviderMissingItemRequestAproovedNameByByWOid($work_orders_details->id);
                }
                return $aproovedBy;
            }
            catch (\Throwable $th) {
                Log::error("getAproovedBy Error: ".$th);
            }
        }
        public function getWorkTimeFrameByProjectId($work_orders_details){
            try {
                return WorkTimeFrame::getWorkTimeFrameByProjectUserId($work_orders_details->project_user_id);
            }
            catch (\Throwable $th) {
                Log::error("getWorkTimeFrameByProjectId Error: ".$th);
            }
        }
        public function getDateTimeWorkorkder($work_orders_details){
            try {
                $time ="";
                $wtfs = $this->getWorkTimeFrameByProjectId($work_orders_details);
                if($work_orders_details->wtf_start_time == '' || ($work_orders_details->work_order_type == "preventive" && $work_orders_details->status == 1)){
                    if(!isset($wtfs)) //If work order time frame has added
                    {
                        $time = "00:00:00";
                    }
                    else {
                        $time = $wtfs->start_time;
                    }
                }
                else{
                    $time = $work_orders_details->wtf_start_time;
                }
                return $time;
            }
            catch (\Throwable $th) {
                Log::error("getDateTimeWorkorkder Error: ".$th);
            }
        }

        public function setCreatedAt($work_orders_details){
            try {

                $time = $this->getDateTimeWorkorkder($work_orders_details);
                if($work_orders_details->work_order_type == "preventive") //If work_order_type is preventive
                {
                    $work_orders_details->created_at = $work_orders_details->start_date.' '.$time;
                }
                //dd($work_orders_details);
                return $work_orders_details;
            }
            catch (\Throwable $th) {
                Log::error("setCreatedAt Error: ".$th);
            }
        }

        public function getStatus(){
            try{
                return __("data_contract.contract_forms.label.contract_number");
            }catch (\Throwable $th) {
                Log::error("getStatus Error: ".$th);
            }
        }

        public function getProjectID($work_orders_details){
            try{
                $project_user_id = $work_orders_details->project_user_id;
                $project_id = null;
                if(isset($project_user_id) && $project_user_id != 0)
                {   //If has project_user_id and project_user_id not equal to 0
                    $userDetails = User::where('id', $project_user_id)->first();
                    if (! ($userDetails->project_user_id==Auth::user()->project_user_id) ) {
                        return redirect()->route('admin.dashboard');
                    }
                    Log::info('Project User ID Authentificad : ' . Auth::user()->project_user_id . ', Project User ID From WorkOrder : ' . $userDetails->project_user_id);
                    $project_id = $userDetails->project_id;
                }
                else
                {
                    $userDetails = User::where('id', $work_orders_details->created_by)->first();
                    if (! ($userDetails->project_user_id==Auth::user()->project_user_id) ) {
                        return redirect()->route('admin.dashboard');
                    }
                    Log::info('Project User ID Authentificad : ' . Auth::user()->project_user_id . ', Project User ID From WorkOrder : ' . $userDetails->project_user_id);
                    WorkOrders::where('id', $work_orders_details->id)->update(['project_user_id' => $userDetails->project_user_id]);
                    $project_id = $userDetails->project_id;
                }
                return $project_id;
            }catch (\Throwable $th) {
                Log::error("getStatus Error: ".$th);
            }
        }

        public function getProjectSetting($work_orders_details){
            try{
                $project_id = $this->getProjectID($work_orders_details);
                return ProjectSettings::select('workorder_reopen_periods')->where('project_id', $project_id)->first();
            }catch (\Throwable $th) {
                Log::error("getProjectSetting Error: ".$th);
            }

        }
        public function workOrderPeriods($work_orders_details){
            try{
                $projectSetting = $this->getProjectSetting($work_orders_details);
                if($projectSetting) { //If has project settings
                    $workorder_reopen_periods= $projectSetting->workorder_reopen_periods;
                } else {
                    $workorder_reopen_periods= '14';
                }
                return $workorder_reopen_periods;
            }catch (\Throwable $th) {
                Log::error("getProjectSetting Error: ".$th);
            }
        }

        public function getTimeLine($id, $work_orders_details){
            try {
                return WorkOrders::get_work_order_timeline($id, $work_orders_details->unique_id);
            }
            catch (\Throwable $th) {
                Log::error("getTimeLine Error: ".$th);
            }
        }

        public function getChats($id){
            try {
                return WorkOrders::get_work_order_chat($id);
            }
            catch (\Throwable $th) {
                Log::error("getChats Error: ".$th);
            }
        }

        public function getWorkers($work_orders_details){
            try {
                return  WorkOrders::get_workers($work_orders_details->contract_id, $work_orders_details->asset_category_id, $work_orders_details->building_id);
            }
            catch (\Throwable $th) {
                Log::error("getWorks Error: ".$th);
            }
        }

        public function getupdatedWorkers($work_orders_details){
            try {
                $workers = $this->getWorkers($work_orders_details);
                $cu_la = \App::getLocale();
                if(isset($workers) && !empty($workers))
                {
                    foreach($workers as $key => $val)
                    {
                        $availability_record = Helper::CheckChangeAvailabilitystatusworkorder($val['id']);
                        if($availability_record['id'] != "no_record")
                        {
                            $leave_reason = $cu_la == "ar" ? $availability_record['reason_type_ar'] : $availability_record['reason_type_en'];
                            $workers[$key]['name'] = $val['name'] ." - ". $leave_reason ." - ". $availability_record['from_datetime'] ." - ". $availability_record['to_datetime'];
                        }
                    }
                }
                return $workers;
            }
            catch (\Throwable $th) {
                Log::error("getupdatedWorkers Error: ".$th);
            }
        }

        public function getWorkOrderImages($id){
            try {
                return Helper::getWorkorderChecklistImages($id);
            }
            catch (\Throwable $th) {
                Log::error("getWorkOrderImages Error: ".$th);
            }
        }

        public function getWorkOrderComments($id){
            try {
                return WorkOrders::get_checklist_worker_comment($id);
            }
            catch (\Throwable $th) {
                Log::error("getWorkOrderComments Error: ".$th);
            }
        }

        public function getWorkOrderSupervisors($work_orders_details){
            try {
                $supervisors = null;
                if((isset($work_orders_details->supervisor_id) && $work_orders_details->supervisor_id != NULL && $work_orders_details->assigned_to == 'supervisor') || (count(explode(',', $work_orders_details->supervisor_id)) == 1 && $work_orders_details->assigned_to == 'sp_worker'))
                {
                    $supervisors = WorkOrders::get_supervisors($work_orders_details->supervisor_id);
                }
                else
                {
                    $supervisors = WorkOrders::get_supervisors($work_orders_details->service_provider_id);
                }
                return $supervisors;
            }
            catch (\Throwable $th) {
                Log::error("getWorkOrderSupervisors Error: ".$th);
            }
        }

        /**
         * getContracts
         *
         * @param  mixed $work_orders_details
         * @return void
         */
        public function getContracts($work_orders_details){
            try{
                return Contracts::select('id', 'is_deleted', 'deleted_at', 'contract_number')->where('id',$work_orders_details->contract_id)->first();
            }catch (\Throwable $th) {
                Log::error("getContracts Error: ".$th);
            }
        }

        public function updateWODetailsContracts($work_orders_details){
            try{

                $contract_details = $this->getContracts($work_orders_details);
                $work_orders_details->contract_deleted = false;
                if($contract_details->is_deleted != 'no' || $contract_details->deleted_at !=''){
                    $contract_details->contract_number =$contract_details->contract_number.__('general_sentence.modal.deleted') ;
                    $work_orders_details->contract_deleted = true;
                }
                $contract_number = $contract_details->contract_number;
                $work_orders_details->contract_number = $contract_details->contract_number;
                $work_orders_details->contract_id = $contract_details->id;
                return ['contract_number' => $contract_number, 'work_orders_details' => $work_orders_details];
            }catch (\Throwable $th) {
                Log::error("updateWODetailsContracts Error: ".$th);
            }
        }

        public function updateWODContractNumber($work_orders_details){
            try{
                $contract_details = $this->getContracts($work_orders_details);
                $work_orders_details->contract_deleted = false;
                if($contract_details->is_deleted != 'no' || $contract_details->deleted_at !=''){
                    $contract_details->contract_number =$contract_details->contract_number.__('general_sentence.modal.deleted') ;
                    $work_orders_details->contract_deleted = true;
                }
                $contract_number = $contract_details->contract_number;
                $work_orders_details->contract_number = $contract_details->contract_number;
                $work_orders_details->contract_id = $contract_details->id;
                return ['contract_number' => $contract_number, 'work_orders_details' => $work_orders_details];
            }catch (\Throwable $th) {
                Log::error("updateWODContractNumber Error: ".$th);
            }
        }

        public function getContractPrioritybyIdAndNumber($priority_id, $contract_number ){
            try{
                return  DB::table('contract_priorities')
                ->where('priority_id', $priority_id)
                ->where('contract_number', $contract_number)
                ->orderBy('id', 'desc')
                ->first();

            }catch (\Throwable $th) {
                Log::error("getContractPrioritybyIdAndNumber Error: ".$th);
            }
        }

        public function getWindowPeriode($work_orders_details){
            try{

                $result = $this->updateWODetailsContracts($work_orders_details);
                //dd($contract_number);
                $contract_number = $result['contract_number'];
                $work_orders_details = $result['work_orders_details'];
                $sla_asset_categories = ContractAssetCategories::getByCategoryIdAndContractNumber($work_orders_details->asset_category_id, $contract_number);
                $response_time = 0;
                $service_window = 0;
                $response_time_type = 'hours';
                $service_window_type = 'minutes';

                //dd($work_orders_details->work_order_type);
                if($work_orders_details->work_order_type == "reactive") //If work_order_type is reactive
                {
                    if(!empty($sla_asset_categories->priority_id) || !empty($work_orders_details->sla_service_window_priority)){ //If sla asset categories has priority_id
                        if($work_orders_details->sla_service_window_priority != 0)
                        {
                            $priority_id = $work_orders_details->sla_service_window_priority;
                        }
                        else
                        {
                            $priority_id = $sla_asset_categories->priority_id;
                        }
                        $contract_priorities = $this->getContractPrioritybyIdAndNumber($priority_id, $contract_number);
                        if(empty($contract_priorities))
                        {
                            $contract_priorities = Priorities::getPriorityById($priority_id);
                        }
                        if(isset($contract_priorities))
                        {
                            $response_time = $contract_priorities->response_time;
                            $service_window = $contract_priorities->service_window;
                            $response_time_type = $contract_priorities->response_time_type;
                            $service_window_type = $contract_priorities->service_window_type;

                            $priorities = DB::table('priorities')->select('priority_level', 'deleted_at')->where('id', $priority_id)->first();
                            //dd($priorities);
                            if(isset($priorities))
                            {
                                $deleted = "";
                                if($priorities->deleted_at != "")
                                {
                                    $deleted = " [".__("work_order.bread_crumbs.deleted")."]";
                                }
                                $work_orders_details->priority_level = $priorities->priority_level.$deleted;
                            }
                            $priorities = DB::table('priorities')->select('priority_level', 'deleted_at')->where('id', $priority_id)->first();
                            //dd($priorities);
                            $work_orders_details->response_time_priority_level = $work_orders_details->priority_level;
                            if(isset($priorities))
                            {
                                $deleted = "";
                                if($priorities->deleted_at != "")
                                {
                                    $deleted = " [".__("work_order.bread_crumbs.deleted")."]";
                                }
                                $work_orders_details->response_time_priority_level = $priorities->priority_level.$deleted;
                            }
                        }
                    }
                }
                elseif($work_orders_details->work_order_type == "preventive" && $work_orders_details->wo_priority_id != 0) //If work_order_type is preventive and priority_id is not equal to 0
                {
                    $priority_id = $work_orders_details->wo_priority_id;
                    if($work_orders_details->sla_service_window_priority != 0)
                    {
                        $priority_id = $work_orders_details->sla_service_window_priority;
                    }
                    $contract_priorities = $this->getContractPrioritybyIdAndNumber($priority_id, $contract_number);
                    //dd($contract_priorities);
                    if(empty($contract_priorities))
                    {
                        $contract_priorities = Priorities::getPriorityById($priority_id);
                    }
                    if(isset($contract_priorities))
                    {
                        $response_time = $contract_priorities->response_time;
                        $service_window = $contract_priorities->service_window;
                        $response_time_type = $contract_priorities->response_time_type;
                        $service_window_type = $contract_priorities->service_window_type;
                        //dd($service_window);
                        $priorities = Priorities::getPriorityById($work_orders_details->wo_priority_id);
                        //dd($priorities);
                        if(isset($priorities))
                        {
                            $deleted = "";
                            if($priorities->deleted_at != "")
                            {
                                $deleted = " [".__("work_order.bread_crumbs.deleted")."]";
                            }
                            $work_orders_details->priority_level = $priorities->priority_level.$deleted;
                        }
                        //dd($priorities);
                        $work_orders_details->response_time_priority_level = $work_orders_details->priority_level;
                        if(isset($priorities))
                        {
                            $deleted = "";
                            if($priorities->deleted_at != "")
                            {
                                $deleted = " [".__("work_order.bread_crumbs.deleted")."]";
                            }
                            $work_orders_details->response_time_priority_level = $priorities->priority_level.$deleted;
                        }
                    }
                }
                else
                {
                    $contract_frequencies = FrequencyMaster::getFrequencyMasterByIdAndContractNbr($work_orders_details, $contract_number);
                    $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
                    $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : '';
                    $response_time_type = isset($contract_priorities->response_time_type)?$contract_priorities->response_time_type:'hours';
                    $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
                }
                return [
                    'response_time' => $response_time,
                    'service_window' => $service_window,
                    'response_time_type' => $response_time_type,
                    'service_window_type' => $service_window_type,
                    'work_orders_details' => $work_orders_details
                ];
            }catch (\Throwable $th) {
                Log::error("getWindowPeriode Error: ".$th);
            }
        }

        public function getWorkOrdersDetailsPriorityLevel($work_orders_details){
            try{
                $priorities = Priorities::getPriorityById($work_orders_details->sla_service_window_priority);
                //dd($priorities);
                if(isset($priorities))
                {
                    $deleted = "";
                    if($priorities->deleted_at != "")
                    {
                        $deleted = " [".__("work_order.bread_crumbs.deleted")."]";
                    }
                    $work_orders_details->priority_level = $priorities->priority_level.$deleted;
                }
                return $work_orders_details;
            }catch (\Throwable $th) {
                Log::error("getWorkOrdersDetailsPriorityLevel Error: ".$th);
            }
        }

        public function updateWorkOrdersDetailsPrios($work_orders_details, $response_data){
            try{
                $priorities = Priorities::getPriorityById($work_orders_details->sla_response_time_priority);
                $tdate = date('Y-m-d H:i:s');
                $user_id = auth()->user()->id;
                $work_orders_details->response_time_priority_level = $work_orders_details->priority_level;
                $service_window = $response_data['service_window'];
                $service_window_type = $response_data['service_window_type'];
                if(isset($priorities))
                {
                    $deleted = "";
                    if($priorities->deleted_at != "")
                    {
                        $deleted = " [".__("work_order.bread_crumbs.deleted")."]";
                    }
                    $work_orders_details->response_time_priority_level = $priorities->priority_level.$deleted;
                }
                //dd($work_orders_details->job_started_at)
                if($work_orders_details->job_started_at == NULL || $work_orders_details->bm_approve_issue == 2) //If job_started_at is null
                {
                    $target_date = $work_orders_details->target_date;
                    $work_orders_details->target_date = $target_date;
                }
                else
                {
                    //dd($service_window.' -- '.$service_window_type);
                    $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($work_orders_details->job_started_at)));
                    //dd($target_date.'----'.$work_orders_details->target_date);
                    $work_orders_details->target_date = $target_date;

                }
                //dd($work_orders_details->job_started_at != '' && $work_orders_details->status != 4 && $work_orders_details->pass_fail == "pending");
                if($work_orders_details->job_started_at != '' && $work_orders_details->status != 4 && $work_orders_details->pass_fail == "pending") //If job_started_at not equal to empty and status not equal to 4 and workorder_journey not equal to job_approval
                {
                    if(strtotime($target_date) >= strtotime($tdate)) //If target_date greater than equal to todays date
                    {
                        $work_orders_details->pass_fail = 'Pass';
                    }
                    else
                    {
                        $work_orders_details->pass_fail = 'Fail';
                    }
                }
                //update work_order_chat with is_read=1
                $updateChatIsRead = WorkOrderChat::updateWorkOrderChatIsRead($this->workOrderId, $user_id);
                //dd($updateChatIsRead);//0:nothing, 1:updated
                $work_orders_details->actual_response_time =  __("work_order.bread_crumbs.in_progress");
                $work_orders_details->actual_execution_time =  __("work_order.bread_crumbs.in_progress");
                $work_orders_details->job_evaluation_time = __("work_order.bread_crumbs.in_progress");
                //dd($work_orders_details);
                return  $work_orders_details;
            }catch (\Throwable $th) {
                Log::error("updateWorkOrdersDetailsPrios Error: ".$th);
            }
        }

        public function updateWorkOrdersExecutionAndResponseTime($work_orders_details, $response_data){
            try{
                $priority_details = Priorities::getPriorityById($work_orders_details->priority_id);
                $response_time = $response_data['response_time'];
                $response_time_type = $response_data['response_time_type'];
                $service_window = $response_data['service_window'];
                $service_window_type = $response_data['service_window_type'];
                if($priority_details){

                    if($work_orders_details->sla_response_time)
                    {
                        $response_time = $work_orders_details->sla_response_time;
                        $response_time_type = $work_orders_details->response_time_type;
                    }
                    $work_orders_details->sla_response_time = $response_time.' '.__('configration_priority.priority_forms.label.'.$response_time_type);
                    if($work_orders_details->sla_service_window)
                    {
                        $service_window = $work_orders_details->sla_service_window;
                        $service_window_type = $work_orders_details->service_window_type;
                    }
                    $work_orders_details->sla_excecution_time = $service_window.' '. __('configration_priority.priority_forms.label.'.$service_window_type);
                }
                else
                {
                    $work_orders_details->sla_response_time = '-';
                    $work_orders_details->sla_excecution_time = '-';
                }

                return  $work_orders_details;
            }catch (\Throwable $th) {
                Log::error("updateWorkOrdersExecutionAndResponseTime Error: ".$th);
            }
        }

        public function getDateInternval($startDate, $endDate){
            try{
                $datetime1 = strtotime($startDate);
                $datetime2 = strtotime($endDate);
                return abs($datetime2 - $datetime1);
            }catch (\Throwable $th) {
                Log::error("getDateInternval Error: ".$th);
            }
        }

        public function getDayHoursMinutesSecondsInterval($work_orders_details){
            try{
                if($work_orders_details->Job_approved_by_sp_at != '' && $work_orders_details->worker_started_at != ''){
                    $interval  = $this->getDateInternval($work_orders_details->worker_started_at, $work_orders_details->Job_approved_by_sp_at);
                    $ret = "";
                    $days = intval(intval($interval) / (3600*24));

                    if($days> 0)
                    {
                        $ret .= "$days ". __("configration_assets.comminucation_table.Days").' ';
                    }
                    /*** get the hours ***/
                    $hours = (intval($interval) / 3600) % 24;
                    if($hours > 0)
                    {
                        $ret .= "$hours ". __("configration_assets.comminucation_table.Hours").' ';
                    }
                    /*** get the minutes ***/
                    $minutes = (intval($interval) / 60) % 60;
                    if($minutes > 0)
                    {
                        $ret .= "$minutes ". __("configration_assets.comminucation_table.Minutes").' ';
                    }
                    /*** get the seconds ***/
                    $seconds = intval($interval) % 60;
                    if ($seconds > 0) {
                        $ret .= "$seconds ".__("configration_assets.comminucation_table.Seconds");
                    }
                    $work_orders_details->job_evaluation_time  = $ret;
                }
                elseif($work_orders_details->status == 4 && $work_orders_details->Job_approved_by_sp_at == '')
                {
                    $work_orders_details->job_evaluation_time  =  __("work_order.forms.label.not_calculated_yet");
                }
                if($work_orders_details->job_started_at != ''){
                    $created_at = $work_orders_details->created_at;
                    //dd($created_at.' -- '.$work_orders_details->job_started_at);
                    $interval  = $this->getDateInternval($created_at, $work_orders_details->job_started_at);
                    $ret = "";
                    /*** get the days ***/
                    $days = intval(intval($interval) / (3600*24));
                    if($days> 0)
                    {
                        $ret .= "$days ". __("configration_assets.comminucation_table.Days").' ';
                    }
                    /*** get the hours ***/
                    $hours = (intval($interval) / 3600) % 24;
                    if($hours > 0)
                    {
                        $ret .= "$hours ". __("configration_assets.comminucation_table.Hours").' ';
                    }
                    /*** get the minutes ***/
                    $minutes = (intval($interval) / 60) % 60;
                    if($minutes > 0)
                    {
                        $ret .= "$minutes ". __("configration_assets.comminucation_table.Minutes").' ';
                    }
                    /*** get the seconds ***/
                    $seconds = intval($interval) % 60;
                    if ($seconds > 0) {
                        $ret .= "$seconds ".__("configration_assets.comminucation_table.Seconds");
                    }
                    if($ret == "")
                    {
                        $ret = __("work_order.bread_crumbs.automatically_assigned");
                    }

                    $work_orders_details->actual_response_time  = $ret;
                }
                if($work_orders_details->job_started_at != '' && $work_orders_details->job_submitted_at != ''){
                    if(isset($work_orders_details->time_spent_by_worker) && trim($work_orders_details->time_spent_by_worker) != "")
                    {
                        // Step 1: Split the input (hours:minutes:seconds)
                        list($hours, $minutes, $seconds) = explode(':', $work_orders_details->time_spent_by_worker);

                        // Convert hours, minutes, and seconds to total seconds
                        $totalSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;

                        // Step 2: Convert total seconds to days, hours, minutes, and seconds
                        $days = floor($totalSeconds / 86400); // 86400 seconds in a day
                        $seconds = $totalSeconds % 86400;

                        $hours = floor($seconds / 3600); // 3600 seconds in an hour
                        $seconds %= 3600;

                        $minutes = floor($seconds / 60); // 60 seconds in a minute
                        $seconds %= 60;

                    }
                    else
                    {
                        $interval  = $this->getDateInternval($work_orders_details?->job_started_at, $work_orders_details?->job_submitted_at);
                        /*** get the days ***/
                        $days = intval(intval($interval) / (3600*24));
                        /*** get the hours ***/
                        $hours = (intval($interval) / 3600) % 24;
                        /*** get the minutes ***/
                        $minutes = (intval($interval) / 60) % 60;
                        /*** get the seconds ***/
                        $seconds = intval($interval) % 60;
                    }

                    $ret = "";

                    if($days> 0)
                    {
                        $ret .= "$days ". __("configration_assets.comminucation_table.Days").' ';
                    }

                    if($hours > 0)
                    {
                        $ret .= "$hours ". __("configration_assets.comminucation_table.Hours").' ';
                    }

                    if($minutes > 0)
                    {
                        $ret .= "$minutes ". __("configration_assets.comminucation_table.Minutes").' ';
                    }

                    if ($seconds > 0) {
                        $ret .= "$seconds ".__("configration_assets.comminucation_table.Seconds");
                    }
                    if($ret == "")
                    {
                        $ret = __("work_order.bread_crumbs.automatically_assigned");
                    }
                    $work_orders_details->actual_execution_time  = $ret;
                }
                return $work_orders_details;
            }catch (\Throwable $th) {
                Log::error("getDayHoursMinutesSecondsInterval Error: ".$th);
            }
        }

        public function setWorkOrdersDetailsCreatedAt2WithStatus($work_orders_details){
            try{

                $work_orders_details->created_at2  = date('d-m-Y H:i', strtotime($work_orders_details?->created_at));

                $work_orders_details->maintenance_request = MaintenanceRequest::getFirstMaintenanceRequestWithUserById($work_orders_details?->maintanance_request_id);

                if($work_orders_details->status == 1)
                {
                    if($work_orders_details->start_date > date('Y-m-d'))
                    {
                        $work_orders_details->status = 7;
                    }
                }
                return $work_orders_details;
            }catch (\Throwable $th) {
                Log::error("setWorkOrdersDetailsCreatedAt2WithStatus Error: ".$th);
            }
        }

        public function getWorkOrderZonesData($work_orders_details){
            try{
                //dd($work_orders_details->building_id);
                return RoomsTypeFloors::getZonesByBuildingId($work_orders_details?->building_id);
           }catch (\Throwable $th) {
               Log::error("getWorkOrderZonesData Error: ".$th);
           }
        }

        public function getWorkOrderFloorData($work_orders_details){
            try{
                 return RoomsTypeFloors::getRoomsTypeFloorsByBuildingIdAndFloor($work_orders_details?->building_id, $work_orders_details?->floor);
           }catch (\Throwable $th) {
               Log::error("getWorkOrderFloorData Error: ".$th);
           }
        }

        public function getRowData($work_orders_details){
            try{
                $row_data = Asset::getAssetByNumberId($work_orders_details?->asset_number_id);
                if($row_data)
                {
                    // Check if $row_data->assetCategories exists and has items
                    if ($row_data->assetCategories && count($row_data->assetCategories) > 0) {
                        $row_data->asset_categories = $row_data->assetCategories->pluck('id')->toArray();
                    } else {
                        $row_data->asset_categories = [$row_data->asset_category_id];
                    }
                }
                return $row_data;
            }catch (\Throwable $th) {
                Log::error("getRowData Error: ".$th);
            }
        }

        public function getSelectableItems($contract){
            try{
                $selectableItems = $contract->usableItems->map(function (ContractUsableItem $item) {
                    return $item->getItem();
                });
                return $selectableItems;
            }catch (\Throwable $th) {
                Log::error("getSelectableItems Error: ".$th);
            }
        }

        public function getDamageImages($data){
            try{
                // Parse damage images
                $damage_images = [];
                if(isset($data) && array_key_exists('row_data', $data)){
                    if (!empty($this->data['row_data']->damage_images)) {
                        $data['row_data']->damage_images = explode(',', $row_data->damage_images);
                        $damage_images = $this->data['row_data']->damage_images;
                    }
                }
                return $damage_images;
            }catch (\Throwable $th) {
                Log::error("getDamageImages Error: ".$th);
            }
        }

        public function getAssetNamesWithAssetCategories($asset_name_id, $logedin_user){
            try{
                // Get asset names with related asset categories
                $data = AssetName::getAssetNamesWithAssetCategoriesById($asset_name_id, $logedin_user);
                return $data;
            }catch (\Throwable $th) {
                Log::error("getAssetNamesWithAssetCategories Error: ".$th);
            }
        }

        public function getAssetNamesWithDirectCategory($logedin_user){
            try{
                // Get asset names with related asset categories
                $data = AssetName::getAssetNamesWithDirectCategoryById($logedin_user);
                return $data;
            }catch (\Throwable $th) {
                Log::error("getAssetNamesWithDirectCategory Error: ".$th);
            }
        }

        public function getAllItems($selectableItems){
            try{
                $allItems = WorkorderHelper::getFilteredCollection($selectableItems);
                return $allItems;
            }catch (\Throwable $th) {
                Log::error("getAllItems Error: ".$th);
            }
        }
        public function getIsItemRequest($id){
            try{
                $isItemRequest = WorkorderHelper::checkItemRequestsByWorker($id);
                return $isItemRequest;
            }catch (\Throwable $th) {
                Log::error("getIsItemRequest Error: ".$th);
            }
        }
        public function getCheckItemRequestsForPo($id){
            try{
                $checkItemRequestsForPo = WorkorderHelper::checkItemRequestsForPo($id);
                return $checkItemRequestsForPo;
            }catch (\Throwable $th) {
                Log::error("getCheckItemRequestsForPo Error: ".$th);
            }
        }
        public function getRequestedItemsBySp($work_orders_details_id){
            try{
                return ServiceProviderMissingItemRequest::getItemsRequestedBySp($work_orders_details_id);
            }catch (\Throwable $th) {
                Log::error("getRequestedItemsBySp Error: ".$th);
            }
        }

        public function workOrderItemRequestAprooved($work_orders_details_id){
            try{
                return WorkOrderItemRequest::workOrderItemRequestAproovedById($work_orders_details_id);
            }catch (\Throwable $th) {
                Log::error("workOrderItemRequestAprooved Error: ".$th);
            }
        }

        public function ServiceProviderMissingItemRequest($work_orders_details_id){
            try{
                return ServiceProviderMissingItemRequest::ServiceProviderMissingItemRequestByWOId($work_orders_details_id);
            }catch (\Throwable $th) {
                Log::error("ServiceProviderMissingItemRequest Error: ".$th);
            }
        }

        public function getRoomsTypeFloorsByUnit($row_data){
            try{
                if ($row_data && $row_data->unit_id != 0) {
                    $row_data->floor = RoomsTypeFloors::getRoomsTypeFloorsByUnitId($row_data->unit_id);
                    $row_data->room_id = $row_data->unit_id;
                }
            }catch (\Throwable $th) {
                Log::error("ServiceProviderMissingItemRequest Error: ".$th);
            }
        }

        public function getAssetsFiles($work_orders_details_id){
            try{
                $data = AssetFile::getAssetsFilesById($work_orders_details_id);
                return $data;
            }catch (\Throwable $th) {
                Log::error("getAssetsFilesById Error: ".$th);
            }
        }

        public function getPropertyBuildings($row_data){
            try{
                $data = isset($row_data['building_id'])?PropertyBuildings::getPropertyBuildingsById($row_data['building_id']):0;
                return $data;
            }catch (\Throwable $th) {
                Log::error("getPropertyBuildings Error: ".$th);
            }
        }

        public function getBuildingById($row_data){
            try{
                $data = isset($row_data['building_id'])?$row_data['building_id']:0;
                return $data;
            }catch (\Throwable $th) {
                Log::error("getBuildingById Error: ".$th);
            }
        }
        public function getFirstMaintenanceRequestWithUser($work_orders_details){
            try{
                $work_orders_details->maintenance_request = MaintenanceRequest::getFirstMaintenanceRequestWithUserById($work_orders_details->maintanance_request_id);
                return $work_orders_details;
            }catch (\Throwable $th) {

                Log::error("getFirstMaintenanceRequestWithUser Error: ".$th);
            }
        }

        public function getWorkOrdersRelatedWos($work_orders_details){
            try{
                //dd($work_orders_details->related_wos);
                $work_orders_details->related_wos = WorkOrders::getWorkOrdersRelatedWosByUniqueId($work_orders_details->unique_id);
                return $work_orders_details;
            }catch (\Throwable $th) {
                Log::error("getWorkOrdersRelatedWos Error: ".$th);
            }
        }

        public function getWorkOrdersShowCounter($work_orders_details){
            try{
                $show_counter = "";
                $project_user_id = $work_orders_details?->project_user_id;

                if(isset($project_user_id) && $project_user_id != 0) //If has project_user_id and project_user_id not equal to 0
                {
                    $userDetails = User::getFirstUserByWOProjectUserId($project_user_id);
                    $project_id = $userDetails?->project_id;
                }
                else
                {
                    $userDetails = User::getFirstUserByWOCreatedById($work_orders_details?->created_by);
                    $project_id = $userDetails->project_id;
                }
                $projectSetting = ProjectSettings::getProjectSettingByProjectId($project_id);
                if(isset($projectSetting) && !empty($projectSetting))
                {
                    $workorder_reminder_periods = trim($projectSetting?->workorder_reminder_periods) != "" ? trim($projectSetting?->workorder_reminder_periods) : '7';
                }
                else
                {
                    $workorder_reminder_periods = '7';
                }
                /*---- All the code is regarding asset popup opening-----*/
                if((trim($work_orders_details?->wo_reminder_sent_on) != null) && (trim($work_orders_details->wo_reminder_sent_on) != ""))
                {
                    $wo_reminder_sent_on_timestamp = strtotime($work_orders_details->wo_reminder_sent_on);
                    $workorder_reminder_periods_in_sec = 86400*$workorder_reminder_periods;

                    if($wo_reminder_sent_on_timestamp > time() + $workorder_reminder_periods_in_sec) {
                        $show_counter = 'no';
                    } else {
                        $show_counter = 'yes';
                    }
                }
                else
                {
                    $show_counter = 'no';
                }
                return ['show_counter' => $show_counter, 'workorder_reminder_periods' =>$workorder_reminder_periods] ;
            }catch (\Throwable $th) {
                Log::error("getWorkOrdersShowCounter Error: ".$th);
            }
        }


        public function getWODReminderSentOnFormatted($work_orders_details){
            try{
                if(trim($work_orders_details->wo_reminder_sent_on) != "")
                {
                    $wo_reminder_sent_on_formatted = date_create(trim($work_orders_details->wo_reminder_sent_on));
                    // date_add($wo_reminder_sent_on_formatted,date_interval_create_from_date_string("$workorder_reminder_periods days"));
                    $work_orders_details->wo_reminder_sent_on_formatted = date_format($wo_reminder_sent_on_formatted,"Y-m-d H:i:s");
                }
                else
                {
                    $work_orders_details->wo_reminder_sent_on_formatted = "";
                }
                return $work_orders_details;
            }catch (\Throwable $th) {
                Log::error("getWODReminderSentOnFormatted Error: ".$th);
            }
        }

        public function getAllSupervisors($work_orders_details){
            try{
                $data = [];
                $auth_user = Auth::user();
                if($auth_user->user_type == "sp_admin")
                {
                    $data = User::getAllSupervisorsByWO($auth_user->service_provider, $work_orders_details->building_id, $work_orders_details->contract_id, $work_orders_details->asset_category_id);
                }
                return $data;
            }catch (\Throwable $th) {
                Log::error("getAllSupervisors Error: ".$th);
            }
        }

        public function getChecklists($work_orders_details){
            try{
                //For showing the chcklist popup
                $checklists = Checklists::getChecklistsById($work_orders_details->checklist_id);
                if(isset($checklists))
                {
                    $checklists['tasks'] = ChecklistTasks::getChecklistTasksByChecklistId($checklists->list_id);
                }
                return $checklists;
            }catch (\Throwable $th) {
                Log::error("getChecklists Error: ".$th);
            }

        }

        public function getReopenWorkOrderDetails($work_orders_details){
            try{
                return ReopenWorkOrderDetail::getReopenWorkOrderDetailsByWOId($work_orders_details->id);
            }catch (\Throwable $th) {
                Log::error("getReopenWoCount Error: ".$th);
            }

        }

        public function getWorkOrderItems($id){
            try {
                return WorkOrderItem::getWorkOrderItemById($id);
            }
            catch (\Throwable $th) {
                Log::error("getWorkOrderItems Error: ".$th);
            }
        }

        public function getDataProvider($id, $selectableItems){
            try{
                $items = $this->getWorkOrderItems($id);
                $dataProvider = new FormDataProvider([
                    'items' => $items,
                    'selectableItems' => $selectableItems,
                ]);
                return $dataProvider;
            }catch (\Throwable $th) {
                Log::error("getDataProvider Error: ".$th);
            }
        }

        public function getProjectDetails($maintanance_request){
            try{
                $loggedInUser = Auth::user();
                $data['projectDetails'] = $projectDetails = $loggedInUser->projectDetails;
                if ($projectDetails && isset($projectDetails->projectSettings)) {
                    $data['projectSettings'] = $projectDetails->projectSettings;
                    $data['warrantyStatus'] = Helper::getWarrantyStatus($data['projectSettings'], $maintanance_request);
                } else {
                    $data['projectSettings'] = null; // handle the absence of projectSettings
                    $data['warrantyStatus'] = null;
                }
                return $data;
            }catch (\Throwable $th) {
                Log::error("getDataProvider Error: ".$th);
            }
        }

        public function getAllWarehousesList($contract){
            try {
                $warehouse_owner = $contract->warehouse_owner ?? 'admin';
                if ($warehouse_owner == 'admin') {
                    $company_id = $contract->projectsDetails()->first()->projectCompany->company_id ?? null;
                } else {
                    $company_id = $contract->serviceProvider->serviceProviderAdmin->userCompany->company_id ?? null;
                }
                $allWarehousesList = WorkorderHelper::getAllWarehousesByCompanyId($company_id);
                $allWarehousesList = WarehouseData::collection($allWarehousesList);
                $allWarehousesList = $allWarehousesList->filter(function (WarehouseData $warehouse) use ($contract) {
                    if ($warehouse->name === 'Main Warehouse'){
                        return true;
                    }
                    if ($warehouse->name === '[Warehouse] '.$contract->contract_number){
                        return true;
                    }
                    return false;
                })->all();
                return $allWarehousesList;
            } catch (\Throwable $th) {
                Log::error("getDataProvider Error: ".$th);
            }
        }

        public function getWorkersDetails($work_orders_details_id){
            try {
                $data= WorkorderHelper::getWorkersDetailsForWorkOrder($work_orders_details_id);
                return $data;
            } catch (\Throwable $th) {
                Log::error("getWorkersDetails Error: ".$th);
            }

        }

        public function getUsedItems($work_orders_details_id){
            try {
                //dd($work_orders_details_id);
                $usedItems = [];
                $workOrderRequestedItems = $this->getWorkOrderRequestedItemsById($work_orders_details_id);
                $requestedItemsBySp = ServiceProviderMissingItemRequest::getItemsRequestedBySp($work_orders_details_id);
                $statusCheck = [StatusCheck::PartiallyGiven->value, StatusCheck::FullyGiven->value, StatusCheck::Accepted->value];

                if ((isset($workOrderRequestedItems) || count($workOrderRequestedItems) > 0 ) && in_array($workOrderRequestedItems?->status, $statusCheck)) {
                    $usedItems = $workOrderRequestedItems?->requestedItems;
                } elseif (isset($requestedItemsBySp?->status) && in_array($requestedItemsBySp?->status, $statusCheck)) {
                    $usedItems =  $requestedItemsBySp->requestedItems;
                }

                return $usedItems;
            } catch (\Throwable $th) {
                Log::error("getUsedItems Error: ".$th);
            }
        }

        public function getTitle(){
            return __('Work Order Details');
        }

        public function getDatas($work_orders_details){
            try{
                $logedin_user = Auth::user();
                $asset_floor = $this->getWorkOrderZonesData($work_orders_details);
                $asset_room = $this->getWorkOrderFloorData($work_orders_details);
                $data_row = $this->getRowData($work_orders_details);
                $damage_images = $this->getDamageImages($data_row);
                $asset_category  = $this->getAssetNamesWithAssetCategories($work_orders_details?->asset_name_id, $logedin_user);
                $asset_name  = $this->getAssetNamesWithDirectCategory($logedin_user);
                $data_row = $this->getRoomsTypeFloorsByUnit($data_row);
                $asset_files = $this->getAssetsFiles($work_orders_details?->id);
                $builiding_name = $this->getPropertyBuildings($data_row);
                $building_id = $this->getBuildingById($data_row);
                $maintanance_request = $this->getFirstMaintenanceRequestWithUser($work_orders_details);
                $all_supervisors = $this->getAllSupervisors($work_orders_details);
                $checklists = $this->getChecklists($work_orders_details);
                $reopen_wo_countget = $this->getReopenWorkOrderDetails($work_orders_details);
                $projectDetails = $this->getProjectDetails($maintanance_request);
                $workersDetails = $this->getWorkersDetails($work_orders_details?->id);
                $row_data = $this->getRowData($work_orders_details);
                $pageTitle = $this->getTitle();

                return [
                    'pageTitle' => $pageTitle,
                    'row_data' => $row_data,
                    'damage_images' => $damage_images,
                    'asset_category' => $asset_category,
                    'asset_name' => $asset_name,
                    'assets_files' => $asset_files,
                    'asset_floor' => $asset_floor,
                    'asset_room' => $asset_room,
                    'id' => $work_orders_details->id,
                    'property_id' => $work_orders_details->property_id,
                    'builiding_name' => $builiding_name,
                    'building_id' => $building_id,
                    'maintanance_request' => $maintanance_request->maintenance_request,
                    'all_supervisors' => $all_supervisors,
                    'checklists' => $checklists,
                    'user_type' => Auth::user()->user_type,
                    'reopen_wo_count' => $reopen_wo_countget,
                    'projectDetails' => $projectDetails['projectDetails'],
                    'projectSettings' => $projectDetails['projectSettings'],
                    'warrantyStatus' => $projectDetails['warrantyStatus'],
                    'workersDetails' => $workersDetails
                ];
            }catch (\Throwable $th) {
                Log::error("getDatas Error: ".$th);
            }
        }




        public function getClosedCountAll($userId, $userType, $projectUserId, $userAssetCategories, $url = null) {
            try {
                if(trim(array_reverse(explode('/',$url))[0]) == "" || array_reverse(explode('/',$url))[0] == "closed"){
                    $serviceProviderId = 'no';
                }

                else{
                    try {
                        $serviceProviderId = $this->decryptCryptedString(trim(array_reverse(explode('/',$url))[0]));

                        if(trim(array_reverse(explode('/',$url))[1]) != 'work-order' && trim(array_reverse(explode('/',$url))[2]) != 'work-order'){
                            $serviceProviderId = 'no';
                        }
                    }

                    catch(DecryptException $e){
                        $serviceProviderId = 'no';
                    }

                    if(trim(array_reverse(explode('/',$url))[1]) == 'work-order' && in_array($userType, array('osool_admin', 'admin_employee', 'admin'))){
                        $serviceProviderId = 'no';
                    }

                    $currentDate = $this->getCurrentDate();
                    $date = $this->changeDateFormat('Y-m-d', $currentDate);
                    $workOrders = WorkOrders::select('work_orders.*', 'contracts.contract_number')
                        ->join('contracts', 'contracts.id', 'work_orders.contract_id')
                        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                        ->join('users', 'users.id', 'work_orders.created_by')
                        ->where('work_orders.status', 4)
                        ->where('work_orders.is_deleted', "no")
                        ->where('work_orders.start_date', '<=', $date)
                        ->orderByRaw('work_orders.created_at asc');

                    if($userType != 'sp_admin' && $userType != 'supervisor'){
                        $workOrders = $workOrders->where('work_orders.project_user_id', $projectUserId);
                    }

                    if(in_array($userType, array('building_manager_employee', 'building_manager'))){
                        $bmEmployees = $this->getUserInformationsByUserId($userId);
                        $buildingIds = $this->explodeDataFromField($bmEmployees->building_ids);
                        $workOrders = $workOrders->whereIn('work_orders.property_id', $buildingIds);
                        $assetCategories = $this->explodeDataFromField($userAssetCategories);
                        $workOrders = $workOrders->whereIn('work_orders.asset_category_id', $assetCategories);
                    }

                    elseif($userType == 'sp_admin'){
                        $contractId = $this->getContractInformationByServiceProvider($serviceProviderId);
                        $workOrders = $workOrders->whereIn('work_orders.contract_id', $contractId)
                        ->where('work_orders.contract_type', 'regular')
                        ->where('work_orders.status', 4);
                    }

                    elseif($userType == 'supervisor'){
                        $workOrders = $workOrders->whereHas('supervisor', function ($query) use ($userId) {
                            $query->whereIn('id', explode(',', $userId));
                        })
                        ->where('work_orders.contract_type', 'regular');
                    }

                    elseif($serviceProviderId != 'no'){
                        $workOrders = $workOrders->where('contracts.service_provider_id', '=', $serviceProviderId);
                    }

                    if(in_array($userType, array('super_admin', 'admin_employee', 'admin'))){
                        if($serviceProviderId != 'no'){
                            $workOrders = $workOrders->where('contracts.service_provider_id', '=', $serviceProviderId);
                        }
                    }
                }

                return $workOrders->count();
            }

            catch (DecryptException $th) {
                Log::error("getClosedCountAll error: ".$th);
            }
        }

        public function getAllCompletedWorkOrderByValues($key, $value) {
            try {
                return WorkOrders::where($key, $value)
                ->select('work_orders.worker_id', 'contracts.service_provider_id', 'contract_id')
                ->leftjoin('contracts', 'contracts.id', 'work_orders.contract_id')
                ->get();
            }

            catch (DecryptException $th) {
                Log::error("getAllCompletedWorkOrderByValues error: ".$th);
            }
        }

        public function getWorkerAvgFromWorkOrder($status, $workerId) {
            try {
                return WorkOrders::where('status', $status)
                ->where('worker_id', $workerId)
                ->where('rating', '>', 0)
                ->selectRaw('AVG(uniform_specified_by_authority + extent_of_cleanliness + safety_procedure) / 3 as average_rating')
                ->value('average_rating');
            } 
            
            catch (DecryptException $th) {
                Log::error("getWorkerAvgFromWorkOrder error: ".$th);
            }
        }

        public function getWorkOrderCountForWorkOrder($status, $workerId) {
            try {
                return WorkOrders::where('status', $status)
                ->where('worker_id', $workerId)
                ->count();
            }

            catch (DecryptException $th) {
                Log::error("getWorkOrderCountForWorkOrder error: ".$th);
            }
        }

        public function manageClosedWorkOrders($contractsList, $status, $serviceProviderId) {
            try {
                $reopened = $this->getReopenedClosedWorkOrdersCount($contractsList, $status) ?? 0;
                $closed = $this->getClosedWorkOrdersCount($contractsList, $status) ?? 0;
                $execusionRows = $this->getClosedOnTimeExecusionWorkOrdersCount($contractsList, $status) ?? 0;
                $execusion = $closed > 0 ? $this->calculPourcentage(($execusionRows / $closed) * 100, 2) : 0;
                $responseRows = $this->getClosedOnTimeResponseWorkOrdersCount($contractsList, $status) ?? 0;
                $response = $closed > 0 ? $this->calculPourcentage(($responseRows / $closed) * 100, 2) : 0;
                $jobRatings = $this->getClosedJobRatingsWorkOrdersAvg($contractsList, $status);
                $jobRatingsAvg = $this->calculPourcentage($jobRatings, 1);
                $workerRatings = $this->getAvgForRatingWorkerByValues('service_provider_id', $serviceProviderId);
                $workerRatingsAvg = $this->calculPourcentage($workerRatings, 1);
                $execusionRate = $this->calculPourcentage(($execusion / 100) * 5, 2);
                $responseRate = $this->calculPourcentage(($response / 100) * 5, 2);
                $overAllRatingAvg = $this->calculPourcentage(($execusionRate + $responseRate + $jobRatingsAvg + $workerRatingsAvg) / 4, 2);

                return [
                    'reopenedWO' => $reopened,
                    'closedWO' => $closed,
                    'onTimeExecusionWO' => $execusion === 0.00 ? 0 : ($execusion === 100.00 ? 100 : $execusion),
                    'onTimeResponseWO' => $response === 0.00 ? 0 : ($response === 100.00 ? 100 : $response),
                    'jobRatingsWO' => $jobRatingsAvg,
                    'workerRatingsWO' => $workerRatingsAvg,
                    'overWallRatingWO' => $overAllRatingAvg
                ];
            }

            catch (DecryptException $th) {
                Log::error("manageClosedWorkOrders error: ".$th);
            }
        }

        public function getReopenedClosedWorkOrdersCount($contractsList, $status) {
            try {
                return WorkOrders::where('status', $status)
                ->whereIn('contract_id', $contractsList)
                ->where('reopen', 'yes')
                ->count();
            }

            catch (DecryptException $th) {
                Log::error("getReopenedClosedWorkOrdersCount error: ".$th);
            }
        }

        public function getClosedWorkOrdersCount($contractsList, $status) {
            try {
                return WorkOrders::where('status', $status)
                ->whereIn('contract_id', $contractsList)
                ->count();
            }

            catch (DecryptException $th) {
                Log::error("getClosedWorkOrdersCount error: ".$th);
            }
        }

        public function getClosedOnTimeExecusionWorkOrdersCount($contractsList, $status) {
            try {
                return WorkOrders::where('status', $status)
                ->whereIn('contract_id', $contractsList)
                ->where('pass_fail', 'pass')
                ->count();
            }

            catch (DecryptException $th) {
                Log::error("getOnTimeExecusionCount error: ".$th);
            }
        }

        public function getClosedOnTimeResponseWorkOrdersCount($contractsList, $status) {
            try {
                return WorkOrders::where('status', $status)
                ->whereIn('contract_id', $contractsList)
                ->whereRaw("(response_time = 'On time' OR work_orders.sp_approove = 0)")
                ->where('work_orders.contract_type', '<>', 'warranty')
                ->count();
            }

            catch (DecryptException $th) {
                Log::error("getClosedOnTimeResponseWorkOrdersCount error: ".$th);
            }
        }

        public function getClosedJobRatingsWorkOrdersAvg($contractsList, $status) {
            try {
                return WorkOrders::where('status', $status)
                ->where('work_orders.rating', '>', 0)
                ->whereIn('contract_id', $contractsList)
                ->avg('rating');
            }

            catch (DecryptException $th) {
                Log::error("getClosedJobRatingsWorkOrdersAvg error: ".$th);
            }
        }

        public function getPaginatedClosedWorkOrderList($contractsList, $status, $perPage, $worker, $responseTime, $execusionTime, $property, $supervisor, $contract, $rating) {
            try {
                return WorkOrders::leftjoin('users AS US', 'US.id', 'work_orders.worker_id')
                ->leftjoin('users AS USSUP', 'USSUP.id', 'work_orders.supervisor_id')
                ->where('work_orders.status', $status)
                //->where('work_orders.rating', '>', 0)
                ->whereIn('work_orders.contract_id', $contractsList)
                ->select('work_orders.id', 'work_orders.work_order_id', 'work_orders.worker_id', 'US.name', 'work_orders.response_time', 'work_orders.reopen', 'work_orders.rating', 'work_orders.pass_fail', 'USSUP.name AS sup_name')
                ->where('work_orders.is_deleted','no')
                ->orderBy('start_date', 'DESC')
                ->when(!empty($worker), function ($query) use($worker) {
                    $query->whereIn('work_orders.worker_id', $worker);
                })
                ->when(!empty($responseTime), function ($query) use($responseTime) {
                    $query->whereIn('work_orders.response_time', $responseTime);
                })
                ->when(!empty($execusionTime), function ($query) use($execusionTime) {
                    $query->whereIn('work_orders.pass_fail', $execusionTime);
                })
                ->when(!empty($property), function ($query) use($property) {
                    $query->whereIn('work_orders.property_id', $property);
                })
                ->when(!empty($supervisor), function ($query) use($supervisor) {
                    $supp = $this->implodeDataFromField($supervisor);

                    $query->where(function($innerQuery) use($supp) {
                        $innerQuery->whereRaw("(((work_orders.supervisor_id IS NOT NULL and assigned_to = 'supervisor') || (assigned_to = 'sp_worker' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) = 1)) AND work_orders.supervisor_id IN ($supp)AND (assigned_to != 'sp_worker' || worker_id != 0)) OR (service_provider_id IN ($supp) AND (assigned_to != 'sp_worker' || worker_id != 0) AND assigned_to != 'supervisor' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) > 1)");
                    });
                })
                ->when(!empty($contract), function ($query) use($contract) {
                    $query->whereIn('work_orders.contract_id', $contract)
                    ->where('work_orders.contract_type', 'regular');
                })
                ->when(!empty($rating), function ($query) use($rating) {
                    $query->where('work_orders.rating', $rating);
                })
                ->paginate($perPage, ['*'], 'page');

            }

            catch (DecryptException $th) {
                Log::error("getPaginatedClosedWorkOrderList error: ".$th);
            }
        }

        public function getSearchedWorkersListByValues($key, $value, $search, $status) {
            try {
                return WorkOrders::where('work_orders.status', $status)
                ->where('work_orders.rating', '>', 0)
                ->whereIn($key, $value)
                ->where('work_orders.worker_id', '>', 0)
                ->select('work_orders.worker_id', 'users.name')
                ->leftjoin('users', 'users.id', 'work_orders.worker_id')
                ->when(!empty($search) , function ($query) use($search) {
                    $query->where('users.name', 'LIKE', '%' . $search . '%');
                })
                ->groupBy('work_orders.worker_id')
                ->get();
            }

            catch (DecryptException $th) {
                Log::error("getSearchedWorkersListByValues error: ".$th);
            }
        }

        public function getWorkersFromWorkOrdersListByValues($contractId, $status, $serviceId) {
            try {
                return WorkOrders::join('user_assets_mapping', 'user_assets_mapping.user_id', '=', 'work_orders.worker_id')
                ->select('worker_id')
                ->whereIn('user_assets_mapping.contract_id', explode(',',$contractId))
                ->whereIn('user_assets_mapping.asset_id', explode(',',$serviceId))
                ->where('user_assets_mapping.user_type', 'sp_worker')
                ->where('work_orders.rating', '>', 0)
                ->where('work_orders.worker_id', '>', 0)
                ->where('work_orders.status', $status)
                ->pluck('worker_id')
                ->toArray();
            }

            catch (DecryptException $th) {
                Log::error("getWorkersFromWorkOrdersListByValues error: ".$th);
            }
        }

        public function checkWorkersFromWorkOrdersListStatusByValues($contractId, $status, $serviceId, $workerId) {
            try {
                return WorkOrders::join('user_assets_mapping', 'user_assets_mapping.user_id', '=', 'work_orders.worker_id')
                ->whereIn('user_assets_mapping.contract_id', explode(',',$contractId))
                ->whereIn('user_assets_mapping.asset_id', explode(',',$serviceId))
                ->where('user_assets_mapping.user_type', 'sp_worker')
                ->where('work_orders.worker_id', '>', 0)
                ->where('work_orders.status', '=', $status)
                ->whereIn('worker_id', $workerId)
                ->exists();
            }

            catch (DecryptException $th) {
                Log::error("checkWorkersFromWorkOrdersListNotStatusByValues error: ".$th);
            }
        }

        public function getMaxWorkersWorkOrderCount($workers) {
            try {
                return WorkOrders::select('work_orders.worker_id', DB::raw('COUNT(work_orders.id) as work_orders_count'))
                ->groupBy('work_orders.worker_id')
                ->whereIn('work_orders.worker_id', $workers)
                ->where('work_orders.is_deleted', 'no')
                ->orderBy('work_orders_count', 'DESC')
                ->get();
            }

            catch (DecryptException $th) {
                Log::error("getMaxWorkersWorkOrderCount error: ".$th);
            }
        }

        public function manageWorkersSelection($contractId, $propertyId, $serviceId, $newDate, $user, $workOrderId) {
            try {
                $workerId = 0;
                $criteriaArray = [];
                $projectSettings = $this->grtProjectSettingsInformationByValues('project_id', $user->project_id);

                if(is_null($projectSettings)){
                    $workerId = 0;
                }

                else{
                    if($projectSettings->use_smart_assigning){
                        $contractInformation = $this->getContractDetailsByValues('id', $contractId);

                        if(is_null($contractInformation)){
                            $workerId = 0;
                        }

                        else{
                            if($contractInformation->use_smart_assigning){
                                $smartAssignServices = $this->getSmartAssigningContractInformationByValues('contract_id', $contractId);

                                if(is_null($smartAssignServices)){
                                    $workerId = 0;
                                }

                                else{
                                    $explodeSmartAssignServices = $this->explodeDataFromField($smartAssignServices->service_id);
                                    $checkServices = isset($explodeSmartAssignServices) && count($explodeSmartAssignServices) > 0 ? in_array($serviceId, $explodeSmartAssignServices): false;
                                    
                                    if($checkServices){
                                        $contractWorkers = $this->getWorkersListArrayByValues($contractId, $user->id, $serviceId);

                                        if(isset($contractWorkers) && count($contractWorkers) > 0){
                                            $workersInLeave = $this->checkWorkerIdAvailability($contractWorkers, $newDate, 'approved');
                                            $workersNotInLeave = isset($workersInLeave) && count($workersInLeave) > 0 ? array_values(array_diff($contractWorkers, $workersInLeave)) : $contractWorkers;
                                            
                                            if(isset($workersNotInLeave) && count($workersNotInLeave) == 1){
                                                $workerId = $workersNotInLeave[0];
                                                $criteriaArray[] = ['availability' => true, 'wo_date' => $newDate];
                                            }

                                            elseif(isset($workersNotInLeave) && count($workersNotInLeave) > 1){
                                                $workerInProgressWo = $this->getNotAvailableWorkersFromWorkOrders(WorkOrderStatus::InProgress->value, $contractId);
                                                $availableWorkers = isset($workerInProgressWo) && count($workerInProgressWo) > 0 ? array_values(array_diff($workersNotInLeave, $workerInProgressWo)) : $workersNotInLeave;
                                                
                                                if(isset($availableWorkers) && count($availableWorkers) == 1){
                                                    $workerId = $availableWorkers[0];
                                                    $criteriaArray[] = ['availability' => true, 'wo_date' => $newDate];
                                                }

                                                elseif(isset($availableWorkers) && count($availableWorkers) > 1){
                                                    $configNumber = $this->getConfigurationByValue('code', ConfigurationCode::BestWorkersListValue->value);
                                                    $workerNumberToTake = isset($configNumber) ? $configNumber->value : 5;
                                                    $bestRatedWorkersList = $this->getBestValueWorkerRatingByValues('worker_id', $availableWorkers, $workerNumberToTake);
                                                    
                                                    if(isset($bestRatedWorkersList) && count($bestRatedWorkersList) == 1){
                                                        $ratingWorkerInformation = $this->getRatingWorkerInformationByValues('worker_id', $availableWorkers[0]);
                                                        $ratingValue = isset($ratingWorkerInformation) ? $ratingWorkerInformation->rating_avg : 5.0;
                                                        $workerId = isset($ratingWorkerInformation) ? $ratingWorkerInformation->worker_id : 0;
                                                        $criteriaArray[] = ['availability' => true, 'wo_date' => $newDate, 'rating' => true, 'rating_value' => $ratingValue];
                                                    }

                                                    else{
                                                        if(isset($bestRatedWorkersList) && count($bestRatedWorkersList) == 0){
                                                            foreach($availableWorkers as $worker){
                                                                $bestRatedWorkersList->push(['worker_id'=> $worker, 'rating_avg'=> '5.0']);
                                                            }
                                                        }

                                                        if(isset($bestRatedWorkersList) && count($bestRatedWorkersList) > 1){
                                                            $firstWorkerData = $bestRatedWorkersList[0];
                                                            $secondWorkerData = $bestRatedWorkersList[1];
                                                            
                                                            if($firstWorkerData['rating_avg'] == $secondWorkerData['rating_avg']){
                                                                $propertyBuildingLocation = $this->getPropertyBuildingInformationsById($propertyId);
                                                                $longitude = isset($propertyBuildingLocation) ? $propertyBuildingLocation->property->longitude : null;
                                                                $latitude = isset($propertyBuildingLocation) ? $propertyBuildingLocation->property->latitude : null;
                                                                $result = $this->calculUsersLocation($bestRatedWorkersList, $longitude, $latitude);
                                                                
                                                                if(isset($result) && count($result) > 0){
                                                                    $workerId = $result['workerId'];
                                                                    $ratingWorkerInformation = $this->getRatingWorkerInformationByValues('worker_id', $workerId);
                                                                    $ratingValue = isset($ratingWorkerInformation) ? $ratingWorkerInformation->rating_avg : 5.0;
                                                                    $criteriaArray[] = ['availability' => true, 'wo_date' => $newDate, 'location' => true, 'distance' => $result['distance'], 'rating' => true, 'rating_value' => $ratingValue];
                                                                }

                                                                else{
                                                                    $jsonArray = json_decode($bestRatedWorkersList, true);
                                                                    $workerIds = array_column($jsonArray, 'worker_id');
                                                                    $workOrdersCount = $this->getMaxWorkersWorkOrderCount($workerIds);

                                                                    if(isset($workOrdersCount) && count($workOrdersCount) == 0){
                                                                        $oldUser = $this->getUsersListData($workerIds);
                                                                        $workerId = isset($oldUser) ? $oldUser->id : 0;
                                                                        $createdUserDateTime = isset($oldUser) ? $oldUser->created_at : null;
                                                                        $criteriaArray[] = $workerId > 0 ? ['availability' => true, 'wo_date' => $newDate, 'old_user' => true, 'created_user_date_time' => $createdUserDateTime] : ['availability' => false, 'old_user' => false];
                                                                    }

                                                                    elseif(isset($workOrdersCount) && count($workOrdersCount) == 1){
                                                                        $workerId = $workOrdersCount[0]['worker_id']; 
                                                                        $ratingWorkerInformation = $this->getRatingWorkerInformationByValues('worker_id', $workerId);
                                                                        $ratingValue = isset($ratingWorkerInformation) ? $ratingWorkerInformation->rating_avg : 5.0;
                                                                        $criteriaArray[] = ['availability' => true, 'wo_date' => $newDate, 'experience' => true, 'wo_count' => $workOrdersCount[0]['work_orders_count'], 'rating' => true, 'rating_value' => $ratingValue];
                                                                    }

                                                                    else{
                                                                        $firstWorkOrderData = $workOrdersCount[0];
                                                                        $secondWorkOrderData = $workOrdersCount[1];

                                                                        if($firstWorkOrderData['work_orders_count'] == $secondWorkOrderData['work_orders_count']){
                                                                            $usersArray = [$firstWorkOrderData['worker_id'], $secondWorkOrderData['worker_id']];
                                                                            $oldUser = $this->getUsersListData($usersArray);
                                                                            $workerId = isset($oldUser) ? $oldUser->id : 0;
                                                                            $createdUserDateTime = isset($oldUser) ? $oldUser->created_at : null;
                                                                            $woCount = $workerId == $firstWorkOrderData['worker_id'] ? $firstWorkOrderData['work_orders_count'] : $secondWorkOrderData['work_orders_count'];
                                                                            $criteriaArray[] = ['availability' => true, 'wo_date' => $newDate, 'experience' => true, 'wo_count' => $woCount, 'old_user' => true, 'created_user_date_time' => $createdUserDateTime];
                                                                        }

                                                                        elseif($firstWorkOrderData['work_orders_count']  > $secondWorkOrderData['work_orders_count']){
                                                                            $workerId = $firstWorkOrderData['worker_id']; 
                                                                            $ratingWorkerInformation = $this->getRatingWorkerInformationByValues('worker_id', $workerId);
                                                                            $ratingValue = isset($ratingWorkerInformation) ? $ratingWorkerInformation->rating_avg : 5.0;
                                                                            $criteriaArray[] = ['availability' => true, 'wo_date' => $newDate, 'rating' => true, 'rating_value' => $ratingValue, 'experience' => true, 'wo_count' => $firstWorkOrderData['work_orders_count']];
                                                                        }

                                                                        else{
                                                                            $workerId = $secondWorkOrderData['worker_id']; 
                                                                            $ratingWorkerInformation = $this->getRatingWorkerInformationByValues('worker_id', $workerId);
                                                                            $ratingValue = isset($ratingWorkerInformation) ? $ratingWorkerInformation->rating_avg : 5.0;
                                                                            $criteriaArray[] = ['availability' => true, 'wo_date' => $newDate, 'rating' => true, 'rating_value' => $ratingValue, 'experience' => true, 'wo_count' => $secondWorkOrderData['work_orders_count']];
                                                                        }
                                                                    }
                                                                }
                                                            }

                                                            else{
                                                                $workerId = $firstWorkerData['rating_avg'] > $secondWorkerData['rating_avg'] ? $firstWorkerData['worker_id'] : $secondWorkerData['worker_id'];
                                                                
                                                                if($firstWorkerData['rating_avg'] > $secondWorkerData['rating_avg']){
                                                                    $criteriaArray[] = ['availability' => true, 'wo_date' => $newDate, 'rating' => true, 'rating_value' => $firstWorkerData['rating_avg']];
                                                                }

                                                                else{
                                                                    $criteriaArray[] = ['availability' => true, 'wo_date' => $newDate, 'rating' => true, 'rating_value' => $secondWorkerData['rating_avg']];
                                                                }
                                                            }
                                                        }
                                                    }
                                                }

                                                else{
                                                    $workersInProgress = $this->getNotAvailableWorkersList(WorkOrderStatus::InProgress->value, $contractId);

                                                    if(isset($workersInProgress)){
                                                        if($workersInProgress->count() == 1){
                                                            $workerId = $workersInProgress[0]['worker_id'];
                                                            $criteriaArray[] = ['availability' => true, 'wo_date' => $newDate];
                                                        }

                                                        else{
                                                            $firstWorkerData = $workersInProgress[0];
                                                            $secondWorkerData = $workersInProgress[1];

                                                            if($firstWorkerData['work_orders_count'] == $secondWorkerData['work_orders_count']){
                                                                $usersArray = [$firstWorkerData['worker_id'], $secondWorkerData['worker_id']];
                                                                $oldUser = $this->getUsersListData($usersArray);
                                                                $workerId = isset($oldUser) ? $oldUser->id : 0;
                                                                $createdUserDateTime = isset($oldUser) ? $oldUser->created_at : null;
                                                                $criteriaArray[] = $workerId > 0 ? ['availability' => true, 'wo_date' => $newDate, 'old_user' => true, 'created_user_date_time' => $createdUserDateTime] : ['availability' => false, 'old_user' => false];
                                                            }

                                                            elseif($firstWorkerData['work_orders_count'] > $secondWorkerData['work_orders_count']){
                                                                $workerId = $secondWorkerData['worker_id'];
                                                                $criteriaArray[] = ['availability' => true, 'wo_date' => $newDate];
                                                            }

                                                            else{
                                                                $workerId = $firstWorkerData['worker_id'];
                                                                $criteriaArray[] = ['availability' => true, 'wo_date' => $newDate];
                                                            }
                                                        }
                                                    }

                                                    else{
                                                        $workerId = 0;
                                                    }
                                                }
                                            }

                                            else{
                                                $workerId = 0;
                                            }
                                        }

                                        else{
                                            $workerId = 0;
                                        }
                                    }

                                    else{
                                        $workerId = 0;
                                    }
                                }
                            }

                            else{
                                $workerId = 0;
                            }
                        }
                    }

                    else{
                        $workerId = 0;
                    }
                }

                if(isset($workerId) && $workerId > 0){
                    $logId = isset($user) ? $this->saveSmartAssigningLog($workerId, $contractId, $serviceId, $propertyId, $user->id) : null;

                    if($logId){
                        $criteriaArray[] = ['worker_id' => $workerId, 'service_id' => $serviceId, 'property_building_id' => $propertyId, 'log_id' => $logId ?? null, 'work_order_id' => $workOrderId, 'created_by' => $user->id, 'contract_id' => $contractId];
                        $collection = collect($criteriaArray);
                        $mergedArray = $collection->reduce(function ($carry, $item) {
                            return array_merge($carry, $item);
                        }, []);

                        $smartAssignCriteriaDescriptionInformation = $this->getSmartAssignCriteriaDescriptionDetailsByValues('work_order_id', $workOrderId);

                        if(is_null($smartAssignCriteriaDescriptionInformation)){
                            $saveCriteria = isset($user) ? $this->saveSmartAssignCriteriaDescription($mergedArray) : null;

                            if(!$saveCriteria){
                                Log::info("manageWorkersSelection error: We cannot save the used criteria in smart assign worker with this Work Order: ".$workOrderId);  
                            }
                        }

                        else{
                            $updatedArray = [
                                'worker_id' => $workerId,
                                'updated_by' => $user->id,
                                'log_id' => $logId
                            ];

                            $updateCriteria = $this->updateSmartAssignCriteriaDescriptionByValues('work_order_id', $workOrderId, $updatedArray);

                            if(!$updateCriteria){
                                Log::info("manageWorkersSelection error: We cannot update the used criteria in smart assign worker with this Work Order: ".$workOrderId);  
                            }
                        }
                    }

                    else{
                        Log::info("manageWorkersSelection error: We cannot save the log for smart assign worker with this worker ID: ".$workerId);
                    }
                }

                return $workerId;
            }

            catch (\Throwable $th) {
                Log::error("manageWorkersSelection error: ".$th);
            }
        }

        public function updateWorkOrderDetailsByValues($workOrderId, $data) {
            try {
                return WorkOrders::where('id', $workOrderId)
                ->update($data);
            }

            catch (\Throwable $th) {
                Log::error("updateWorkOrderDetailsByValues error: ".$th);
            }
        }

        public function getWorkOrdersDetailsByValues($key, $value) {
            try {
                return WorkOrders::where($key, $value) 
                ->pluck('id');
            } 
            
            catch (\Throwable $th) {
                Log::error("getWorkOrdersDetailsByValues error: ".$th);
            }
        }

        public function getNotAvailableWorkersFromWorkOrders($status, $contractId) {
            try {
                return WorkOrders::where('status', $status)
                ->where('contract_id', $contractId)
                ->where('worker_id', '>', 0)
                ->groupBy('worker_id')
                ->pluck('worker_id')
                ->toArray();
            } 
            
            catch (\Throwable $th) {
                Log::error("getNotAvailableWorkersFromWorkOrders error: ".$th);
            }
        }

        public function getNotAvailableWorkersList($status, $contractId) {
            try {
                return WorkOrders::leftjoin('users', 'work_orders.worker_id', 'users.id')
                ->select('worker_id', DB::raw('COUNT(work_orders.id) as work_orders_count'), 'users.deleted_at')
                ->where('work_orders.status', $status)
                ->where('work_orders.contract_id', $contractId)
                ->where('worker_id', '>', 0)
                ->where('work_orders.is_deleted', 'no')
                ->whereNull('users.deleted_at')
                ->groupBy('work_orders.worker_id')
                ->orderBy('work_orders_count', 'DESC')
                ->get();
            } 
            
            catch (\Throwable $th) {
                Log::error("getNotAvailableWorkersList error: ".$th);
            }
        }

        public function getFiltredPmWorkOrdersForAdmin($serviceProviderId, $perPage, $search, $dates, $frequency, $property, $service, $project_user_id) {
            try {
                return WorkOrders::with([
                    'assetCategory' => function ($subquery) {
                        $subquery->select(['id', 'asset_category', 'deleted_at AS asc_deleted_at', 'is_deleted AS asc_is_deleted']);
                    },
                    'assetName' => function ($subquery) {
                        $subquery->select(['id', 'asset_name']);
                    },
                    'frequencyMaster' => function ($subquery) {
                        $subquery->select(['id', 'title', 'title_ar']);
                    },
                    'propertyBuilding' => function ($subquery) {
                        $subquery->with('property:id,complex_name')
                        ->select(['building_name', 'deleted_at AS building_deleted_at']);
                    },
                    'createdBy:id,name'
                ])
                ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
                ->join('users', 'users.id', '=', 'work_orders.created_by')
                ->whereIn('contracts.service_provider_id', $serviceProviderId)
                ->where('work_orders.project_user_id', $project_user_id)
                ->where('work_orders.contract_type', 'regular')
                ->where('work_orders.work_order_type', 'preventive')
                ->where('work_orders.is_deleted', 'no')
                ->when(!empty($search) && isset($search), function ($subquery) use($search) {
                    $subquery->where('work_orders.pm_title', 'LIKE', "%{$search}%");
                })
                ->when(isset($dates) && count($dates) > 0, function ($subquery) use($dates) {
                    $subquery->whereBetween('work_orders.start_date', [$dates[0], $dates[1]]);
                })
                ->when(isset($frequency) && count($frequency) > 0 && $frequency['0'] <> '0', function ($subquery) use($frequency) {
                    $subquery->whereIn('work_orders.frequency_id', $frequency); 
                })
                ->when(isset($property) && count($property) > 0, function ($subquery) use($property) {
                    $subquery->whereIn('work_orders.property_id', $property);
                })
                ->when(isset($service) && count($service) > 0, function ($subquery) use($service) {
                    $subquery->whereIn('work_orders.asset_category_id', $service);
                })
                ->select([
                    'work_orders.*',
                    DB::raw('count(*) AS wo_total'),
                    DB::raw('MAX(work_orders.start_date) AS max_start_date')
                ])
                ->groupBy('unique_id')
                ->orderBy('work_orders.id', 'DESC')
                ->paginate($perPage, ['*'], 'page'); 
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredPmWorkOrdersForAdmin error: ".$th);
            }
        }

        public function getFiltredPmWorkOrdersForBuildingManager($buildingId, $perPage, $search, $dates, $frequency, $property,$service) {
            try {
                return WorkOrders::with([
                    'assetCategory' => function ($subquery) {
                        $subquery->select(['id', 'asset_category', 'deleted_at AS asc_deleted_at', 'is_deleted AS asc_is_deleted']);
                    },
                    'assetName' => function ($subquery) {
                        $subquery->select(['id', 'asset_name']);
                    },
                    'frequencyMaster' => function ($subquery) {
                        $subquery->select(['id', 'title', 'title_ar']);
                    },
                    'propertyBuilding' => function ($subquery) {
                        $subquery->with('property:id,complex_name')
                        ->select(['building_name', 'deleted_at AS building_deleted_at']);
                    },
                    'createdBy:id,name'
                ])
                ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                ->where('work_orders.work_order_type', 'preventive')
                ->where('work_orders.is_deleted', 'no')
                ->whereIn('work_orders.property_id', $buildingId)
                ->when(!empty($search) && isset($search), function ($subquery) use($search) {
                    $subquery->where('work_orders.pm_title', 'LIKE', "%{$search}%");
                })
                ->when(isset($dates) && count($dates) > 0, function ($subquery) use($dates) {
                    $subquery->whereBetween('work_orders.start_date', [$dates[0], $dates[1]]);
                })
                ->when(isset($frequency) && count($frequency) > 0 && $frequency['0'] <> '0', function ($subquery) use($frequency) {
                    $subquery->whereIn('work_orders.frequency_id', $frequency); 
                })
                ->when(isset($property) && count($property) > 0, function ($subquery) use($property) {
                    $subquery->whereIn('work_orders.property_id', $property);
                })
                ->when(isset($service) && count($service) > 0, function ($subquery) use($service) {
                    $subquery->whereIn('work_orders.asset_category_id', $service);
                })
                ->select([
                    'work_orders.*',
                    DB::raw('count(*) AS wo_total'),
                    DB::raw('MAX(work_orders.start_date) AS max_start_date')
                ])
                ->groupBy('unique_id')
                ->orderBy('work_orders.id', 'DESC')
                ->paginate($perPage, ['*'], 'page'); 
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredPmWorkOrdersForBuildingManager error: ".$th);
            }
        }

        public function getFiltredPmWorkOrdersForSpAdmin($spAdminId, $perPage, $search, $dates, $frequency, $property ,$service) {
            try {
                return WorkOrders::with([
                    'assetCategory' => function ($subquery) {
                        $subquery->select(['id', 'asset_category', 'deleted_at AS asc_deleted_at', 'is_deleted AS asc_is_deleted']);
                    },
                    'assetName' => function ($subquery) {
                        $subquery->select(['id', 'asset_name']);
                    },
                    'frequencyMaster' => function ($subquery) {
                        $subquery->select(['id', 'title', 'title_ar']);
                    },
                    'propertyBuilding' => function ($subquery) {
                        $subquery->with('property:id,complex_name')
                        ->select(['building_name', 'deleted_at AS building_deleted_at']);
                    },
                    'createdBy:id,name'
                ])
                ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                ->where('work_orders.work_order_type', 'preventive')
                ->where('work_orders.is_deleted', 'no')
                ->whereIn('work_orders.service_provider_id', $spAdminId)
                ->when(!empty($search) && isset($search), function ($subquery) use($search) {
                    $subquery->where('work_orders.pm_title', 'LIKE', "%{$search}%");
                })
                ->when(isset($dates) && count($dates) > 0, function ($subquery) use($dates) {
                    $subquery->whereBetween('work_orders.start_date', [$dates[0], $dates[1]]);
                })
                ->when(isset($frequency) && count($frequency) > 0 && $frequency['0'] <> '0', function ($subquery) use($frequency) {
                    $subquery->whereIn('work_orders.frequency_id', $frequency); 
                })
                ->when(isset($property) && count($property) > 0, function ($subquery) use($property) {
                    $subquery->whereIn('work_orders.property_id', $property);
                })
                ->when(isset($service) && count($service) > 0, function ($subquery) use($service) {
                    $subquery->whereIn('work_orders.asset_category_id', $service);
                })
                ->select([
                    'work_orders.*',
                    DB::raw('count(*) AS wo_total'),
                    DB::raw('MAX(work_orders.start_date) AS max_start_date')
                ])
                ->groupBy('unique_id')
                ->orderBy('work_orders.id', 'DESC')
                ->paginate($perPage, ['*'], 'page'); 
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredPmWorkOrdersForSpAdmin error: ".$th);
            }
        }

        public function getFiltredPmWorkOrdersForSupervisor($userId, $perPage, $search, $dates, $frequency, $property,$service) {
            try {
                return WorkOrders::with([
                    'assetCategory' => function ($subquery) {
                        $subquery->select(['id', 'asset_category', 'deleted_at AS asc_deleted_at', 'is_deleted AS asc_is_deleted']);
                    },
                    'assetName' => function ($subquery) {
                        $subquery->select(['id', 'asset_name']);
                    },
                    'frequencyMaster' => function ($subquery) {
                        $subquery->select(['id', 'title', 'title_ar']);
                    },
                    'propertyBuilding' => function ($subquery) {
                        $subquery->with('property:id,complex_name')
                        ->select(['building_name', 'deleted_at AS building_deleted_at']);
                    },
                    'createdBy:id,name'
                ])
                ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                ->where('work_orders.work_order_type', 'preventive')
                ->where('work_orders.is_deleted', 'no')
                ->whereRaw("find_in_set($userId, work_orders.supervisor_id)")
                ->when(!empty($search) && isset($search), function ($subquery) use($search) {
                    $subquery->where('work_orders.pm_title', 'LIKE', "%{$search}%");
                })
                ->when(isset($dates) && count($dates) > 0, function ($subquery) use($dates) {
                    $subquery->whereBetween('work_orders.start_date', [$dates[0], $dates[1]]);
                })
                ->when(isset($frequency) && count($frequency) > 0 && $frequency['0'] <> '0', function ($subquery) use($frequency) {
                    $subquery->whereIn('work_orders.frequency_id', $frequency); 
                })
                ->when(isset($property) && count($property) > 0, function ($subquery) use($property) {
                    $subquery->whereIn('work_orders.property_id', $property);
                })
                ->when(isset($service) && count($service) > 0, function ($subquery) use($service) {
                    $subquery->whereIn('work_orders.asset_category_id', $service);
                })
                ->select([
                    'work_orders.*',
                    DB::raw('count(*) AS wo_total'),
                    DB::raw('MAX(work_orders.start_date) AS max_start_date')
                ])
                ->groupBy('unique_id')
                ->orderBy('work_orders.id', 'DESC')
                ->paginate($perPage, ['*'], 'page'); 
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredPmWorkOrdersForSupervisor error: ".$th);
            }
        }

        public function getLoopWorkOrdersList($result) {
            try {
                if(isset($result)){
                    foreach ($result as $key => $row) {
                        $latestWorkOrder = $this->getWorkOrderInformationsByValues('unique_id', $row['unique_id']);
                        $result[$key]['pm_title'] = isset($latestWorkOrder) ? $latestWorkOrder->pm_title : null;

                        if ($latestWorkOrder->asset_category_id) {
                            $result[$key]['asset_category'] = isset($latestWorkOrder->assetCategory) ? $latestWorkOrder->assetCategory->asset_category : null;
                        }

                        if ($latestWorkOrder->property_id) {
                            $result[$key]['building_name'] = isset($latestWorkOrder) ? $latestWorkOrder->propertyBuilding->building_name : null;
                            $result[$key]['complex_name'] = isset($latestWorkOrder) ? $latestWorkOrder->propertyBuilding->property->complex_name : null;
                        }

                        $getPmEndDate = $latestWorkOrder->pm_end_date;

                        if(empty($getPmEndDate) || !isset($getPmEndDate) || $getPmEndDate == null){
                            $getPmEndDate = $latestWorkOrder->end_date;
                        }

                        $result[$key]['pmEndDate'] = isset($getPmEndDate) ? $getPmEndDate : null;
                    }
                }
                
                return $result;
            } 
            
            catch (\Throwable $th) {
                Log::error("getLoopWorkOrdersList error: ".$th);
            }
        }

        public function getWorkOrderInformationsByValues($key, $value) { 
            try {
                return WorkOrders::where($key, $value)
                ->select('id', 'asset_category_id', 'unique_id', 'pm_end_date', 'end_date', 'property_id', 'pm_title', 'floor', 'room', 'service_category_id', 'asset_name_id', 'service_type', 'asset_number_id', 'unique_id', 'contract_id', 'frequency_id', 'priority_id', 'start_date', 'checklist_id', 'description', 'bm_approove', 'status', 'workorder_journey', 'schedule_start_time', 'wtf_start_time', 'work_order_type')
                ->orderBy('id', 'DESC')
                ->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getWorkOrderInformationsByValues error: ".$th);
            }
        }

        public function updateWorkOrderDetailsByUniqueId($uniqueId, $data) {
            try {
                return WorkOrders::where('unique_id', $uniqueId)
                ->update($data);
            }

            catch (\Throwable $th) {
                Log::error("updateWorkOrderDetailsByValues error: ".$th);
            }
        }

        public function getSelectedPmWorkOrdersByValues($key, $value) {
            try {
                return WorkOrders::where($key, $value)
                ->select('property_buildings.building_name', 'work_orders.id', 'work_orders.work_order_id')
                ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                ->where($key, $value)
                ->get();
            } 
            
            catch (\Throwable $th) {
                Log::error("getSelectedPmWorkOrdersByValues error: ".$th);
            }
        }

        public function getCountPmWorkOrdersForAdmin($serviceProviderId) {
            try {
                return WorkOrders::with([
                    'assetCategory' => function ($subquery) {
                        $subquery->select(['id', 'asset_category', 'deleted_at AS asc_deleted_at', 'is_deleted AS asc_is_deleted']);
                    },
                    'assetName' => function ($subquery) {
                        $subquery->select(['id', 'asset_name']);
                    },
                    'frequencyMaster' => function ($subquery) {
                        $subquery->select(['id', 'title', 'title_ar']);
                    },
                    'propertyBuilding' => function ($subquery) {
                        $subquery->with('property:id,complex_name')
                            ->select(['building_name', 'deleted_at AS building_deleted_at']);
                    },
                    'createdBy:id,name'
                ])
                ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                ->whereIn('contracts.service_provider_id', $serviceProviderId)
                ->where('work_orders.contract_type', 'regular')
                ->where('work_orders.work_order_type', 'preventive')
                ->where('work_orders.is_deleted', 'no')
                ->distinct('unique_id')
                ->count();
            } 
            
            catch (\Throwable $th) {
                Log::error("getCountPmWorkOrdersForAdmin error: ".$th);
            }
        }

        public function getCountPmWorkOrdersForBuildingManager($buildingId) {
            try {
                return WorkOrders::with([
                    'assetCategory' => function ($subquery) {
                        $subquery->select(['id', 'asset_category', 'deleted_at AS asc_deleted_at', 'is_deleted AS asc_is_deleted']);
                    },
                    'assetName' => function ($subquery) {
                        $subquery->select(['id', 'asset_name']);
                    },
                    'frequencyMaster' => function ($subquery) {
                        $subquery->select(['id', 'title', 'title_ar']);
                    },
                    'propertyBuilding' => function ($subquery) {
                        $subquery->with('property:id,complex_name')
                        ->select(['building_name', 'deleted_at AS building_deleted_at']);
                    },
                    'createdBy:id,name'
                ])
                ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                ->where('work_orders.work_order_type', 'preventive')
                ->where('work_orders.is_deleted', 'no')
                ->whereIn('work_orders.property_id', $buildingId)
                ->distinct('unique_id')
                ->count();
            } 
            
            catch (\Throwable $th) {
                Log::error("getCountPmWorkOrdersForBuildingManager error: ".$th);
            }
        }

        public function getCountPmWorkOrdersForSpAdmin($spAdminId) {
            try {
                return WorkOrders::with([
                    'assetCategory' => function ($subquery) {
                        $subquery->select(['id', 'asset_category', 'deleted_at AS asc_deleted_at', 'is_deleted AS asc_is_deleted']);
                    },
                    'assetName' => function ($subquery) {
                        $subquery->select(['id', 'asset_name']);
                    },
                    'frequencyMaster' => function ($subquery) {
                        $subquery->select(['id', 'title', 'title_ar']);
                    },
                    'propertyBuilding' => function ($subquery) {
                        $subquery->with('property:id,complex_name')
                        ->select(['building_name', 'deleted_at AS building_deleted_at']);
                    },
                    'createdBy:id,name'
                ])
                ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                ->where('work_orders.work_order_type', 'preventive')
                ->where('work_orders.is_deleted', 'no')
                ->whereIn('work_orders.service_provider_id', $spAdminId)
                ->distinct('unique_id')
                ->count();
            } 
            
            catch (\Throwable $th) {
                Log::error("getCountPmWorkOrdersForSpAdmin error: ".$th);
            }
        }

        public function getCountPmWorkOrdersForSupervisor($userId) {
            try {
                return WorkOrders::with([
                    'assetCategory' => function ($subquery) {
                        $subquery->select(['id', 'asset_category', 'deleted_at AS asc_deleted_at', 'is_deleted AS asc_is_deleted']);
                    },
                    'assetName' => function ($subquery) {
                        $subquery->select(['id', 'asset_name']);
                    },
                    'frequencyMaster' => function ($subquery) {
                        $subquery->select(['id', 'title', 'title_ar']);
                    },
                    'propertyBuilding' => function ($subquery) {
                        $subquery->with('property:id,complex_name')
                        ->select(['building_name', 'deleted_at AS building_deleted_at']);
                    },
                    'createdBy:id,name'
                ])
                ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                ->where('work_orders.work_order_type', 'preventive')
                ->where('work_orders.is_deleted', 'no')
                ->whereRaw("find_in_set($userId, work_orders.supervisor_id)")
                ->distinct('unique_id')
                ->count('work_orders.id', 'DESC');
            } 
            
            catch (\Throwable $th) {
                Log::error("getCountPmWorkOrdersForSupervisor error: ".$th);
            }
        }

        public function getCountWorkOrders($currentDate, $dateRange, $page, $userId, $buildingIds, $assetCategories, $serviceProviderIds, $contracts, $projectUserId, $userType, $userServiceProvider) {
            try {
                $currentDate = $this->changeDateFormat('Y-m-d', $currentDate);
                $query = WorkOrders::selectRaw("count(work_orders.id) as total")
                ->selectRaw("count(case when work_orders.status = 1 then 1 end) as open_work_orders_count")
                ->selectRaw("count(case when work_orders.status = 2 then 1 end) as inprogress_work_orders_count")
                ->selectRaw("count(case when work_orders.status = 3 then 1 end) as under_evaluation_work_orders_count")
                ->selectRaw("count(case when work_orders.status = 4 then 1 end) as closed_work_orders_count")
                ->selectRaw("count(case when work_orders.status = 5 then 1 end) as deleted_work_orders_count")
                ->selectRaw("count(case when work_orders.status = 6 then 1 end) as reopened_work_orders_count")
                ->selectRaw("count(case when work_orders.status = 7 then 1 end) as scheduled_work_orders_count")
                ->where('is_deleted', '!=', 'yes')
                ->where('work_orders.is_deleted', '<>', 'yes')
                ->when(isset($dateRange) && count($dateRange) == 0, function ($subquery) use($currentDate) {
                    $subquery->where('start_date', '<=', $currentDate);
                }) 
                ->when(isset($dateRange) && count($dateRange) > 0, function ($subquery) use($dateRange) {
                    $start_date = $dateRange['0'].' 00:00:00';
                    $end_date = $dateRange['1'].' 23:59:59';
                    $subquery->whereRaw("(IF(`work_orders`.`work_order_type` != 'preventive', IF(NULLIF(work_orders.start_date, '') IS NOT NULL,work_orders.start_date,work_orders.created_at), work_orders.start_date) Between '$start_date' AND '$end_date')");
                })
                ->when(!empty($page) && $page == 'open', function ($subquery) {
                    $subquery = $this->getFiltredWorkOrdersListForOpenPage($subquery, WorkOrderStatus::Open->value);
                })
                ->when(!empty($page) && $page == 'in-progress', function ($subquery) {
                    $subquery = $this->getFiltredWorkOrdersListForInProgressPage($subquery);
                })
                ->when(!empty($page) && $page == 'under-evaluation', function ($subquery) {
                    $subquery = $this->getFiltredWorkOrdersListForUnderEvaluationPage($subquery);
                })
                ->when(!empty($page) && $page == 'closed', function ($subquery) {
                    $subquery = $this->getFiltredWorkOrdersListForClosedPage($subquery, WorkOrderStatus::Closed->value);
                })
                ->when(!empty($page) && $page == 'spare-parts', function ($subquery) use ($userType) {
                    $subquery = $this->getFiltredWorkOrdersListForSparePartsPage($subquery, $userType);
                })
                ->when(!empty($page) && $page == 'request', function ($subquery) use ($userType, $buildingIds, $assetCategories, $serviceProviderIds, $contracts, $userId) {
                    $subQuery = $this->getFiltredWorkOrdersListForRequestPage($subquery, $userType, $buildingIds, $assetCategories, $serviceProviderIds, $contracts, $userId);
                })
                ->when(!empty($page) && $page == 'pending', function ($subquery) use ($userType, $buildingIds, $assetCategories, $serviceProviderIds, $contracts, $userId, $userServiceProvider) {
                    if(in_array($userType, array('building_manager_employee', 'building_manager'))){
                        $subQuery = $this->getFiltredWorkOrdersListForRequestPage($subquery, $userType, $buildingIds, $assetCategories, $serviceProviderIds, $contracts, $userId);
                    }

                    else{
                        $subquery = $this->getFiltredWorkOrdersListForWatingForActionPage($subquery, $userType, $userId, $userServiceProvider);
                    }
                });

                if(!in_array($page, ['request', 'pending'])){
                    if(!in_array($userType, array('sp_admin', 'supervisor'))){
                        $query = $this->getFiltredWorkOrdersListForNotAdmin($query, $projectUserId);
                    }
    
                    if(in_array($userType, array('building_manager', 'building_manager_employee'))){
                        $query = $this->getFiltredWorkOrdersListForBuildingManager($query, $buildingIds, $assetCategories, $serviceProviderIds, $contracts);
                    }
    
                    elseif($userType == "sp_admin"){
                        $query = $this->getFiltredWorkOrdersListForSpAdmin($query, $contracts);
                    }
              
                    elseif($userType == "supervisor"){
                        $query = $this->getFiltredWorkOrdersListForSupervisor($query, $userId);
                    }
                    
                    elseif($serviceProviderIds){
                        $query = $this->getFiltredWorkOrdersListForOtherUsers($query, $serviceProviderIds);
                    }
                }
                
                return $query->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getCountWorkOrders error: ".$th);
            }
        }

        public function getSearchedWorkOrdersList($dataList, $page, $userType, $userId, $contracts, $buildingIds, $assetCategories, $serviceProviderIds, $projectUserId, $search, $currentDate, $dateRange, $selectedRows, $showSelectedRows, $filters, $userServiceProvider) {
            try {
                $currentDate = $this->changeDateFormat('Y-m-d', $currentDate);

                $query = WorkOrders::
                    select('work_orders.*', 'work_orders.priority_id as wo_priority_id', DB::raw('(SELECT COUNT(*) FROM work_orders AS wo INNER JOIN work_time_frame ON work_time_frame.user_id = wo.project_user_id WHERE wo.work_order_type = "preventive" and wo.status = 1 AND wo.start_date = CURDATE() AND wo.id = work_orders.id AND work_time_frame.start_time >= CURTIME()) AS pm_count'),
                    DB::raw('
                        CASE
                            WHEN work_orders.work_order_type = "preventive" THEN
                                DATE_FORMAT(
                                    CASE
                                            WHEN work_orders.status = 1 THEN
                                                CONCAT(work_orders.start_date, " ", COALESCE(wf.start_time, "00:00:00"))
                                            ELSE
                                                CONCAT(work_orders.start_date, " ", 
                                                    COALESCE(work_orders.wtf_start_time, wf.start_time, "00:00:00")
                                                )
                                        END, "%d-%m-%Y %H:%i"
                                )
                            ELSE
                                DATE_FORMAT(work_orders.created_at, "%d-%m-%Y %H:%i")
                        END AS submission_date
                    '),
                DB::raw('
                    CASE
                        WHEN work_orders.worker_id = 0 AND work_orders.job_started_at IS NULL THEN \'' . $dataList['no_worker_assigned'] . '\'
                        WHEN work_orders.assigned_to != \'sp_worker\' AND work_orders.job_started_at IS NOT NULL THEN \'' . $dataList['started_on_behalf'] . '\'
                        ELSE us.name
                    END AS assigned_worker
                '),
                DB::raw('CASE 
                    WHEN contract_type = "regular" 
                    THEN CASE work_orders.status 
                            WHEN 1 THEN "' . $dataList['status_open'] . '" 
                            WHEN 2 THEN "' . $dataList['status_in_progress'] . '" 
                            WHEN 3 THEN "' . $dataList['status_on_hold'] . '" 
                            WHEN 4 THEN "' . $dataList['status_closed'] . '" 
                            WHEN 5 THEN "' . $dataList['status_deleted']  . '" 
                            WHEN 6 THEN "' . $dataList['status_reopen'] . '" 
                            WHEN 7 THEN "' . $dataList['status_scheduled'] . '" 
                            WHEN 8 THEN "' . $dataList['status_scheduled'] . '" 
                            ELSE "" 
                        END 
                    ELSE CASE 
                            WHEN work_orders.status = 4 THEN "' . $dataList['status_closed'] . '" 
                            ELSE "' . $dataList['status_warrantly'] . '" 
                        END 
                    END AS status_html'),
                        'mr.phone as mr_phone'
                    )
                ->leftJoin('work_time_frame as wf', 'work_orders.project_user_id', '=', 'wf.user_id')
                ->leftJoin('maintanance_request as mr', 'work_orders.maintanance_request_id', '=', 'mr.id')
                ->leftJoin('users as us', 'work_orders.worker_id', '=', 'us.id')
                ->with([
                    'contract',
                    'propertyBuilding.property',
                    'worker',
                    'chatMessages',
                    'relatedWorkOrders',
                    'chatMessages',
                    'slaAssetCategory',
                    'contractPriority',
                    'frequencyMaster.frequencies.contractFrequencies',
                    'workTimeFrame'
                ])
                ->where('work_orders.is_deleted', '<>', 'yes')
                ->where('start_date', '<=', $currentDate)
                ->where(function ($q) {
                    $q->where(function ($subQuery) {
                        $subQuery->whereNotNull('schedule_start_time')
                               // ->where('schedule_start_time', '!=', '')
                               ->whereRaw("schedule_start_time != ''")
                                ->whereRaw('TIME(schedule_start_time) < CURTIME()');
                    })
                    ->orWhereNull('schedule_start_time')
                    ->orWhere('schedule_start_time', '');
                })
                ->when(isset($dateRange) && count($dateRange) > 0, function ($subquery) use($dateRange) {
                    $start_date = $dateRange['0'].' 00:00:00';
                    $end_date = $dateRange['1'].' 23:59:59';
                    $subquery->whereRaw("(IF(`work_orders`.`work_order_type` != 'preventive', IF(NULLIF(work_orders.start_date, NULL) IS NOT NULL,work_orders.start_date,work_orders.created_at), work_orders.start_date) Between '$start_date' AND '$end_date')");
                })
                ->when(!empty($page) && $page == 'open', function ($subquery) {
                    $subquery = $this->getFiltredWorkOrdersListForOpenPage($subquery, WorkOrderStatus::Open->value);
                })
                ->when(!empty($page) && $page == 'in-progress', function ($subquery) {
                    $subquery = $this->getFiltredWorkOrdersListForInProgressPage($subquery);
                })
                ->when(!empty($page) && $page == 'under-evaluation', function ($subquery) {
                    $subquery = $this->getFiltredWorkOrdersListForUnderEvaluationPage($subquery);
                })
                ->when(!empty($page) && $page == 'closed', function ($subquery) {
                    $subquery = $this->getFiltredWorkOrdersListForClosedPage($subquery, WorkOrderStatus::Closed->value);
                })
                ->when(!empty($page) && $page == 'spare-parts', function ($subquery) use ($userType) {
                    $subquery = $this->getFiltredWorkOrdersListForSparePartsPage($subquery, $userType);
                })
                ->when(!empty($page) && $page == 'request', function ($subquery) use ($userType, $buildingIds, $assetCategories, $serviceProviderIds, $contracts, $userId) {
                    $subQuery = $this->getFiltredWorkOrdersListForRequestPage($subquery, $userType, $buildingIds, $assetCategories, $serviceProviderIds, $contracts, $userId);
                })
                ->when(!empty($page) && $page == 'pending', function ($subquery) use ($userType, $buildingIds, $assetCategories, $serviceProviderIds, $contracts, $userId, $userServiceProvider) {
                    if(in_array($userType, array('building_manager_employee', 'building_manager'))){
                        $subQuery = $this->getFiltredWorkOrdersListForRequestPage($subquery, $userType, $buildingIds, $assetCategories, $serviceProviderIds, $contracts, $userId);
                    }

                    else{
                        $subquery = $this->getFiltredWorkOrdersListForWatingForActionPage($subquery, $userType, $userId, $userServiceProvider);
                    }
                })
                ->when(!empty($search) && isset($search), function ($subquery) use($search) {
                    $subquery->where(function ($innerQuery) use ($search) {
                        $innerQuery->where('work_orders.work_order_id', 'like', "%{$search}%")
                        ->orWhere(function ($innerQuery) use ($search) {
                            $innerQuery->where('work_orders.unique_id', 'like', "%{$search}%")
                                ->where('work_orders.work_order_type', 'reactive');
                        });
                    });
                })
                ->when($showSelectedRows && isset($selectedRows) && count($selectedRows) > 0, function ($subquery) use ($selectedRows) {
                    $subquery->whereIn('work_orders.id', $selectedRows);
                })
                ->when(isset($filters) && count($filters) > 0, function ($subquery) use ($filters, $page) {
                    $subquery->when(isset($filters['ratings']) && count($filters['ratings']) > 0, function ($innerQuery) use ($filters) {
                        $innerQuery->whereIn('work_orders.rating', $filters['ratings'])
                        ->where('work_orders.status', WorkOrderStatus::Closed->value);
                    });

                    $subquery->when(isset($page) && $page == "all", function ($innerQuery) use ($filters) {
                        $innerQuery->when(isset($filters['status']) && count($filters['status']) > 0, function ($childQuery) use ($filters) {
                            $childQuery->whereIn('work_orders.status', $filters['status']);
                        });
                    });

                    $subquery->when(isset($filters['passFail']) && count($filters['passFail']) > 0, function ($innerQuery) use ($filters) {
                        $innerQuery->whereIn('pass_fail', $filters['passFail']);
                    });  

                    $subquery->when(isset($filters['type']) && count($filters['type']) > 0, function ($innerQuery) use ($filters) {
                        $innerQuery->whereIn('work_order_type', $filters['type']);
                    }); 
                    
                    $subquery->when(isset($filters['assets']) && count($filters['assets']) > 0, function ($innerQuery) use ($filters) {
                        $innerQuery->whereIn('work_orders.asset_number_id', $filters['assets']);
                    }); 

                    $subquery->when(isset($filters['services']) && count($filters['services']) > 0, function ($innerQuery) use ($filters) {
                        $innerQuery->whereIn('work_orders.asset_category_id', $filters['services']);
                    }); 

                    $subquery->when(isset($filters['workers']) && count($filters['workers']) > 0, function ($innerQuery) use ($filters) {
                        $workers = $filters['workers'];

                        $innerQuery->where(function($childQuery) use($workers) {
                            $childQuery->whereIn('work_orders.worker_id', $workers)
                            ->where(function($workerQuery) {
                                $workerQuery->where('work_orders.assigned_to', 'sp_worker')
                                    ->orWhere('work_orders.assigned_to', 'supervisor');
                            });
                        });
                    }); 

                    $subquery->when(isset($filters['supervisors']) && count($filters['supervisors']) > 0, function ($innerQuery) use ($filters) {
                        $supervisors = $filters['supervisors'];

                        $innerQuery->where(function ($childQuery) use ($supervisors) {
                            $childQuery->where(function ($q1) use ($supervisors) {
                                $q1->where(function ($q1a) {
                                    $q1a->whereNotNull('work_orders.supervisor_id')
                                        ->where('work_orders.assigned_to', 'supervisor');
                                })->orWhere(function ($q1b) {
                                    $q1b->where('work_orders.assigned_to', 'sp_worker')
                                        ->whereRaw('CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, \',\', \'\')) + 1 = 1');
                                })
                                ->whereIn('work_orders.supervisor_id', $supervisors)
                                ->where(function ($q1c) {
                                  $q1c->where('work_orders.assigned_to', '!=', 'sp_worker')
                                      ->orWhere('work_orders.worker_id', '!=', 0);
                                });
                            })->orWhere(function ($q2) use ($supervisors) {
                                $q2->whereIn('work_orders.service_provider_id', $supervisors)
                                   ->where(function ($q2a) {
                                     $q2a->where('work_orders.assigned_to', '!=', 'sp_worker')
                                         ->orWhere('work_orders.worker_id', '!=', 0);
                                   })
                                   ->where('work_orders.assigned_to', '!=', 'supervisor')
                                   ->whereRaw('CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, \',\', \'\')) + 1 > 1');
                            });
                        });
                    }); 

                    $subquery->when(
                        (isset($filters['buildingMangerEmployee']) && count($filters['buildingMangerEmployee']) > 0) ||
                        (isset($filters['buildingManger']) && count($filters['buildingManger']) > 0),
                        function ($innerQuery) use ($filters) {
                            $combined = [];
                            if (isset($filters['buildingMangerEmployee']) && is_array($filters['buildingMangerEmployee'])) {
                                $combined = array_merge($combined, $filters['buildingMangerEmployee']);
                            }

                            if (isset($filters['buildingManger']) && is_array($filters['buildingManger'])) {
                                $combined = array_merge($combined, $filters['buildingManger']);
                            }

                            $combined = array_unique($combined);

                            $innerQuery->whereIn('work_orders.created_by', $combined);
                        }
                    );
                    $subquery->when(isset($filters['properties']) && count($filters['properties']) > 0, function ($innerQuery) use ($filters) {
                        $innerQuery->whereIn('work_orders.property_id', $filters['properties']);
                    }); 
                })
                ->when(isset($serviceProviderIds) && count($serviceProviderIds) > 0 && !in_array($userType, array('sp_admin', 'supervisor')), function ($innerQuery) use ($serviceProviderIds) {
                    $innerQuery = $this->getFiltredWorkOrdersListForOtherUsers($innerQuery, $serviceProviderIds);
                });

                if(!in_array($page, ['request', 'pending'])){
                    if(!in_array($userType, array('sp_admin', 'supervisor'))){
                        $query = $this->getFiltredWorkOrdersListForNotAdmin($query, $projectUserId);
                    }
    
                    if(in_array($userType, array('building_manager', 'building_manager_employee'))){
                        $query = $this->getFiltredWorkOrdersListForBuildingManager($query, $buildingIds, $assetCategories, $serviceProviderIds, $contracts);
                    }
    
                    elseif($userType == "sp_admin"){
                        $query = $this->getFiltredWorkOrdersListForSpAdmin($query, $contracts);
                    }
              
                    elseif($userType == "supervisor"){
                        $query = $this->getFiltredWorkOrdersListForSupervisor($query, $userId);
                    }
                    
                    elseif($serviceProviderIds){
                        $query = $this->getFiltredWorkOrdersListForOtherUsers($query, $serviceProviderIds);
                    }
                }

                return $query;
            } 
            
            catch (\Throwable $th) {
                Log::error("getSearchedWorkOrdersList error: ".$th); 
            }
        }

        public function getFiltredWorkOrdersList($perPage, $dataList, $page, $userType, $userId, $contracts, $buildingIds, $assetCategories, $serviceProviderIds, $projectUserId, $search, $currentDate, $dateRange, $sortBy, $selectedRows, $showSelectedRows, $filters = [], $userServiceProvider) {
            try {
                $query = $this->getSearchedWorkOrdersList($dataList, $page, $userType, $userId, $contracts, $buildingIds, $assetCategories, $serviceProviderIds, $projectUserId, $search, $currentDate, $dateRange, $selectedRows, $showSelectedRows, $filters, $userServiceProvider);
                
                return $query->when(isset($sortBy) && count($sortBy) > 0, function ($subquery) use($sortBy) {
                    $subquery->orderBy($sortBy['0'], $sortBy['1']);
                })
                ->paginate($perPage, ['*'], 'page');   
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredWorkOrdersList error: ".$th); 
            }
        }

        public function getPluckWorkOrdersList($data) {
            try {
                $selectedRows = [];

                if (isset($data)) {
                    $items = collect($data);

                    $selectedRows = $items->map(function ($subQuery) {
                        return $subQuery['id'];
                    })->toArray();
                }

                return $selectedRows;
            } 
            
            catch (\Throwable $th) {
                Log::error("getPluckWorkOrdersList error: ".$th); 
            }   
        }

       public function getFiltredWorkOrdersListForOpenPage($query, $status) {
            try {
                return $query->where('work_orders.status', $status)
                ->where('work_orders.workorder_journey', 'submitted');
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredWorkOrdersListForOpenPage error: ".$th);
            }
        } 

        public function getFiltredWorkOrdersListForInProgressPage($query) {
            try {
                return $query->where('work_orders.workorder_journey', 'job_execution');
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredWorkOrdersListForInProgressPage error: ".$th);
            }
        }

        public function getFiltredWorkOrdersListForUnderEvaluationPage($query) {
            try {
                return $query->where(function ($innerQuery) {
                    $innerQuery->where('work_orders.workorder_journey', 'job_evaluation')
                    ->orWhere('work_orders.workorder_journey', 'job_approval');
                });
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredWorkOrdersListForUnderEvaluationPage error: ".$th);
            }
        }

        public function getFiltredWorkOrdersListForClosedPage($query, $status) {
            try {
                return $query->where('work_orders.status', $status);
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredWorkOrdersListForClosedPage error: ".$th);
            }
        }

        public function getFiltredWorkOrdersListForSparePartsPage($query, $userType) {
            try {
                return $query->when(!empty($userType) && in_array($userType, array('sp_admin', 'supervisor')), function ($subquery) {
                    $subquery->whereHas('workOrderItemRequests', function ($innerQuery) {
                        $innerQuery->where('worker_spare_parts_requests.status', 'requested')
                        ->where('sent_to_project_owner', 0);
                    });
                })
                ->when(!empty($userType) && in_array($userType, array('admin', 'admin_employee')), function ($subquery) {
                    $subquery->where(function ($innerQuery) {
                        $innerQuery->whereHas('workOrderItemRequests', function ($childQuery) {
                            $childQuery->where('worker_spare_parts_requests.status', 'requested')
                            ->where('sent_to_project_owner', 1);
                        })
                        ->orWhereHas('serviceProviderMissingItemRequests', function ($childQuery) {
                            $childQuery->where('service_provider_missing_items_requests.status', 'requested');
                        });
                    });
                });
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredWorkOrdersListForSparePartsPage error: ".$th);
            }
        }

        public function getFiltredWorkOrdersListForWatingForActionPage($query, $userType, $userId, $userServiceProvider) {
            try {
                return $query->when(!empty($userType) && $userType == "sp_admin", function ($subquery) use ($userServiceProvider) {
                    //$subquery->where('work_orders.service_provider_id', $userId)
                    $subquery->whereHas('contract', function ($innerQuery) use ($userServiceProvider) {
                        $innerQuery->where('contracts.service_provider_id', $userServiceProvider);
                    })
                    ->whereRaw("(((work_orders.contract_type = 'regular' and (work_orders.status = 2 OR work_orders.status = 6) and work_orders.workorder_journey = 'job_evaluation') and work_orders.sp_approve_job != 1))");
                })
                ->when(!empty($userType) && $userType == "supervisor", function ($subquery) use ($userId) {
                    $subquery->whereRaw("find_in_set($userId, work_orders.supervisor_id)")
                    ->whereRaw("(((work_orders.contract_type = 'regular' and (work_orders.status = 2 OR work_orders.status = 6) and work_orders.workorder_journey = 'job_evaluation') and work_orders.sp_approve_job != 1))");
                });
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredWorkOrdersListForWatingForActionPage error: ".$th);
            }
        }

        public function getFiltredWorkOrdersListForRequestPage($query, $userType, $buildingIds, $assetCategories, $serviceProviderIds, $contracts, $userId) {
            try {
                return $query->when(!empty($userType) && in_array($userType, array('building_manager', 'building_manager_employee')), function ($subquery) use($buildingIds, $assetCategories, $serviceProviderIds, $contracts) {
                    $subquery->whereIn('work_orders.property_id', $buildingIds)
                    ->whereIn('work_orders.asset_category_id', $assetCategories);

                    if(!is_null($serviceProviderIds) && is_array($serviceProviderIds)) {
                        $subquery->whereIn('work_orders.contract_id', $contracts);
                    }

                    $subquery->whereRaw("((work_orders.contract_type = 'warranty' and work_orders.status != 4)
                    OR ((work_orders.contract_type = 'regular' and work_orders.status = 1) and work_orders.proposed_new_date IS NOT NULL and work_orders.bm_approve_issue = 0)
                    OR ((work_orders.contract_type = 'regular' and (work_orders.status = 2 OR work_orders.status = 6) and work_orders.workorder_journey = 'job_approval') and (work_orders.sp_approve_job = 2 or work_orders.sp_approve_job = 3) and work_orders.bm_approve_job = 0)
                    OR ((work_orders.contract_type = 'regular' and work_orders.status = 6 and work_orders.workorder_journey = 'submitted') && work_orders.sp_reopen_status = 1))");
                })
                ->when(!empty($userType) && $userType == "sp_admin", function ($subquery) use($contracts) {
                    $subquery->whereIn('work_orders.contract_id', $contracts)
                    ->whereRaw("(((work_orders.contract_type = 'regular' and work_orders.status = 1) and work_orders.proposed_new_date IS NULL and work_orders.bm_approve_issue = 0)
                    OR ((work_orders.contract_type = 'regular' and work_orders.status = 1) and work_orders.proposed_new_date IS NOT NULL and work_orders.bm_approve_issue = 1)
                    OR ((work_orders.contract_type = 'regular' and (work_orders.status = 6) and work_orders.workorder_journey = 'job_execution'))
                    OR ((work_orders.contract_type = 'regular' and (work_orders.status = 6) and work_orders.workorder_journey = 'job_evaluation') and work_orders.sp_approve_job != 1)
                    OR ((work_orders.contract_type = 'regular' and work_orders.status = 6 and work_orders.workorder_journey = 'submitted') and work_orders.sp_reopen_status = 0))");
                })
                ->when(!empty($userType) && $userType == "supervisor", function ($subquery) use($userId) {  
                    $subquery->whereRaw("find_in_set($userId, work_orders.supervisor_id)")
                    ->whereRaw("(((work_orders.contract_type = 'regular' and work_orders.status = 1) and work_orders.proposed_new_date IS NULL and work_orders.bm_approve_issue = 0)
                    OR ((work_orders.contract_type = 'regular' and work_orders.status = 1) and work_orders.proposed_new_date IS NOT NULL and work_orders.bm_approve_issue = 1)
                    OR ((work_orders.contract_type = 'regular' and (work_orders.status = 6) and work_orders.workorder_journey = 'job_execution'))
                    OR ((work_orders.contract_type = 'regular' and (work_orders.status = 6) and work_orders.workorder_journey = 'job_evaluation') and work_orders.sp_approve_job != 1)
                    OR ((work_orders.contract_type = 'regular' and work_orders.status = 6 and work_orders.workorder_journey = 'submitted') and work_orders.sp_reopen_status = 0))");
                });
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredWorkOrdersListForRequestPage error: ".$th);
            }
        }

        public function getFiltredWorkOrdersListForNotAdmin($query, $projectUserId) {
            try {
                return $query->where('work_orders.project_user_id', $projectUserId);
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredWorkOrdersListForNotAdmin error: ".$th);
            }
        }

        public function getFiltredWorkOrdersListForBuildingManager($query, $buildingIds, $assetCategories, $serviceProviderIds, $contracts) {
            try {
                if (!is_null($serviceProviderIds) && is_array($serviceProviderIds)) {
                    return $query->whereIn('work_orders.contract_id', $contracts)
                    ->whereIn('work_orders.property_id', $buildingIds)
                    ->whereIn('work_orders.asset_category_id', $assetCategories)
                    ->where('work_orders.contract_type', 'regular');
                }

                else{
                    return $query->whereIn('work_orders.property_id', $buildingIds)
                    ->whereIn('work_orders.asset_category_id', $assetCategories);
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredWorkOrdersListForBuildingManager error: ".$th);
            }
        }

        public function getFiltredWorkOrdersListForSpAdmin($query, $contracts) {
            try {
                return $query->whereIn('work_orders.contract_id', $contracts)
                ->where('work_orders.contract_type', 'regular');
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredWorkOrdersListForSpAdmin error: ".$th);
            }
        }

        public function getFiltredWorkOrdersListForSupervisor($query, $userId) {
            try {
                return $query->whereRaw("find_in_set($userId, work_orders.supervisor_id)");
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredWorkOrdersListForSupervisor error: ".$th);
            }
        }

        public function getFiltredWorkOrdersListForOtherUsers($query, $serviceProviderIds) {
            try {
                return $query->whereHas('contract', function ($innerQuery) use ($serviceProviderIds) {
                    $innerQuery->whereIn('service_provider_id', $serviceProviderIds);
                });
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredWorkOrdersListForOtherUsers error: ".$th);
            }
        }

        public function getFormatWorkOrderId($userId, $checkCreateButton, $data) {
            try {
                $url = '';

                if ($checkCreateButton) {
                    $url = route('workorder.show', $this->encryptDecryptedString($data->id));
                }

                $related_wos = $data->relatedWorkOrders()->count();
                $badgeClass = $related_wos > 1 ? 'badge-success' : 'badge-primary';
                $badgeClass = WorkorderHelper::hasUnresolvedRequestedItem($data->id) ? 'badge-warning' : $badgeClass;

                $styleOverride = '';

                if ($data->status == WorkOrderStatus::InProgress->value) {
                    $styleOverride = 'style="background: #e0f0ff; color: #5eb1ff; border-color: #5eb1ff"';
                }

                $unreadMsgNoti = '<span class="unreadMsgNoti">' . $data->setIsReadAttribute($userId) . '</span>';
                $workOrderIdLink = '<a href="' . $url . '"><span ' . $styleOverride . ' class="fw-400 badge badge-round ' . $badgeClass . ' badge-lg badge-outlined">' . $data->work_order_id . '</span></a>';
        
                return '<div class="d-flex">' . $unreadMsgNoti . '
                        <div class="userDatatable-inline-title">' . $workOrderIdLink . '</div>
                    </div>';
            }

            catch (\Throwable $th) {
                Log::error("getFormatWorkOrderId error: ".$th);
            }
        }

        public function getPropertiesForNotificationsListFromWO($userType, $contractId, $userId) {
            try {
                return WorkOrders::select('work_orders.property_id')
                ->where('work_orders.contract_type', 'regular')
                ->when(isset($userType) && $userType == "sp_admin", function ($query) use ($contractId) {
                    $query->whereIn('work_orders.contract_id', $contractId);
                }) 
                ->when(isset($userType) && $userType == "supervisor", function ($query) use ($userId) {
                    $query->whereRaw("find_in_set($userId, work_orders.supervisor_id)");
                }) 
                ->pluck('work_orders.property_id')
                ->toArray();
            } 
            
            catch (\Throwable $th) {
                Log::error("getPropertiesForNotificationsListFromWO error: ".$th);
            }
        }

        public function getPassFailColumn($passFail, $workorderJourney, $contractType, $status){
            if ($passFail <> 'pending') {
                $passFail = $passFail;
            } 
            
            elseif ($workorderJourney <> 'job_approval') {
                $passFail = 'pending';
            }

            else {
                $passFail = '-';
            }
    
            if ($contractType == "warranty" || $status == WorkOrderStatus::Deleted->value) {
                return '-';
            }
    
            if ($passFail == 'pass') {
                $color = 'color-white bg-success';
                $passFailStatus = __('work_order.bread_crumbs.pass');
            } 
            
            elseif ($passFail == 'fail') {
                $color = 'color-white bg-danger';
                $passFailStatus = __('work_order.bread_crumbs.fail');
            } 
            
            else {
                $color = 'color-white bg-light';
                $passFailStatus = __('work_order.bread_crumbs.pending');
            }
    
            return '<span class="media-badge ' . $color . '">' . $passFailStatus . '</span>';
        }

        public function getWorkOrdersListByValues($key, $value) {
            try {
                return WorkOrders::select('id', 'created_by', 'contract_type', 'description', 'workorder_journey', 'status')
                ->where($key, $value)
                ->orderBy('id', 'DESC')
                ->get();
            } 
            
            catch (\Throwable $th) {
                Log::error("getWorkOrdersListByValues error: ".$th);
            }
        }
    }
?>
