<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Auth;
use Carbon\Carbon;
use DateTime;
use DB;
use Log;
use Mail;
use PDF2;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Illuminate\Http\Request;
use App\Models\User;
use Helper;
use Response;
use Storage;
use ArPHP\I18N\Arabic;
use \App;
use Session;
use PDF;
use App\Http\Helpers\ReportQueryHelper;
use App\Models\Report;
use App\Traits\MediaFilesTrait;
use Mccarlosen\LaravelMpdf\Facades\LaravelMpdf;
use App\Exports\AdvanceContract\MonthlyCostReport;
use Maatwebsite\Excel\Facades\Excel;
use App\Traits\ManagesAssetsTrait;
use App\Models\Asset;

class SendAssetsReportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use MediaFilesTrait,ManagesAssetsTrait;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    private $session_data;
    private $logged_user;
    private $filename;
    private $report_number;
    private $lang;
    private $root;
    private $project_image_link;
    private $link_start;
    private $report_id;
    private $project_user_id;
    private $report;


    public function __construct($session_data,$logged_user, $report_number, $filename,$lang ,$root, $project_image_link ,$link_start,$report_id, $project_user_id, $report)
    {
        $this->session_data = $session_data;
        $this->logged_user = $logged_user;
        $this->report_number = $report_number;
        $this->filename = $filename;
        $this->lang = $lang;
        $this->root = $root;
        $this->project_image_link = $project_image_link;
        $this->link_start = $link_start;
        $this->report_id = $report_id;
        $this->project_user_id = $project_user_id;
        $this->report = $report;
    }




    public function handle()
    {
       
        Log::info("Assets Report Job Started ".$this->report);
        Log::info($this->session_data);

        $request = json_decode(json_encode($this->session_data), FALSE);

        $lang=$request->language;
        $report=$request->choose_type??'';
        $report_type=$request->file_type ?? 'csv';
        $filterText = $request->filter_text;
        $filterType = $request->asset_search_by;
        $propertiesFilter = $request->properties;
        $assetCategories = $request->asset_categories;


        app()->setLocale($lang);

        $user               = $this->logged_user;
        $report_number      = $this->report_number;
        $filename           = $this->filename;
        $project_image_link = $this->project_image_link;
        $link_start         = $this->link_start;
        $report_id          = $this->report_id;
        $project_user_id    = $this->project_user_id;
        //Log::info(["Assets Report get all request data=========>>>>>>>>>>.",$request]);
 
        $logedin_user=$user;

        //Log::info(["Assets Report get logedin_user=========>>>>>>>>>>.",$logedin_user]);


        $properties=$this->getProperties($logedin_user);

        //Log::info(["Assets Report get logedin_user type=========>>>>>>>>>>.",$logedin_user->user_type]);


        //Log::info(["Assets Report get properties=========>>>>>>>>>>."]);


        $data = Asset::select('assets.*', 'property_buildings.id as building_id', 'property_buildings.building_name as building_name', 'asset_names.asset_name as asset_name', 'asset_categories.asset_category as asset_category', 'properties.project as property_name')
             ->leftJoin('property_buildings', 'property_buildings.id', '=', 'assets.building_id')
             ->leftJoin('properties', 'properties.id', '=', 'assets.property_id')
             ->leftJoin('asset_names', 'asset_names.id', '=', 'assets.asset_name_id')
             ->leftJoin('asset_asset_category', 'asset_asset_category.asset_id', '=', 'assets.id')
             ->leftJoin('asset_categories', 'asset_categories.id', '=', 'asset_asset_category.asset_category_id')
             ->where('assets.is_deleted', 'no')
             ->whereIn('property_buildings.id', $properties->pluck('building_id'))
             ->when(collect($propertiesFilter)->isNotEmpty(), function ($query) use ($propertiesFilter) {
                return $query->whereIn('property_buildings.id', $propertiesFilter);
            })
             ->when(collect($assetCategories)->isNotEmpty(), function ($query) use ($assetCategories) {
                return $query->whereIn('asset_categories.id', $assetCategories);
            })
             ->when($filterText, function ($query, $filterText) use ($filterType) {
                 if ($filterType == 'Asset_Name') {
                     return $query->where('asset_names.asset_name', 'LIKE', "%$filterText%");
                 } else {
                     return $query->where('assets.asset_tag', 'LIKE', "%$filterText%");
                 }
             })
             ->groupBy('assets.id')
             ->get();

        //Log::info(["Assets Report get assets ===>>>>>>>>>>>".$data]);
             
        $html = view('applications.admin.assets-managements.templates.standard', [
            'data' => $data
        ])->render();

        //Log::info(["Assets Report get html ===>>>>>>>>>>>".$html]);

        $reader = IOFactory::createReader('Html');
        $spreadsheet = $reader->loadFromString($html);
        if($lang === 'ar'){
            $spreadsheet->getActiveSheet()->setRightToLeft(true);
        }
        $filePath = public_path('reports/'.$filename);
        $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save($filePath);
        $ociUrlFile = $this->pushMediasToOCI(
          $this->setModelLog('Report', $report_id),
          $filename,
          'reports',
          null,
          true
        );

        //Log::info(["Assets Report report file Path ===>>>>>>>>>>>".$filePath]);

        if(isset($ociUrlFile)){
          $file_link = $ociUrlFile;
        }else{
          $file_link = asset('reports').DIRECTORY_SEPARATOR.$filename;
        }

        $subject = 'New report has been generated';
        if ($lang=='en')
        {
        $mail_content['dir'] = 'ltr';
        }
        else{
        $mail_content['dir'] = 'rtl';
        }
        $user = $user;
        $to_name  = $user->name;
        $to_email = $user->email;
        $mail_content['name'] = $user->name;
        $mail_content['filename'] = $filename;
        $mail_content['subject_en'] = $subject;
        $mail_content['subject_ar'] = $subject;
        $mail_content['from'] =date('d/m/Y');
        $mail_content['to'] =date('d/m/Y');
        $mail_content['total_wo'] =count($data);
        $mail_content['report_file_link'] = $file_link;
        $mail_content['report_no'] = $report_number;
        $sender_email = \Helper::getAdminContactMail();

        if ($lang=='en')
        {
        $mail_content['label_report_no'] ='Report Number' ;
        $mail_content['label_report_heading'] = 'New report has been generated';
        $mail_content['label_starting_date'] = 'From';
        $mail_content['label_ending_date'] =  'To';
        $mail_content['label_total_no_wo'] =  'Number of Assets';
        $mail_content['label_report_file'] = 'Excel File';
        $mail_content['label_download'] =  'Download';
        $mail_content['label_email_footer_text'] = '*Note: This report is available to be downloaded during the next 24 hours.
        You can generate new reports anytime';
        }
        else{
        $mail_content['label_report_no'] ='رقم التقرير' ;
        $mail_content['label_report_heading'] ='تم إنشاء تقرير جديد ';
        $mail_content['label_starting_date'] =  'من';
        $mail_content['label_ending_date'] =  'إلى';
        $mail_content['label_total_no_wo'] = 'عدد الأصول';
        $mail_content['label_report_file'] =  'ملف Excel';
        $mail_content['label_download'] =  'تنزيل';
        $mail_content['label_email_footer_text'] =  '*ملاحظة: هذا التقرير متاح للتنزيل خلال ال24 ساعة القادمة فقط.
        يمكنك انشاء المزيد من التقارير في أي وقت. ';
        }

        $headers = array(
        'Content-Type: application/xlsx'
        );

        //Log::info(["Assets Report mail content ===>>>>>>>>>>>",$mail_content]);

        try {
        $rpt = ['generated_at'=>date('Y-m-d H:i:s'), 'status'=>'generated', 'oci_link' => $file_link];
        ReportQueryHelper::editReport($report_id, $rpt);
        $generated = true;
        } catch (\Exception $e) {
        $rpt = [ 'status'=>'failed'];
        $generated = false;
        ReportQueryHelper::editReport($report_id, $rpt);
        }
        if($generated){
        $notification_msg = $report_number.' Your generated report is ready ';
        $notification_msg_ar = $report_number.'  التقرير الخاص بك جاهز';
        $notificationData = [
        'user_id' => $user->id,
        'message' => $notification_msg,
        'message_ar' => $notification_msg_ar,
        'section_type' => 'report',
        'notification_sub_type' => 'report',
        'additional_param' => $filename,
        'created_at' => Carbon::now(),
        ];
        ReportQueryHelper::createNotification($notificationData);
        try {
        Mail::send('mail.reportGenerated', ['mail_content' => $mail_content]
        ,function ($message) use ($to_name, $to_email, $subject, $sender_email, $report_number,$report_id,$user,$filename) {
        $message->to($to_email, $to_name)
          ->subject($subject);
        $message->from($sender_email,'Osool Team');
        });
        }
        catch (\Exception $e) {
        }
        }
        Log::info("Assets Report Finished");
    }
}
