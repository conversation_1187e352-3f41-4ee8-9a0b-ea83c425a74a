<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
class Asset extends Model
{
    use HasFactory; use SoftDeletes;
    protected $table = 'assets';

    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'modified_at';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'property_id',
        'floor',
        'room',
        'unit_id',
        'asset_category_id',
        'asset_name_id',
        'asset_number',
        'barcode',
        'asset_status',
        'barcode_value',
        'barcode_select',
        'barcode_img_str',
        'asset_tag',
        'asset_symbol',
        'building_id',
        'purchase_date',
        'model_number',
        'manufacturer_name',
        'general_information',
        'gen_image',
        'hide_asset_symbol'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
    ];

    public function categories()
    {
        return $this->belongsToMany(AssetCategory::class, 'asset_asset_category', 'asset_id', 'asset_category_id');
    }

    public function assetCategory()
    {
        return $this->belongsTo(AssetCategory::class, 'asset_category_id');
    }

    public function assetCategories()
    {
        return $this->belongsToMany(AssetCategory::class, 'asset_asset_category', 'asset_id', 'asset_category_id');
    }

    public function assetName()
    {
        return $this->belongsTo(AssetName::class, 'asset_name_id');
    }

    public function workorder()
    {
        return $this->belongsTo(WorkOrders::class, 'asset_number_id');
    }

    public function files()
    {
        return $this->hasMany(AssetFile::class, 'assets_id');
    }

    public function property(){
        return $this->belongsTo(Property::class, 'property_id');
    }

    public function building(){
        return $this->belongsTo(PropertyBuildings::class, 'building_id');
    }
    // Relationship to custodians (users)
    public function custodians()
    {
        return $this->belongsToMany(User::class, 'asset_custodian')
                    ->withPivot('start_date', 'end_date', 'created_by');
    }

    public function assetHistory()
    {
        return $this->hasMany(AssetHistory::class, 'asset_id');
    }

   public function getAssetWorkOrderSummary()
    {
        $workorders = WorkOrders::where('asset_number_id', $this->id)
            ->selectRaw('status, COUNT(*) as total')
            ->groupBy('status')
            ->get()
            ->map(function ($item) {
                return [
                    'status' => $item->status,
                    'status_label' => WorkOrders::statusMap()[$item->status] ?? 'Unknown',
                    'total' => $item->total,
                ];
            })
            ->toArray();

        $totalCount = array_sum(array_column($workorders, 'total'));

        return [
            'workorders' => $workorders,
            'totalCount' => $totalCount,
        ];
    }


}
