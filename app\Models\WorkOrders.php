<?php

namespace App\Models;

use Akaunting\Api\Akaunting;
use Akaunting\Api\Data\InvoiceData;
use App\Enums\CachingTTL;
use App\Http\Controllers\Api\V1\Tenant\ApiHelper;
use App\Http\Helpers\WorkorderHelper;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Models\{User,Property,Contracts,ReopenWorkOrderDetail};
use App\Models\WorkOrderWorkerTiming;
use App\Models\WorkOrderWorkers;
use Auth;
use Illuminate\Contracts\Encryption\DecryptException;
use DB;
use Helper;
use App\Models\AssetNameAssetCategory;
use App\Models\AssetName;
use App\Models\NoChecklistAction;
use App\Models\WorkOrderItem;
use App\Models\ContractAssetCategories;
use App\Models\ContractPropertyBuildings;
use App\Models\AssetCategory;
use ImagesUploadHelper;
use App\Models\WorkerWorkOrderTimeTracking;
use App\Models\HasChecksum\HasChecksum;
use App\QueryBuilders\WorkOrderQueryBuilder;
use App\Enums\AssignType;
use App\Enums\AdvanceContract\ContractTypes;



/**
 * Class WorkOrders
 *
 * @property-read Contracts $contract
 */
class WorkOrders extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $table = 'work_orders';
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'modified_at';


    public static function statusMap(): array
    {
        return [
            1 => 'Open',
            2 => 'In Progress',
            3 => 'On Hold',
            4 => 'Closed',
            5 => 'Deleted',
            6 => 'Re Open',
            7 => 'Warranty',
            8 => 'Scheduled',
            9 => 'Pause',
        ];
    }
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
      'examine_button_clicked_at',
      'requested_missing_spare_parts',
      'pause_start_time',
      'status',
      'pause_time_spent',
      'pause_end_time',
      'pause_time_spent_minutes',
      'asset_category_id',
      'priority_id',
      'worker_id',
      'assigned_to',
      'worker_for_responsible',
      'job_started_at',
      'pass_fail',
      'workdo_task_id',
      'workdo_project_id'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
    ];

    public function invoices()
    {
        // this will be tricky, we have a junction table but in reality we just want the id's for the next api request
        // we make the api request here as well, I know it's ugly but meh

        $akaunting = new Akaunting();

        $invoiceIds = DB::table('workorder_invoices')->where('workorder_id', $this->id)->pluck('invoice_id')->toArray();
        $invoices = [];

        foreach ($invoiceIds as $invoiceId) {
            $response = $akaunting->invoices()->getInvoice($invoiceId, 'type:invoice');
            $invoices[] = InvoiceData::from($response->json('data', []));
        }

        return $invoices;
    }

    public function contract()
    {
        // Inner join-like behavior using `belongsTo`
        return $this->belongsTo(Contracts::class);
    }


    public function assetCategory()
    {
        // Inner join-like behavior using `belongsTo`
        return $this->belongsTo(AssetCategory::class);
    }

    public function assetName()
    {
        // Inner join-like behavior using `belongsTo`
        return $this->belongsTo(AssetName::class);
    }

    public function selectedAssetName()
    {
        // Inner join-like behavior using `belongsTo`
        return $this->belongsTo(AssetName::class,'asset_name_id');
    }

    public function asset()
    {
        // Left join-like behavior using `belongsTo` with optional second argument for foreign key
        return $this->belongsTo(Asset::class, 'asset_number_id');
    }

    // Define an accessor for the 'created_at' attribute
    public function getCreatedAtAttribute($value)
    {
        // Use the 'format' method to convert the date to 'Y-m-d H:i:s' format
        return Carbon::parse($value)->format('Y-m-d H:i:s');
    }

    // Define an accessor for the 'modified_at' attribute
    public function getModifiedAtAttribute($value)
    {
        // Use the 'format' method to convert the date to 'Y-m-d H:i:s' format
        return Carbon::parse($value)->format('Y-m-d H:i:s');
    }

    public function priority()
    {
        return $this->belongsTo(Priorities::class, 'asset_categories.priority_id');
    }

    public function propertyBuilding()
    {
        // Inner join-like behavior using `belongsTo`
        return $this->belongsTo(PropertyBuildings::class, 'property_id')->withTrashed();
    }

    public function worker()
    {
        return $this->belongsTo(User::class, 'worker_id');
    }
    public function AssignedSupervisor()
    {
        return $this->belongsTo(User::class, 'supervisor_id');
    }


    public function teamleader()
    {
        return $this->belongsTo(User::class, 'team_leader_id');
    }

    public function permanentWorker() {
      return $this->belongsTo(User::class, 'permanent_worker');
    }

    public function createdByUser()
    {
        // Inner join-like behavior using `belongsTo`
        return $this->belongsTo(User::class, 'created_by');
    }

    public static function getStatusString($status)
    {
        switch ($status) {
            case 1:
                return 'open';
            case 2:
                return 'progress';
            case 3:
                return 'hold';
            case 4:
                return 'closed';
            case 5:
                return 'deleted';
            case 6:
                return 'reopen';
            case 7:
                return 'warranty';
            case 8:
                return 'scheduled';
            default:
                return 'unknown';
        }
    }

    public function chatMessages()
    {
        return $this->hasMany(WorkOrderChat::class, 'work_order_id');
    }

    public function itemRequests()
    {
        return $this->hasOne(WorkOrderItemRequest::class, 'work_order_id');
    }

    public function serviceProviderItemRequests()
    {
        return $this->hasOne(ServiceProviderMissingItemRequest::class, 'work_order_id');
    }

    public function setIsReadAttribute($user_id)
    {
        $this->attributes['isread'] = $this->chatMessages()->where('receiver_id', $user_id)->where('is_read', 0)->count() > 0 ? '&bull;' : '';
    }

    public function relatedWorkOrders()
    {
        return $this->hasMany(WorkOrders::class, 'unique_id', 'unique_id')
            ->where('work_order_type', 'reactive');
    }
    public function NoChecklistSubtaskAction()
    {
        return $this->hasMany(NoChecklistSubtaskAction::class, 'work_order_id');
    }
    public function relatedPMWorkOrders()
    {
        return $this->hasMany(WorkOrders::class, 'unique_id', 'unique_id');
    }

    public function slaAssetCategory()
    {
        return $this->hasOne(ContractAssetCategories::class, 'asset_category_id', 'asset_category_id')
            ->orderByDesc('id');
    }

    public function checklist()
    {
        return $this->belongsTo(Checklists::class);
    }
    public function checklists()
    {
        return $this->belongsTo(Checklists::class);
    }

    public function closedBy()
    {
        return $this->belongsTo(User::class, 'closed_by');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function contractPriority()
    {
        return $this->belongsTo(ContractPriority::class, 'priority_id', 'priority_id');
    }

    public function frequencyMaster()
    {
        return $this->belongsTo(FrequencyMaster::class, 'frequency_id', 'id');
    }

    public function frequency()
    {
        return $this->belongsTo(FrequencyMaster::class, 'id');
    }

    public function work_order_priority()
    {
        return $this->belongsTo(Priorities::class, 'priority_id');
    }

    public function workTimeFrame()
    {
        return $this->hasOne(WorkTimeFrame::class, 'user_id', 'project_user_id');
    }

    public function projectSettings()
    {
        return $this->hasOne(ProjectSettings::class, 'user_id','project_user_id');
    }

    public function maintenanceRequest()
    {
        return $this->belongsTo(MaintenanceRequest::class, 'maintanance_request_id');
    }

    public function projectOwner()
    {
        return $this->belongsTo(User::class, 'project_user_id');
    }

    public function serviceWindowPriority()
    {
        return $this->belongsTo(Priorities::class, 'sla_service_window_priority');
    }

    public function serviceProviderAdmin()
    {
        return $this->belongsTo(User::class, 'service_provider_id');
    }

    public function woNoChecklistActions()
    {
        return $this->hasMany(NoChecklistAction::class, 'work_order_id');
    }

    // Relationship to fetch assigned workers from the work_order_workers table
    public function assignedWorkers()
    {
        return $this->belongsToMany(User::class, 'work_order_workers', 'work_order_id', 'worker_id');
    }

    // Relationship to fetch start and end times from the work_order_workers_timings table
    public function workerTimings()
    {
        return $this->hasMany(WorkOrderWorkerTiming::class, 'work_order_id');
    }

    public function serviceProvider()
    {
        return $this->belongsTo(ServiceProvider::class, 'service_provider');
    }

    /**
     * add checksums based on specific WO fields and compare them to determine if changes have been made.
     */
    public function hasChecksum()
    {
        return $this->morphMany(HasChecksum::class, 'checksumable');
    }

    public function workOrderItemRequests()
    {
        return $this->hasMany(WorkOrderItemRequest::class, 'work_order_id');
    }

    public function serviceProviderMissingItemRequests()
    {
        return $this->hasMany(ServiceProviderMissingItemRequest::class, 'work_order_id');
    }

    public function preventiveWoActions()
    {
        return $this->belongsTo(PreventiveWoAction::class, 'unique_id', 'unique_id');
    }

    public static function applyFilters($work_orders, $search)
    {
      if (!empty($search['preventive_title'])) {
          $searchValue = $search['preventive_title'];
          $work_orders->where('work_orders.pm_title', 'LIKE', "%{$searchValue}%");
      }

      $work_orders->when(!empty($search['passfail']) && $search['passfail'] !== 'all', function ($query) use ($search) {
          return $query->where('pass_fail', '=', $search['passfail']);
      });

      $work_orders->when(!empty($search['buildings']) && is_array($search['buildings']), function ($query) use ($search) {
          return $query->whereIn('work_orders.property_id', $search['buildings']);
      });

      $work_orders->when(!empty($search['type']) && $search['type'] !== 'all', function ($query) use ($search) {
          return $query->where('work_order_type', '=', $search['type']);
      });

      $work_orders->when(!empty($search['status']) && !in_array("0", $search['status']), function ($query) use ($search) {
          return $query->whereIn('work_orders.frequency_id', $search['status']);
      });

      $work_orders->when(!empty($search['dateRange']['startd']) && !empty($search['dateRange']['endd']), function ($query) use ($search) {
          return $query->whereBetween('work_orders.created_at', [
              $search['dateRange']['startd'],
              $search['dateRange']['endd']
          ]);
      });


        $work_orders = $work_orders->orderBy('work_orders.id', 'desc')->paginate(10);

        if(isset($search) && !empty($search))
        {
              $work_orders = $work_orders->appends($search);
        }
        $work_orders = json_decode(json_encode($work_orders), true);
        if(!empty($work_orders['data']))
        {
          foreach($work_orders['data'] as $key => $row)
          {
            //dd($row['unique_id']);
            $latest_wo = WorkOrders::where('unique_id', $row['unique_id'])->orderBy('id', 'desc')->first();
            $asset_cat = DB::table('asset_categories')->where('id', $latest_wo->asset_category_id)->orderBy('id', 'desc')->first();
            $work_orders['data'][$key]['pm_title'] = $latest_wo->pm_title;
            if(isset($asset_cat->asset_category))
            {
              $work_orders['data'][$key]['asset_category'] = $asset_cat->asset_category;
            }
            $property_buildings = DB::table('property_buildings')->where('id', $latest_wo->property_id)->orderBy('id', 'desc')->first();
            $properties = DB::table('properties')->where('id', $property_buildings->property_id)->orderBy('id', 'desc')->first();
            if(isset($property_buildings->building_name))
            {
              if($properties->complex_name)
              {
                $work_orders['data'][$key]['building_name'] = $property_buildings->building_name;
                $work_orders['data'][$key]['complex_name'] = $properties->complex_name;
              }
              else
              {
                $work_orders['data'][$key]['building_name'] = $property_buildings->building_name;
              }
            }
          }
        }
        return $work_orders;
    }
    // @flip2@ supervisor name
    public static function get_supervisor_name($supervisor_ids){
        if($supervisor_ids == null){
            // $message = "No supervisor assigned yet";
            return null;
        }
        else {
        $supervisor_ids = explode(",",$supervisor_ids);
        $supervisor_ids = array_filter($supervisor_ids, function($id){
            return $id != null;
        });
        // dd($supervisor_ids);
        $supervisor_names = User::whereIn('id', $supervisor_ids)->pluck('name');
        // dd($supervisor_names);

        // $supervisor_name = User::where('id', $supervisor_id)->value('name');
        return $supervisor_names;
    }
    }

    /**
     *
     * Get Work Orders Count
     *
     * @param $user_id, $service_provider_id is optional, $search is optional
     * @return string
     *
     */
    public static function get_work_orders_count($user_id, $service_provider_id = NULL, $search = NULL, $page=NULL)
    {
        //dd(Auth::user()->user_type);
        $date = date('Y-m-d');
        $response_time = 0;
        $service_window = 0;
        $work_orders = WorkOrders::select('work_orders.*', 'contracts.contract_number')
              ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
              ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
              ->join('users', 'users.id', '=', 'work_orders.created_by');


              if(isset($search['supervisors']) && !empty($search['supervisors'])){
                // $work_orders = $work_orders->whereRaw('FIND_IN_SET(?, work_orders.supervisor_id)', $search['supervisors']);
                    $supp = implode(',',$search['supervisors']);

                    $work_orders = $work_orders->where(function($query) use($supp) {
                      $query->whereRaw("(((work_orders.supervisor_id IS NOT NULL and work_orders.assigned_to = 'supervisor') || (work_orders.assigned_to = 'sp_worker' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) = 1)) AND work_orders.supervisor_id IN ($supp)AND (work_orders.assigned_to != 'sp_worker' || work_orders.worker_id != 0)) OR (work_orders.service_provider_id IN ($supp) AND (work_orders.assigned_to != 'sp_worker' || work_orders.worker_id != 0) AND work_orders.assigned_to != 'supervisor' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) > 1)");
                      });

              }

              if(isset($search['service_types']) && !empty($search['service_types']))
              {
                $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $search['service_types']);
              }

              if($page == NULL || $page == "work-order-list")
              {
                //$work_orders = $work_orders->where('work_orders.status', '!=', 4);
              }
              elseif($page == "open-workorders")
              {
                $work_orders = $work_orders->where('work_orders.status', 1);
                $work_orders = $work_orders->where('work_orders.workorder_journey', 'submitted');
              }
              elseif($page == "in-progress-workorders")
              {
                //$work_orders = $work_orders->where('work_orders.status', '!=', 4);
                $work_orders = $work_orders->where('work_orders.workorder_journey', 'job_execution');
              }
              elseif($page == "under-evaluation-workorders")
              {
                //$work_orders = $work_orders->where('work_orders.status', '!=', 4);
                $work_orders = $work_orders->whereRaw("(work_orders.workorder_journey = 'job_evaluation' OR work_orders.workorder_journey = 'job_approval')");
              }
              $work_orders = $work_orders->where('work_orders.is_deleted', '=', "no")
              ->where('work_orders.start_date', '<=', $date)
              //->where('work_orders.project_user_id', Auth::user()->project_user_id)
             // ->orderByRaw('work_orders.created_at asc');
             ->orderByRaw('work_orders.start_date desc');
        if(!empty(Auth::user()->user_type != 'sp_admin') && !empty(Auth::user()->user_type != 'supervisor')) //If logged in user type is not SP Admin and not Supervisor
        {
          $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);
        }

        if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager')) //If logged in user type is Building Manager or Building Manager Employee
        {
          $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
          $building_ids = explode(',', $bm_employees->building_ids);
          //print_r($bm_employees);exit;
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin')) //If logged in user type is SP Admin
        {
          $user_id = Auth::user()->id;
          //dd(Auth::user());
          $contract_id = Contracts::where('status', 1)->where('is_deleted', 'no')->where('service_provider_id', Auth::user()->service_provider)->pluck('id')->toArray();
          $work_orders = $work_orders->whereIn('work_orders.contract_id', $contract_id)->where('work_orders.contract_type', 'regular');
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor')) //If logged in user type is Supervisor
        {
          //dd($user_id);
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)")->where('work_orders.contract_type', 'regular');
        }
        elseif($service_provider_id) //If the logged in user is Admin or Osool Admin or Super Admin
        {
            $work_orders = $work_orders->where('contracts.service_provider_id', '=', $service_provider_id);
        }
        if(isset($search['buildings'])) //If searched for Pass or Fail
        {
          //dd($search['buildings']);
          $work_orders = $work_orders->whereIn('work_orders.property_id', $search['buildings']);
        }
        if($search['passfail']) //If searched for Pass or Fail
        {
          if($search['passfail'] != "all") //If Pass or Fail is not All
          {
            $work_orders = $work_orders->where('pass_fail', '=', $search['passfail']);
            //->where('workorder_journey', '=', 'job_approval');
          }
        }
        if($search['type']) //If searched for Work order type
        {
          if($search['type'] != "all") //If Work order type is not all
          {
            $work_orders = $work_orders->where('work_order_type', '=', $search['type']);
          }
        }



        if($search['status'] && is_array($search['status'])) //If the search is with Work Order Status
        {
          if(!in_array(0, $search['status'])) //If the Work Order Status array doesn't have 0
          {
            //dd($search['status']);
            if(in_array("7", $search['status'])) //If the Work Order Status array has 7
            {
              if(count($search['status']) == 1)
              {
                $work_orders = $work_orders->where('work_orders.status','!=',4)->where('work_orders.contract_type','=','warranty');
              }
              else
              {
                //dd($search['status']);
                //$work_orders = $work_orders->whereIn('work_orders.status',$search['status']);
                if (($key = array_search(7, $status)) !== false) {
                  unset($status[$key]);
                }
                //dd($status);
                $status = implode(',',$status);
                $work_orders = $work_orders->whereRaw("(work_orders.contract_type = 'warranty' OR work_orders.status IN ($status))");
              }
            }
            else{
              //dd($search['status']);
              $work_orders = $work_orders->whereIn('work_orders.status',$search['status']);
            }
          }
        }
        elseif($search['status']) //If searched for Work Order Status
        {
          if($search['status'] != "all") //If Work Order Status is not all
          {
            if($search['status'] == "7") //If Work Order Status is 7
            {
              $work_orders = $work_orders->where('work_orders.status','!=',4)->where('work_orders.contract_type','=','warranty');
            }
            else
            {
              $work_orders = $work_orders->where('work_orders.status', $search['status']);
            }
          }
        }
        if($search['dateRange'] && !empty($search['dateRange']['startd'])) //If searched with Date Range
        {
          //$work_orders= $work_orders->whereBetween('work_orders.created_at', [$search['dateRange']['startd'], $search['dateRange']['endd']]);
          $start_date = $search['dateRange']['startd'].' 00:00:00';
          $end_date = $search['dateRange']['endd'].' 23:59:59';
          //echo $start_date.' -- '.$end_date;exit;
          //$work_orders= $work_orders->whereBetween('work_orders.created_at', [$start_date, $end_date]);
          $work_orders = $work_orders->whereRaw("(IF(`work_orders`.`work_order_type` != 'preventive', work_orders.created_at, work_orders.start_date) Between '$start_date' AND '$end_date')");
        }
        if(isset($search['rating']) && !empty($search['rating']))
        {
          $work_orders = $work_orders->where('work_orders.rating', $search['rating'])->where('work_orders.status',4);
        }

        if(isset($search['wo_id']) && ($search['wo_id'] && $search['wo_id'] != null)){
          $s_wo_id = $search['wo_id'];
          $work_orders = $work_orders->whereRaw("(work_orders.work_order_id like '%$s_wo_id%' OR (work_orders.unique_id like '%$s_wo_id%' and work_orders.work_order_type = 'reactive'))");
        }
        $work_orders = $work_orders->count();
        return $work_orders;
    }

    public static function get_bm_work_orders_count($user_id)
    {
        $date = date('Y-m-d');
        $work_orders = WorkOrders::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by')->where('work_orders.is_deleted', '=', "no")->where('work_orders.start_date', '<=', $date);

        if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager')) //If the logged in User Type is Building Manager or Building Manager Employee
        {
          $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
          if(!empty($bm_employees->building_ids)) { //If the Building Manager or Building Manager Employee has Building IDs
            $building_ids = explode(',', $bm_employees->building_ids);
            //print_r($bm_employees);exit;
            $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
            $asset_categories = explode(',', Auth::user()->asset_categories);
            $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
            $work_orders = $work_orders->count();
          } else {
            $work_orders = 0;
          }
        }
        return $work_orders;
    }

    public static function get_sp_work_orders_count($user_id)
    {
      $date = date('Y-m-d');
      $work_orders = WorkOrders::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by')->where('work_orders.is_deleted', '=', "no")->where('work_orders.start_date', '<=', $date);
      if(!empty(Auth::user()->user_type == 'sp_admin')) //If the logged in User Type is SP Admin
      {
        $work_orders = $work_orders->where('work_orders.service_provider_id', $user_id);
      }
      elseif(!empty(Auth::user()->user_type == 'supervisor')) //If the logged in User Type is Supervisor
      {
        $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
      }
      $work_orders = $work_orders->count();
      return $work_orders;
    }

    public static function get_supervisor_work_orders_count($user_id)
    {
      $date = date('Y-m-d');
      $work_orders = WorkOrders::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by')->whereRaw("find_in_set($user_id, work_orders.supervisor_id)")->where('work_orders.is_deleted', '=', "no")->where('work_orders.start_date', '<=', $date)->count();
      return $work_orders;
    }

    public static function get_work_orders($user_id, $service_provider_id = NULL, $search=null, $page=null,$asset_id = null)
    {
      $response_time = 0;
      $service_window = 0;
      //dd(Auth::user()->user_type);
      $date = date('Y-m-d');
      //DB::enableQueryLog();
      $work_orders = WorkOrders::select('work_orders.*', 'contracts.contract_number','properties.property_type', 'properties.complex_name', 'property_buildings.building_name', 'worker.name as worker_name')
            ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
            ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
            ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
            ->join('users', 'users.id', '=', 'work_orders.created_by')
            ->leftjoin('users as worker', 'worker.id', '=', 'work_orders.worker_id');
            if($page == NULL || $page == "work-order-list")
            {
              //$work_orders = $work_orders->where('work_orders.status', '!=', 4);
            }
            elseif($page == "open-workorders")
            {
              $work_orders = $work_orders->where('work_orders.status', 1);
              $work_orders = $work_orders->where('work_orders.workorder_journey', 'submitted');
            }
            elseif($page == "in-progress-workorders")
            {
              //$work_orders = $work_orders->where('work_orders.status', '!=', 4);
              $work_orders = $work_orders->where('work_orders.workorder_journey', 'job_execution');
            }
            elseif($page == "under-evaluation-workorders")
            {
              //$work_orders = $work_orders->where('work_orders.status', '!=', 4);
              $work_orders = $work_orders->whereRaw("(work_orders.workorder_journey = 'job_evaluation' OR work_orders.workorder_journey = 'job_approval')");
            }
            $work_orders = $work_orders->where('work_orders.is_deleted', '!=', "yes")
            ->where('work_orders.start_date', '<=', $date)
            ->orderByRaw('work_orders.start_date desc');
      if(!empty(Auth::user()->user_type != 'sp_admin') && !empty(Auth::user()->user_type != 'supervisor')) //If the logged in User Type is not SP Admin and Supervisor
      {
        $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);
      }

      if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager')) // If the logged in User Type is Building Manager or Building Manager Employee
      {
        $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
        $building_ids = explode(',', $bm_employees->building_ids);
        //print_r($bm_employees);exit;
        $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
        $asset_categories = explode(',', Auth::user()->asset_categories);
        $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
      }
      elseif(!empty(Auth::user()->user_type == 'sp_admin')) //If the logged in User Type is SP Admin
      {
        $user_id = Auth::user()->id;
        //dd(Auth::user());
        $contract_id = Contracts::where('status', 1)->where('is_deleted', 'no')->where('service_provider_id', Auth::user()->service_provider)->pluck('id')->toArray();
        $work_orders = $work_orders->whereIn('work_orders.contract_id', $contract_id)->where('work_orders.contract_type', 'regular');
      }
      elseif(!empty(Auth::user()->user_type == 'supervisor')) //If the Logged in User Type is Supervisor
      {
        $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)")->where('work_orders.contract_type', 'regular');
      }
      elseif($service_provider_id) //If the Logged in user is Admin or Osool Admin or Super Admin
      {
          $work_orders = $work_orders->where('contracts.service_provider_id', '=', $service_provider_id);
      }
      if(isset($search['buildings'])) //If searched for Pass or Fail
      {
        //dd($search['buildings']);
        $work_orders = $work_orders->whereIn('work_orders.property_id', $search['buildings']);
      }
      if(isset($search['passfail'])) //If the Search with Pass or Fail
      {
        if($search['passfail'] == "pass" || $search['passfail'] == "fail") //If the search is for Pass or Fail
        {
          //die($search['passfail']);
          $work_orders = $work_orders->where('pass_fail', '=', $search['passfail']);
          //->where('workorder_journey', '=', 'job_approval');
        }
      }
      if(isset($search['type'])) //If the search is with Work order Type
      {
        if($search['type'] != "all") //If the Work Order Type is not All
        {
          $work_orders = $work_orders->where('work_order_type', '=', $search['type']);
        }
      }
      if(isset($search['status'])) //If the search is with Work Order Status
      {
        if(!in_array("0", $search['status'])) //If the Work Order Status array doesn't have 0
        {
          //dd($search['status']);
          if(in_array("7", $search['status'])) //If the Work Order Status array has 7
          {
            if(count($search['status']) == 1)
            {
              $work_orders = $work_orders->where('work_orders.status','!=',4)->where('work_orders.contract_type','=','warranty');
            }
            else
            {
              $status = $search['status'];
              //dd($search['status']);
              if (($key = array_search(7, $status)) !== false) {
                unset($status[$key]);
              }
              //dd($status);
              $status = implode(',',$status);
              $work_orders = $work_orders->whereRaw("(work_orders.contract_type = 'warranty' OR work_orders.status IN ($status))");
            }
          }
          else{
            //dd($search['status']);
            $work_orders = $work_orders->whereIn('work_orders.status',$search['status']);
          }
        }
      }

      if(isset($search['dateRange']) && !empty($search['dateRange']['startd'])) //If the search is with date range
      {
          $start_date = $search['dateRange']['startd'].' 00:00:00';
          $end_date = $search['dateRange']['endd'].' 23:59:59';
          //echo $start_date.' -- '.$end_date;exit;
          //$work_orders= $work_orders->whereBetween('work_orders.created_at', [$start_date, $end_date]);
          $work_orders = $work_orders->whereRaw("(IF(`work_orders`.`work_order_type` != 'preventive', work_orders.created_at, work_orders.start_date) Between '$start_date' AND '$end_date')");
      }
      if(isset($search['wo_id']) && $search['wo_id'] != null){
        $s_wo_id = $search['wo_id'];
        //$work_orders = $work_orders->where('work_orders.work_order_id', 'LIKE', '%'.$search['wo_id'].'%');
        $work_orders = $work_orders->whereRaw("(work_orders.work_order_id like '%$s_wo_id%' OR (work_orders.unique_id like '%$s_wo_id%' and work_orders.work_order_type = 'reactive'))");
      }
      else if(!empty(Auth::user()->user_type == 'supervisor')){
        $user_id = Auth::user()->id;
        $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)")->where('work_orders.contract_type', 'regular');
      }

      if(isset($search['supervisors']) && !empty($search['supervisors'])){
        $supp = implode(',',$search['supervisors']);
        $work_orders = $work_orders->where(function($query) use($supp) {
          $query->whereRaw("(((work_orders.supervisor_id IS NOT NULL and work_orders.assigned_to = 'supervisor') || (work_orders.assigned_to = 'sp_worker' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) = 1)) AND work_orders.supervisor_id IN ($supp)AND (work_orders.assigned_to != 'sp_worker' || work_orders.worker_id != 0)) OR (work_orders.service_provider_id IN ($supp) AND (work_orders.assigned_to != 'sp_worker' || work_orders.worker_id != 0) AND work_orders.assigned_to != 'supervisor' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) > 1)");
          });
      }

      if(isset($search['service_types']) && !empty($search['service_types']))
      {
        $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $search['service_types']);
      }
      if(isset($search['rating']) && !empty($search['rating']))
      {
        $work_orders = $work_orders->where('work_orders.rating', $search['rating'])->where('work_orders.status',4);
      }
      if(isset($asset_id)){
        $work_orders = $work_orders->where('work_orders.asset_number_id', $asset_id);
        $date = date('Y-m-d');
        $work_orders = $work_orders->where('work_orders.start_date','<=',$date);
      }
      if(isset($search['start']) && isset($search['length'])){
        $work_orders = $work_orders->skip($search['start'])->take($search['length'])->get();
      }
      else{
        $work_orders = $work_orders->get();
      }
      $work_orders = json_decode(json_encode($work_orders), true);
      if(!empty($work_orders))//If there are work orders with provided conditions
      {
        foreach($work_orders as $key => $wo)
        {
          $sla_asset_categories = DB::table('contract_asset_categories')
                    ->where('asset_category_id', $wo['asset_category_id'])
                    ->where('contract_number', $wo['contract_number'])
                    ->orderBy('id', 'desc')
                    ->first();

          //dd($sla_asset_categories);

          $response_time = 0;
          $service_window = 0;
          $response_time_type = 'hours';
          $service_window_type = 'minutes';
          if($wo['work_order_type'] == "reactive") //If the work order type is Reactive
          {
            if(!empty($sla_asset_categories->priority_id)){
              $contract_priorities = DB::table('contract_priorities')
                    ->where('priority_id', $sla_asset_categories->priority_id)
                    ->where('contract_number', $wo['contract_number'])
                    ->orderBy('id', 'desc')
                    ->first();
              $response_time = 0;
              $service_window = 0;
              $response_time_type = 'hours';
              $service_window_type = 'minutes';
              if(isset($contract_priorities->response_time))
              {
                $response_time = $contract_priorities->response_time;
                $service_window = $contract_priorities->service_window;
                $response_time_type = $contract_priorities->response_time_type;
                $service_window_type = $contract_priorities->service_window_type;
              }
            }
          }
          elseif($wo['work_order_type'] == "preventive" && $wo['priority_id'] != 0) //If the Work order Type is Preventive and Priority ID is not 0
          {
            //echo $wo['priority_id'].' -- '.$wo['contract_number'];exit;
            //echo $wo['work_order_id'];exit;
            $contract_priorities = DB::table('contract_priorities')
                  ->where('priority_id', $wo['priority_id'])
                  ->where('contract_number', $wo['contract_number'])
                  ->orderBy('id', 'desc')
                  ->first();
            //dd($contract_priorities);
            $response_time = 0;
            $service_window = 0;
            $response_time_type = 'hours';
            $service_window_type = 'minutes';
            if(isset($contract_priorities->response_time))
            {
              $response_time = $contract_priorities->response_time;
              $service_window = $contract_priorities->service_window;
              $response_time_type = $contract_priorities->response_time_type;
              $service_window_type = $contract_priorities->service_window_type;
            }
          }
          else
          {
            $contract_frequencies = DB::table('frequencies_master')
                      ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                      ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                      ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                      ->where('frequencies_master.id', $wo['frequency_id'])
                      ->where('contract_frequencies.contract_number', $wo['contract_number'])
                      ->first();
            $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
            $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : 0;
            $response_time_type = isset($contract_priorities->response_time_type)?$contract_priorities->response_time_type:'hours';
            $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
          }
          $wtfs = DB::table('work_time_frame')
                    ->select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
                    ->where('user_id', $wo['project_user_id'])
                    ->first();
          if($wo['wtf_start_time'] == ''){
            if(!isset($wtfs)) //If work order time frame has added
            {
              $time = "00:00:00";
            }
            else {
              $time = $wtfs->start_time;
            }
          }
          else{
            $time =$wo['wtf_start_time'];
          }
          $created_at = $wo['created_at'];
          if($wo['work_order_type'] == "preventive") //If the work order type is Preventive
          {
            $work_orders[$key]['created_at'] = $wo['start_date'].' '.$time;
            $created_at = $wo['start_date'].' '.$time;
          }
          $tdate = date('Y-m-d H:i:s');
          $datetime1 = strtotime($created_at);
          $datetime2 = strtotime($tdate);
          $interval  = abs($datetime2 - $datetime1);
          $minutes   = round($interval / 60);
          if($response_time_type == "days")
          {
            $response_time = $response_time * 1440;
          }
          elseif($response_time_type == "hours")
          {
            $response_time = $response_time * 60;
          }
          //echo $response_time .'-'. $minutes;exit;
          $time_left = $response_time - $minutes;

          if($wo['job_started_at'] == NULL || $wo['bm_approve_issue'] == 2) //If the job is not started
          {
            $target_date = $wo['target_date'];
            $work_orders[$key]['target_date'] = $target_date;
          }
          else
          {
            $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($wo['job_started_at'])));
            $work_orders[$key]['target_date'] = $target_date;

          }

          if($wo['pass_fail'] != 'pending')
          {
            $work_orders[$key]['pass_fail'] = $wo['pass_fail'];
          }
          elseif($wo['workorder_journey'] != 'job_approval') //If the job is started and the status is not closed and work order journey is not Job Approval
          {
            $work_orders[$key]['pass_fail'] = 'pending';
          }
          if($wo['response_time'] == 'On time' || $wo['response_time'] == 'Late') //If the response time is On time or Late
          {
            $work_orders[$key]['response_time'] = $wo['response_time'];
          }
          else
          {
            if($time_left > 0) //If the time left is greater than equal to 0
            {
              $work_orders[$key]['response_time'] = $time_left;
            }
            else
            {
              $work_orders[$key]['response_time'] = 'Late';
            }
          }
          $total = DB::table('work_order_chat')->where('receiver_id',$user_id)->where('work_order_id', $wo['id'])->where('is_read','0')->count();
          if($total>0) //If the work order chat count is greater than 0
          {
            $isread='&bull;';
          }
          else
          {
            $isread='';
          }
          $work_orders[$key]['isread'] = $isread;
        }
      }
      $work_orders = json_decode(json_encode($work_orders));
      return  $work_orders;
      /// For CLosed Status
    }

    // Closed work order
    public static function get_closed_work_orders_count($user_id, $service_provider_id = NULL, $search = NULL)
    {
        //dd(Auth::user()->user_type);
        $date = date('Y-m-d');
        $response_time = 0;
        $service_window = 0;
        $work_orders = WorkOrders::select('work_orders.*', 'contracts.contract_number')
              ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
              ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
              ->join('users', 'users.id', '=', 'work_orders.created_by');

              if(isset($search['supervisors']) && !empty($search['supervisors'])){
                $supp = $search['supervisors'];
                $supp = implode(',',$search['supervisors']);

                $work_orders = $work_orders->where(function($query) use($supp) {
                  $query->whereRaw("(((work_orders.supervisor_id IS NOT NULL and work_orders.assigned_to = 'supervisor') || (work_orders.assigned_to = 'sp_worker' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) = 1)) AND work_orders.supervisor_id IN ($supp)AND (work_orders.assigned_to != 'sp_worker' || work_orders.worker_id != 0)) OR (work_orders.service_provider_id IN ($supp) AND (work_orders.assigned_to != 'sp_worker' || work_orders.worker_id != 0) AND work_orders.assigned_to != 'supervisor' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) > 1)");
                  });
              }
              if(isset($search['service_types']) && !empty($search['service_types']))
              {
                $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $search['service_types']);
              }

              $work_orders =  $work_orders->where('work_orders.status', '=', 4)
              ->where('work_orders.is_deleted', '=', "no")
              ->where('work_orders.start_date', '<=', $date)
              //->where('work_orders.project_user_id', Auth::user()->project_user_id)
              ->orderByRaw('work_orders.created_at asc');
        if(!empty(Auth::user()->user_type != 'sp_admin') && !empty(Auth::user()->user_type != 'supervisor')) //If the User Type is not SP Admin and not Supervisor
        {
          $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);
        }

        if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager')) //If the logged in User Type is Building Manager or Building Manager Employee
        {
          $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
          $building_ids = explode(',', $bm_employees->building_ids);
          //print_r($bm_employees);exit;
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin')) //If the logged in User Type is SP Admin
        {
          $user_id = Auth::user()->id;
        //dd(Auth::user());
        $contract_id = Contracts::where('status', 1)->where('is_deleted', 'no')->where('service_provider_id', Auth::user()->service_provider)->pluck('id')->toArray();
        $work_orders = $work_orders->whereIn('work_orders.contract_id', $contract_id)->where('work_orders.contract_type', 'regular');
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor')) //If the logged in User Type is Supervisor
        {
          //dd($user_id);
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)")->where('work_orders.contract_type', 'regular');
        }
        elseif($service_provider_id)
        {
            $work_orders = $work_orders->where('contracts.service_provider_id', '=', $service_provider_id);
        }

        if($search['passfail'])
        {
            if($search['passfail'] != "all")
            {
                $work_orders = $work_orders->where('pass_fail', '=', $search['passfail']);
            }
        }
        if($search['type'])
        {
            if($search['type'] != "all")
            {
                $work_orders = $work_orders->where('work_order_type', '=', $search['type']);
            }
        }
        if($search['status']){
          if($search['status'] != "all")
          {
            if($search['status'] == "7")
            {

              $work_orders = $work_orders->where('work_orders.status','!=',4)->where('work_orders.contract_type','=','warranty');
            }
            else
            {
              $work_orders = $work_orders->where('work_orders.status', '=', $search['status']);
            }
          }
        }
        if($search['dateRange'] && !empty($search['dateRange']['startd'])){
            $work_orders= $work_orders->whereBetween('work_orders.created_at', [$search['dateRange']['startd'], $search['dateRange']['endd']]);
        }
        if(isset($search['rating']) && !empty($search['rating']))
        {
          $work_orders = $work_orders->where('work_orders.rating', $search['rating']);
        }
        $work_orders = $work_orders->count();
        return $work_orders;
    }


    public static function get_closed_work_orders($user_id, $service_provider_id = NULL, $search=null)
    {

        $response_time = 0;
        $service_window = 0;
        //dd(Auth::user()->user_type);
        $date = date('Y-m-d');
        //DB::enableQueryLog();
        /// For CLosed Status
        $work_orders_closed = WorkOrders::select('work_orders.*', 'contracts.contract_number','properties.property_type', 'properties.complex_name', 'property_buildings.building_name','worker.name as worker_name')
          ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
          ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
          ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
          ->join('users', 'users.id', '=', 'work_orders.created_by')
          ->leftjoin('users as worker', 'worker.id', '=', 'work_orders.worker_id');



          if(isset($search['supervisors']) && !empty($search['supervisors'])){
            $supp = $search['supervisors'];
            $supp = implode(',',$search['supervisors']);


            $work_orders_closed = $work_orders_closed->where(function($query) use($supp) {
              $query->whereRaw("(((work_orders.supervisor_id IS NOT NULL and work_orders.assigned_to = 'supervisor') || (work_orders.assigned_to = 'sp_worker' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) = 1)) AND work_orders.supervisor_id IN ($supp)AND (work_orders.assigned_to != 'sp_worker' || work_orders.worker_id != 0)) OR (work_orders.service_provider_id IN ($supp) AND (work_orders.assigned_to != 'sp_worker' || work_orders.worker_id != 0) AND work_orders.assigned_to != 'supervisor' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) > 1)");
              });
          }
          if(isset($search['service_types']) && !empty($search['service_types']))
          {
            $work_orders_closed = $work_orders_closed->whereIn('work_orders.asset_category_id', $search['service_types']);
          }
          $work_orders_closed = $work_orders_closed->where('work_orders.status', '=', 4)
          ->where('work_orders.is_deleted', '=', "no")
          ->where('work_orders.start_date', '<=', $date)
          ->orderByRaw('work_orders.target_date desc');


        if(!empty(Auth::user()->user_type != 'sp_admin') && !empty(Auth::user()->user_type != 'supervisor'))
        {
          $work_orders_closed = $work_orders_closed->where('work_orders.project_user_id', Auth::user()->project_user_id);
        }

        if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager'))
        {
          $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
          $building_ids = explode(',', $bm_employees->building_ids);
          //print_r($bm_employees);exit;
          $work_orders_closed = $work_orders_closed->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders_closed = $work_orders_closed->whereIn('work_orders.asset_category_id', $asset_categories);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          //dd(Auth::user());
          $contract_id = Contracts::where('status', 1)->where('is_deleted', 'no')->where('service_provider_id', Auth::user()->service_provider)->pluck('id')->toArray();
          $work_orders_closed = $work_orders_closed->whereIn('work_orders.contract_id', $contract_id)->where('work_orders.contract_type', 'regular');
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $work_orders_closed = $work_orders_closed->whereRaw("find_in_set($user_id, work_orders.supervisor_id)")->where('work_orders.contract_type', 'regular');
        }
        elseif($service_provider_id)
        {
            $work_orders_closed = $work_orders_closed->where('contracts.service_provider_id', '=', $service_provider_id);
        }
        if(isset($search['buildings'])) //If searched for Pass or Fail
        {
          //dd($search['buildings']);
          $work_orders_closed = $work_orders_closed->whereIn('work_orders.property_id', $search['buildings']);
        }
        //dd($search['passfail']);

        if($search['passfail'])
        {
          if($search['passfail'] == "pass" || $search['passfail'] == "fail")
          {
            $work_orders_closed = $work_orders_closed->where('pass_fail', '=', $search['passfail']);
          }
        }
        if($search['type'])
        {
          if($search['type'] != "all")
          {
            $work_orders_closed = $work_orders_closed->where('work_order_type', '=', $search['type']);
          }
        }
        if($search['status'])
        {
          if(!in_array("0", $search['status']))
          {
            //dd($search['status']);
            $work_orders_closed = $work_orders_closed->whereIn('work_orders.status',$search['status']);
          }
        }
        if($search['dateRange'] && !empty($search['dateRange']['startd'])){
          $start_date = $search['dateRange']['startd'].' 00:00:00';
          $end_date = $search['dateRange']['endd'].' 23:59:59';
          //echo $start_date.' -- '.$end_date;exit;
          $work_orders_closed = $work_orders_closed->whereBetween('work_orders.created_at', [$start_date, $end_date]);

          //$work_orders_closed= $work_orders_closed->whereBetween('work_orders.created_at', [$search['dateRange']['startd'], $search['dateRange']['endd']]);
        }
        if($search['wo_id'] && $search['wo_id'] != null){
          $s_wo_id = $search['wo_id'];
          $work_orders_closed = $work_orders_closed->whereRaw("(work_orders.work_order_id like '%$s_wo_id%' OR (work_orders.unique_id like '%$s_wo_id%' and work_orders.work_order_type = 'reactive'))");;
        }
        if(isset($search['rating']) && !empty($search['rating']))
        {
          $work_orders_closed = $work_orders_closed->where('work_orders.rating', $search['rating']);
        }
        //$work_orders_closed = $work_orders_closed->get();
        $work_orders_closed = $work_orders_closed->skip($search['start'])->take($search['length'])->get();
        $work_orders_closed = json_decode(json_encode($work_orders_closed), true);
        if(!empty($work_orders_closed))
        {
          foreach($work_orders_closed as $key => $wo)
          {

            $sla_asset_categories = DB::table('contract_asset_categories')
                      ->where('asset_category_id', $wo['asset_category_id'])
                      ->where('contract_number', $wo['contract_number'])
                      ->orderBy('id', 'desc')
                      ->first();
            $response_time = 0;
            $service_window = 0;
            $response_time_type = 'hours';
            $service_window_type = 'minutes';
            if($wo['work_order_type'] == "reactive")
            {
              if(!empty($sla_asset_categories->priority_id)){
                $contract_priorities = DB::table('contract_priorities')
                      ->where('priority_id', $sla_asset_categories->priority_id)
                      ->where('contract_number', $wo['contract_number'])
                      ->orderBy('id', 'desc')
                      ->first();
                $response_time = $contract_priorities->response_time;
                $service_window = $contract_priorities->service_window;
                $response_time_type = $contract_priorities->response_time_type;
                $service_window_type = $contract_priorities->service_window_type;
              }
            }
            elseif($wo['work_order_type'] == "preventive" && $wo['priority_id'] != 0)
            {
              $contract_priorities = DB::table('contract_priorities')
                    ->where('priority_id', $wo['priority_id'])
                    ->where('contract_number', $wo['contract_number'])
                    ->orderBy('id', 'desc')
                    ->first();
              if(isset($contract_priorities))
              {
                $response_time = $contract_priorities->response_time;
                $service_window = $contract_priorities->service_window;
                $response_time_type = $contract_priorities->response_time_type;
                $service_window_type = $contract_priorities->service_window_type;
              }
            }
            else
            {
              $contract_frequencies = DB::table('frequencies_master')
                        ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                        ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                        ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                        ->where('frequencies_master.id', $wo['frequency_id'])
                        ->where('contract_frequencies.contract_number', $wo['contract_number'])
                        ->first();
              $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
              $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : '';
              $response_time_type = isset($contract_priorities->response_time_type)?$contract_priorities->response_time_type:'hours';
              $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
            }
            $created_at = $wo['created_at'];
            $tdate = date('Y-m-d H:i:s');
            $datetime1 = strtotime($created_at);
            $datetime2 = strtotime($tdate);
            $interval  = abs($datetime2 - $datetime1);
            $minutes   = round($interval / 60);
            if($response_time_type == "days")
            {
              $response_time = $response_time * 1440;
            }
            elseif($response_time_type == "hours")
            {
              $response_time = $response_time * 60;
            }
            $time_left = $response_time - $minutes;

            if($wo['job_started_at'] == NULL || $wo['bm_approve_issue'] == 2)
            {
              $target_date = $wo['target_date'];
              $work_orders_closed[$key]['target_date'] = $target_date;
            }
            else
            {
              $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($wo['job_started_at'])));
              $work_orders_closed[$key]['target_date'] = $target_date;
            }
            if($wo['job_started_at'] != '' && $wo['status'] != 4 && $wo['workorder_journey'] != 'job_approval') //If the job is started and the status is not closed and work order journey is not Job Approval
            {
              if(strtotime($target_date) >= strtotime($tdate)) //If Target date is greater that today's date
              {
                $work_orders_closed[$key]['pass_fail'] = 'pass';
              }
              else
              {
                $work_orders_closed[$key]['pass_fail'] = 'fail';
              }
            }
            if($wo['response_time'] == 'On time' || $wo['response_time'] == 'Late')
            {
              $work_orders_closed[$key]['response_time'] = $wo['response_time'];
            }
            else
            {
              if($time_left >= 0)
              {
                $work_orders_closed[$key]['response_time'] = 'On time';
              }
              else
              {
                $work_orders_closed[$key]['response_time'] = 'Late';
              }
            }
          }
        }
        $filter = $search['passfail'];
        //dd($search['passfail']);
        if(!empty($filter)){
          if($filter == 'fail') {
            $work_orders_closed = array_filter($work_orders_closed, function ($var) {
              return ($var['pass_fail'] == 'fail');
            });
          }
          elseif($filter == 'pass') {
            $work_orders_closed = array_filter($work_orders_closed, function ($var) {
              return ($var['pass_fail'] == 'pass');
            });
          }
        }


        $work_orders_closed;

        return json_decode(json_encode($work_orders_closed));
    }

    // End Closed work order

    public static function get_bm_work_orders($user_id, $search)
    {
        $date = date('Y-m-d');
        //DB::enableQueryLog();
        $work_orders = WorkOrders::select('work_orders.*', 'contracts.contract_number')
                      ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                      // ->leftjoin('contract_asset_categories', 'contract_asset_categories.asset_category_id', '=', 'work_orders.asset_category_id')
                      // ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
                      // ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                      // ->leftjoin('contract_priorities', 'contract_priorities.priority_id', '=', 'contract_asset_categories.priority_id')
                      // ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'work_orders.frequency_id')
                      ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                      ->join('users', 'users.id', '=', 'work_orders.created_by')
                      //->where('work_orders.created_by', '=', $user_id)
                      ->where('work_orders.is_deleted', '=', "no")
                      ->where('work_orders.start_date', '<=', $date);
        // if(!empty(Auth::user()->user_type == 'building_manager'))
        // {
        //   $bm_employees = User::select('id')->where('sp_admin_id', $user_id)->where('status', 1)->where('is_deleted', 'no')->get()->pluck('id')->implode(',');
        //   $bm_employees = explode(',', $bm_employees);
        //   $bm_employees[] = $user_id;
        //   //print_r($bm_employees);exit;
        //   $work_orders = $work_orders->whereIn('work_orders.created_by', $bm_employees);
        // }
        if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager'))
        {
          $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
          $building_ids = explode(',', $bm_employees->building_ids);
          //print_r($bm_employees);exit;
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
        }
        if($search['passfail'])
        {
            if($search['passfail'] != "all")
            {
                $work_orders = $work_orders->where('pass_fail', '=', $search['passfail']);
            }
        }
        if($search['type'])
        {
            if($search['type'] != "all")
            {
                $work_orders = $work_orders->where('work_order_type', '=', $search['type']);
            }
        }
        if($search['status'])
        {
          // if($search['status'] == 7)
          // {
          //   $work_orders = $work_orders->where('work_orders.contract_type', '=', 'warranty');
          // }
          // elseif($search['status'] != "all")
          // {
          //   $work_orders = $work_orders->where('work_orders.status', '=', $search['status']);
          // }
          if(in_array("7", $search['status'])){
            $work_orders = $work_orders->where('work_orders.contract_type', '=', 'warranty');
          }
          elseif(!in_array("0", $search['status']))
          {
            //dd($search['status']);
            $work_orders = $work_orders->whereIn('work_orders.status',$search['status']);
          }
        }
        if($search['dateRange'] && !empty($search['dateRange']['startd'])){
            $work_orders= $work_orders->whereBetween('work_orders.created_at', [$search['dateRange']['startd'], $search['dateRange']['endd']]);
        }
        $work_orders = $work_orders->orderBy('work_orders.id', 'desc')->paginate(10);
        //dd(DB::getQueryLog());
        //print_r($work_orders);exit;
        //$work_orders = json_decode(json_encode($work_orders), true);
        $response_time_type = 'hours';
        $service_window_type = 'minutes';
        if(!empty($work_orders))
        {
          foreach($work_orders as $key => $wo)
          {

            $sla_asset_categories = DB::table('contract_asset_categories')
                      ->where('asset_category_id', $wo['asset_category_id'])
                      ->where('contract_number', $wo['contract_number'])
                      ->orderBy('id', 'desc')
                      ->first();

            if($wo['work_order_type'] == "reactive")
            {
              if(!empty($sla_asset_categories->priority_id)){
                $contract_priorities = DB::table('contract_priorities')
                      ->where('priority_id', $sla_asset_categories->priority_id)
                      ->where('contract_number', $wo['contract_number'])
                      ->orderBy('id', 'desc')
                      ->first();
                $response_time = $contract_priorities->response_time;
                $service_window = $contract_priorities->service_window;
                $response_time_type = $contract_priorities->response_time_type;
                $service_window_type = $contract_priorities->service_window_type;
              }
            }
            elseif($wo['work_order_type'] == "preventive" && $wo['priority_id'] != 0)
            {
              $contract_priorities = DB::table('contract_priorities')
                    ->where('priority_id', $wo['priority_id'])
                    ->where('contract_number', $wo['contract_number'])
                    ->orderBy('id', 'desc')
                    ->first();
              $response_time = $contract_priorities->response_time;
              $service_window = $contract_priorities->service_window;
              $response_time_type = $contract_priorities->response_time_type;
              $service_window_type = $contract_priorities->service_window_type;
            }
            else
            {
              $contract_frequencies = DB::table('frequencies_master')
                        ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                        ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                        ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                        ->where('frequencies_master.id', $wo['frequency_id'])
                        ->where('contract_frequencies.contract_number', $wo['contract_number'])
                        ->first();
                      $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
                      $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : '';
                      $response_time_type = isset($contract_frequencies->response_time_type) ? $contract_frequencies->response_time_type:'hours';
                      $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
            }
            $wtfs = DB::table('work_time_frame')
                    ->select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
                    ->where('user_id', $wo['project_user_id'])
                    ->first();
            if($wo['wtf_start_time'] == ''){
              if(!isset($wtfs)) //If work order time frame has added
              {
                $time = "00:00:00";
              }
              else {
                $time = $wtfs->start_time;
              }
            }
            else{
                $time =$wo['wtf_start_time'];

            }
            $created_at = $wo['created_at'];
            if($wo['work_order_type'] == "preventive") //If the work order type is Preventive
            {
              $work_orders[$key]['created_at'] = $wo['start_date'].' '.$time;
              $created_at = $wo['start_date'].' '.$time;
            }
            $tdate = date('Y-m-d H:i:s');
            $datetime1 = strtotime($created_at);
            $datetime2 = strtotime($tdate);
            $interval  = abs($datetime2 - $datetime1);
            $minutes   = round($interval / 60);
            if($response_time_type == "days")
            {
              $response_time = $response_time * 1440;
            }
            elseif($response_time_type == "hours")
            {
              $response_time = $response_time * 60;
            }
            $time_left = $response_time - $minutes;

            if($wo['job_started_at'] == NULL || $wo['bm_approve_issue'] == 2)
            {
              $target_date = $wo['target_date'];
              $work_orders[$key]['target_date'] = $target_date;
            }
            else
            {
              $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($wo['job_started_at'])));
              $work_orders[$key]['target_date'] = $target_date;
            }
            // if($wo['job_started_at'] != '' && $wo['status'] != 4 && $wo['workorder_journey'] != 'job_approval') //If the job is started and the status is not closed and work order journey is not Job Approval
            // {
            //   if(strtotime($target_date) >= strtotime($tdate)) //If Target date is greater that today's date
            //   {
            //     $work_orders[$key]['pass_fail'] = 'pass';
            //   }
            //   else
            //   {
            //     $work_orders[$key]['pass_fail'] = 'fail';
            //   }
            // }
            if($wo['pass_fail'] != 'pending')
            {
              $work_orders[$key]['pass_fail'] = $wo['pass_fail'];
            }
            elseif($wo['workorder_journey'] != 'job_approval') //If the job is started and the status is not closed and work order journey is not Job Approval
            {
              $work_orders[$key]['pass_fail'] = 'pending';
            }
            if($wo['response_time'] == 'On time' || $wo['response_time'] == 'Late')
            {
              $work_orders[$key]['response_time'] = $wo['response_time'];
            }
            else
            {
              if($time_left >= 0)
              {
                $work_orders[$key]['response_time'] = $time_left;
              }
              else
              {
                $work_orders[$key]['response_time'] = 'Late';
              }
            }
          }
        }
        $work_orders = json_decode(json_encode($work_orders), true);
        return $work_orders;
    }

    /**Manage preventive work order
     *  manage Building manager preventive work order
     */

     //Building manager start
    public static function get_bm_manage_pm_work_order_preventive($user_id, $search){
        $date = date('Y-m-d');
        //DB::enableQueryLog();
        // $work_orders = WorkOrders::select('work_orders.*','frequencies_master.title','asset_categories.asset_category','asset_names.asset_name')
        //               ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
        //               ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
        //               ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
        //               ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
        //               ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
        //               ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
        //               ->join('users', 'users.id', '=', 'work_orders.created_by')
        //               ->leftjoin('frequencies_master','frequencies_master.id','=','work_orders.frequency_id')
        //               ->where('work_orders.work_order_type', '=','preventive')
        //               ->where('work_orders.created_by', '=', $user_id)
        //               ->where('work_orders.is_deleted', '=', "no")
        //               ->where('work_orders.start_date', '<=', $date);
        $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
        $building_ids = explode(',', $bm_employees->building_ids);
        //print_r($bm_employees);exit;


        $work_orders = WorkOrders::select('work_orders.*','asset_categories.asset_category','asset_categories.deleted_at as asc_deleted_at','asset_categories.is_deleted as asc_is_deleted','asset_names.asset_name','frequencies_master.title','frequencies_master.title_ar', DB::raw('count(*) as wo_total'), DB::raw('MAX(work_orders.pm_end_date) as max_date_entered'), 'property_buildings.building_name','properties.complex_name', 'property_buildings.deleted_at as building_deleted_at' )
          ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
          ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
          ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
          ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
          ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
          ->leftjoin('frequencies_master','frequencies_master.id','=','work_orders.frequency_id')
          ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
          ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
          ->join('users', 'users.id', '=', 'work_orders.created_by')
          ->whereIn('work_orders.property_id', $building_ids)
          ->where('work_orders.work_order_type','=','preventive')
          ->where('work_orders.is_deleted', '!=', "yes")

          ->groupBy('unique_id');

          $work_orders = self::applyFilters($work_orders, $search);

          return $work_orders;

    }

    public static function get_bm_work_orders_preventive_count($user_id)
    {
        $date = date('Y-m-d');
        $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
        $building_ids = explode(',', $bm_employees->building_ids);


        $work_orders = WorkOrders::select('work_orders.*','asset_categories.asset_category','asset_names.asset_name','frequencies_master.title','frequencies_master.title_ar', DB::raw('count(*) as wo_total'), DB::raw('MAX(work_orders.start_date) as max_date_entered'))
                      ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                      ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
                      ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
                      ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                      ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
                      ->leftjoin('frequencies_master','frequencies_master.id','=','work_orders.frequency_id')
                      ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                      ->join('users', 'users.id', '=', 'work_orders.created_by')
                      // ->where('work_orders.created_by', '=', $user_id)
                      ->whereIn('work_orders.property_id', $building_ids)

                      ->where('work_orders.work_order_type','=','preventive')
                      ->where('work_orders.is_deleted', '=', "no")
                      //->where('work_orders.start_date', '<=', $date)
                      ->groupBy('unique_id');
        $work_orders = $work_orders->get();
        //dd(DB::getQueryLog());
        $work_orders = json_decode(json_encode($work_orders), true);
        //dd($work_orders);
        return $work_orders;
    }
    // end building manager

    public static function get_sp_manage_pm_work_order_preventive($user_id, $search)
    {
        $date = date('Y-m-d');
        $sp_admin_ids =self::get_sp_admin_id(Auth::user()->service_provider);
        $work_orders =
        WorkOrders::select('work_orders.*','asset_categories.asset_category','asset_categories.deleted_at as asc_deleted_at','asset_categories.is_deleted as asc_is_deleted','asset_names.asset_name','frequencies_master.title','frequencies_master.title_ar', DB::raw('count(*) as wo_total'), DB::raw('MAX(work_orders.start_date) as max_date_entered'), 'property_buildings.building_name','properties.complex_name', 'property_buildings.deleted_at as building_deleted_at' )
          ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
          ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
          ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
          ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
          ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
          ->leftjoin('frequencies_master','frequencies_master.id','=','work_orders.frequency_id')
          ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
          ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
          ->join('users', 'users.id', '=', 'work_orders.created_by');
          $work_orders = $work_orders->whereIn('work_orders.service_provider_id', $sp_admin_ids)
          ->where('work_orders.contract_type', 'regular');
          $work_orders = $work_orders->where('work_orders.work_order_type','=','preventive');
          $work_orders = $work_orders->where('work_orders.is_deleted', '=', "no");
          $work_orders = $work_orders->groupBy('unique_id');

          $work_orders = self::applyFilters($work_orders, $search);

          return $work_orders;
    }

    public static function get_pm_preventive_sp_work_orders_count($user_id)
    {
      $date = date('Y-m-d');
      $sp_admin_ids =self::get_sp_admin_id(Auth::user()->service_provider);
      $work_orders = WorkOrders::select('work_orders.*')
      ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
      ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
      ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
      ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
      ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
      ->join('users', 'users.id', '=', 'work_orders.created_by')
      ->where('work_orders.work_order_type', '=','preventive');
      $work_orders = $work_orders->where('work_orders.is_deleted', '=', "no");
      $work_orders = $work_orders->groupBy('unique_id');
      $work_orders = $work_orders->whereIn('work_orders.service_provider_id', $sp_admin_ids)
      ->where('work_orders.contract_type', 'regular');
      $work_orders = $work_orders->where('work_orders.is_deleted', '=', "no")
      ->get();
      return count($work_orders);
    }
    public static function get_sp_admin_id($service_provider_id)
    {
      $get_sp_admin = User::select('id', 'name')
      ->where('status', 1)
      ->where('is_deleted', 'no')
      ->where('service_provider', $service_provider_id)
      ->where('user_type', 'sp_admin')->withTrashed()
      ->pluck('id');
      return $get_sp_admin;
    }



    //end  sp admin count

    //start supervisor
    public static function get_pm_preventive_supervisor_work_orders_count($user_id)
    {
        $date = date('Y-m-d');

        $uid = Auth::user()->id;
        $work_orders = WorkOrders::select('work_orders.id')
                      // ->max('work_orders.start_date as sbn_last_date')
                      ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                      ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
                      ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
                      ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                      ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
                      ->leftjoin('frequencies_master','frequencies_master.id','=','work_orders.frequency_id')
                      ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                      ->join('users', 'users.id', '=', 'work_orders.created_by')
                      ->whereRaw("find_in_set($uid, work_orders.supervisor_id)")
                      ->where('work_orders.work_order_type','=','preventive')
                      ->where('work_orders.is_deleted', '=', "no")
                      //->where('work_orders.start_date', '<=', $date)
                      ->groupBy('unique_id')->get();

        return count($work_orders);
    }
    public static function get_supervisor_manage_pm_work_order_preventive($user_id, $search)
    {
        $date = date('Y-m-d');
        $uid = auth()->user()->id;
        $work_orders = WorkOrders::select('work_orders.*','asset_categories.asset_category','asset_categories.deleted_at as asc_deleted_at','asset_categories.is_deleted as asc_is_deleted','asset_names.asset_name','frequencies_master.title','frequencies_master.title_ar', DB::raw('count(*) as wo_total'), DB::raw('MAX(work_orders.start_date) as max_date_entered'), 'property_buildings.building_name','properties.complex_name', 'property_buildings.deleted_at as building_deleted_at' )
          ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
          ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
          ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
          ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
          ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
          ->leftjoin('frequencies_master','frequencies_master.id','=','work_orders.frequency_id')
          ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
          ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
          ->join('users', 'users.id', '=', 'work_orders.created_by')
          ->whereRaw("find_in_set($uid, work_orders.supervisor_id)")
          ->where('work_orders.work_order_type','=','preventive')
          ->where('work_orders.is_deleted', '=', "no")
          ->groupBy('unique_id');

          $work_orders = self::applyFilters($work_orders, $search);
          return $work_orders;
    }

    //end supervisor

    // start admin
    public static function get_pm_preventive_work_orders_count($service_provider_id)
    {
      $date = date('Y-m-d');
      $work_orders = WorkOrders::select('work_orders.*')
        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
        ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
        ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
        ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
        ->join('users', 'users.id', '=', 'work_orders.created_by')
        ->where('work_orders.work_order_type', '=','preventive');
        $work_orders = $work_orders->where('work_orders.is_deleted', '=', "no");
        $work_orders = $work_orders->groupBy('unique_id');
        $work_orders = $work_orders->where('contracts.service_provider_id', '=', $service_provider_id)->where('work_orders.project_user_id', Auth::user()->project_user_id)->where('work_orders.contract_type', 'regular');
        $work_orders = $work_orders->where('work_orders.is_deleted', '=', "no")
        ->get();
      return count($work_orders);
    }

    public static function get_pm_preventive_admin_work_orders($service_provider_id, $search)
    {
        $date = date('Y-m-d');
        $work_orders = WorkOrders::select('work_orders.*','asset_categories.asset_category','asset_categories.deleted_at as asc_deleted_at','asset_categories.is_deleted as asc_is_deleted','asset_names.asset_name','frequencies_master.title','frequencies_master.title_ar', DB::raw('count(*) as wo_total'), DB::raw('MAX(work_orders.start_date) as max_date_entered'), 'property_buildings.building_name','properties.complex_name', 'property_buildings.deleted_at as building_deleted_at' )
          ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
          ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
          ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
          ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
          ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
          ->leftjoin('frequencies_master','frequencies_master.id','=','work_orders.frequency_id')
          ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
          ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
          ->join('users', 'users.id', '=', 'work_orders.created_by');
          $work_orders = $work_orders->where('contracts.service_provider_id', '=', $service_provider_id)->where('work_orders.project_user_id', Auth::user()->project_user_id)->where('work_orders.contract_type', 'regular');
          $work_orders = $work_orders->where('work_orders.work_order_type','=','preventive');
          $work_orders = $work_orders->where('work_orders.is_deleted', '=', "no");
          $work_orders = $work_orders->groupBy('unique_id');

          $work_orders = self::applyFilters($work_orders, $search);
          return $work_orders;
    }

    //super admin get_pm_preventive_superadmin_work_orders_count
    public static function get_pm_sa_preventive_work_orders_count($user_id)
    {
        $date = date('Y-m-d');
        $work_orders = WorkOrders::select('work_orders.*','asset_categories.asset_category','asset_names.asset_name','frequencies_master.title','frequencies_master.title_ar', DB::raw('count(*) as wo_total'), DB::raw('MAX(work_orders.start_date) as max_date_entered'))
                      ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                      ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
                      ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
                      ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                      ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
                      ->leftjoin('frequencies_master','frequencies_master.id','=','work_orders.frequency_id')

                      ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                      ->join('users', 'users.id', '=', 'work_orders.created_by')
                      //->where('work_orders.created_by', '=', $user_id)
                      ->where('work_orders.work_order_type','=','preventive')
                      ->where('work_orders.project_user_id',$user_id)
                      ->where('work_orders.is_deleted', '=', "no")
                      //->where('work_orders.start_date', '<=', $date)
                      ->groupBy('unique_id');
        $work_orders = $work_orders->get();
        //dd(DB::getQueryLog());
        $work_orders = json_decode(json_encode($work_orders), true);
        //dd($work_orders);
        return $work_orders;
    }
    public static function get_pm_preventive_superadmin_work_orders($user_id, $search)
    {
        $date = date('Y-m-d');
        //DB::enableQueryLog();

        $work_orders = WorkOrders::select('work_orders.*','asset_categories.asset_category','asset_categories.deleted_at as asc_deleted_at','asset_categories.is_deleted as asc_is_deleted','asset_names.asset_name','frequencies_master.title','frequencies_master.title_ar', DB::raw('count(*) as wo_total'), DB::raw('MAX(work_orders.start_date) as max_date_entered'),'properties.complex_name','property_buildings.building_name' , 'property_buildings.deleted_at as building_deleted_at')
                      ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                      ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
                      ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
                      ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                      ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
                      ->leftjoin('frequencies_master','frequencies_master.id','=','work_orders.frequency_id')

                      ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                      ->leftjoin('properties', 'properties.id', '=', 'property_buildings.property_id')

                      ->join('users', 'users.id', '=', 'work_orders.created_by')
                      //->where('work_orders.created_by', '=', $user_id)
                      ->where('work_orders.work_order_type','=','preventive')
                      ->where('work_orders.project_user_id',$user_id)
                      ->where('work_orders.is_deleted', '=', "no")
                      //->where('work_orders.start_date', '<=', $date)
                      ->groupBy('unique_id');
        if($search['passfail'])
        {
            if($search['passfail'] != "all")
            {
                $work_orders = $work_orders->where('pass_fail', '=', $search['passfail']);
            }
        }
        if (isset($search['buildings']) && is_array($search['buildings']))
        {
          $work_orders = $work_orders->whereIn('work_orders.property_id', $search['buildings']);
        }
        if($search['type'])
        {
            if($search['type'] != "all")
            {
                $work_orders = $work_orders->where('work_order_type', '=', $search['type']);
            }
        }
        if($search['status']){
            if(!in_array("0", $search['status']))
            {
              //dd($search['status']);
              $work_orders = $work_orders->whereIn('work_orders.frequency_id', $search['status']);
            }
        }
        if($search['dateRange'] && !empty($search['dateRange']['startd'])){
            $work_orders= $work_orders->whereBetween('work_orders.created_at', [$search['dateRange']['startd'], $search['dateRange']['endd']]);
        }
        $work_orders = $work_orders->orderBy('work_orders.id', 'desc')->paginate(10);
        if(isset($search) && !empty($search))
        {
              $work_orders = $work_orders->appends($search);
        }
        //dd(DB::getQueryLog());
        $work_orders = json_decode(json_encode($work_orders), true);
        if(!empty($work_orders['data']))
        {
          foreach($work_orders['data'] as $key => $row)
          {
            //dd($row['unique_id']);
            $latest_wo = WorkOrders::where('unique_id', $row['unique_id'])->orderBy('id', 'desc')->first();
            $asset_cat = DB::table('asset_categories')->where('id', $latest_wo->asset_category_id)->orderBy('id', 'desc')->first();
            $work_orders['data'][$key]['pm_title'] = $latest_wo->pm_title;
            if(isset($asset_cat->asset_category))
            {
              $work_orders['data'][$key]['asset_category'] = $asset_cat->asset_category;
            }
            $property_buildings = DB::table('property_buildings')->where('id', $latest_wo->property_id)->orderBy('id', 'desc')->first();
            $properties = DB::table('properties')->where('id', $property_buildings->property_id)->orderBy('id', 'desc')->first();
            if(isset($property_buildings->building_name))
            {
              if($properties->complex_name)
              {
                $work_orders['data'][$key]['building_name'] = $property_buildings->building_name;
                $work_orders['data'][$key]['complex_name'] = $properties->complex_name;
              }
              else
              {
                $work_orders['data'][$key]['building_name'] = $property_buildings->building_name;
              }
            }
          }
        }
        return $work_orders;
    }

    /**end
     * building manager preventive manage work order
     */
    public static function get_pending_bm_work_orders_count($user_id, $search=[])
    {
        $date = date('Y-m-d');
        //DB::enableQueryLog();
        $work_orders = WorkOrders::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by');

        if(isset($search['supervisors']) && !empty($search['supervisors'])){
          $supp = $search['supervisors'];
          $supp = implode(',',$search['supervisors']);

          $work_orders = $work_orders->where(function($query) use($supp) {
            $query->whereRaw("(((work_orders.supervisor_id IS NOT NULL and work_orders.assigned_to = 'supervisor') || (work_orders.assigned_to = 'sp_worker' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) = 1)) AND work_orders.supervisor_id IN ($supp)AND (work_orders.assigned_to != 'sp_worker' || work_orders.worker_id != 0)) OR (work_orders.service_provider_id IN ($supp) AND (work_orders.assigned_to != 'sp_worker' || work_orders.worker_id != 0) AND work_orders.assigned_to != 'supervisor' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) > 1)");
            });

        }
        if(isset($search['service_types']) && !empty($search['service_types']))
        {
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $search['service_types']);
        }
        $work_orders=$work_orders->where('work_orders.start_date', '<=', $date);

        if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager'))
        {
          $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
          if(!empty($bm_employees->building_ids)) {
            $building_ids = explode(',', $bm_employees->building_ids);
          } else {
            $building_ids = [];
          }

            //print_r($bm_employees);exit;
            $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
            $asset_categories = explode(',', Auth::user()->asset_categories);
            $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
            $work_orders = $work_orders->whereRaw("((work_orders.contract_type = 'warranty' and work_orders.status != 4)  OR ((work_orders.contract_type = 'regular' and work_orders.status = 1) and work_orders.proposed_new_date IS NOT NULL and work_orders.bm_approve_issue = 0)  OR ((work_orders.contract_type = 'regular' and (work_orders.status = 2 OR work_orders.status = 6) and work_orders.workorder_journey = 'job_approval') and (work_orders.sp_approve_job = 2 or work_orders.sp_approve_job = 3) and work_orders.bm_approve_job = 0) OR ((work_orders.contract_type = 'regular' and work_orders.status = 6 and work_orders.workorder_journey = 'submitted') && work_orders.sp_reopen_status = 1))");

        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
            $user_id = Auth::user()->id;
            $contract_id = Contracts::where('status', 1)->where('is_deleted', 'no')->where('service_provider_id', Auth::user()->service_provider)->pluck('id')->toArray();
            $work_orders = $work_orders->whereIn('work_orders.contract_id', $contract_id);
            //$work_orders = $work_orders->where('work_orders.service_provider_id', '=', $user_id);
            $work_orders = $work_orders->whereRaw("(((work_orders.contract_type = 'regular' and work_orders.status = 1) and work_orders.proposed_new_date IS NULL and work_orders.bm_approve_issue = 0) OR ((work_orders.contract_type = 'regular' and work_orders.status = 1) and work_orders.proposed_new_date IS NOT NULL and work_orders.bm_approve_issue = 1) OR ((work_orders.contract_type = 'regular' and (work_orders.status = 6) and work_orders.workorder_journey = 'job_execution')) OR ((work_orders.contract_type = 'regular' and (work_orders.status = 6) and work_orders.workorder_journey = 'job_evaluation') and work_orders.sp_approve_job != 1) OR ((work_orders.contract_type = 'regular' and work_orders.status = 6 and work_orders.workorder_journey = 'submitted') and work_orders.sp_reopen_status = 0))");
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
            $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
            $work_orders = $work_orders->whereRaw("(((work_orders.contract_type = 'regular' and work_orders.status = 1) and work_orders.proposed_new_date IS NULL and work_orders.bm_approve_issue = 0) OR ((work_orders.contract_type = 'regular' and work_orders.status = 1) and work_orders.proposed_new_date IS NOT NULL and work_orders.bm_approve_issue = 1) OR ((work_orders.contract_type = 'regular' and (work_orders.status = 6) and work_orders.workorder_journey = 'job_execution')) OR ((work_orders.contract_type = 'regular' and (work_orders.status = 6) and work_orders.workorder_journey = 'job_evaluation') and work_orders.sp_approve_job != 1) OR ((work_orders.contract_type = 'regular' and work_orders.status = 6 and work_orders.workorder_journey = 'submitted') and work_orders.sp_reopen_status = 0))");
        }

        if(isset($search['buildings'])) //If searched for Pass or Fail
        {
          //dd($search['buildings']);
          $work_orders = $work_orders->whereIn('work_orders.property_id', $search['buildings']);
        }
        if(isset($search['passfail']))
        {
            if($search['passfail'] != "all")
            {
                $work_orders = $work_orders->where('pass_fail', '=', $search['passfail']);
            }
        }
        if(isset($search['type']))
        {
            if($search['type'] != "all")
            {
                $work_orders = $work_orders->where('work_order_type', '=', $search['type']);

            }
        }
        if(isset($search['status'])){
            if($search['status'] != "all")
            {
                $work_orders = $work_orders->where('work_orders.status', '=', $search['status']);
            }
        }
        if(isset($search['dateRange']) && !empty($search['dateRange']['startd'])){
          $start_date = $search['dateRange']['startd'].' 00:00:00';
          $end_date = $search['dateRange']['endd'].' 23:59:59';
          //echo $start_date.' -- '.$end_date;exit;
          //$work_orders = $work_orders->whereBetween('work_orders.created_at', [$start_date, $end_date]);
          $work_orders = $work_orders->whereRaw("(IF(`work_orders`.`work_order_type` != 'preventive', work_orders.created_at, work_orders.start_date) Between '$start_date' AND '$end_date')");
        }
        if(isset($search['wo_id']) && $search['wo_id'] != null){
          $s_wo_id = $search['wo_id'];
          $work_orders = $work_orders->whereRaw("(work_orders.work_order_id like '%$s_wo_id%' OR (work_orders.unique_id like '%$s_wo_id%' and work_orders.work_order_type = 'reactive'))");
        }
        if(isset($search['service_types']) && !empty($search['service_types']))
        {
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $search['service_types']);
        }
        $work_orders = $work_orders->count();
        //dd(DB::getQueryLog());
        return $work_orders;
    }

    public static function get_all_work_orders_count($user_id, $url=null)
    {

      if(trim(array_reverse(explode('/',$url))[0]) == "" || array_reverse(explode('/',$url))[0] == "closed")
      {
        $service_provider_id = 'no';
      }
      else
      {
        try
        {
          $service_provider_id = \Crypt::decryptString(trim(array_reverse(explode('/',$url))[0]));
          if(trim(array_reverse(explode('/',$url))[1]) != 'work-order' && trim(array_reverse(explode('/',$url))[2]) != 'work-order')
          {
            $service_provider_id = 'no';
          }
        }
        catch(DecryptException $e)
        {
          $service_provider_id = 'no';
        }
        if(trim(array_reverse(explode('/',$url))[1]) == 'work-order' && (!empty(Auth::user()->user_type == 'super_admin') || !empty(Auth::user()->user_type == 'osool_admin') || !empty(Auth::user()->user_type == 'admin_employee') || !empty(Auth::user()->user_type == 'admin')))
        {
          $service_provider_id = 'no';
        }
      }

      $date = date('Y-m-d');
      $response_time = 0;
      $service_window = 0;
      $work_orders = WorkOrders::select('work_orders.*', 'contracts.contract_number')
            ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
            ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
            ->join('users', 'users.id', '=', 'work_orders.created_by');

            //$work_orders = $work_orders->where('work_orders.status', '!=', 4);

            $work_orders = $work_orders->where('work_orders.is_deleted', '=', "no")
            ->where('work_orders.start_date', '<=', $date)
            //->where('work_orders.project_user_id', Auth::user()->project_user_id)
           // ->orderByRaw('work_orders.created_at asc');
           ->orderByRaw('work_orders.start_date desc');
      if(!empty(Auth::user()->user_type != 'sp_admin') && !empty(Auth::user()->user_type != 'supervisor')) //If logged in user type is not SP Admin and not Supervisor
      {
        $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);
      }
      // if(!empty(Auth::user()->user_type == 'building_manager'))
      // {
      //   //dd($user_id);
      //   $bm_employees = User::select('id')->where('sp_admin_id', $user_id)->where('status', 1)->where('is_deleted', 'no')->get()->pluck('id')->implode(',');
      //   //dd($bm_employees);
      //   $bm_employees = explode(',', $bm_employees);
      //   $bm_employees[] = $user_id;
      //   //print_r($bm_employees);exit;
      //   $work_orders = $work_orders->whereIn('work_orders.created_by', $bm_employees);
      // }
      if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager')) //If logged in user type is Building Manager or Building Manager Employee
      {
        $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
        $building_ids = explode(',', $bm_employees->building_ids);
        //print_r($bm_employees);exit;
        $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
        $asset_categories = explode(',', Auth::user()->asset_categories);
        $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
      }
      elseif(!empty(Auth::user()->user_type == 'sp_admin')) //If logged in user type is SP Admin
      {
        $user_id = Auth::user()->id;
        //dd(Auth::user());
        $contract_id = Contracts::where('status', 1)->where('is_deleted', 'no')->where('service_provider_id', Auth::user()->service_provider)->pluck('id')->toArray();
        $work_orders = $work_orders->whereIn('work_orders.contract_id', $contract_id)->where('work_orders.contract_type', 'regular');
      }
      elseif(!empty(Auth::user()->user_type == 'supervisor')) //If logged in user type is Supervisor
      {
        //dd($user_id);
        $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)")->where('work_orders.contract_type', 'regular');
      }
      elseif($service_provider_id != 'no')
      {
        $work_orders = $work_orders->where('contracts.service_provider_id', '=', $service_provider_id);
      }
      if(Auth::user()->user_type == 'super_admin' || Auth::user()->user_type == 'admin_employee' || Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'osool_admin')
      {
        if($service_provider_id != 'no')
        {
          $work_orders = $work_orders->where('contracts.service_provider_id', '=', $service_provider_id)->where('work_orders.project_user_id', Auth::user()->project_user_id)
          ;
          //$work_orders = $work_orders->where('work_orders.status', '!=', 4);

        }
        else{

          $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);
          //$work_orders = $work_orders->where('work_orders.status', '!=', 4);
        }
      }
      // dd(Auth::user()->project_user_id);
      // dd($work_orders->pluck('project_user_id'));

      $work_orders = $work_orders->count();
      //dd($work_orders);
      //dd(DB::getQueryLog());
      return $work_orders;
    }

    public static function get_open_work_orders_count($user_id, $url=null)
    {
      if(trim(array_reverse(explode('/',$url))[0]) == "" || array_reverse(explode('/',$url))[0] == "closed")
      {
        $service_provider_id = 'no';
      }
      else
      {
        try
        {
          $service_provider_id = \Crypt::decryptString(trim(array_reverse(explode('/',$url))[0]));
          if(trim(array_reverse(explode('/',$url))[1]) != 'work-order' && trim(array_reverse(explode('/',$url))[2]) != 'work-order')
          {
            $service_provider_id = 'no';
          }
        }
        catch(DecryptException $e)
        {
          $service_provider_id = 'no';
        }
        if(trim(array_reverse(explode('/',$url))[1]) == 'work-order' && (!empty(Auth::user()->user_type == 'super_admin') || !empty(Auth::user()->user_type == 'osool_admin') || !empty(Auth::user()->user_type == 'admin_employee') || !empty(Auth::user()->user_type == 'admin')))
        {
          $service_provider_id = 'no';
        }
      }
        $date = date('Y-m-d');
        $response_time = 0;
        $service_window = 0;
        $work_orders = WorkOrders::select('work_orders.*', 'contracts.contract_number')
              ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
              ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
              ->join('users', 'users.id', '=', 'work_orders.created_by');

              $work_orders = $work_orders->where('work_orders.status', 1);
              $work_orders = $work_orders->where('work_orders.workorder_journey', 'submitted');

              $work_orders = $work_orders->where('work_orders.is_deleted', '=', "no")
              ->where('work_orders.start_date', '<=', $date)
              //->where('work_orders.project_user_id', Auth::user()->project_user_id)
             // ->orderByRaw('work_orders.created_at asc');
             ->orderByRaw('work_orders.start_date desc');
        if(!empty(Auth::user()->user_type != 'sp_admin') && !empty(Auth::user()->user_type != 'supervisor')) //If logged in user type is not SP Admin and not Supervisor
        {
          $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);
        }

        if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager')) //If logged in user type is Building Manager or Building Manager Employee
        {
          $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
          $building_ids = explode(',', $bm_employees->building_ids);
          //print_r($bm_employees);exit;
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin')) //If logged in user type is SP Admin
        {
          $user_id = Auth::user()->id;
          //dd(Auth::user());
          $contract_id = Contracts::where('status', 1)->where('is_deleted', 'no')->where('service_provider_id', Auth::user()->service_provider)->pluck('id')->toArray();
          $work_orders = $work_orders->whereIn('work_orders.contract_id', $contract_id)->where('work_orders.contract_type', 'regular');
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor')) //If logged in user type is Supervisor
        {
          //dd($user_id);
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)")->where('work_orders.contract_type', 'regular');
        }
        elseif($service_provider_id != 'no')
        {
          $work_orders = $work_orders->where('contracts.service_provider_id', '=', $service_provider_id);
        }
        if(Auth::user()->user_type == 'super_admin' || Auth::user()->user_type == 'admin_employee' || Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'osool_admin'  )
        {
          if($service_provider_id != 'no')
          {
            $work_orders = $work_orders->where('contracts.service_provider_id', '=', $service_provider_id)->where('work_orders.project_user_id', Auth::user()->project_user_id)
            ;
            //$work_orders = $work_orders->where('work_orders.status', '!=', 4);
            $work_orders = $work_orders->where('work_orders.workorder_journey', '=', 'submitted');
          }
          else{
            $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);
            //$work_orders = $work_orders->where('work_orders.status', '!=', 4);
            $work_orders = $work_orders->where('work_orders.workorder_journey', '=', 'submitted');
          }
        }
        // dd($work_orders->get());
        $work_orders = $work_orders->count();
        //dd(DB::getQueryLog());
        return $work_orders;
    }

    public static function get_inprogress_work_orders_count($user_id, $url=null)
    {

      if(trim(array_reverse(explode('/',$url))[0]) == "" || array_reverse(explode('/',$url))[0] == "closed")
      {
        $service_provider_id = 'no';
      }
      else
      {
        try
        {
          $service_provider_id = \Crypt::decryptString(trim(array_reverse(explode('/',$url))[0]));
          if(trim(array_reverse(explode('/',$url))[1]) != 'work-order' && trim(array_reverse(explode('/',$url))[2]) != 'work-order')
          {
            $service_provider_id = 'no';
          }
        }
        catch(DecryptException $e)
        {
          $service_provider_id = 'no';
        }
        if(trim(array_reverse(explode('/',$url))[1]) == 'work-order' && (!empty(Auth::user()->user_type == 'super_admin') || !empty(Auth::user()->user_type == 'osool_admin') || !empty(Auth::user()->user_type == 'admin_employee') || !empty(Auth::user()->user_type == 'admin')))
        {
          $service_provider_id = 'no';
        }
      }
        $date = date('Y-m-d');
        $response_time = 0;
        $service_window = 0;
        $work_orders = WorkOrders::select('work_orders.*', 'contracts.contract_number')
              ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
              ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
              ->join('users', 'users.id', '=', 'work_orders.created_by');

                $work_orders = $work_orders->where('work_orders.status', '!=', 4);
                $work_orders = $work_orders->where('work_orders.workorder_journey', 'job_execution');

              $work_orders = $work_orders->where('work_orders.is_deleted', '=', "no")
              ->where('work_orders.start_date', '<=', $date)
              //->where('work_orders.project_user_id', Auth::user()->project_user_id)
             // ->orderByRaw('work_orders.created_at asc');
             ->orderByRaw('work_orders.start_date desc');
        if(!empty(Auth::user()->user_type != 'sp_admin') && !empty(Auth::user()->user_type != 'supervisor')) //If logged in user type is not SP Admin and not Supervisor
        {
          $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);
        }

        if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager')) //If logged in user type is Building Manager or Building Manager Employee
        {
          $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
          $building_ids = explode(',', $bm_employees->building_ids);
          //print_r($bm_employees);exit;
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin')) //If logged in user type is SP Admin
        {
          $user_id = Auth::user()->id;
          //dd(Auth::user());
          $contract_id = Contracts::where('status', 1)->where('is_deleted', 'no')->where('service_provider_id', Auth::user()->service_provider)->pluck('id')->toArray();
          $work_orders = $work_orders->whereIn('work_orders.contract_id', $contract_id)->where('work_orders.contract_type', 'regular');
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor')) //If logged in user type is Supervisor
        {
          //dd($user_id);
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)")->where('work_orders.contract_type', 'regular');
        }

        elseif($service_provider_id != 'no')
        {
          $work_orders = $work_orders->where('contracts.service_provider_id', '=', $service_provider_id);
        }
        if(Auth::user()->user_type == 'super_admin' || Auth::user()->user_type == 'admin_employee' || Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'osool_admin'  )
        {
          if($service_provider_id != 'no')
          {
            $work_orders = $work_orders->where('contracts.service_provider_id', '=', $service_provider_id)->where('work_orders.project_user_id', Auth::user()->project_user_id)
            ;
            $work_orders = $work_orders->where('work_orders.status', '!=', 4);
            $work_orders = $work_orders->where('work_orders.workorder_journey', '=', 'job_execution');

          }
          else{

            $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);
            $work_orders = $work_orders->where('work_orders.status', '!=', 4);
            $work_orders = $work_orders->where('work_orders.workorder_journey', '=', 'job_execution');

          }
        }
        // dd($work_orders->get());
        $work_orders = $work_orders->count();
        //dd(DB::getQueryLog());
        return $work_orders;
    }

    public static function get_under_evaluation_work_orders_count($user_id , $url = null)
    {

      if(trim(array_reverse(explode('/',$url))[0]) == "" || array_reverse(explode('/',$url))[0] == "closed")
      {
        $service_provider_id = 'no';
      }
      else
      {
        try
        {
          $service_provider_id = \Crypt::decryptString(trim(array_reverse(explode('/',$url))[0]));
          if(trim(array_reverse(explode('/',$url))[1]) != 'work-order' && trim(array_reverse(explode('/',$url))[2]) != 'work-order')
          {
            $service_provider_id = 'no';
          }
        }
        catch(DecryptException $e)
        {
          $service_provider_id = 'no';
        }
        if(trim(array_reverse(explode('/',$url))[1]) == 'work-order' && (!empty(Auth::user()->user_type == 'super_admin') || !empty(Auth::user()->user_type == 'osool_admin') || !empty(Auth::user()->user_type == 'admin_employee') || !empty(Auth::user()->user_type == 'admin')))
        {
          $service_provider_id = 'no';
        }
      }

      $date = date('Y-m-d');
        $response_time = 0;
        $service_window = 0;
        $work_orders = WorkOrders::select('work_orders.*', 'contracts.contract_number')
              ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
              ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
              ->join('users', 'users.id', '=', 'work_orders.created_by');

                $work_orders = $work_orders->where('work_orders.status', '!=', 4);
                $work_orders = $work_orders->whereRaw("(work_orders.workorder_journey = 'job_evaluation' OR work_orders.workorder_journey = 'job_approval')");
              $work_orders = $work_orders->where('work_orders.is_deleted', '=', "no")
              ->where('work_orders.start_date', '<=', $date)
              //->where('work_orders.project_user_id', Auth::user()->project_user_id)
             // ->orderByRaw('work_orders.created_at asc');
             ->orderByRaw('work_orders.start_date desc');
        if(!empty(Auth::user()->user_type != 'sp_admin') && !empty(Auth::user()->user_type != 'supervisor')) //If logged in user type is not SP Admin and not Supervisor
        {
          $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);
        }

        if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager')) //If logged in user type is Building Manager or Building Manager Employee
        {
          $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
          $building_ids = explode(',', $bm_employees->building_ids);
          //print_r($bm_employees);exit;
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin')) //If logged in user type is SP Admin
        {
          $user_id = Auth::user()->id;
          //dd(Auth::user());
          $contract_id = Contracts::where('status', 1)->where('is_deleted', 'no')->where('service_provider_id', Auth::user()->service_provider)->pluck('id')->toArray();
          $work_orders = $work_orders->whereIn('work_orders.contract_id', $contract_id)->where('work_orders.contract_type', 'regular');
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor')) //If logged in user type is Supervisor
        {
          //dd($user_id);
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)")->where('work_orders.contract_type', 'regular');
        }

        elseif($service_provider_id != 'no')
        {
          $work_orders = $work_orders->where('contracts.service_provider_id', '=', $service_provider_id);
        }
        if(Auth::user()->user_type == 'super_admin' || Auth::user()->user_type == 'admin_employee' || Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'osool_admin'  )
        {
          if($service_provider_id != 'no')
          {
            $work_orders = $work_orders->where('contracts.service_provider_id', '=', $service_provider_id)->where('work_orders.project_user_id', Auth::user()->project_user_id)
            ;

          }
          else{

            $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);

          }
        }
        $work_orders = $work_orders->count();
        //dd(DB::getQueryLog());
        return $work_orders;
    }

    public static function get_pending_bm_maintenance_requests_count($user_id)
    {
      $user = Auth::user();
      $buildings_arr = !empty($user->building_ids) ? explode(',',$user->building_ids) : [];

      $sqlList = MaintenanceRequest::whereIn('building_id',$buildings_arr);

      $sqlList = $sqlList->where('status','pending')->count();

      return $sqlList;
    }

    public static function get_pending_bm_work_orders($user_id, $search)
    {
        $date = date('Y-m-d');
        //dd($date);
        //DB::enableQueryLog();
        $work_orders = WorkOrders::select('work_orders.*', 'contracts.contract_number','properties.property_type', 'properties.complex_name', 'property_buildings.building_name', 'worker.name as worker_name')
        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
        //->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
        ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
        ->join('users', 'users.id', '=', 'work_orders.created_by')
        ->leftjoin('users as worker', 'worker.id', '=', 'work_orders.worker_id');
        if(isset($search['supervisors']) && !empty($search['supervisors'])){
          $supp = $search['supervisors'];
          $supp = implode(',',$search['supervisors']);

          $work_orders = $work_orders->where(function($query) use($supp) {
            $query->whereRaw("(((work_orders.supervisor_id IS NOT NULL and work_orders.assigned_to = 'supervisor') || (work_orders.assigned_to = 'sp_worker' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) = 1)) AND work_orders.supervisor_id IN ($supp)AND (work_orders.assigned_to != 'sp_worker' || work_orders.worker_id != 0)) OR (work_orders.service_provider_id IN ($supp) AND (work_orders.assigned_to != 'sp_worker' || work_orders.worker_id != 0) AND work_orders.assigned_to != 'supervisor' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) > 1)");
            });
        }
        $work_orders=$work_orders->where('work_orders.start_date', '<=', $date);

        if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager'))
        {
          $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
          $building_ids = explode(',', $bm_employees->building_ids);
          //print_r($bm_employees);exit;
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
          $work_orders = $work_orders->whereRaw("((work_orders.contract_type = 'warranty' and work_orders.status != 4)  OR ((work_orders.contract_type = 'regular' and work_orders.status = 1) and work_orders.proposed_new_date IS NOT NULL and work_orders.bm_approve_issue = 0)  OR ((work_orders.contract_type = 'regular' and (work_orders.status = 2 OR work_orders.status = 6) and work_orders.workorder_journey = 'job_approval') and (work_orders.sp_approve_job = 2 or work_orders.sp_approve_job = 3) and work_orders.bm_approve_job = 0) OR ((work_orders.contract_type = 'regular' and work_orders.status = 6 and work_orders.workorder_journey = 'submitted') && work_orders.sp_reopen_status = 1))");
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
            $user_id = Auth::user()->id;
            //dd($user_id);
            $contract_id = Contracts::where('status', 1)->where('is_deleted', 'no')->where('service_provider_id', Auth::user()->service_provider)->pluck('id')->toArray();
            $work_orders = $work_orders->whereIn('work_orders.contract_id', $contract_id);
            //$work_orders = $work_orders->where('work_orders.service_provider_id', '=', $user_id);
            $work_orders = $work_orders->whereRaw("(((work_orders.contract_type = 'regular' and work_orders.status = 1) and work_orders.proposed_new_date IS NULL and work_orders.bm_approve_issue = 0) OR ((work_orders.contract_type = 'regular' and work_orders.status = 1) and work_orders.proposed_new_date IS NOT NULL and work_orders.bm_approve_issue = 1) OR ((work_orders.contract_type = 'regular' and (work_orders.status = 6) and work_orders.workorder_journey = 'job_execution')) OR ((work_orders.contract_type = 'regular' and (work_orders.status = 6) and work_orders.workorder_journey = 'job_evaluation') and work_orders.sp_approve_job != 1) OR ((work_orders.contract_type = 'regular' and work_orders.status = 6 and work_orders.workorder_journey = 'submitted') and work_orders.sp_reopen_status = 0))");
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
            $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
            $work_orders = $work_orders->whereRaw("(((work_orders.contract_type = 'regular' and work_orders.status = 1) and work_orders.proposed_new_date IS NULL and work_orders.bm_approve_issue = 0) OR ((work_orders.contract_type = 'regular' and work_orders.status = 1) and work_orders.proposed_new_date IS NOT NULL and work_orders.bm_approve_issue = 1) OR ((work_orders.contract_type = 'regular' and (work_orders.status = 6) and work_orders.workorder_journey = 'job_execution')) OR ((work_orders.contract_type = 'regular' and (work_orders.status = 6) and work_orders.workorder_journey = 'job_evaluation') and work_orders.sp_approve_job != 1) OR ((work_orders.contract_type = 'regular' and work_orders.status = 6 and work_orders.workorder_journey = 'submitted') and work_orders.sp_reopen_status = 0))");
        }

        if(isset($search['buildings'])) //If searched for Pass or Fail
        {
          //dd($search['buildings']);
          $work_orders = $work_orders->whereIn('work_orders.property_id', $search['buildings']);
        }
        if($search['passfail'])
        {
            if($search['passfail'] != "all")
            {
                $work_orders = $work_orders->where('pass_fail', '=', $search['passfail']);
            }
        }
        if($search['type'])
        {
            if($search['type'] != "all")
            {
                $work_orders = $work_orders->where('work_order_type', '=', $search['type']);

            }
        }
        // dd($search);

        if($search['status']){
            if($search['status'] != "all")
            {
                $work_orders = $work_orders->where('work_orders.status', '=', $search['status']);
            }
        }
        if($search['dateRange'] && !empty($search['dateRange']['startd'])){
          $start_date = $search['dateRange']['startd'].' 00:00:00';
          $end_date = $search['dateRange']['endd'].' 23:59:59';
          //echo $start_date.' -- '.$end_date;exit;
          //$work_orders = $work_orders->whereBetween('work_orders.created_at', [$start_date, $end_date]);
          $work_orders = $work_orders->whereRaw("(IF(`work_orders`.`work_order_type` != 'preventive', work_orders.created_at, work_orders.start_date) Between '$start_date' AND '$end_date')");
        }

        if($search['wo_id'] && $search['wo_id'] != null){
          $s_wo_id = $search['wo_id'];
          $work_orders = $work_orders->whereRaw("(work_orders.work_order_id like '%$s_wo_id%' OR (work_orders.unique_id like '%$s_wo_id%' and work_orders.work_order_type = 'reactive'))");
        }

        if(isset($search['service_types']) && !empty($search['service_types']))
        {
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $search['service_types']);
        }
          /*
            $query = str_replace(array('?'), array('\'%s\''), $work_orders->toSql());
            $query = vsprintf($query, $work_orders->getBindings());
            dump($query);
            die;
          */
        //$ttal_cnt = $work_orders->count();

        $work_orders = $work_orders
        ->orderByRaw('work_orders.start_date desc')
        ->skip($search['start'])->take($search['length'])->get();
        $work_orders = json_decode(json_encode($work_orders), true);
      if(!empty($work_orders))//If there are work orders with provided conditions
      {
        foreach($work_orders as $key => $wo)
        {
          $sla_asset_categories = DB::table('contract_asset_categories')
                    ->where('asset_category_id', $wo['asset_category_id'])
                    ->where('contract_number', $wo['contract_number'])
                    ->orderBy('id', 'desc')
                    ->first();

          $response_time = 0;
          $service_window = 0;
          $response_time_type = 'hours';
          $service_window_type = 'minutes';
          if($wo['work_order_type'] == "reactive") //If the work order type is Reactive
          {
            if(!empty($sla_asset_categories->priority_id)){
              $contract_priorities = DB::table('contract_priorities')
                    ->where('priority_id', $sla_asset_categories->priority_id)
                    ->where('contract_number', $wo['contract_number'])
                    ->orderBy('id', 'desc')
                    ->first();
              $response_time = 0;
              $service_window = 0;
              $response_time_type = 'hours';
              $service_window_type = 'minutes';
              if(isset($contract_priorities->response_time))
              {
                $response_time = $contract_priorities->response_time;
                $service_window = $contract_priorities->service_window;
                $response_time_type = $contract_priorities->response_time_type;
                $service_window_type = $contract_priorities->service_window_type;
              }
            }
          }
          elseif($wo['work_order_type'] == "preventive" && $wo['priority_id'] != 0) //If the Work order Type is Preventive and Priority ID is not 0
          {
            //echo $wo['priority_id'].' -- '.$wo['contract_number'];exit;
            //echo $wo['work_order_id'];exit;
            $contract_priorities = DB::table('contract_priorities')
                  ->where('priority_id', $wo['priority_id'])
                  ->where('contract_number', $wo['contract_number'])
                  ->orderBy('id', 'desc')
                  ->first();
            //dd($contract_priorities);
            $response_time = 0;
            $service_window = 0;
            $response_time_type = 'hours';
            $service_window_type = 'minutes';
            if(isset($contract_priorities->response_time))
            {
              $response_time = $contract_priorities->response_time;
              $service_window = $contract_priorities->service_window;
              $response_time_type = $contract_priorities->response_time_type;
              $service_window_type = $contract_priorities->service_window_type;
            }
          }
          else
          {
            $contract_frequencies = DB::table('frequencies_master')
                      ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                      ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                      ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                      ->where('frequencies_master.id', $wo['frequency_id'])
                      ->where('contract_frequencies.contract_number', $wo['contract_number'])
                      ->first();
            $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
            $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : 0;
            $response_time_type = isset($contract_priorities->response_time_type)?$contract_priorities->response_time_type:'hours';
            $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
          }
          $wtfs = DB::table('work_time_frame')
                    ->select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
                    ->where('user_id', $wo['project_user_id'])
                    ->first();
          if($wo['wtf_start_time'] == ''){
            if(!isset($wtfs)) //If work order time frame has added
            {
              $time = "00:00:00";
            }
            else {
              $time = $wtfs->start_time;
            }
          }
          else{
            $time =$wo['wtf_start_time'];
          }
          $created_at = $wo['created_at'];
          if($wo['work_order_type'] == "preventive") //If the work order type is Preventive
          {
            $work_orders[$key]['created_at'] = $wo['start_date'].' '.$time;
            $created_at = $wo['start_date'].' '.$time;
          }
          $tdate = date('Y-m-d H:i:s');
          $datetime1 = strtotime($created_at);
          $datetime2 = strtotime($tdate);
          $interval  = abs($datetime2 - $datetime1);
          $minutes   = round($interval / 60);
          if($response_time_type == "days")
          {
            $response_time = $response_time * 1440;
          }
          elseif($response_time_type == "hours")
          {
            $response_time = $response_time * 60;
          }
          //echo $response_time .'-'. $minutes;exit;
          $time_left = $response_time - $minutes;

          if($wo['job_started_at'] == NULL || $wo['bm_approve_issue'] == 2) //If the job is not started
          {
            $target_date = $wo['target_date'];
            $work_orders[$key]['target_date'] = $target_date;
          }
          else
          {
            $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($wo['job_started_at'])));
            $work_orders[$key]['target_date'] = $target_date;

          }

          if($wo['pass_fail'] != 'pending')
          {
            $work_orders[$key]['pass_fail'] = $wo['pass_fail'];
          }
          elseif($wo['workorder_journey'] != 'job_approval') //If the job is started and the status is not closed and work order journey is not Job Approval
          {
            $work_orders[$key]['pass_fail'] = 'pending';
          }
          if($wo['response_time'] == 'On time' || $wo['response_time'] == 'Late') //If the response time is On time or Late
          {
            $work_orders[$key]['response_time'] = $wo['response_time'];
          }
          else
          {
            if($time_left > 0) //If the time left is greater than equal to 0
            {
              $work_orders[$key]['response_time'] = $time_left;
            }
            else
            {
              $work_orders[$key]['response_time'] = 'Late';
            }
          }
          $total = DB::table('work_order_chat')->where('receiver_id',$user_id)->where('work_order_id', $wo['id'])->where('is_read','0')->count();
          if($total>0) //If the work order chat count is greater than 0
          {
            $isread='&bull;';
          }
          else
          {
            $isread='';
          }
          $work_orders[$key]['isread'] = $isread;
        }
      }
      $work_orders = json_decode(json_encode($work_orders));
      return  $work_orders;
    }

    public static function get_request_sp_work_orders_count($user_id,$search=null)
    {
        $date = date('Y-m-d');
        //DB::enableQueryLog();
        $work_orders = WorkOrders::select('work_orders.*')
        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
        ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
        ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
        ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
        ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
        ->join('users', 'users.id', '=', 'work_orders.created_by')   ;

        if(isset($search['supervisors']) && !empty($search['supervisors'])){
          $supp = $search['supervisors'];
          $supp = implode(',',$search['supervisors']);

          // $work_orders=$work_orders->whereRaw("(((work_orders.supervisor_id IS NOT NULL and work_orders.assigned_to = 'supervisor') || (work_orders.assigned_to = 'sp_worker' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) = 1)) AND work_orders.supervisor_id IN ($supp)AND (work_orders.assigned_to != 'sp_worker' || work_orders.worker_id != 0)) OR (work_orders.service_provider_id IN ($supp) AND (work_orders.assigned_to != 'sp_worker' || work_orders.worker_id != 0) AND work_orders.assigned_to != 'supervisor' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) > 1)");

        }
        if(isset($search['service_types']) && !empty($search['service_types']))
        {
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $search['service_types']);
        }
        $work_orders=$work_orders->where('work_orders.start_date', '<=', $date);


        if(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->where('work_orders.service_provider_id', '=', $user_id);
          $work_orders = $work_orders->whereRaw("(((work_orders.contract_type = 'regular' and (work_orders.status = 2 OR work_orders.status = 6) and work_orders.workorder_journey = 'job_evaluation') and work_orders.sp_approve_job != 1))");
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
          $work_orders = $work_orders->whereRaw("(((work_orders.contract_type = 'regular' and (work_orders.status = 2 OR work_orders.status = 6) and work_orders.workorder_journey = 'job_evaluation') and work_orders.sp_approve_job != 1))");
        }
        if(isset($search['buildings'])) //If searched for Pass or Fail
        {
          //dd($search['buildings']);
          $work_orders = $work_orders->whereIn('work_orders.property_id', $search['buildings']);
        }
        if(isset($search['passfail']))
        {
            if($search['passfail'] != "all")
            {
                $work_orders = $work_orders->where('pass_fail', '=', $search['passfail']);
            }
        }
        if(isset($search['type']))
        {
            if($search['type'] != "all")
            {
                $work_orders = $work_orders->where('work_order_type', '=', $search['type']);
            }
        }
        if(isset($search['status'])){
            if($search['status'] != "all")
            {
                $work_orders = $work_orders->where('work_orders.status', '=', $search['status']);
            }
        }
        if(isset($search['dateRange']) && !empty($search['dateRange']['startd'])){
          $start_date = $search['dateRange']['startd'].' 00:00:00';
          $end_date = $search['dateRange']['endd'].' 23:59:59';
          //echo $start_date.' -- '.$end_date;exit;
          //$work_orders = $work_orders->whereBetween('work_orders.created_at', [$start_date, $end_date]);
          $work_orders = $work_orders->whereRaw("(IF(`work_orders`.`work_order_type` != 'preventive', work_orders.created_at, work_orders.start_date) Between '$start_date' AND '$end_date')");
        }
        if(isset($search['wo_id']) && $search['wo_id'] != null){
          $s_wo_id = $search['wo_id'];
          $work_orders = $work_orders->whereRaw("(work_orders.work_order_id like '%$s_wo_id%' OR (work_orders.unique_id like '%$s_wo_id%' and work_orders.work_order_type = 'reactive'))");
        }
        if(isset($search['service_types']) && !empty($search['service_types']))
        {
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $search['service_types']);
        }
        $work_orders = $work_orders->count();
        //dd(DB::getQueryLog());
        return $work_orders;
    }

    public static function get_all_sp_work_orders_count($user_id)
    {
        $date = date('Y-m-d');
        //DB::enableQueryLog();
        $work_orders = WorkOrders::select('work_orders.*')
        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
        ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
        ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
        ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
        ->join('users', 'users.id', '=', 'work_orders.created_by')
        ->where('work_orders.start_date', '<=', $date);
        if(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->where('work_orders.service_provider_id', '=', $user_id);
          $work_orders = $work_orders->whereRaw("(work_orders.contract_type = 'regular')");
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
          $work_orders = $work_orders->whereRaw("(((work_orders.contract_type = 'regular' and (work_orders.status = 2 OR work_orders.status = 6) and work_orders.workorder_journey = 'job_evaluation') and work_orders.sp_approve_job != 1))");
        }
        $work_orders = $work_orders->count();
        //dd(DB::getQueryLog());
        return $work_orders;
    }

    public static function get_request_sp_work_orders($user_id, $search)
    {
        $date = date('Y-m-d');
        //DB::enableQueryLog();
        $work_orders = WorkOrders::select('work_orders.*', 'priorities.id as priority_id', 'contracts.contract_number', 'contracts.contract_number','properties.property_type', 'properties.complex_name', 'property_buildings.building_name','worker.name as worker_name')
        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
        ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
        ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
        ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
        ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
        ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
        ->join('users', 'users.id', '=', 'work_orders.created_by')
        ->leftjoin('users as worker', 'worker.id', '=', 'work_orders.worker_id');
        if(isset($search['supervisors']) && !empty($search['supervisors'])){
          $supp = $search['supervisors'];
          $supp = implode(',',$search['supervisors']);

          $work_orders = $work_orders->where(function($query) use($supp) {
            $query->whereRaw("(((work_orders.supervisor_id IS NOT NULL and work_orders.assigned_to = 'supervisor') || (work_orders.assigned_to = 'sp_worker' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) = 1)) AND work_orders.supervisor_id IN ($supp)AND (work_orders.assigned_to != 'sp_worker' || work_orders.worker_id != 0)) OR (work_orders.service_provider_id IN ($supp) AND (work_orders.assigned_to != 'sp_worker' || work_orders.worker_id != 0) AND work_orders.assigned_to != 'supervisor' AND (CHAR_LENGTH(work_orders.supervisor_id) - CHAR_LENGTH(REPLACE(work_orders.supervisor_id, ',', '')) + 1) > 1)");
            });

        }
        $work_orders=$work_orders->where('work_orders.start_date', '<=', $date);


        if(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->where('work_orders.service_provider_id', '=', $user_id);
          $work_orders = $work_orders->whereRaw("(((work_orders.contract_type = 'regular' and (work_orders.status = 2 OR work_orders.status = 6) and work_orders.workorder_journey = 'job_evaluation') and work_orders.sp_approve_job != 1))");
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
          $work_orders = $work_orders->whereRaw("(((work_orders.contract_type = 'regular' and (work_orders.status = 2 OR work_orders.status = 6) and work_orders.workorder_journey = 'job_evaluation') and work_orders.sp_approve_job != 1))");
        }

        if(isset($search['buildings'])) //If searched for Pass or Fail
        {
          //dd($search['buildings']);
          $work_orders = $work_orders->whereIn('work_orders.property_id', $search['buildings']);
        }
        if($search['passfail'])
        {
            if($search['passfail'] != "all")
            {
                $work_orders = $work_orders->where('pass_fail', '=', $search['passfail']);
            }
        }
        if($search['type'])
        {
            if($search['type'] != "all")
            {
                $work_orders = $work_orders->where('work_order_type', '=', $search['type']);
            }
        }
        if($search['status']){
            if($search['status'] != "all")
            {
                $work_orders = $work_orders->where('work_orders.status', '=', $search['status']);
            }
        }
        if($search['dateRange'] && !empty($search['dateRange']['startd'])){
          $start_date = $search['dateRange']['startd'].' 00:00:00';
          $end_date = $search['dateRange']['endd'].' 23:59:59';
          //echo $start_date.' -- '.$end_date;exit;
          //$work_orders = $work_orders->whereBetween('work_orders.created_at', [$start_date, $end_date]);
          $work_orders = $work_orders->whereRaw("(IF(`work_orders`.`work_order_type` != 'preventive', work_orders.created_at, work_orders.start_date) Between '$start_date' AND '$end_date')");
        }

        if($search['wo_id'] && $search['wo_id'] != null){
          $s_wo_id = $search['wo_id'];
          $work_orders = $work_orders->whereRaw("(work_orders.work_order_id like '%$s_wo_id%' OR (work_orders.unique_id like '%$s_wo_id%' and work_orders.work_order_type = 'reactive'))");
        }

        if(isset($search['service_types']) && !empty($search['service_types']))
        {
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $search['service_types']);
        }

        $work_orders = $work_orders->orderBy('work_orders.id', 'desc');
        $work_orders = $work_orders->skip($search['start'])->take($search['length'])->get();
        $work_orders = json_decode(json_encode($work_orders), true);
        if(!empty($work_orders))//If there are work orders with provided conditions
        {
          foreach($work_orders as $key => $wo)
          {
            $sla_asset_categories = DB::table('contract_asset_categories')
                      ->where('asset_category_id', $wo['asset_category_id'])
                      ->where('contract_number', $wo['contract_number'])
                      ->orderBy('id', 'desc')
                      ->first();

            $response_time = 0;
            $service_window = 0;
            $response_time_type = 'hours';
            $service_window_type = 'minutes';
            if($wo['work_order_type'] == "reactive") //If the work order type is Reactive
            {
              if(!empty($sla_asset_categories->priority_id)){
                $contract_priorities = DB::table('contract_priorities')
                      ->where('priority_id', $sla_asset_categories->priority_id)
                      ->where('contract_number', $wo['contract_number'])
                      ->orderBy('id', 'desc')
                      ->first();
                $response_time = 0;
                $service_window = 0;
                $response_time_type = 'hours';
                $service_window_type = 'minutes';
                if(isset($contract_priorities->response_time))
                {
                  $response_time = $contract_priorities->response_time;
                  $service_window = $contract_priorities->service_window;
                  $response_time_type = $contract_priorities->response_time_type;
                  $service_window_type = $contract_priorities->service_window_type;
                }
              }
            }
            elseif($wo['work_order_type'] == "preventive" && $wo['priority_id'] != 0) //If the Work order Type is Preventive and Priority ID is not 0
            {
              //echo $wo['priority_id'].' -- '.$wo['contract_number'];exit;
              //echo $wo['work_order_id'];exit;
              $contract_priorities = DB::table('contract_priorities')
                    ->where('priority_id', $wo['priority_id'])
                    ->where('contract_number', $wo['contract_number'])
                    ->orderBy('id', 'desc')
                    ->first();
              //dd($contract_priorities);
              $response_time = 0;
              $service_window = 0;
              $response_time_type = 'hours';
              $service_window_type = 'minutes';
              if(isset($contract_priorities->response_time))
              {
                $response_time = $contract_priorities->response_time;
                $service_window = $contract_priorities->service_window;
                $response_time_type = $contract_priorities->response_time_type;
                $service_window_type = $contract_priorities->service_window_type;
              }
            }
            else
            {
              $contract_frequencies = DB::table('frequencies_master')
                        ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                        ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                        ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                        ->where('frequencies_master.id', $wo['frequency_id'])
                        ->where('contract_frequencies.contract_number', $wo['contract_number'])
                        ->first();
              $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
              $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : 0;
              $response_time_type = isset($contract_priorities->response_time_type)?$contract_priorities->response_time_type:'hours';
              $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
            }
            $wtfs = DB::table('work_time_frame')
                      ->select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
                      ->where('user_id', $wo['project_user_id'])
                      ->first();
            if($wo['wtf_start_time'] == ''){
              if(!isset($wtfs)) //If work order time frame has added
              {
                $time = "00:00:00";
              }
              else {
                $time = $wtfs->start_time;
              }
            }
            else{
              $time =$wo['wtf_start_time'];
            }
            $created_at = $wo['created_at'];
            if($wo['work_order_type'] == "preventive") //If the work order type is Preventive
            {
              $work_orders[$key]['created_at'] = $wo['start_date'].' '.$time;
              $created_at = $wo['start_date'].' '.$time;
            }
            $tdate = date('Y-m-d H:i:s');
            $datetime1 = strtotime($created_at);
            $datetime2 = strtotime($tdate);
            $interval  = abs($datetime2 - $datetime1);
            $minutes   = round($interval / 60);
            if($response_time_type == "days")
            {
              $response_time = $response_time * 1440;
            }
            elseif($response_time_type == "hours")
            {
              $response_time = $response_time * 60;
            }
            //echo $response_time .'-'. $minutes;exit;
            $time_left = $response_time - $minutes;

            if($wo['job_started_at'] == NULL || $wo['bm_approve_issue'] == 2) //If the job is not started
            {
              $target_date = $wo['target_date'];
              $work_orders[$key]['target_date'] = $target_date;
            }
            else
            {
              $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($wo['job_started_at'])));
              $work_orders[$key]['target_date'] = $target_date;

            }

            if($wo['pass_fail'] != 'pending')
            {
              $work_orders[$key]['pass_fail'] = $wo['pass_fail'];
            }
            elseif($wo['workorder_journey'] != 'job_approval') //If the job is started and the status is not closed and work order journey is not Job Approval
            {
              $work_orders[$key]['pass_fail'] = 'pending';
            }
            if($wo['response_time'] == 'On time' || $wo['response_time'] == 'Late') //If the response time is On time or Late
            {
              $work_orders[$key]['response_time'] = $wo['response_time'];
            }
            else
            {
              if($time_left > 0) //If the time left is greater than equal to 0
              {
                $work_orders[$key]['response_time'] = $time_left;
              }
              else
              {
                $work_orders[$key]['response_time'] = 'Late';
              }
            }
            $total = DB::table('work_order_chat')->where('receiver_id',$user_id)->where('work_order_id', $wo['id'])->where('is_read','0')->count();
            if($total>0) //If the work order chat count is greater than 0
            {
              $isread='&bull;';
            }
            else
            {
              $isread='';
            }
            $work_orders[$key]['isread'] = $isread;
          }
        }
        $work_orders = json_decode(json_encode($work_orders));
        return  $work_orders;
    }

    public static function get_sp_work_orders($user_id, $search)
    {
        $date = date('Y-m-d');
       // DB::enableQueryLog();
        $work_orders = WorkOrders::select('work_orders.*', 'contracts.contract_number')
                      ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                      // ->leftjoin('contract_asset_categories', 'contract_asset_categories.asset_category_id', '=', 'work_orders.asset_category_id')
                      // ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
                      // ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                      // ->leftjoin('contract_priorities', 'contract_priorities.priority_id', '=', 'contract_asset_categories.priority_id')
                      // ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'work_orders.frequency_id')
                      ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                      ->join('users', 'users.id', '=', 'work_orders.created_by')
                      ->where('work_orders.service_provider_id', '=', $user_id)
                      ->where('work_orders.is_deleted', '=', "no")
                      ->where('work_orders.start_date', '<=', $date);
        if($search['passfail'])
        {
            if($search['passfail'] != "all")
            {
                $work_orders = $work_orders->where('pass_fail', '=', $search['passfail']);
            }
        }
        if($search['type'])
        {
            if($search['type'] != "all")
            {
                $work_orders = $work_orders->where('work_order_type', '=', $search['type']);
            }
        }
        if($search['status']){
            if($search['status'] != "all")
            {
                $work_orders = $work_orders->where('work_orders.status', '=', $search['status']);
            }
        }
        if($search['dateRange'] && !empty($search['dateRange']['startd'])){
            $work_orders= $work_orders->whereBetween('work_orders.created_at', [$search['dateRange']['startd'], $search['dateRange']['endd']]);
        }
        $work_orders = $work_orders->orderBy('work_orders.id', 'desc')->paginate(10);
        //print_r($work_orders);exit;
        //$work_orders = json_decode(json_encode($work_orders), true);
        if(!empty($work_orders))
        {
          foreach($work_orders as $key => $wo)
          {
            $sla_asset_categories = DB::table('contract_asset_categories')
                      ->where('asset_category_id', $wo['asset_category_id'])
                      ->where('contract_number', $wo['contract_number'])
                      ->orderBy('id', 'desc')
                      ->first();

            $response_time = 0;
            $service_window = 0;
            $response_time_type = 'hours';
            $service_window_type = 'minutes';
            if($wo['work_order_type'] == "reactive")
            {
              if(!empty($sla_asset_categories->priority_id)){
                $contract_priorities = DB::table('contract_priorities')
                      ->where('priority_id', $sla_asset_categories->priority_id)
                      ->where('contract_number', $wo['contract_number'])
                      ->orderBy('id', 'desc')
                      ->first();
                $response_time = $contract_priorities->response_time;
                $service_window = $contract_priorities->service_window;
                $response_time_type = $contract_priorities->response_time_type;
                $service_window_type = $contract_priorities->service_window_type;
              }
            }
            elseif($wo['work_order_type'] == "preventive" && $wo['priority_id'] != 0)
            {
              $contract_priorities = DB::table('contract_priorities')
                    ->where('priority_id', $wo['priority_id'])
                    ->where('contract_number', $wo['contract_number'])
                    ->orderBy('id', 'desc')
                    ->first();
              $response_time = $contract_priorities->response_time;
              $service_window = $contract_priorities->service_window;
              $response_time_type = $contract_priorities->response_time_type;
              $service_window_type = $contract_priorities->service_window_type;
            }
            else
            {
              $contract_frequencies = DB::table('frequencies_master')
                        ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                        ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                        ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                        ->where('frequencies_master.id', $wo['frequency_id'])
                        ->where('contract_frequencies.contract_number', $wo['contract_number'])
                        ->first();
                      $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
                      $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : '';
                      $response_time_type = isset($contract_frequencies->response_time_type) ? $contract_frequencies->response_time_type:'hours';
                      $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
            }
            $wtfs = DB::table('work_time_frame')
                    ->select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
                    ->where('user_id', $wo['project_user_id'])
                    ->first();
            if($wo['wtf_start_time'] == ''){
              if(!isset($wtfs)) //If work order time frame has added
              {
                $time = "00:00:00";
              }
              else {
                $time = $wtfs->start_time;
              }
            }
            else{
              $time =$wo['wtf_start_time'];
            }
            $created_at = $wo['created_at'];
            if($wo['work_order_type'] == "preventive") //If the work order type is Preventive
            {
              $work_orders[$key]['created_at'] = $wo['start_date'].' '.$time;
              $created_at = $wo['start_date'].' '.$time;
            }
            $tdate = date('Y-m-d H:i:s');
            $datetime1 = strtotime($created_at);
            $datetime2 = strtotime($tdate);
            $interval  = abs($datetime2 - $datetime1);
            $minutes   = round($interval / 60);
            if($response_time_type == "days")
            {
              $response_time = $response_time * 1440;
            }
            elseif($response_time_type == "hours")
            {
              $response_time = $response_time * 60;
            }
            $time_left = $response_time - $minutes;

            if($wo['job_started_at'] == NULL || $wo['bm_approve_issue'] == 2)
            {
              $target_date = $wo['target_date'];
              $work_orders[$key]['target_date'] = $target_date;
            }
            else
            {
              $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($wo['job_started_at'])));
              $work_orders[$key]['target_date'] = $target_date;
            }
            // if($wo['job_started_at'] != '' && $wo['status'] != 4 && $wo['workorder_journey'] != 'job_approval') //If the job is started and the status is not closed and work order journey is not Job Approval
            // {
            //   if(strtotime($target_date) >= strtotime($tdate)) //If Target date is greater that today's date
            //   {
            //     $work_orders[$key]['pass_fail'] = 'pass';
            //   }
            //   else
            //   {
            //     $work_orders[$key]['pass_fail'] = 'fail';
            //   }
            // }
            if($wo['pass_fail'] != 'pending')
            {
              $work_orders[$key]['pass_fail'] = $wo['pass_fail'];
            }
            elseif($wo['workorder_journey'] != 'job_approval') //If the job is started and the status is not closed and work order journey is not Job Approval
            {
              $work_orders[$key]['pass_fail'] = 'pending';
            }
            if($wo['response_time'] == 'On time' || $wo['response_time'] == 'Late')
            {
              $work_orders[$key]['response_time'] = $wo['response_time'];
            }
            else
            {
              if($time_left >= 0)
              {
                $work_orders[$key]['response_time'] = $time_left;
              }
              else
              {
                $work_orders[$key]['response_time'] = 'Late';
              }
            }
          }
        }
        $work_orders = json_decode(json_encode($work_orders), true);
        return $work_orders;
    }


    public static function get_supervisor_work_orders($user_id, $search)
    {
        $date = date('Y-m-d');
       // DB::enableQueryLog();
        $work_orders = WorkOrders::select('work_orders.*', 'contracts.contract_number')
                      ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                      // ->leftjoin('contract_asset_categories', 'contract_asset_categories.asset_category_id', '=', 'work_orders.asset_category_id')
                      // ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
                      // ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                      // ->leftjoin('contract_priorities', 'contract_priorities.priority_id', '=', 'contract_asset_categories.priority_id')
                      // ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'work_orders.frequency_id')
                      ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                      ->join('users', 'users.id', '=', 'work_orders.created_by')
                      ->whereRaw("find_in_set($user_id, work_orders.supervisor_id)")
                      ->where('work_orders.is_deleted', '=', "no")
                      ->where('work_orders.start_date', '<=', $date);
        if($search['passfail'])
        {
            if($search['passfail'] != "all")
            {
                $work_orders = $work_orders->where('pass_fail', '=', $search['passfail']);
            }
        }
        if($search['type'])
        {
            if($search['type'] != "all")
            {
                $work_orders = $work_orders->where('work_order_type', '=', $search['type']);
            }
        }
        if($search['status']){
            if($search['status'] != "all")
            {
                $work_orders = $work_orders->where('work_orders.status', '=', $search['status']);
            }
        }
        if($search['dateRange'] && !empty($search['dateRange']['startd'])){
            $work_orders= $work_orders->whereBetween('work_orders.created_at', [$search['dateRange']['startd'], $search['dateRange']['endd']]);
        }
        $work_orders = $work_orders->orderBy('work_orders.id', 'desc')->paginate(10);
        //print_r($work_orders);exit;
        //$work_orders = json_decode(json_encode($work_orders), true);
        if(!empty($work_orders))
        {
          foreach($work_orders as $key => $wo)
          {
            $sla_asset_categories = DB::table('contract_asset_categories')
                      ->where('asset_category_id', $wo['asset_category_id'])
                      ->where('contract_number', $wo['contract_number'])
                      ->orderBy('id', 'desc')
                      ->first();

            $response_time = 0;
            $service_window = 0;
            $response_time_type = 'hours';
            $service_window_type = 'minutes';
            if($wo['work_order_type'] == "reactive")
            {
              if(!empty($sla_asset_categories->priority_id)){
                $contract_priorities = DB::table('contract_priorities')
                      ->where('priority_id', $sla_asset_categories->priority_id)
                      ->where('contract_number', $wo['contract_number'])
                      ->orderBy('id', 'desc')
                      ->first();
                $response_time = $contract_priorities->response_time;
                $service_window = $contract_priorities->service_window;
                $response_time_type = $contract_priorities->response_time_type;
                $service_window_type = $contract_priorities->service_window_type;
              }
            }
            elseif($wo['work_order_type'] == "preventive" && $wo['priority_id'] != 0)
            {
                $contract_priorities = DB::table('contract_priorities')
                      ->where('priority_id', $wo['priority_id'])
                      ->where('contract_number', $wo['contract_number'])
                      ->orderBy('id', 'desc')
                      ->first();
                $response_time = $contract_priorities->response_time;
                $service_window = $contract_priorities->service_window;
                $response_time_type = $contract_priorities->response_time_type;
                $service_window_type = $contract_priorities->service_window_type;
            }
            else
            {
              $contract_frequencies = DB::table('frequencies_master')
                        ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                        ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                        ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                        ->where('frequencies_master.id', $wo['frequency_id'])
                        ->where('contract_frequencies.contract_number', $wo['contract_number'])
                        ->first();
                      $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
                      $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : '';
                      $response_time_type = isset($contract_frequencies->response_time_type) ? $contract_frequencies->response_time_type:'hours';
                      $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
            }
            $wtfs = DB::table('work_time_frame')
                    ->select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
                    ->where('user_id', $wo['project_user_id'])
                    ->first();
            if($wo['wtf_start_time'] == ''){
              if(!isset($wtfs)) //If work order time frame has added
              {
                $time = "00:00:00";
              }
              else {
                $time = $wtfs->start_time;
              }
            }
            else{
              $time =$wo['wtf_start_time'];
            }
            $created_at = $wo['created_at'];
            if($wo['work_order_type'] == "preventive") //If the work order type is Preventive
            {
              $work_orders[$key]['created_at'] = $wo['start_date'].' '.$time;
              $created_at = $wo['start_date'].' '.$time;
            }
            $tdate = date('Y-m-d H:i:s');
            $datetime1 = strtotime($created_at);
            $datetime2 = strtotime($tdate);
            $interval  = abs($datetime2 - $datetime1);
            $minutes   = round($interval / 60);
            if($response_time_type == "days")
            {
              $response_time = $response_time * 1440;
            }
            elseif($response_time_type == "hours")
            {
              $response_time = $response_time * 60;
            }
            $time_left = $response_time - $minutes;

            if($wo['job_started_at'] == NULL || $wo['bm_approve_issue'] == 2)
            {
              $target_date = $wo['target_date'];
              $work_orders[$key]['target_date'] = $target_date;
            }
            else
            {
              $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($wo['job_started_at'])));
              $work_orders[$key]['target_date'] = $target_date;
            }
            // if($wo['job_started_at'] != '' && $wo['status'] != 4 && $wo['workorder_journey'] != 'job_approval') //If the job is started and the status is not closed and work order journey is not Job Approval
            // {
            //   if(strtotime($target_date) >= strtotime($tdate)) //If Target date is greater that today's date
            //   {
            //     $work_orders[$key]['pass_fail'] = 'pass';
            //   }
            //   else
            //   {
            //     $work_orders[$key]['pass_fail'] = 'fail';
            //   }
            // }
            if($wo['pass_fail'] != 'pending')
            {
              $work_orders[$key]['pass_fail'] = $wo['pass_fail'];
            }
            elseif($wo['workorder_journey'] != 'job_approval') //If the job is started and the status is not closed and work order journey is not Job Approval
            {
              $work_orders[$key]['pass_fail'] = 'pass';
            }
            if($wo['response_time'] == 'On time' || $wo['response_time'] == 'Late')
            {
              $work_orders[$key]['response_time'] = $wo['response_time'];
            }
            else
            {
              if($time_left >= 0)
              {
                $work_orders[$key]['response_time'] = $time_left;
              }
              else
              {
                $work_orders[$key]['response_time'] = 'Late';
              }
            }
          }
        }
        $work_orders = json_decode(json_encode($work_orders), true);
        return $work_orders;
    }

    public static function get_work_order_details($id)
    {
      //dd($id);

      $contractTypeId = WorkOrders::join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
    ->where('work_orders.id', $id)
    ->value('contracts.contract_type_id');

        $details = WorkOrders::select('work_orders.*', 'contracts.contract_type_id', 'contracts.contract_number','contracts.deleted_at as contract_deleted_at', 'work_orders.priority_id as wo_priority_id','priorities.priority_level','priorities.id as priority_id','property_buildings.building_name','property_buildings.building_tag', 'property_buildings.id as building_id', 'properties.property_type','properties.complex_name', 'properties.id as property_id', 'users.name as raised_by','users.deleted_at as raised_by_user_deleted', 'asset_categories.asset_category', 'asset_names.asset_name', 'assets.asset_tag', 'assets.deleted_at as asset_n_deleted','asset_categories.deleted_at as asset_cat_deleted_at', 'asset_names.deleted_at as asset_name_deleted_at', 'property_buildings.deleted_at as is_building_deleted')
        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
        ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
        ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
        ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
        //->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
        ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
        ->join('users', 'users.id', '=', 'work_orders.created_by')
        ->when(in_array($contractTypeId, [6, 7]), function ($query) {
          $query->leftJoin('priorities', 'priorities.id', '=', 'work_orders.priority_id'); //For Advanced Contracts
        }, function ($query) {
            $query->leftJoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id'); // For Regular Contract
        })
        ->where('work_orders.id', '=', $id)
        ->first();
        
        if(empty($details) || $details ==null || $details ==[]){
          return [];
        }
        $sla_asset_categories = DB::table('contract_asset_categories')
                    ->where('asset_category_id', $details->asset_category_id)
                    ->where('contract_number', $details->contract_number)
                    ->orderBy('id', 'desc')
                    ->first();

        $response_time = 0;
        $service_window = 0;
        $response_time_type = 'hours';
        $service_window_type = 'minutes';

        if($details->work_order_type == "reactive")
        {
          $fetchPriorityId = 0;
          if(in_array($details->contract_type_id,ContractTypes::advanced()))
          {
            $fetchPriorityId = $details->wo_priority_id;        
          }
          
          if(!empty($sla_asset_categories->priority_id)){
            $fetchPriorityId = $sla_asset_categories->priority_id;
          }
          if($fetchPriorityId != 0 && $fetchPriorityId != null){
            $contract_priorities = DB::table('contract_priorities')
                  ->where('priority_id', $fetchPriorityId)
                  ->where('contract_number', $details->contract_number)
                  ->orderBy('id', 'desc')
                  ->first();
            
            if (!$contract_priorities) 
            {
                                // Fallback query if no record found in contract_priorities
                $contract_priorities = DB::table('priorities')
                    //->select('id', 'service_window', 'service_window_type')
                    ->where('id', $sla_asset_categories->priority_id)
                    ->orderBy('id', 'desc')
                    ->first();
            }
            $response_time = isset($contract_priorities->response_time) ? $contract_priorities->response_time : 0;
            $service_window = isset($contract_priorities->service_window) ? $contract_priorities->service_window : 0;
            $response_time_type = isset($contract_priorities->response_time_type) ? $contract_priorities->response_time_type :'hours';
            $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type :'minutes';
          }
        }
        elseif($details->work_order_type == "preventive" && $details->priority_id != 0)
        {
            $contract_priorities = DB::table('contract_priorities')
                  ->where('priority_id', $details->priority_id)
                  ->where('contract_number', $details->contract_number)
                  ->orderBy('id', 'desc')
                  ->first();

            $response_time = isset($contract_priorities->response_time) ? $contract_priorities->response_time : 0;
            $service_window = isset($contract_priorities->service_window) ? $contract_priorities->service_window : 0;
            $response_time_type = isset($contract_priorities->response_time_type) ? $contract_priorities->response_time_type :'hours';
            $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type :'minutes';
        }
        else
        {
          $contract_frequencies = DB::table('frequencies_master')
                    ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                    ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                    ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                    ->where('frequencies_master.id', $details->frequency_id)
                    ->where('contract_frequencies.contract_number', $details->contract_number)
                    ->first();
          $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
          $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : '';
          $response_time_type = isset($contract_frequencies->response_time_type) ? $contract_frequencies->response_time_type:'hours';
          $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
        }
        $created_at = $details->created_at;
        $tdate = date('Y-m-d H:i:s');
        $datetime1 = strtotime($created_at);
        $datetime2 = strtotime($tdate);
        $interval  = abs($datetime2 - $datetime1);
        $minutes   = round($interval / 60);
        if($response_time_type == "days")
        {
          $response_time = $response_time * 1440;
        }
        elseif($response_time_type == "hours")
        {
          $response_time = $response_time * 60;
        }
        $time_left = $response_time - $minutes;

        if($details->job_started_at == NULL || $details->bm_approve_issue == 2)
        {
          $target_date = $details->target_date;
          $details->target_date = $target_date;
        }
        else
        {
          $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($details->job_started_at)));
          $details->target_date = $target_date;
        }
        //dd($service_window);
        // if($details->job_started_at != '' && $details->status != 4 && $details->workorder_journey != 'job_approval') //If the job is started and the status is not closed and work order journey is not Job Approval
        // {
        //   if(strtotime($target_date) >= strtotime($tdate)) //If Target date is greater that today's date
        //   {
        //     $details->pass_fail = 'pass';
        //   }
        //   else
        //   {
        //     $details->pass_fail = 'fail';
        //   }
        // }
        if($details->workorder_journey != 'job_approval' && $details->workorder_journey != 'job_evaluation') //If the job is started and the status is not closed and work order journey is not Job Approval
        {
          $details->pass_fail = 'pending';
        }
        if($details->response_time == 'On time' || $details->response_time == 'Late')
        {
          $details->response_time = $details->response_time;
        }
        else
        {
          if($time_left >= 0)
          {
            $details->response_time = $time_left;
          }
          else
          {
            $details->response_time = 'Late';
          }
        }
        return $details;

    }
    public static function get_work_order_details_pm($id)
    {
        $details = WorkOrders::with('preventiveWoActions.spAdmin', 'preventiveWoActions.supervisor')
        ->select('work_orders.*', 'contracts.contract_number','contracts.deleted_at as contract_deleted_at', 'work_orders.priority_id as wo_priority_id','priorities.priority_level','priorities.id as priority_id','property_buildings.building_name','property_buildings.building_tag', 'property_buildings.id as building_id', 'properties.property_type','properties.complex_name', 'properties.id as property_id', 'users.name as raised_by', 'asset_categories.asset_category', 'asset_names.asset_name', 'assets.asset_tag','asset_categories.deleted_at as asset_cat_deleted_at', 'assets.deleted_at as asset_n_deleted', 'asset_names.deleted_at as asset_name_deleted_at', 'property_buildings.deleted_at as is_building_deleted','users.deleted_at as raised_by_user_deleted')
        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
        ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
        ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
        ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
        ->leftjoin('priorities', 'priorities.id', '=', 'work_orders.priority_id')
        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
        ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
        ->join('users', 'users.id', '=', 'work_orders.created_by')->where('work_orders.id', '=', $id)->first();
        $sla_asset_categories = DB::table('contract_asset_categories')
                    ->where('asset_category_id', $details->asset_category_id)
                    ->where('contract_number', $details->contract_number)
                    ->orderBy('id', 'desc')
                    ->first();

        $response_time = 0;
        $service_window = 0;
        $response_time_type = 'hours';
        $service_window_type = 'minutes';
        if($details->work_order_type == "reactive")
        {
          if(!empty($sla_asset_categories->priority_id)){
            $contract_priorities = DB::table('contract_priorities')
                  ->where('priority_id', $sla_asset_categories->priority_id)
                  ->where('contract_number', $details->contract_number)
                  ->orderBy('id', 'desc')
                  ->first();
            $response_time = $contract_priorities->response_time;
            $service_window = $contract_priorities->service_window;
            $response_time_type = $contract_priorities->response_time_type;
            $service_window_type = $contract_priorities->service_window_type;
          }
        }
        elseif($details->work_order_type == "preventive" && $details->priority_id != 0)
        {
            $contract_priorities = DB::table('contract_priorities')
                  ->where('priority_id', $details->priority_id)
                  ->where('contract_number', $details->contract_number)
                  ->orderBy('id', 'desc')
                  ->first();
            $response_time = 0;
            $service_window = 0;
            if(isset($contract_priorities->response_time))
            {
              $response_time = $contract_priorities->response_time;
              $service_window = $contract_priorities->service_window;
              $response_time_type = $contract_priorities->response_time_type;
              $service_window_type = $contract_priorities->service_window_type;
            }
        }
        else
        {
          $contract_frequencies = DB::table('frequencies_master')
                    ->select('contract_frequencies.response_time', 'contract_frequencies.service_window','contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                    ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                    ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                    ->where('frequencies_master.id', $details->frequency_id)
                    ->where('contract_frequencies.contract_number', $details->contract_number)
                    ->first();
          $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
          $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : '';
          $response_time_type = isset($contract_frequencies->response_time_type) ? $contract_frequencies->response_time_type:'hours';
          $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
        }
        $created_at = $details->created_at;
        $tdate = date('Y-m-d H:i:s');
        $datetime1 = strtotime($created_at);
        $datetime2 = strtotime($tdate);
        $interval  = abs($datetime2 - $datetime1);
        $minutes   = round($interval / 60);
        if($response_time_type == "days")
        {
          $response_time = $response_time * 1440;
        }
        elseif($response_time_type == "hours")
        {
          $response_time = $response_time * 60;
        }
        $time_left = $response_time - $minutes;

        if($details->job_started_at == NULL || $details->bm_approve_issue == 2)
        {
          $target_date = $details->target_date;
          $details->target_date = $target_date;
        }
        else
        {
          if($details->work_order_type == "preventive" && $details->target_date != ""  && $details->target_date != "00:00:00")
          {
            $target_date = $details->target_date;
          }
          else
          {
            $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($details->job_started_at)));
          }

          $details->target_date = $target_date;
        }
        //dd($service_window);
        // if($details->job_started_at != '' && $details->status != 4 && $details->workorder_journey != 'job_approval') //If the job is started and the status is not closed and work order journey is not Job Approval
        // {
        //   if(strtotime($target_date) >= strtotime($tdate)) //If Target date is greater that today's date
        //   {
        //     $details->pass_fail = 'pass';
        //   }
        //   else
        //   {
        //     $details->pass_fail = 'fail';
        //   }
        // }
        if($details->workorder_journey != 'job_approval') //If the job is started and the status is not closed and work order journey is not Job Approval
        {
          $details->pass_fail = 'pending';
        }
        if($details->response_time == 'On time' || $details->response_time == 'Late')
        {
          $details->response_time = $details->response_time;
        }
        else
        {
          if($time_left >= 0)
          {
            $details->response_time = $time_left;
          }
          else
          {
            $details->response_time = 'Late';
          }
        }
        return $details;

    }

    public static function get_work_order_timeline($id, $unique_id = NULL)
    {
      $timeline = DB::table('notifications')
        ->select('notifications.*', 'work_orders.work_order_type')
        ->leftjoin('work_orders', 'work_orders.id', '=', 'notifications.section_id');
                  if($unique_id)
                  {
                    $timeline = $timeline->whereRaw("(section_id = $id or section_id = $unique_id)");
                  }
                  else
                  {
                    $timeline = $timeline->where('section_id', $id);
                  }
                  $timeline = $timeline->where('section_type', 'work_order')
                  ->orderBy('id', 'desc');
                  $timeline = $timeline->where(function ($query) use ($timeline) {
                    $query->where('is_timeline', 'yes')->orWhere('is_timeline',null);
                    })->get();
      $timeline = json_decode(json_encode($timeline), true);
      return $timeline;
    }

    public static function get_supervisors($supervisor_ids)
    {
        $users = DB::table('users')
                    ->select('users.name','users.deleted_at')
                    ->whereIn('id', explode(',', $supervisor_ids))
                    ->orderBy('id', 'desc')
                    ->get();
        $users = json_decode(json_encode($users), true);
        //dd($users);
        $names = [];
        $result = '';
        if(!empty($users))
        {
          foreach($users as $row)
          {
            $deleted_at = !empty($row['deleted_at']) ? __('general_sentence.modal.deleted') : '';
            $names[] = $row['name'].$deleted_at;
          }
          $result = implode(', ', $names);
        }
        return $result;
    }

    public static function get_work_order_chat($id)
    {
        $chat = DB::table('work_order_chat')
                    ->select('work_order_chat.*', 'users.name', 'users.profile_img', 'users.user_type')
                    ->join('users', 'users.id', '=', 'work_order_chat.user_id')
                    ->where('work_order_id', $id)
                    ->orderBy('id', 'asc')
                    ->get();
        // dd($chat);
        $chat = json_decode(json_encode($chat), true);
        foreach($chat as $key => $wo)
          {

            if($wo['attach_file'] && in_array($wo['attach_file_mime_type'],array('image/jpeg','image/jpg','image/png')))
            {
                $chat[$key]['file_exist_path'] = (trim(ImagesUploadHelper::displayImage($wo['attach_file'], 'work_order_chat_files')) != "");
                $chat[$key]['file_path'] = ImagesUploadHelper::displayImage($wo['attach_file'], 'work_order_chat_files');
            }
            elseif($wo['attach_file'] && !in_array($wo['attach_file_mime_type'],array('image/jpeg','image/jpg','image/png')))
            {
                $chat[$key]['file_exist_path'] = file_exists(storage_path('app/public/'.$wo['attach_file']));
                $chat[$key]['file_path'] = url('storage/' . $wo['attach_file']);
            }
            else
            {
                $chat[$key]['file_exist_path'] = '';
                $chat[$key]['file_path'] = '';
            }
          }
        return $chat;
    }

    public static function get_checklist_images($id)
    {
        $photos = DB::table('no_checklist_actions')
                    ->select('no_checklist_actions.photos')
                    ->where('work_order_id', $id)
                    ->orderBy('id', 'asc')
                    ->get();
        $photos = json_decode(json_encode($photos), true);
        $images = [];
        if(!empty($photos))
        {
          foreach($photos as $row)
          {
            $json = json_decode($row['photos']);
            foreach($json as $prow)
            {
              $images[] = $prow;
            }
          }
        }
        return $images;
    }

    public static function get_checklist_worker_comment($id)
    {
        $comment = DB::table('no_checklist_actions')
                    ->select('no_checklist_actions.comment')
                    ->where('work_order_id', $id)
                    ->orderBy('id', 'desc')
                    ->first();

        if(isset($comment))
        {
          return $comment->comment;
        }
        else
        {
          return '';
        }
    }

    public static function get_workers($contract_id, $asset_category_id, $building_id)
    {
      //dd($contract_id);
      if(Auth::user()->user_type == 'sp_admin') {
        $user_id = Auth::user()->project_user_id;
        //dd($building_id);
        $chat = DB::table('users')
        ->select('users.id', 'users.name', 'users.profile_img', 'users.deleted_at', 'users.is_subcontractors_worker')
        ->join('user_assets_mapping', 'user_assets_mapping.user_id', '=', 'users.id')
        ->where('users.user_type', 'sp_worker')
        //->where('users.project_user_id', $user_id)
        ->whereRaw("find_in_set($building_id, users.building_ids)")
        ->whereIn('user_assets_mapping.contract_id', explode(',',$contract_id))
        ->whereIn('user_assets_mapping.asset_id', explode(',',$asset_category_id))
        ->where('user_assets_mapping.user_type', 'sp_worker')
        ->where('users.deleted_at', null)
        ->where('users.is_deleted', 'no')

        ->groupBy('users.id')
        ->orderBy('users.id', 'desc');

        // $query = str_replace(array('?'), array('\'%s\''), $chat->toSql());
        // $query = vsprintf($query, $chat->getBindings());
        // dump($query);

        $chat = $chat->get();
      }
      elseif(Auth::user()->user_type == 'supervisor') {
        $user_id = Auth::user()->id;
        //dd($user_id);
        $chat = DB::table('users')
        ->select('users.id', 'users.name', 'users.profile_img', 'users.deleted_at', 'users.is_subcontractors_worker')
        ->join('user_assets_mapping', 'user_assets_mapping.user_id', '=', 'users.id')
        ->where('users.user_type', 'sp_worker')
        //->where('users.supervisor_id', $user_id)
        ->whereRaw("find_in_set($building_id, users.building_ids)")
        ->whereIn('user_assets_mapping.contract_id', explode(',',$contract_id))
        ->whereIn('user_assets_mapping.asset_id', explode(',',$asset_category_id))
        ->whereRaw("find_in_set($user_id, users.supervisor_id)")
        ->where('user_assets_mapping.user_type', 'sp_worker')
        ->where('users.deleted_at', null)
        ->where('users.is_deleted', 'no')
        ->groupBy('users.id')
        ->orderBy('users.id', 'desc');

        //$query = str_replace(array('?'), array('\'%s\''), $chat->toSql());
        //$query = vsprintf($query, $chat->getBindings());
        //dump($query);
        // die;

        $chat = $chat->get();
      }else {
        // For all
        $user_id = Auth::user()->project_user_id;
        $chat = DB::table('users')
        ->select('users.id', 'users.name', 'users.profile_img', 'users.deleted_at', 'users.is_subcontractors_worker')
        ->join('user_assets_mapping', 'user_assets_mapping.user_id', '=', 'users.id')
        ->where('users.user_type', 'sp_worker')
        ->where('users.project_user_id', $user_id)
        ->whereRaw("find_in_set($building_id, users.building_ids)")
        ->whereIn('user_assets_mapping.contract_id', explode(',',$contract_id))
        ->whereIn('user_assets_mapping.asset_id', explode(',',$asset_category_id))
        ->where('user_assets_mapping.user_type', 'sp_worker')
        ->groupBy('users.id')
        ->orderBy('users.id', 'desc')
        ->get();
      }

      $chat = json_decode(json_encode($chat), true);
      return $chat;
    }

    public static function get_pass_fail_score($user_id)
    {
        $date = date('Y-m-d H:i:s', strtotime('-48 days'));
        DB::enableQueryLog();
        $total = Self::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by')->where('work_orders.pass_fail', '!=', 'pending')->where('work_orders.job_completion_date', '>=', $date);
        if(!empty(Auth::user()->user_type == 'building_manager'))
        {
          $total = $total->where('work_orders.created_by', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          $total = $total->where('work_orders.service_provider_id', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
          $total = $total->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
        }
        elseif(!empty(Auth::user()->user_type == 'admin') || !empty(Auth::user()->user_type == 'admin_employee'))
        {
          $total = $total->where('work_orders.created_by', $user_id);
        }
        $total = $total->count();

        $pass = Self::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by')->where('work_orders.pass_fail', '=', 'pass')->where('work_orders.job_completion_date', '>=', $date);
        if(!empty(Auth::user()->user_type == 'building_manager'))
        {
          $pass = $pass->where('work_orders.created_by', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          $pass = $pass->where('work_orders.service_provider_id', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
          $pass = $pass->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
        }
        elseif(!empty(Auth::user()->user_type == 'admin') || !empty(Auth::user()->user_type == 'admin_employee'))
        {
          $pass = $pass->where('work_orders.created_by', $user_id);
        }
        $pass = $pass->count();
        $pass_fail = 0;

        if($total > 0)
        {
            $pass_fail = ($pass/$total) * 100;
        }
        //echo $pass_fail;exit;
        return $pass_fail;
    }

    public static function get_score($user_id)
    {
        $score = Self::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by');
        if(!empty(Auth::user()->user_type == 'building_manager'))
        {
          $score = $score->where('work_orders.created_by', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          $score = $score->where('work_orders.service_provider_id', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
          $score = $score->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
        }
        elseif(!empty(Auth::user()->user_type == 'admin') || !empty(Auth::user()->user_type == 'admin_employee'))
        {
          $score = $score->where('work_orders.created_by', $user_id);
        }
        $score = $score->avg('work_orders.score');
        //echo round($score, 1);exit;
        return round($score, 1);
    }

    public static function job_distribution($user_id)
    {
        //$count = Self::get_work_orders_count($user_id);
        $count = Self::get_work_orders_count($user_id);


        $reactive_total = Self::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by')->where('work_orders.work_order_type', '=', 'reactive')->where('contracts.user_id', '=', $user_id)->count();

        $preventive_total = Self::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by')->where('work_orders.work_order_type', '=', 'preventive')->where('contracts.user_id', '=', $user_id)->count();

        $reactive_total_percentage_var=0;
        $preventive_total_percentage_var=0;

        if($count>0)
        {
            $reactive_total_percentage_var=round(($reactive_total/$count) * 100);
            $preventive_total_percentage_var=round(($preventive_total/$count) * 100);
        }

        $data = [
            'count' => $count,
            'reactive_total' => $reactive_total,
            'preventive_total' => $preventive_total,
            'reactive_total_percentage' => $reactive_total_percentage_var,
            'preventive_total_percentage' => $preventive_total_percentage_var
        ];
        return $data;
    }

    public static function job_ratings($user_id)
    {
        $ratings = Self::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by');
        if(!empty(Auth::user()->user_type == 'building_manager'))
        {
          $ratings = $ratings->where('work_orders.created_by', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          $ratings = $ratings->where('work_orders.service_provider_id', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
          $ratings = $ratings->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
        }
        elseif(!empty(Auth::user()->user_type == 'admin') || !empty(Auth::user()->user_type == 'admin_employee'))
        {
          $ratings = $ratings->where('work_orders.created_by', $user_id);
        }
        $ratings = $ratings->avg('rating');

        $total_stars = Self::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by');
        if(!empty(Auth::user()->user_type == 'building_manager'))
        {
          $total_stars = $total_stars->where('work_orders.created_by', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          $total_stars = $total_stars->where('work_orders.service_provider_id', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
          $total_stars = $total_stars->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
        }
        elseif(!empty(Auth::user()->user_type == 'admin') || !empty(Auth::user()->user_type == 'admin_employee'))
        {
          $total_stars = $total_stars->where('work_orders.created_by', $user_id);
        }
        $total_stars = $total_stars->count();

        $star_ratings_count = [];
        for($i=5; $i>=1; $i--)
        {
          $ratings_count = Self::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by')->where('rating', $i);
          if(!empty(Auth::user()->user_type == 'building_manager'))
          {
            $ratings_count = $ratings_count->where('work_orders.created_by', $user_id);
          }
          elseif(!empty(Auth::user()->user_type == 'sp_admin'))
          {
            $user_id = Auth::user()->id;
            $ratings_count = $ratings_count->where('work_orders.service_provider_id', $user_id);
          }
          elseif(!empty(Auth::user()->user_type == 'supervisor'))
          {
            $user_id = Auth::user()->id;
            $ratings_count = $ratings_count->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
          }
          elseif(!empty(Auth::user()->user_type == 'admin') || !empty(Auth::user()->user_type == 'admin_employee'))
          {
            $ratings_count = $ratings_count->where('work_orders.created_by', $user_id);
          }
          $ratings_count = $ratings_count->count();

          if($total_stars>0)
          {
            $percent = $ratings_count * 100 / $total_stars;
          }
          else
          {
            $percent = 0;
          }
          $star_ratings_count[$i] = $ratings_count .' '.__('dashboard.job_ratings.jobs').' <span class="d-inline-block direction-ltr">('.round($percent).'%)</span>';
        }
        //echo "<pre>";print_r($star_ratings_count);exit;
        $data = [
          'ratings' => round($ratings, 1),
          'star_ratings_count' => $star_ratings_count
        ];
        return $data;
    }

    /********** Building Manager Start **************/
    public static function get_open_count($user_id, $search=null)
    {
        //DB::enableQueryLog();
        $work_orders = WorkOrders::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by')
        ->where('work_orders.status',1)
        ->where('work_orders.contract_type','!=', 'warranty')
        ->where('work_orders.is_deleted', '=', "no")
        ->where('work_orders.start_date', '<=', date('Y-m-d'));
        if($search!=null)
        {
            $work_orders = $work_orders->where('pass_fail', '=', $search);
        }
        if(!empty(Auth::user()->user_type != 'sp_admin') && !empty(Auth::user()->user_type != 'supervisor'))
        {
          $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);
        }
        // if(!empty(Auth::user()->user_type == 'building_manager'))
        // {
        //   $bm_employees = User::select('id')->where('sp_admin_id', $user_id)->where('status', 1)->where('is_deleted', 'no')->get()->pluck('id')->implode(',');
        //   $bm_employees = explode(',', $bm_employees);
        //   //dd($user_id);
        //   $bm_employees[] = $user_id;
        //   //print_r($bm_employees);exit;
        //   $work_orders = $work_orders->whereIn('work_orders.created_by', $bm_employees);
        // }
        if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager'))
        {
          //dd($user_id);
          $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
          $building_ids = explode(',', $bm_employees->building_ids);
          //print_r($bm_employees);exit;
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          //dd(Auth::user());
          $contract_id = Contracts::where('status', 1)->where('is_deleted', 'no')->where('service_provider_id', Auth::user()->service_provider)->pluck('id')->toArray();
          $work_orders = $work_orders->whereIn('work_orders.contract_id', $contract_id)->where('work_orders.contract_type', 'regular');
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
         $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)")->where('work_orders.contract_type', 'regular');
        }
        elseif($service_provider_id)
        {
            $work_orders = $work_orders->where('contracts.service_provider_id', '=', $service_provider_id);
        }
        $work_orders = $work_orders->count();
        //dd(DB::getQueryLog());
        return $work_orders;
    }

    public static function getPassFailCount($user_id,$status)
    {
      $work_orders = WorkOrders::select('work_orders.*', 'contracts.contract_number')
        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
        ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
        ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
        ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
        ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
        ->join('users', 'users.id', '=', 'work_orders.created_by')
        ->where('work_orders.is_deleted', '=', "no")
        ->where('work_orders.start_date', '<=', date('Y-m-d'))
        ->orderByRaw('work_orders.created_at desc');
      if(isset($status))
      {
        $work_orders = $work_orders->where('work_orders.status',$status);
      }
      if(!empty(Auth::user()->user_type != 'sp_admin') && !empty(Auth::user()->user_type != 'supervisor'))
      {
        $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);
      }
      // if(!empty(Auth::user()->user_type == 'building_manager'))
      // {
      //   $bm_employees = User::select('id')->where('sp_admin_id', $user_id)->where('status', 1)->where('is_deleted', 'no')->get()->pluck('id')->implode(',');
      //   $bm_employees = explode(',', $bm_employees);
      //   //dd($user_id);
      //   $bm_employees[] = $user_id;
      //   //print_r($bm_employees);exit;
      //   $work_orders = $work_orders->whereIn('work_orders.created_by', $bm_employees);
      // }
      if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager'))
      {
        $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
        $building_ids = explode(',', $bm_employees->building_ids);
        //print_r($bm_employees);exit;
        $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
        $asset_categories = explode(',', Auth::user()->asset_categories);
        $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
      }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->where('work_orders.service_provider_id', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
        }
        elseif(!empty(Auth::user()->user_type == 'admin') || !empty(Auth::user()->user_type == 'admin_employee'))
        {
          $work_orders = $work_orders->where('work_orders.created_by', $user_id);
        }
        $work_orders = $work_orders->get();
        $work_orders = json_decode(json_encode($work_orders), true);
        $data = [];
        if(!empty($work_orders))//if(!empty($work_orders))
        {
          $fail_count=0;
          $pass_count=0;
          foreach($work_orders as $key => $wo)
          {
            if($wo['pass_fail'] == "pass")
            {
              $pass_count++;
            }
            elseif($wo['pass_fail'] == "fail")
            {
              $fail_count++;
            }
          }
          $data=[];
          $data['pass_count'] = $pass_count;
          $data['fail_count'] = $fail_count;
        }
        return $data;
    }

    public static function get_inprogress_count($user_id,$search=null)
    {
        $work_orders = WorkOrders::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by')->where('work_orders.status',2)->where('work_orders.is_deleted', '=', "no")->where('work_orders.start_date', '<=', date('Y-m-d'));
        if($search!=null)
        {
          $work_orders = $work_orders->where('pass_fail', '=', $search);
        }
        if(!empty(Auth::user()->user_type == 'building_manager'))
        {
          $building_ids = explode(',', Auth::user()->building_ids);
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
          //$work_orders = $work_orders->where('work_orders.created_by', $user_id);
        }
        if(!empty(Auth::user()->user_type == 'building_manager_employee'))
        {
          $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
          $building_ids = explode(',', $bm_employees->building_ids);
          //print_r($bm_employees);exit;
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->where('work_orders.service_provider_id', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
        }
        elseif(!empty(Auth::user()->user_type == 'admin'))
        {
          $work_orders = $work_orders->where('work_orders.created_by', $user_id);
        }
        $work_orders = $work_orders->count();
        return $work_orders;
    }

    public static function get_onhold_count($user_id,$search=null)
    {
        $work_orders = WorkOrders::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by')->where('work_orders.status',3)->where('work_orders.is_deleted', '=', "no")->where('work_orders.start_date', '<=', date('Y-m-d'));
        if($search!=null)
        {
          $work_orders = $work_orders->where('pass_fail', '=', $search);
        }
        if(!empty(Auth::user()->user_type == 'building_manager'))
        {
          $building_ids = explode(',', Auth::user()->building_ids);
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
          //$work_orders = $work_orders->where('work_orders.created_by', $user_id);
        }
        if(!empty(Auth::user()->user_type == 'building_manager_employee'))
        {
          $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
          $building_ids = explode(',', $bm_employees->building_ids);
          //print_r($bm_employees);exit;
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->where('work_orders.service_provider_id', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
        }
        elseif(!empty(Auth::user()->user_type == 'admin'))
        {
          $work_orders = $work_orders->where('work_orders.created_by', $user_id);
        }
        $work_orders = $work_orders->count();
        return $work_orders;
    }

    public static function get_closed_count($user_id,$search=null)
    {
        $work_orders = WorkOrders::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by')->where('work_orders.status', 4)->where('work_orders.is_deleted', '=', "no")->where('work_orders.start_date', '<=', date('Y-m-d'));
        //dd($search);
        if($search!=null)
        {
          $work_orders = $work_orders->where('pass_fail', '=', $search);
        }
        if(!empty(Auth::user()->user_type != 'sp_admin') && !empty(Auth::user()->user_type != 'supervisor'))
        {
          $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);
        }
        // if(!empty(Auth::user()->user_type == 'building_manager'))
        // {
        //   $bm_employees = User::select('id')->where('sp_admin_id', $user_id)->where('status', 1)->where('is_deleted', 'no')->get()->pluck('id')->implode(',');
        //   $bm_employees = explode(',', $bm_employees);
        //   //dd($user_id);
        //   $bm_employees[] = $user_id;
        //   //print_r($bm_employees);exit;
        //   $work_orders = $work_orders->whereIn('work_orders.created_by', $bm_employees);
        // }
        if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager'))
        {
          $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
          $building_ids = explode(',', $bm_employees->building_ids);
          //print_r($bm_employees);exit;
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->where('work_orders.service_provider_id', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
        }
        elseif(!empty(Auth::user()->user_type == 'admin'))
        {
          $work_orders = $work_orders->where('work_orders.created_by', $user_id);
        }
        $work_orders = $work_orders->count();
        return $work_orders;
    }


    public static function get_closed_count_all($user_id,$url=null)
    {
      if(trim(array_reverse(explode('/',$url))[0]) == "" || array_reverse(explode('/',$url))[0] == "closed")
      {
        $service_provider_id = 'no';
      }
      else
      {
        try
        {
          $service_provider_id = \Crypt::decryptString(trim(array_reverse(explode('/',$url))[0]));
          //dd(trim(array_reverse(explode('/',$url))[1]));
          if(trim(array_reverse(explode('/',$url))[1]) != 'work-order' && trim(array_reverse(explode('/',$url))[2]) != 'work-order')
          {
            $service_provider_id = 'no';
          }
        }
        catch(DecryptException $e)
        {
          $service_provider_id = 'no';
        }
        if(trim(array_reverse(explode('/',$url))[1]) == 'work-order' && (!empty(Auth::user()->user_type == 'osool_admin') || !empty(Auth::user()->user_type == 'admin_employee') || !empty(Auth::user()->user_type == 'admin')))
        {
          $service_provider_id = 'no';
        }
      }

      $date = date('Y-m-d');
        $work_orders = WorkOrders::select('work_orders.*', 'contracts.contract_number')
              ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
              ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
              ->join('users', 'users.id', '=', 'work_orders.created_by')
              ->where('work_orders.status', '=', 4)
              ->where('work_orders.is_deleted', '=', "no")
              ->where('work_orders.start_date', '<=', $date)
              //->where('work_orders.project_user_id', Auth::user()->project_user_id)
              ->orderByRaw('work_orders.created_at asc');
        if(!empty(Auth::user()->user_type != 'sp_admin') && !empty(Auth::user()->user_type != 'supervisor')) //If the User Type is not SP Admin and not Supervisor
        {
          $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);
        }
        if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager')) //If the logged in User Type is Building Manager or Building Manager Employee
        {
          $bm_employees = User::select('building_ids')->where('id', $user_id)->where('status', 1)->where('is_deleted', 'no')->first();
          $building_ids = explode(',', $bm_employees->building_ids);
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin')) //If the logged in User Type is SP Admin
        {
          $user_id = Auth::user()->id;
          //dd($user_id);
          //$work_orders = $work_orders->where('work_orders.service_provider_id', $user_id)
          //->where('work_orders.contract_type', 'regular')
          //->where('work_orders.status', 4);
          $contract_id = Contracts::where('status', 1)->where('is_deleted', 'no')->where('service_provider_id', Auth::user()->service_provider)->pluck('id')->toArray();
          $work_orders = $work_orders->whereIn('work_orders.contract_id', $contract_id)->where('work_orders.contract_type', 'regular')->where('work_orders.status', 4);
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor')) //If the logged in User Type is Supervisor
        {
          //dd($user_id);
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)")->where('work_orders.contract_type', 'regular');
        }
        elseif($service_provider_id != 'no')
        {
          $work_orders = $work_orders->where('contracts.service_provider_id', '=', $service_provider_id);
        }
        if(Auth::user()->user_type == 'super_admin' || Auth::user()->user_type == 'admin_employee' || Auth::user()->user_type == 'admin')
        {
          if($service_provider_id != 'no')
          {
            $work_orders = $work_orders->where('contracts.service_provider_id', '=', $service_provider_id);
          }
        }

        $work_orders = $work_orders->count();
        return $work_orders;

    }

    public static function get_workOrderList($user_id,$value=null,$limit=NULL,$start=NULL, $length=NULL)
    {
        //dd($length);
        $date = date('Y-m-d');
        $work_orders = WorkOrders::select('work_orders.*')
        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
        ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
        ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
        ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
        ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
        ->join('users', 'users.id', '=', 'work_orders.created_by')
        ->where('work_orders.is_deleted', '=', "no")
        ->where('work_orders.start_date', '<=', $date);
        //->orderByRaw('work_orders.created_at desc');
        if($value!=null){
          $work_orders=$work_orders->where('work_orders.status',$value);
        }
        if($limit!=null){
          $work_orders=$work_orders->limit($limit);
        }
        if(!empty(Auth::user()->user_type != 'sp_admin') && !empty(Auth::user()->user_type != 'supervisor'))
        {
          $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);
        }
        // if(!empty(Auth::user()->user_type == 'building_manager'))
        // {
        //   $bm_employees = User::select('id')->where('sp_admin_id', $user_id)->where('status', 1)->where('is_deleted', 'no')->get()->pluck('id')->implode(',');
        //   $bm_employees = explode(',', $bm_employees);
        //   //dd($user_id);
        //   $bm_employees[] = $user_id;
        //   //print_r($bm_employees);exit;
        //   $work_orders = $work_orders->whereIn('work_orders.created_by', $bm_employees);
        // }
        if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager'))
        {
          //dd($user_id);
          $bm_employees = User::select('building_ids')->where('id', Auth::user()->id)->where('status', 1)->where('is_deleted', 'no')->first();
          //dd($bm_employees->building_ids);
          $building_ids = explode(',', $bm_employees->building_ids);
          //print_r($building_ids);exit;
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->where('work_orders.service_provider_id', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
          //dd($user_id);
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
        }
        elseif(!empty(Auth::user()->user_type == 'admin'))
        {
          $work_orders = $work_orders->where('work_orders.created_by', $user_id);
        }
        //$work_orders = $work_orders->orderBy('work_orders.id', 'desc')->paginate(10);
        if($length != null)
        {
          $work_orders = $work_orders->skip($start)->take($length)->orderBy('work_orders.id', 'desc')->get();
        }
        else
        {
          $work_orders = $work_orders->orderBy('work_orders.id', 'desc')->get();
        }
        //dd(DB::getQueryLog());
        // $work_orders = json_decode(json_encode($work_orders), true);
        //echo '<pre>'; print_r($work_orders); die;
        if(!empty($work_orders))//if(!empty($work_orders))
        {
          foreach($work_orders as $key => $wo)
          {
            $sla_asset_categories = DB::table('contract_asset_categories')
                      ->where('asset_category_id', $wo['asset_category_id'])
                      ->where('contract_number', $wo['contract_number'])
                      ->orderBy('id', 'desc')
                      ->first();

            $response_time = 0;
            $service_window = 0;
            $response_time_type = 'hours';
            $service_window_type = 'minutes';
            if($wo['work_order_type'] == "reactive") //If the work order type is Reactive
            {
              if(!empty($sla_asset_categories->priority_id)){
                $contract_priorities = DB::table('contract_priorities')
                      ->where('priority_id', $sla_asset_categories->priority_id)
                      ->where('contract_number', $wo['contract_number'])
                      ->orderBy('id', 'desc')
                      ->first();
                
                $response_time = 0;
                $service_window = 0;
                if(isset($contract_priorities->response_time))
                {
                  $response_time = $contract_priorities->response_time;
                  $service_window = $contract_priorities->service_window;
                  $response_time_type = $contract_priorities->response_time_type;
                  $service_window_type = $contract_priorities->service_window_type;
                }
              }
            }
            elseif($wo['work_order_type'] == "preventive" && $wo['priority_id'] != 0) //If the Work order Type is Preventive and Priority ID is not 0
            {
              $contract_priorities = DB::table('contract_priorities')
                    ->where('priority_id', $wo['priority_id'])
                    ->where('contract_number', $wo['contract_number'])
                    ->orderBy('id', 'desc')
                    ->first();
              $response_time = 0;
              $service_window = 0;
              if(isset($contract_priorities->response_time))
              {
                $response_time = $contract_priorities->response_time;
                $service_window = $contract_priorities->service_window;
                $response_time_type = $contract_priorities->response_time_type;
                $service_window_type = $contract_priorities->service_window_type;
              }
            }
            else
            {
              $contract_frequencies = DB::table('frequencies_master')
                        ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                        ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                        ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                        ->where('frequencies_master.id', $wo['frequency_id'])
                        ->where('contract_frequencies.contract_number', $wo['contract_number'])
                        ->first();
              $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
              $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : 0;
              $response_time_type = isset($contract_frequencies->response_time_type) ? $contract_frequencies->response_time_type:'hours';
              $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
            }
            $created_at = $wo['created_at'];
            $tdate = date('Y-m-d H:i:s');
            $datetime1 = strtotime($created_at);
            $datetime2 = strtotime($tdate);
            $interval  = abs($datetime2 - $datetime1);
            $minutes   = round($interval / 60);
            if($response_time_type == "days")
            {
              $response_time = $response_time * 1440;
            }
            elseif($response_time_type == "hours")
            {
              $response_time = $response_time * 60;
            }
            $time_left = $response_time - $minutes;

            if($wo['job_started_at'] == NULL || $wo['bm_approve_issue'] == 2)
            {
              $target_date = $wo['target_date'];
              $work_orders[$key]['target_date'] = $target_date;
            }
            else
            {
              $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($wo['job_started_at'])));
              $work_orders[$key]['target_date'] = $target_date;

            }

            // if($wo['job_started_at'] != '' && $wo['status'] != 4 && $wo['workorder_journey'] != 'job_approval') //If the job is started and the status is not closed and work order journey is not Job Approval
            // {
            //   if(strtotime($target_date) >= strtotime($tdate)) //If Target date is greater that today's date
            //   {
            //     $work_orders[$key]['pass_fail'] = 'pass';
            //   }
            //   else
            //   {
            //     $work_orders[$key]['pass_fail'] = 'fail';
            //   }
            // }
            if($wo['workorder_journey'] == 'finished') //If the job is started and the status is not closed and work order journey is not Job Approval
            {
              $work_orders[$key]['pass_fail'] = $wo['pass_fail'];
            }
            elseif($wo['workorder_journey'] != 'job_approval') //If the job is started and the status is not closed and work order journey is not Job Approval
            {
              $work_orders[$key]['pass_fail'] = 'pending';
            }

            if($wo['response_time'] == 'On time' || $wo['response_time'] == 'Late')
            {
              $work_orders[$key]['response_time'] = $wo['response_time'];
            }
            else
            {
              if($time_left >= 0)
              {
                $work_orders[$key]['response_time'] = $time_left;
              }
              else
              {
                $work_orders[$key]['response_time'] = 'Late';
              }
            }
          }
        }
        return $work_orders;
    }

    public static function get_workOrderList_count($user_id,$value=null,$limit=NULL)
    {
        //dd($value);
        $date = date('Y-m-d');
        $work_orders = WorkOrders::select('work_orders.*')
        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
        ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
        ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
        ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
        ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
        ->join('users', 'users.id', '=', 'work_orders.created_by')
        ->where('work_orders.is_deleted', '=', "no")
        ->where('work_orders.start_date', '<=', $date);
        //->orderByRaw('work_orders.created_at desc');
        if($value!=null){
          $work_orders=$work_orders->where('work_orders.status',$value);
        }
        if($limit!=null){
          $work_orders=$work_orders->limit($limit);
        }
        if(!empty(Auth::user()->user_type != 'sp_admin') && !empty(Auth::user()->user_type != 'supervisor'))
        {
          $work_orders = $work_orders->where('work_orders.project_user_id', Auth::user()->project_user_id);
        }
        // if(!empty(Auth::user()->user_type == 'building_manager'))
        // {
        //   $bm_employees = User::select('id')->where('sp_admin_id', $user_id)->where('status', 1)->where('is_deleted', 'no')->get()->pluck('id')->implode(',');
        //   $bm_employees = explode(',', $bm_employees);
        //   //dd($user_id);
        //   $bm_employees[] = $user_id;
        //   //print_r($bm_employees);exit;
        //   $work_orders = $work_orders->whereIn('work_orders.created_by', $bm_employees);
        // }
        if(!empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'building_manager'))
        {
          //dd($user_id);
          $bm_employees = User::select('building_ids')->where('id', Auth::user()->id)->where('status', 1)->where('is_deleted', 'no')->first();
          //dd($bm_employees->building_ids);
          $building_ids = explode(',', $bm_employees->building_ids);
          //print_r($building_ids);exit;
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->where('work_orders.service_provider_id', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
          //dd($user_id);
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
        }
        elseif(!empty(Auth::user()->user_type == 'admin'))
        {
          $work_orders = $work_orders->where('work_orders.created_by', $user_id);
        }
        //$work_orders = $work_orders->orderBy('work_orders.id', 'desc')->paginate(10);
        $work_orders = $work_orders->orderBy('work_orders.id', 'desc')->count();
        //dd(DB::getQueryLog());
        // $work_orders = json_decode(json_encode($work_orders), true);
        //echo '<pre>'; print_r($work_orders); die;
        return $work_orders;
    }

    public static function get_workOrderListFilter($user_id,$value)
    {
        //echo $value; die;
        $work_orders = WorkOrders::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by');
        if($value=='Open')
        {
          $work_orders=$work_orders->where('work_orders.status',1);
        }
        else if($value=='In Progress')
        {
          $work_orders=$work_orders->where('work_orders.status',2);
        }
        else if($value=='On Hold')
        {
          $work_orders=$work_orders->where('work_orders.status',3);
        }
        if(!empty(Auth::user()->user_type == 'building_manager'))
        {
          $building_ids = explode(',', Auth::user()->building_ids);
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
          //$work_orders = $work_orders->where('work_orders.created_by', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->where('work_orders.service_provider_id', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
        }
        elseif(!empty(Auth::user()->user_type == 'admin'))
        {
          $work_orders = $work_orders->where('work_orders.created_by', $user_id);
        }
        $work_orders = $work_orders->orderBy('work_orders.id', 'desc')->paginate(100);
        //dd(DB::getQueryLog());
        // $work_orders = json_decode(json_encode($work_orders), true);
        return $work_orders;
    }

    public static function get_workOrderListFilterClosed($user_id,$value,$date)
    {
      if($date!=NULL)
      {
        $search = explode('-',$date);
        $start = date('Y-m-d',strtotime($search[0]));
        $end = date('Y-m-d',strtotime($search[1]));
        //echo $value; die;
        $work_orders = WorkOrders::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by');
        if($value=='Closed')
        {
          $work_orders=$work_orders->where('work_orders.status', 4);
        }

        if(!empty(Auth::user()->user_type == 'building_manager'))
        {
          $building_ids = explode(',', Auth::user()->building_ids);
          $work_orders = $work_orders->whereIn('work_orders.property_id', $building_ids);
          $asset_categories = explode(',', Auth::user()->asset_categories);
          $work_orders = $work_orders->whereIn('work_orders.asset_category_id', $asset_categories);
          //$work_orders = $work_orders->where('work_orders.created_by', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'sp_admin'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->where('work_orders.service_provider_id', $user_id);
        }
        elseif(!empty(Auth::user()->user_type == 'supervisor'))
        {
          $user_id = Auth::user()->id;
          $work_orders = $work_orders->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
        }
        elseif(!empty(Auth::user()->user_type == 'admin'))
        {
          $work_orders = $work_orders->where('work_orders.created_by', $user_id);
        }
        $work_orders = $work_orders->whereBetween('work_orders.start_date', [$start, $end])->orderBy('work_orders.id', 'desc')->paginate(100);
        //dd(DB::getQueryLog());
        // $work_orders = json_decode(json_encode($work_orders), true);
        return $work_orders;
      }
      return true;
    }

    /*********** Building Manager Ends *************/


    /*********** Calendar Filter Start *************/
    // @flip@ $flag='total_work_order_count'
    // @flip@ $flag='all' return all related data as array
    public static function filter_pass_fail_score($user_id, $date,$flag=null)
    {
      Carbon::setWeekStartsAt(Carbon::SUNDAY);
      Carbon::setWeekEndsAt(Carbon::SATURDAY);
      if($date!=NULL)
      {
          //dd($date);
          $search = explode('-',$date);
          $start = date('Y-m-d',strtotime($search[0]));
          $end = date('Y-m-d',strtotime($search[1]));
          //dd($start);

          //dd('gdg');
          /*
          $subdays=84;
          $start=Carbon::now()->startOfWeek()->subDays($subdays)->format('Y-m-d');

          $subdays=7;
          $end=Carbon::now()->endOfWeek()->subDays($subdays)->format('Y-m-d');

          $start = date("Y-m-d", strtotime('monday this week'));
          $end = date('Y-m-d');
          */

          //dd($start.'---'.$end);

          $total = Self::select('work_orders.id')
          ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
          ->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
          ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
          ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
          ->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
          ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
          ->join('users', 'users.id', '=', 'work_orders.created_by')
          ->where('work_orders.contract_type','!=', 'warranty')

          ->where('work_orders.status',4)
          ;

          if(!empty(Auth::user()->user_type != 'sp_admin') && (!empty(Auth::user()->user_type != 'supervisor')))
          {
            $total = $total->where('work_orders.project_user_id',$user_id);
          }
          /**********************condition for user specific work order ********************/
          if(!empty(Auth::user()->user_type != 'admin') && !empty(Auth::user()->user_type != 'admin_employee') &&(!empty(Auth::user()->user_type != 'osool_admin') && !empty(Auth::user()->user_type != 'super_admin')))
          {
            $assigned_assetCat=Auth::user()->asset_categories;
            $assigned_reg=Auth::user()->role_regions;
            $assigned_city=Auth::user()->role_cities;
            if(!empty(Auth::user()->user_type == 'spga_employee'))
            {
              $assigned_assetCat_arr = explode(',', $assigned_assetCat);
              $assigned_reg_arr = explode(',', $assigned_reg);
              $assigned_city_arr = explode(',', $assigned_city);

              $total = $total->whereIn('work_orders.asset_category_id', $assigned_assetCat_arr);
              $property = Property::select('property_buildings.id')->whereIn('properties.city_id', $assigned_city_arr)->leftJoin("property_buildings",function($join){
              $join->on("property_buildings.property_id","=","properties.id");})->get();
              $propArr=array();
              if(isset($property))
              {
                foreach($property as $prop)
                {
                  $propArr[]=$prop->id;
                }
                $total = $total->whereIn('work_orders.property_id', $propArr);
              }
            }
            elseif(!empty(Auth::user()->user_type == 'building_manager_employee'||Auth::user()->user_type == 'building_manager'))
            {
              $building_ids = explode(',', Auth::user()->building_ids);

              $total = $total->whereIn('work_orders.property_id', $building_ids);
            }
            elseif(!empty(Auth::user()->user_type == 'sp_admin'))
            {
              $contract_id = Contracts::where('status', 1)->where('is_deleted', 'no')->where('service_provider_id', Auth::user()->service_provider)->pluck('id')->toArray();
              $total = $total->whereIn('work_orders.contract_id', $contract_id)->where('work_orders.contract_type', 'regular');
            }
            elseif(!empty(Auth::user()->user_type == 'supervisor'))
            {
              $supervisor_id = Auth::user()->id;
              $total = $total->whereRaw("find_in_set($supervisor_id, work_orders.supervisor_id)");
            }
            else
            {
              $total = $total->where('work_orders.project_user_id', '=', Auth::user()->project_user_id);
            }
          }
          /*********************************************************************************/
          // $total = $total->where('work_orders.pass_fail', '!=', 'pending')->whereBetween('work_orders.start_date', [$start, $end])->count();
          if(!empty($start) && !empty($end)) {
            $total_t = $total->whereBetween('work_orders.start_date', [$start, $end])
                          ->count();
            //dd($total_t);
            $pass = $total->where('work_orders.pass_fail', '=', 'pass')
                          ->whereBetween('work_orders.start_date', [$start, $end])
                          ->count();
          } else {
            $total_t = $total->count();
            $pass = $total->where('work_orders.pass_fail', '=', 'pass')->count();
          }
          $pass_fail = 0;
          if($total_t>0)
          {

              $pass_fail = ($pass/$total_t) * 100;
          }

          if($flag=='total_work_order_count'){

            return $total_t;
          }
          if($flag=='all'){

            return [
              'total_work_order_count'=>$total_t,
              'pass_fail_count'=>[
                'pass_count'=>$pass,
                'fail_count'=>$total_t-$pass,
              ],
            ];
          }


          // dd('pass:'.$pass_fail.'total_t:'.$total_t. 'pass:'.$pass);

          //dd($pass_fail);

          return round($pass_fail);
      }
      return true;
    }

    /*******************************weekly pass fail ratio ******************************/
    public static function weekly_pass_fail_score($user_id, $date=null)
    {
      Carbon::setWeekStartsAt(Carbon::SUNDAY);
      Carbon::setWeekEndsAt(Carbon::SATURDAY);
      //echo $lastWeek = Carbon::now()->subWeek(2);die;
      $start = '';
      $end = '';

        if(!empty($date)) {
          $search = explode('-',$date);
          $start = date('Y-m-d',strtotime($search[0]));
          $end = date('Y-m-d',strtotime($search[1]));
        }


        $pass_fail_arr=array();
        $subdays=0;
        for($i=0;$i<12;$i++)
        {
            //DB::enableQueryLog();
            $subdays=$subdays+7;

            //$start=Carbon::now()->startOfWeek()->subDays($subdays)->format('Y-m-d');
            //$end=Carbon::now()->endOfWeek()->subDays($subdays)->format('Y-m-d');

            //$start=Carbon::now()->subDays($subdays)->format('Y-m-d');
            //$end=Carbon::now()->addDays($subdays)->format('Y-m-d');
            //die(date('Y-m-d',strtotime('last sunday')));

            //die(date('Y-m-d',strtotime('last sunday')));


            //$start = '2021-11-21';
            //$end = '2021-11-28';


            $total = Self::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by')->where('work_orders.status',4);

            /**********************condition for user specific work order ********************/
            if(!empty(Auth::user()->user_type != 'admin') && !empty(Auth::user()->user_type != 'admin_employee') &&(!empty(Auth::user()->user_type != 'osool_admin') && !empty(Auth::user()->user_type != 'super_admin')))
            {
              $assigned_assetCat=Auth::user()->asset_categories;
              $assigned_reg=Auth::user()->role_regions;
              $assigned_city=Auth::user()->role_cities;
              if(!empty(Auth::user()->user_type == 'spga_employee'))
               {
                  $assigned_assetCat_arr = explode(',', $assigned_assetCat);
                  $assigned_reg_arr = explode(',', $assigned_reg);
                  $assigned_city_arr = explode(',', $assigned_city);

                  $total = $total->whereIn('work_orders.asset_category_id', $assigned_assetCat_arr);

                  $property = Property::select('property_buildings.id')->whereIn('properties.city_id', $assigned_city_arr)->leftJoin("property_buildings",function($join){
                  $join->on("property_buildings.property_id","=","properties.id");})->get();
                  $propArr=array();

                  if(isset($property))
                  {
                    foreach($property as $prop)
                    {
                      $propArr[]=$prop->id;
                    }

                    $total = $total->whereIn('work_orders.property_id', $propArr);
                  }

               }

               elseif(!empty(Auth::user()->user_type == 'building_manager_employee' || Auth::user()->user_type == 'building_manager'))
               {
                  $building_ids = explode(',', Auth::user()->building_ids);

                  $total = $total->whereIn('work_orders.property_id', $building_ids);
                  //dd($total);
               }
               elseif(!empty(Auth::user()->user_type == 'sp_admin'))
               {
                  $total = $total->where('work_orders.service_provider_id', Auth::user()->id);
                  //$total = $total->where('work_orders.project_user_id', Auth::user()->project_user_id);
               }
               elseif(!empty(Auth::user()->user_type == 'supervisor'))
               {
                 $supervisor_id = Auth::user()->id;
                 $total = $total->whereRaw("find_in_set($supervisor_id, work_orders.supervisor_id)");
               }
               else
               {
                 $total = $total->where('work_orders.created_by', '=', $user_id);
               }
            }
            else{
              $total = $total->where('work_orders.project_user_id',$user_id);
            }
            /*********************************************************************************/

            //$total = $total->where('work_orders.pass_fail', '!=', 'pending')->whereBetween('work_orders.start_date', [$start, $end])->count();
            // ->where('work_orders.job_completion_date', '>=', $date)->count();
            // ->where('work_orders.start_date', '>=', $start)
            // ->where('work_orders.end_date', '<=', $end)->count();
            if(!empty($start) && !empty($end)) {
              $total = $total->whereBetween('work_orders.start_date', [$start, $end])
                              ->count();
            } else {
              $total = $total->count();
            }

            //$total = $total->count();
            //dd(DB::getQueryLog());


            $pass = Self::select('work_orders.*')->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')->leftjoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')->leftjoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')->join('users', 'users.id', '=', 'work_orders.created_by');
             /**********************condition for user specific work order ********************/
            if(!empty(Auth::user()->user_type != 'admin') && !empty(Auth::user()->user_type != 'admin_employee') &&(!empty(Auth::user()->user_type != 'osool_admin') && !empty(Auth::user()->user_type != 'super_admin')))
            {
              $assigned_assetCat=Auth::user()->asset_categories;
              $assigned_reg=Auth::user()->role_regions;
              $assigned_city=Auth::user()->role_cities;
              if(!empty(Auth::user()->user_type == 'spga_employee'))
               {
                  $assigned_assetCat_arr = explode(',', $assigned_assetCat);
                  $assigned_reg_arr = explode(',', $assigned_reg);
                  $assigned_city_arr = explode(',', $assigned_city);

                  $pass = $pass->whereIn('work_orders.asset_category_id', $assigned_assetCat_arr);
                  $property = Property::select('property_buildings.id')->whereIn('properties.city_id', $assigned_city_arr)->leftJoin("property_buildings",function($join){
                  $join->on("property_buildings.property_id","=","properties.id");})->get();
                  $propArr=array();
                  if(isset($property))
                  {
                    foreach($property as $prop)
                    {
                      $propArr[]=$prop->id;
                    }
                    $pass = $pass->whereIn('work_orders.property_id', $propArr);
                  }

               }
               elseif(!empty(Auth::user()->user_type == 'building_manager_employee'||Auth::user()->user_type == 'building_manager'))
               {
                  $building_ids = explode(',', Auth::user()->building_ids);

                  $pass = $pass->whereIn('work_orders.property_id', $building_ids);
               }
               elseif(!empty(Auth::user()->user_type == 'sp_admin'))
               {
                $user_id = Auth::user()->id;
                  $pass = $pass->where('work_orders.service_provider_id', $user_id);
               }
               elseif(!empty(Auth::user()->user_type == 'supervisor'))
               {
                $user_id = Auth::user()->id;
                $pass = $pass->whereRaw("find_in_set($user_id, work_orders.supervisor_id)");
               }
               else
               {
                 $pass = $pass->where('work_orders.created_by', '=', $user_id);
               }
            }else{
              $pass = $pass->where('work_orders.project_user_id', '=', $user_id);
            }

            /*********************************************************************************/

            $pass = $pass->where('work_orders.pass_fail', '=', 'pass');

            if(!empty($start) && !empty($end)) {
              $pass = $pass->whereBetween('work_orders.start_date', [$start, $end])
                            ->count();
            } else {
              $pass = $pass->count();
            }
            //$pass = $pass->count();

            // ->where('work_orders.start_date', '>=', $start)
            // ->where('work_orders.end_date', '<=', $end)->count();

            //dd($pass);
            $pass_fail = 0;
            if($total>0)
            {
              // echo $pass;
              //echo "T".$total;

                $pass_fail = ($pass/$total) * 100;
            }

            $pass_fail_arr[]=round($pass_fail);
         }
         //echo '<pre>';print_r($pass_fail); die;

      return array_reverse($pass_fail_arr);


    }



  public static function active_task($worker_id,$search=null,$sort_by='desc',$filter_project_id=null,$filter_property_id=null,$filter_service_type=null,$filter_asset_name_id=null, $offline_mode=false, $checksum_only=false)
  {
    $loginuser_asset_categories = trim(Auth::user()->asset_categories) != "" ? Auth::user()->asset_categories : 0;
    $upcoming_orders = WorkOrders::with('workerTimings');
    //offline mode fetch workorders
    if($offline_mode && $checksum_only){
      $upcoming_orders = $upcoming_orders->withOnly('hasChecksum')->select('work_orders.id');
    }else{
      if($offline_mode){
        $upcoming_orders = $upcoming_orders->with(
          'assignedWorkers',
          'contract',
          'assetCategory', 'assetName', 'asset',
          'priority','propertyBuilding', 'projectSettings',
          'worker', 'createdByUser', 'chatMessages', 'itemRequests',
          'serviceProviderItemRequests', 'relatedWorkOrders', 'NoChecklistSubtaskAction',
          'relatedPMWorkOrders', 'slaAssetCategory', 'checklist', 'closedBy', 'createdBy',
          'contractPriority', 'frequencyMaster', 'projectOwner', 'hasChecksum'
        );
      }
      $upcoming_orders->select('work_orders.id', 'work_orders.work_order_id', 'poa.project_id', 'work_orders.property_id', 'work_orders.service_type', 'work_orders.asset_name_id', 'work_orders.description', 'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag', 'assets.asset_symbol', 'assets.asset_number', 'contracts.contract_number', 'properties.latitude', 'properties.longitude', 'properties.location', 'property_buildings.building_name', 'work_orders.asset_category_id', 'work_orders.work_order_type', 'work_orders.frequency_id', 'projects_details.project_name as project', 'work_orders.priority_id', 'work_orders.response_time');
    }
    $upcoming_orders = $upcoming_orders->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                    ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                    ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
                    ->join('users', 'users.id', '=', 'work_orders.created_by')
                    ->leftjoin('users as poa', 'poa.id', '=', 'contracts.user_id')
                    ->leftjoin('projects_details', 'projects_details.id', '=', 'poa.project_id')
                    ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                    ->leftJoin('work_order_workers', 'work_order_workers.work_order_id', '=', 'work_orders.id')
                    ->whereIn('work_orders.asset_category_id', explode(',', $loginuser_asset_categories))
                    ->where('work_orders.workorder_journey', 'job_execution')
                    ->whereNotNull('job_started_at')
                    ->whereRaw("(work_orders.status = 2)")
                    ->where(function ($query) use ($worker_id) {
                      $query->where('work_orders.worker_id', $worker_id)->whereNotNull('worker_started_at')
                            ->orWhere(function ($query) use ($worker_id) {
                                $query->where('work_order_workers.worker_id', $worker_id)
                                      ->where('work_orders.is_collaborative', 1);
                            })
                            ->where(function ($query) use ($worker_id) {
                                $query->whereHas('workerTimings', function ($query) use ($worker_id) {
                                    $query->where('worker_id', $worker_id)
                                          ->where('end_time', NULL);
                                });
                            });
                    });
        if($search!=null)
        {
            $upcoming_orders = $upcoming_orders->where('work_orders.work_order_id', 'like', '%' . $search . '%');
        }

        if($filter_project_id !=null && $filter_project_id != "")
        {
          $upcoming_orders = $upcoming_orders->whereIn('poa.project_id', explode(',', $filter_project_id));
        }

        if($filter_property_id !=null && $filter_property_id != "")
        {
          $upcoming_orders = $upcoming_orders->whereIn('work_orders.property_id', explode(',', $filter_property_id));
        }

        if($filter_service_type !=null && $filter_service_type != "")
        {
          $upcoming_orders = $upcoming_orders->whereIn('work_orders.service_type', explode(',', $filter_service_type));
        }

        if($filter_asset_name_id != null && $filter_asset_name_id != "")
        {
          $upcoming_orders = $upcoming_orders->whereIn('work_orders.asset_name_id', explode(',', $filter_asset_name_id));
        }

        if($sort_by == 'desc')
        {
          $upcoming_orders = $upcoming_orders->orderBy('id', 'desc');
        }
        elseif($sort_by == 'asc')
        {
          $upcoming_orders = $upcoming_orders->orderBy('id', 'asc');
        }
        elseif($sort_by == 'deadline')
        {
          $upcoming_orders = $upcoming_orders->orderBy('target_date', 'asc');
        }
        elseif($sort_by == 'preventive')
        {
          $upcoming_orders = $upcoming_orders->orderBy('work_order_type', 'desc');
        }
        elseif($sort_by == 'reactive')
        {
          $upcoming_orders = $upcoming_orders->orderBy('work_order_type', 'asc');
        }

        $upcoming_orders = $upcoming_orders->skip(0)->take(3)->get();

        $upcoming_orders = json_decode(json_encode($upcoming_orders), true);
      if(!empty($upcoming_orders))
      {
        foreach($upcoming_orders as $key => $uo)
        {
          if(!$checksum_only || ((!$checksum_only && $offline_mode) || (!$checksum_only && !$offline_mode) ) ){
            $upcoming_orders[$key]['building_name'] = $uo['building_name'];
            $upcoming_orders[$key]['location'] = $uo['floor'].'-'.$uo['room'];
            $upcoming_orders[$key]['asset_tag'] = $uo['asset_symbol'].$uo['asset_number'];

            $sla_asset_categories = DB::table('contract_asset_categories')
                          ->where('asset_category_id', $uo['asset_category_id'])
                          ->where('contract_number', $uo['contract_number'])
                          ->orderBy('id', 'desc')
                          ->first();

            $response_time = 0;
            $service_window = 0;
            $response_time_type = 'hours';
            $service_window_type = 'minutes';
            if($uo['work_order_type'] == "reactive")
            {
              if(!empty($sla_asset_categories->priority_id)){
                $contract_priorities = DB::table('contract_priorities')
                      ->where('priority_id', $sla_asset_categories->priority_id)
                      ->where('contract_number', $uo['contract_number'])
                      ->orderBy('id', 'desc')
                      ->first();
                $response_time = $contract_priorities->response_time;
                $service_window = $contract_priorities->service_window;
                $response_time_type = $contract_priorities->response_time_type;
                $service_window_type = $contract_priorities->service_window_type;
              }
            }
            elseif($uo['work_order_type'] == "preventive" && $uo['priority_id'] != 0)
            {
              $contract_priorities = DB::table('contract_priorities')
                    ->where('priority_id', $uo['priority_id'])
                    ->where('contract_number', $uo['contract_number'])
                    ->orderBy('id', 'desc')
                    ->first();
              if(isset($contract_priorities)) {
                $response_time = $contract_priorities->response_time;
                $service_window = $contract_priorities->service_window;
                $response_time_type = $contract_priorities->response_time_type;
                $service_window_type = $contract_priorities->service_window_type;
              } else {
                $contract_priorities = DB::table('priorities')
                    ->where('id', $uo['priority_id'])
                    ->orderBy('id', 'desc')
                    ->first();
                if(isset($contract_priorities)) {
                  $response_time = $contract_priorities->response_time;
                  $service_window = $contract_priorities->service_window;
                  $response_time_type = $contract_priorities->response_time_type;
                  $service_window_type = $contract_priorities->service_window_type;
                }
              }
            }
            else
            {
              $contract_frequencies = DB::table('frequencies_master')
                        ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                        ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                        ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                        ->where('frequencies_master.id', $uo['frequency_id'])
                        ->where('contract_frequencies.contract_number', $uo['contract_number'])
                        ->first();
              $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
              $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : '';
              $response_time_type = isset($contract_frequencies->response_time_type) ? $contract_frequencies->response_time_type:'hours';
              $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
            }
            $created_at = $uo['created_at'];
            $tdate = date('Y-m-d H:i:s');
            $datetime1 = strtotime($created_at);
            $datetime2 = strtotime($tdate);
            $interval  = abs($datetime2 - $datetime1);
            $minutes   = round($interval / 60);
            if($response_time_type == "days")
            {
              $response_time = $response_time * 1440;
            }
            elseif($response_time_type == "hours")
            {
              $response_time = $response_time * 60;
            }
            $time_left = $response_time - $minutes;

            if($uo['job_started_at'] == NULL)
            {
              $target_date = $uo['target_date'];
              $upcoming_orders[$key]['target_date'] = $target_date;
            }
            else
            {
              $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($uo['job_started_at'])));
              $upcoming_orders[$key]['target_date'] = $target_date;
            }
            //dd($service_window);
            if($uo['job_started_at'] != '')
            {
              if(strtotime($target_date) >= strtotime($tdate))
              {
                $upcoming_orders[$key]['pass_fail'] = 'pass';
              }
              else
              {
                $upcoming_orders[$key]['pass_fail'] = 'fail';
              }
            }
            if($uo['response_time'] == 'On time' || $uo['response_time'] == 'Late')
            {
              $upcoming_orders[$key]['response_time'] = $uo['response_time'];
            }
            else
            {
              if($time_left >= 0)
              {
                $upcoming_orders[$key]['response_time'] = $time_left;
              }
              else
              {
                $upcoming_orders[$key]['response_time'] = 'Late';
              }
            }
          }elseif($checksum_only && $offline_mode){
            $upcoming_orders[$key]['md5_checksum'] = $uo['has_checksum'][0]['md5_checksum'];
            unset($upcoming_orders[$key]['has_checksum']);
          }
        }
      }
      return $upcoming_orders;
    }


    public static function offline_active_task($worker_id, $search = null, $sort_by = 'desc', $filter_project_id = null, $filter_property_id = null, $filter_service_type = null, $filter_asset_name_id = null, $offline_mode = false, $checksum_only = false,$workOrderIds=null)
    {
        $loginuser_asset_categories = trim(Auth::user()->asset_categories) != "" ? Auth::user()->asset_categories : 0;
        $upcoming_orders = WorkOrders::with('workerTimings');
        //offline mode fetch workorders
        if ($offline_mode && $checksum_only) {
            $upcoming_orders = $upcoming_orders->withOnly('hasChecksum')->select('work_orders.id');
        } else {
            if ($offline_mode) {
                $upcoming_orders = $upcoming_orders->with(
                    'assignedWorkers',
                    'contract', 'contract.usableItems',
                    'assetCategory', 'assetName', 'asset',
                    'priority', 'propertyBuilding', 'projectSettings',
                    'worker', 'createdByUser', 'chatMessages', 'itemRequests.requestedItems.contractUsableItem',
                    'serviceProviderItemRequests.requestedItems.contractUsableItem', 'relatedWorkOrders', 'NoChecklistSubtaskAction',
                    'relatedPMWorkOrders', 'slaAssetCategory', 'checklist', 'closedBy', 'createdBy',
                    'contractPriority', 'frequencyMaster', 'projectOwner', 'hasChecksum'
                );
            }
            $upcoming_orders->select('work_orders.id', 'work_orders.work_order_id',
                'work_orders.project_user_id','work_orders.wo_images', 'work_orders.maintanance_request_id',
                'work_orders.preventive_start_time', 'properties.property_type', 'properties.complex_name',
                'poa.project_id', 'work_orders.property_id', 'work_orders.service_type', 'assets.barcode_value',
                'work_orders.asset_name_id', 'work_orders.description', 'contract_type','work_orders.reason',
                'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'assets.barcode_value',
                'work_orders.end_date', 'work_orders.worker_started_at', 'target_date', 'wtf_start_time',
                'work_orders.status', 'workorder_journey', 'work_orders.created_at',
                'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag',
                'assets.asset_symbol', 'assets.asset_number', 'contracts.contract_number',
                'properties.latitude', 'properties.longitude', 'properties.location',
                'property_buildings.building_name', 'work_orders.asset_category_id',
                'work_orders.work_order_type', 'work_orders.frequency_id', 'work_orders.asset_number_id',
                'projects_details.project_name as project', 'work_orders.priority_id',
                'work_orders.response_time', 'work_orders.sp_approve_job', 'work_orders.bm_approve_job',
                'work_orders.sp_reopen_status', 'work_orders.worker_id', 'work_orders.old_worker_id',
                'work_orders.checklist_id', 'work_orders.examine_button_clicked_at','work_orders.place',
                'maintanance_request.app_type','maintanance_request.generated_from', 'maintanance_request.image1',
                'maintanance_request.image2', 'maintanance_request.image3','work_orders.schedule_start_time','work_orders.bm_approve_issue','work_orders.sla_service_window_priority');
        }
        $upcoming_orders = $upcoming_orders
        ->when(!empty($workOrderIds),function($query) use($workOrderIds){
            return $query->whereIn('work_orders.id',$workOrderIds);
        })
        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
            ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
            ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
            ->join('users', 'users.id', '=', 'work_orders.created_by')
            ->leftjoin('users as poa', 'poa.id', '=', 'contracts.user_id')
            ->leftjoin('projects_details', 'projects_details.id', '=', 'poa.project_id')
            ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
            ->leftJoin('work_order_workers', 'work_order_workers.work_order_id', '=', 'work_orders.id')
            ->leftjoin('maintanance_request', 'maintanance_request.id', '=', 'work_orders.maintanance_request_id')
            ->whereIn('work_orders.asset_category_id', explode(',', $loginuser_asset_categories))
            ->where('work_orders.workorder_journey', 'job_execution')
            ->whereNotNull('job_started_at')
            ->whereRaw("(work_orders.status = 2)")
            ->where(function ($query) use ($worker_id) {
                $query->where('work_orders.worker_id', $worker_id)->whereNotNull('worker_started_at')
                    ->orWhere(function ($query) use ($worker_id) {
                        $query->where('work_order_workers.worker_id', $worker_id)
                            ->where('work_orders.is_collaborative', 1);
                    })
                    ->where(function ($query) use ($worker_id) {
                        $query->whereHas('workerTimings', function ($query) use ($worker_id) {
                            $query->where('worker_id', $worker_id)
                                ->where('end_time', NULL);
                        });
                    });
            });
        if ($search != null) {
            $upcoming_orders = $upcoming_orders->where('work_orders.work_order_id', 'like', '%' . $search . '%');
        }

        if ($filter_project_id != null && $filter_project_id != "") {
            $upcoming_orders = $upcoming_orders->whereIn('poa.project_id', explode(',', $filter_project_id));
        }

        if ($filter_property_id != null && $filter_property_id != "") {
            $upcoming_orders = $upcoming_orders->whereIn('work_orders.property_id', explode(',', $filter_property_id));
        }

        if ($filter_service_type != null && $filter_service_type != "") {
            $upcoming_orders = $upcoming_orders->whereIn('work_orders.service_type', explode(',', $filter_service_type));
        }

        if ($filter_asset_name_id != null && $filter_asset_name_id != "") {
            $upcoming_orders = $upcoming_orders->whereIn('work_orders.asset_name_id', explode(',', $filter_asset_name_id));
        }

        if ($sort_by == 'desc') {
            $upcoming_orders = $upcoming_orders->orderBy('id', 'desc');
        } elseif ($sort_by == 'asc') {
            $upcoming_orders = $upcoming_orders->orderBy('id', 'asc');
        } elseif ($sort_by == 'deadline') {
            $upcoming_orders = $upcoming_orders->orderBy('target_date', 'asc');
        } elseif ($sort_by == 'preventive') {
            $upcoming_orders = $upcoming_orders->orderBy('work_order_type', 'desc');
        } elseif ($sort_by == 'reactive') {
            $upcoming_orders = $upcoming_orders->orderBy('work_order_type', 'asc');
        }

        if (!empty($offline_mode))
            $upcoming_orders = $upcoming_orders->get();
        else
            $upcoming_orders = $upcoming_orders->skip(0)->take(3)->get();


        $upcoming_orders = json_decode(json_encode($upcoming_orders), true);
        if (!empty($upcoming_orders)) {
            foreach ($upcoming_orders as $key => $uo) {
                if (!$checksum_only || ((!$checksum_only && $offline_mode) || (!$checksum_only && !$offline_mode))) {

                    $workOrderContract = Cache::remember('wo_contract_' . $uo['contract_number'], CachingTTL::TTL_ONE_HOUR->value, function () use ($uo) {
                        return Contracts::query()
                            ->where('contract_number', 'like', $uo['contract_number'])
                            ->with(['usableItems'])
                            ->first();
                    });
                    // Fetch usable items related to the contract
                    $selectableItems = $workOrderContract->usableItems->map(function (ContractUsableItem $item) {
                        return Cache::remember('ContractUsableItems_' . $item->id, CachingTTL::TTL_ONE_HOUR->value, function () use ($item) {
                            return $item->getItem();
                        });
                    });
                    // Filter out items where all fields are null and reset the array keys
                    $filteredItems = $selectableItems->filter(function ($item) {
                        // Check if any values in the item are not null
                        return collect($item)->filter()->isNotEmpty();
                    })->values(); // Reset array keys


                                        $wo_images = [];

                    // Process images from wo_images field
                    if (!empty($uo['wo_images'])) {

                        foreach (explode(',', trim($uo['wo_images'])) as $image) {
                            if ((trim($image) != "") && (trim($image) != null))
                                $extension = pathinfo($image)['extension'];
                            if ($extension != 'pdf') {
                                if (filled(ImagesUploadHelper::displayImageMobileApp($image, 'uploads/workorder'))) {
                                    $wo_images[] = ImagesUploadHelper::displayImageMobileApp($image, 'uploads/workorder');
                                }

                            } else {
                                $wo_images[] = url('uploads/workorder/' . $image);
                            }
                        }
                    }
                    // Process images from maintanance_request

                    if (
                        isset($uo['maintanance_request_id']) &&
                        $uo['maintanance_request_id'] != null &&
                        (filled($uo['image1']) || filled($uo['image2']) || filled($uo['image3']))
                    ) {

                        if (filled($uo['image1'])) {
                            if (filled(ImagesUploadHelper::displayImageMobileApp($uo['image1'], 'uploads/maintanance_request'))) {
                                $wo_images[] = ImagesUploadHelper::displayImageMobileApp($uo['image1'], 'uploads/maintanance_request');
                            }
                        }
                        if (filled($uo['image2'])) {
                            if (filled(ImagesUploadHelper::displayImageMobileApp($uo['image2'], 'uploads/maintanance_request'))) {
                                $wo_images[] = ImagesUploadHelper::displayImageMobileApp($uo['image2'], 'uploads/maintanance_request');
                            }
                        }
                        if (filled($uo['image3'])) {
                            if (filled(ImagesUploadHelper::displayImageMobileApp($uo['image3'], 'uploads/maintanance_request'))) {
                                $wo_images[] = ImagesUploadHelper::displayImageMobileApp($uo['image3'], 'uploads/maintanance_request');
                            }
                        }
                    }
                    $upcoming_orders[$key]['wo_images']=$wo_images;



                                        $response_time = 0;
                    $service_window = 0;
                    $response_time_type = 'hours';
                    $service_window_type = 'minutes';
                    if ($uo['work_order_type'] == "reactive") {
                        if (!empty($sla_asset_categories->priority_id)  || !empty($uo['sla_service_window_priority'])) {
                            if ($uo['sla_service_window_priority'] != 0) {
                                $priority_id = $uo['sla_service_window_priority'];
                            } else {
                                $priority_id = $sla_asset_categories->priority_id;
                            }
                            $contract_priorities = DB::table('contract_priorities')
                                ->where('priority_id', $priority_id)
                                ->where('contract_number', $uo['contract_number'])
                                ->orderBy('id', 'desc')
                                ->first();
                            if (empty($contract_priorities)) {
                                $contract_priorities = DB::table('priorities')
                                    ->where('id', $priority_id)
                                    ->orderBy('id', 'desc')
                                    ->first();
                            }
                            if (!empty($uo['priority_id']) && $uo['priority_id'] != 0) {
                                $contract_priorities = DB::table('priorities')
                                    ->where('id', $uo['priority_id'])
                                    ->orderBy('id', 'desc')
                                    ->first();
                                $priority_id = $uo['priority_id'];
                            }
                            if (isset($contract_priorities)) {
                                $response_time = $contract_priorities->response_time;
                                $service_window = $contract_priorities->service_window;
                                $response_time_type = $contract_priorities->response_time_type;
                                $service_window_type = $contract_priorities->service_window_type;
                            }
                        }
                    } elseif ($uo['work_order_type'] == "preventive" && $uo['priority_id'] != 0) {
                        $priority_id = $uo['priority_id'];
                        if ($uo['sla_service_window_priority'] != 0) {
                            $priority_id = $uo['sla_service_window_priority'];
                        }
                        $contract_priorities = DB::table('contract_priorities')
                            ->where('priority_id', $priority_id)
                            ->where('contract_number', $uo['contract_number'])
                            ->orderBy('id', 'desc')
                            ->first();
//                        $response_time = $contract_priorities->response_time;
//                        $service_window = $contract_priorities->service_window;
//                        $response_time_type = $contract_priorities->response_time_type;
//                        $service_window_type = $contract_priorities->service_window_type;
                        if (isset($contract_priorities)) {
                            $response_time = $contract_priorities->response_time;
                            $service_window = $contract_priorities->service_window;
                            $response_time_type = $contract_priorities->response_time_type;
                            $service_window_type = $contract_priorities->service_window_type;
                        } else {
                            $contract_priorities = DB::table('priorities')
                                ->where('id', $priority_id)
                                ->orderBy('id', 'desc')
                                ->first();

                            if (!empty($details->priority_id) && $details->priority_id != 0) {
                                $contract_priorities = DB::table('priorities')
                                    ->where('id', $details->priority_id)
                                    ->orderBy('id', 'desc')
                                    ->first();
                            }
                            if (isset($contract_priorities)) {
                                $response_time = $contract_priorities->response_time;
                                $service_window = $contract_priorities->service_window;
                                $response_time_type = $contract_priorities->response_time_type;
                                $service_window_type = $contract_priorities->service_window_type;
                            }
                        }
                    } else {
                        $contract_frequencies = DB::table('frequencies_master')
                            ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                            ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                            ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                            ->where('frequencies_master.id', $uo['frequency_id'])
                            ->where('contract_frequencies.contract_number', $uo['contract_number'])
                            ->first();
                        $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
                        $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : '';
                        $response_time_type = isset($contract_frequencies->response_time_type) ? $contract_frequencies->response_time_type : 'hours';
                        $service_window_type = isset($contract_priorities->service_window_type) ? $contract_priorities->service_window_type : 'minutes';
                    }
                    $created_at = $uo['created_at'];
                    $tdate = date('Y-m-d H:i:s');
                    $datetime1 = strtotime($created_at);
                    $datetime2 = strtotime($tdate);
                    $interval = abs($datetime2 - $datetime1);
                    $minutes = round($interval / 60);
                    if ($response_time_type == "days") {
                        $response_time = $response_time * 1440;
                    } elseif ($response_time_type == "hours") {
                        $response_time = $response_time * 60;
                    }
                    $time_left = $response_time - $minutes;

                                        if ($uo['job_started_at'] == NULL || $uo['bm_approve_issue'] == 2) {
                        $target_date = $uo['target_date'];
                        $upcoming_orders[$key]['target_date'] = $target_date;
                    } else {

                        if ($uo['work_order_type'] == "preventive" && $uo['target_date'] != "" && $uo['target_date'] != "00:00:00") {
                            $target_date = $uo['target_date'];
                        } else {
                            $target_date = date('Y-m-d H:i:s', strtotime('+' . $service_window . ' ' . $service_window_type, strtotime($uo['job_started_at'])));
                        }
                        $upcoming_orders[$key]['target_date'] = $target_date;
                    }

                    //dd($service_window);
                    if ($uo['job_started_at'] != '') {
                        if (strtotime($target_date) >= strtotime($tdate)) {
                            $upcoming_orders[$key]['pass_fail'] = 'pass';
                        } else {
                            $upcoming_orders[$key]['pass_fail'] = 'fail';
                        }
                    }
                    if ($uo['response_time'] == 'On time' || $uo['response_time'] == 'Late') {
                        $upcoming_orders[$key]['response_time'] = $uo['response_time'];
                    } else {
                        if ($time_left >= 0) {
                            $upcoming_orders[$key]['response_time'] = $time_left;
                        } else {
                            $upcoming_orders[$key]['response_time'] = 'Late';
                        }
                    }

                    $upcoming_orders[$key]['screen_status'] = '';
//                    $upcoming_orders[$key]['over_due'] = false;

                    if ($uo['status'] == 2 && ($uo['workorder_journey'] == 'job_execution' || $uo['workorder_journey'] == 'job_evaluation') && ($uo['bm_approve_job'] == 1 || $uo['sp_approve_job'] == 1)) {
                        $upcoming_orders[$key]['screen_status'] = 'Rejected';
                    } elseif ($uo['status'] == 2 && $uo['workorder_journey'] == 'job_execution' && $uo['sp_reopen_status'] == 2 && $uo['worker_started_at'] == NULL) {
                        $upcoming_orders[$key]['screen_status'] = 'Re Opened';
                    } elseif ($uo['status'] == 3 && ($uo['workorder_journey'] == 'job_execution' || $uo['workorder_journey'] == 'job_evaluation')) {
                        $upcoming_orders[$key]['screen_status'] = 'On Hold';
                    } elseif ($upcoming_orders[$key]['target_date'] < now() && ($uo['status'] == 2) && ($uo['workorder_journey'] == 'job_execution')) {
                        $upcoming_orders[$key]['screen_status'] = 'Over Due';
                    }

                    $upcoming_orders[$key]['over_due'] = $upcoming_orders[$key]['target_date'] < now() ?? false;

                    //that to make sure the over_due key return
                    if (!isset($upcoming_orders[$key]['over_due'])) {
                        $upcoming_orders[$key]['over_due'] = false;
                        $upcoming_orders[$key]['fixed_over_due'] = true;
                    }



                    if (isset($uo['item_requests']['requested_items'])) {
                        // Access the relationship data for itemRequests
                        foreach ($uo['item_requests']['requested_items'] as $key => $requestedItem) {
                            // Get the ContractUsableItem associated with the requestedItem
                            $contractUsableItem = $requestedItem['contract_usable_item'];
                            // Set the name of the requestedItem based on the ContractUsableItem's item
                            $uo['item_requests']['requested_items'][$key]['name'] = Cache::remember("contract_" . $workOrderContract->id . "_contract_usable_item_" . $contractUsableItem['id'], CachingTTL::TTL_ONE_HOUR->value, function () use ($contractUsableItem) {
                                return optional(ContractUsableItem::query()->find($contractUsableItem['id'])->getItem())->name ?? null;
                            });
                            // Missing quantity should be according the status
                            $uo['item_requests']['requested_items'][$key]['quantity_accepted'] = $uo['item_requests']['status'] == 'partially_given' ? $requestedItem['quantity'] - $requestedItem['quantity_accepted'] : $requestedItem['quantity_accepted'];
                        }
                    }
                    if (isset($uo['service_provider_item_requests']['requested_items'])) {
                        // Access the relationship data for serviceProviderItemRequests
                        foreach ($uo['service_provider_item_requests']['requested_items'] as $key => $requestedItem) {
                            // Get the ContractUsableItem associated with the requestedItem
                            $contractUsableItem = $requestedItem['contract_usable_item'];
                            // Set the name of the requestedItem based on the ContractUsableItem's item
                            $uo['service_provider_item_requests']['requested_items'][$key]['name'] = Cache::remember("contract_" . $workOrderContract->id . "_contract_service_provider__usable_item_" . $contractUsableItem['id'], CachingTTL::TTL_ONE_HOUR->value, function () use ($contractUsableItem) {
                                return optional(ContractUsableItem::query()->find($contractUsableItem['id'])->getItem())->name ?? null;
                            });
                        }
                    }

                    $upcoming_orders[$key]['item_requests'] = $uo['item_requests'] ?? null;
                    $upcoming_orders[$key]['service_provider_item_requests'] = $uo['service_provider_item_requests'] ?? null;
                    $upcoming_orders[$key]['spare_items'] = $filteredItems ?? null;
                    $upcoming_orders[$key]['contract'] = $workOrderContract;

                    $upcoming_orders[$key]['place_for_maintenance'] = is_null($uo['place']) ? "" : trim($uo['place']);
                    $upcoming_orders[$key]['apartment_villa'] = '';
                    $upcoming_orders[$key]['tenant_phone_number'] = '';
                    $upcoming_orders[$key]['is_workorder_from_tenant'] = $uo['app_type'] == 'tenant' || $uo['generated_from'] == 'tenant' ? true : false;

                    if (isset($uo['maintanance_request_id']) &&
                        $uo['maintanance_request_id'] != null) {
                        $details_maintenance_request = Cache::remember('details_maintenance_request_' . $uo['maintanance_request_id'], CachingTTL::TTL_ONE_HOUR->value, function () use ($uo) {
                            return MaintenanceRequest::with('user')->where('id', $uo['maintanance_request_id'])->first();
                        });
                        if (isset($details_maintenance_request->user)) {
//                            $upcoming_orders[$key]['apartment_villa'] = $details_maintenance_request->user?->apartment ?? '';
                            $upcoming_orders[$key]['apartment_villa'] = $details_maintenance_request->user->apartment ?? '';

                            if ($uo['app_type'] == 'tenant' || $uo['generated_from'] == 'tenant') {
                                $upcoming_orders[$key]['tenant_phone_number'] = $details_maintenance_request->user->phone ?? '';
                            }
                        }
                    }

                    $upcoming_orders[$key]['building_name'] = $uo['building_name'];
                    $upcoming_orders[$key]['location'] = $uo['floor'] . '-' . $uo['room'];
                    $upcoming_orders[$key]['asset_tag'] = $uo['asset_symbol'] . $uo['asset_number'];
                    if(empty($upcoming_orders[$key]['asset_tag']))
                        $upcoming_orders[$key]['asset_tag']="";

                    $sla_asset_categories = DB::table('contract_asset_categories')
                        ->where('asset_category_id', $uo['asset_category_id'])
                        ->where('contract_number', $uo['contract_number'])
                        ->orderBy('id', 'desc')
                        ->first();

                    if (isset($uo['asset_category'])) {
                        $deleted = "";
                        $upcoming_orders[$key]['asset_category_name'] = $uo['asset_category']['asset_category'] . $deleted;
                    } else {
                        $upcoming_orders[$key]['asset_category_name'] = '';
                    }
                    $upcoming_orders[$key]['project_name'] = Helper::getProjectNameByWorkOrderID($uo['id']);
                    $upcoming_orders[$key]['current_time'] = date('Y-m-d H:i:s');

//                    $details->zone = $details->floor;
//                    $details->unit = $details->room;

                    $upcoming_orders[$key]['zone']= $uo['floor'] ?? '';
                    $upcoming_orders[$key]['unit']= $uo['room'] ?? '';

                    $upcoming_orders[$key]['floor']= $uo['floor'] ?? '';
                    $upcoming_orders[$key]['room']= $uo['room'] ?? '';


                    $upcoming_orders[$key]['place']= $uo['place'] ?? '';

                    $upcoming_orders[$key]['assets_files_data']= self::getAssetDetails($uo['asset_number_id'], $uo['property_id']);
//                    if (isset($uo['asset']['files'])){
//                        foreach ($uo['asset']['files'] as $f => $h) {
//                            $uo['asset']['files'][$f]['uri']= trim($uo['asset']['files'][$f]['file_name']) != "" ? asset('uploads/asset_images/' . $uo['asset']['files'][$f]['file_name']) : $uo['asset']['files'][$f]['file_name'];
//                            $uo['asset']['files'][$f]['file_name']= trim($uo['asset']['files'][$f]['file_name']);
//
//                            $upcoming_orders[$key]['assets_files'][$f]= $uo['asset']['files'][$f];
//                            $upcoming_orders[$key]['assets_files'][$f]['uri']= trim($uo['asset']['files'][$f]['file_name']) != "" ? asset('uploads/asset_images/' . $uo['asset']['files'][$f]['file_name']) : $uo['asset']['files'][$f]['file_name'];
//                            $upcoming_orders[$key]['assets_files'][$f]['file_name']= trim($uo['asset']['files'][$f]['file_name']);
//                        }
//                    }

                    $wtfs = Cache::remember('work_time_frame_' . $uo['project_user_id'], CachingTTL::TTL_ONE_HOUR->value, function () use ($uo) {
                        return DB::table('work_time_frame')
                            ->select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
                            ->where('user_id', $uo['project_user_id'])
                            ->first();
                    });

                    if ($uo['wtf_start_time'] == '') {
                        if (!isset($wtfs)) //If work order time frame has added
                        {
                            $time = "00:00:00";
                        } else {
                            $time = $wtfs->start_time;
                        }
                    } else {
                        $time = $uo['wtf_start_time'];
                    }

                    $preventive_start_time = WorkorderHelper::getWorkorderstarttime($time, $uo['preventive_start_time']);
                    if ($uo['work_order_type'] == "preventive") //If work_order_type is preventive
                    {
                        $upcoming_orders[$key]['reported_at'] = $uo['start_date'] . ' ' . $preventive_start_time;
                    } else {
                        $upcoming_orders[$key]['reported_at'] = $uo['created_at'];
                    }


                    $worker_started_at = $uo['worker_started_at'];
                    $upcoming_orders[$key]['worker_started_at'] = $worker_started_at;

                    #region checklist tasks for WO
//              $active_task = self::active_task(Auth::user()->id);
                    $active_tasks = collect($upcoming_orders)->where('worker_id', '=', Auth::user()->id);
                    $upcoming_orders[$key]['has_active_task'] = false;
                    if (!empty((array)$active_tasks) && count($active_tasks) >= 3) {
                        foreach ($active_tasks as $atkey => $at) {
                            if ($at['id'] != $uo['id']) {
                                $upcoming_orders[$key]['has_active_task'] = true;
                            }
                        }
                    }
                    $action = self::check_no_checklist_actions_completed($uo['id']);
                    if ($uo['checklist_id'] == 0) {

                        $upcoming_orders[$key]['has_checklist'] = false;
                        $upcoming_orders[$key]['total_checklists'] = 1;
                        $upcoming_orders[$key]['work_order_checklists'] = 1;

                        if ($action == 0) {
                            $upcoming_orders[$key]['checklist_actions_left'] = 1;
                            $upcoming_orders[$key]['work_order_checklists_pending'] = 1;
                        } else {
                            $upcoming_orders[$key]['checklist_actions_left'] = 0;
                            $upcoming_orders[$key]['work_order_checklists_pending'] = 0;
                        }
                    } else {
                        $checklist_tasks_count = count(self::checklist_tasks($uo['checklist_id'], $uo['id']));
                        $upcoming_orders[$key]['has_checklist'] = true;
                        $upcoming_orders[$key]['total_checklists'] = $checklist_tasks_count;
                        $checklist_actions_left = $checklist_tasks_count - $action;
                        $upcoming_orders[$key]['checklist_actions_left'] = $checklist_actions_left;
                        if ($checklist_actions_left <= 0) {
                            $upcoming_orders[$key]['checklist_actions_left'] = 0;
                        }
                        //$upcoming_orders[$key]['checklist_tasks'] = ApiHelper::getWorkorderChecklists($uo['id']); //self::checklist_tasks($uo['checklist_id'], $uo['id']);
                        $checklist_tasksItems = ApiHelper::getWorkorderChecklists($uo['id']); //self::checklist_tasks($uo['checklist_id'], $uo['id']);
                        $upcoming_orders[$key]['checklist_tasks']= collect($checklist_tasksItems)->map(function($item) use($uo){
                            $checklist_completed_task_details = ApiHelper::getChecklistCompletedTaskDetails($uo['id'], $item['id']);
                            $isSubmitted= $checklist_completed_task_details ? 'yes' : 'no';
                            $subtask_checklists = collect(ApiHelper::getChecklistSubmittedSubtask($uo['id'], $item['id'], $isSubmitted));
                            $reformatSubtask= [];
                            foreach ($item['subtask_list'] as $key=>$subtask) {
                                $nestedsubtask_checklists= $subtask_checklists->firstWhere('id', $subtask['id']);
                                $reformatSubtask[]=Helper::Fetchsubtaskactionlist($subtask['id'], $uo['id']);
                                $reformatSubtask[$key]['id']= $subtask['id'] ?? 0;
                                $reformatSubtask[$key]['name']= $subtask['name'] ?? '-';
                                $reformatSubtask[$key]['check_count_checklist_action'] = $nestedsubtask_checklists['check_count_checklist_action'] ?? 0;
                                $reformatSubtask[$key]['is_actions_available']= $nestedsubtask_checklists['is_actions_available'] ?? false;
                                $reformatSubtask[$key]['check_user_by']= $nestedsubtask_checklists['check_user_by'] ?? null;
                                #todo just for testing
                                $reformatSubtask[$key]['checklist_completed_task_details'] = $checklist_completed_task_details;
                                $reformatSubtask[$key]['subtask_checklists_details'] = $nestedsubtask_checklists;
                            }
                            $item['subtask_list']=$reformatSubtask;
                            $item['task_photos'] = (!empty($checklist_completed_task_details)) ? (is_object($checklist_completed_task_details)) ? $checklist_completed_task_details->photos : $checklist_completed_task_details['photos'] : [];
                            $item['task_feedback_options'] = (!empty($checklist_completed_task_details)) ? (is_object($checklist_completed_task_details)) ? $checklist_completed_task_details->feedback_options : $checklist_completed_task_details['feedback_options'] : [];
                            $item['task_comment'] = (!empty($checklist_completed_task_details)) ? (is_object($checklist_completed_task_details)) ? $checklist_completed_task_details->comment : $checklist_completed_task_details['comment'] : "";
                            $item['task_checked_at'] = (!empty($checklist_completed_task_details)) ? (is_object($checklist_completed_task_details)) ? $checklist_completed_task_details->checked_at : $checklist_completed_task_details['checked_at'] : null;
                            return $item;
                        })->toArray();
                        $upcoming_orders[$key]['work_order_checklists'] = $checklist_tasks_count;
                        $upcoming_orders[$key]['work_order_checklists_pending'] = $checklist_actions_left;

                    }

//              if (!empty($uo['worker_started_at'])) {
//                  $upcoming_orders[$key]['has_active_task'] = true;
//              }
                    #endrgion


//                    if ($uo['job_started_at'] == NULL) {
//                        $target_date = $uo['target_date'];
//                        $upcoming_orders[$key]['target_date'] = $target_date;
//                    } else {
//                        $target_date = date('Y-m-d H:i:s', strtotime('+' . $service_window . ' ' . $service_window_type, strtotime($uo['job_started_at'])));
//                        $upcoming_orders[$key]['target_date'] = $target_date;
//                    }


                    if ($offline_mode) {
                        $upcoming_orders[$key]['md5_checksum'] = $uo['has_checksum'][0]['md5_checksum'] ?? md5(json_encode($uo));
                        unset($upcoming_orders[$key]['has_checksum']);
                    }
                } elseif ($checksum_only && $offline_mode) {
                    $upcoming_orders[$key]['md5_checksum'] = $uo['has_checksum'][0]['md5_checksum'] ?? md5(json_encode($uo));
                    unset($upcoming_orders[$key]['has_checksum']);
                }
            }
            //remove any object from the $upcoming_orders array that doesn't contain the id key
            $upcoming_orders = array_filter($upcoming_orders, function ($item) {
                return is_array($item) && array_key_exists('id', $item);
            });
            $upcoming_orders = array_values($upcoming_orders);
        }
        return $upcoming_orders;
    }

  public static function upcoming_orders($worker_id, $search,$sort_by='desc', $filter_project_id, $filter_property_id, $filter_service_type, $filter_asset_name_id, $examine = NULL, $offline_mode=false, $checksum_only=false)
  {
    $tdate = date('Y-m-d');
    $loginuser_asset_categories = trim(Auth::user()->asset_categories) != "" ? Auth::user()->asset_categories : 0;
    $upcoming_orders = WorkOrders::with('workerTimings');
    if($offline_mode && $checksum_only){
        $upcoming_orders = $upcoming_orders->withOnly('hasChecksum')->select('work_orders.id');
    }else{
      if($offline_mode){
          $upcoming_orders = $upcoming_orders->with(
            'assignedWorkers',
            'contract',
            'assetCategory', 'assetName', 'asset',
            'priority','propertyBuilding', 'projectSettings',
            'worker', 'createdByUser', 'chatMessages', 'itemRequests',
            'serviceProviderItemRequests', 'relatedWorkOrders', 'NoChecklistSubtaskAction',
            'relatedPMWorkOrders', 'slaAssetCategory', 'checklist', 'closedBy', 'createdBy',
            'contractPriority', 'frequencyMaster', 'projectOwner', 'hasChecksum'
          );

      }
      $upcoming_orders = $upcoming_orders->select('work_orders.id', 'work_orders.work_order_id', 'poa.project_id', 'work_orders.property_id', 'work_orders.service_type', 'work_orders.asset_name_id', 'work_orders.sp_reopen_status', 'work_orders.worker_id', 'work_orders.old_worker_id', 'work_orders.description', 'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag', 'assets.asset_symbol', 'assets.asset_number', 'contracts.contract_number', 'properties.latitude', 'properties.longitude', 'properties.location', 'property_buildings.building_name', 'work_orders.asset_category_id', 'work_orders.work_order_type', 'work_orders.frequency_id', 'projects_details.project_name as project', 'work_orders.priority_id', 'work_orders.response_time', 'work_orders.worker_started_at', 'work_orders.sp_approve_job', 'work_orders.bm_approve_job')
      ;
    }
    $upcoming_orders = $upcoming_orders->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                        ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
                        ->join('users', 'users.id', '=', 'work_orders.created_by')
                        ->leftjoin('users as poa', 'poa.id', '=', 'contracts.user_id')
                        ->leftjoin('projects_details', 'projects_details.id', '=', 'poa.project_id')
                        ->leftJoin('work_order_workers', 'work_order_workers.work_order_id', '=', 'work_orders.id')
                        ->whereIn('work_orders.asset_category_id', explode(',', $loginuser_asset_categories))
                        ->where('job_status', '<>', 'active')
                        ->where(fn($query) =>
                        $query->where(function ($query) use ($tdate, $examine) {

                          if($examine == NULL) {
                              // If $examine is null, apply based on work_orders.type
                              $query->where(function ($q) use ($tdate) {
                                $q->where(function ($inner) use ($tdate) {
                                    $inner->where('work_orders.work_order_type', 'reactive')
                                          ->where(function ($cond) use ($tdate) {
                                              $cond->where('work_orders.start_date', '>=', $tdate)
                                                  ->orWhere('work_orders.start_date', '<=', $tdate);
                                          });
                                })
                                ->orWhere(function ($inner) use ($tdate) {
                                    $inner->where('work_orders.work_order_type', 'preventive')
                                          ->where('work_orders.start_date', '<=', $tdate);
                                });
                            });
                          }
                          else
                          {
                              // If $examine is not null, apply both conditions
                              $query->where(function ($q) use ($tdate) {
                                $q->where('work_orders.start_date', '>=', $tdate)
                                  ->orWhere('work_orders.start_date', '<=', $tdate);
                            });
                          }
                          // $q->where('work_orders.start_date', '>=', date('Y-m-d'))
                          //       ->orWhere('work_orders.start_date', '<=', date('Y-m-d'));
                        })
                                    ->orWhere(fn($subQuery) =>
                                        $subQuery->where('work_orders.assign_type', AssignType::Smart->value)
                                                ->where('work_orders.job_started_at', '<=', date('Y-m-d') . ' 23:59:59')
                                    ));
                        if($examine == NULL) {
                          $upcoming_orders = $upcoming_orders->whereRaw("( (work_orders.status = 1 and workorder_journey = 'job_execution') OR ((work_orders.status = 2 or work_orders.status = 3) and (workorder_journey = 'job_execution') and (work_orders.bm_approve_job != 1 and (work_orders.sp_approve_job = 0 OR work_orders.sp_approve_job = 1))))");
                        }
                        else
                        {
                          $upcoming_orders = $upcoming_orders->where('workorder_journey', 'job_execution')->whereRaw("(work_orders.status = 2 or work_orders.status = 3) and (work_orders.bm_approve_job != 1 and (work_orders.sp_approve_job = 0 OR work_orders.sp_approve_job = 1))");
                        }

                        $upcoming_orders = $upcoming_orders->where(function ($query) use ($worker_id) {
                          $query->where('work_orders.worker_id', $worker_id)->whereNull('worker_started_at')
                                ->orWhere(function ($query) use ($worker_id) {
                                    $query->where('work_order_workers.worker_id', $worker_id)
                                          ->where('work_orders.is_collaborative', 1);
                                })
                                ->where(function ($query) use ($worker_id) {
                                    $query->whereDoesntHave('workerTimings', function ($query) use ($worker_id) {
                                        $query->where('worker_id', $worker_id);
                                    })
                                    ->orWhereDoesntHave('workerTimings');
                                });
                        });
        if($examine == NULL) {
          $upcoming_orders = $upcoming_orders->whereNull('examine_button_clicked_at');
        } else {
          $upcoming_orders = $upcoming_orders->whereNotNull('examine_button_clicked_at');
        }
        if($search)
        {
            $upcoming_orders = $upcoming_orders->where('work_orders.work_order_id', 'like', '%' . $search . '%');
        }

        if($filter_project_id != null && $filter_project_id != "")
        {
          $upcoming_orders = $upcoming_orders->whereIn('poa.project_id', explode(',', $filter_project_id));
        }

        if($filter_property_id != null && $filter_property_id != "")
        {
          $upcoming_orders = $upcoming_orders->whereIn('work_orders.property_id', explode(',', $filter_property_id));
        }

        if($filter_service_type != null && $filter_service_type != "")
        {
          $upcoming_orders = $upcoming_orders->whereIn('work_orders.service_type', explode(',', $filter_service_type));
        }

        if($filter_asset_name_id != null && $filter_asset_name_id != "")
        {
          $upcoming_orders = $upcoming_orders->whereIn('work_orders.asset_name_id', explode(',', $filter_asset_name_id));
        }

    if($sort_by == 'desc')
    {
      $upcoming_orders = $upcoming_orders->orderBy('id', 'desc');
    }
    elseif($sort_by == 'asc')
    {
      $upcoming_orders = $upcoming_orders->orderBy('id', 'asc');
    }
    elseif($sort_by == 'deadline')
    {
      $upcoming_orders = $upcoming_orders->orderBy('target_date', 'asc');
    }
    elseif($sort_by == 'preventive')
    {
      $upcoming_orders = $upcoming_orders->orderBy('work_order_type', 'desc');
    }
    elseif($sort_by == 'reactive')
    {
      $upcoming_orders = $upcoming_orders->orderBy('work_order_type', 'asc');
    }

    $upcoming_orders = $upcoming_orders->groupBy('work_orders.id')->get();

    //$upcoming_orders = $upcoming_orders->orderBy('target_date', 'asc')->get();
    $upcoming_orders = json_decode(json_encode($upcoming_orders), true);
    if(!empty($upcoming_orders))
    {

      foreach($upcoming_orders as $key => $uo)
      {
       if(!$checksum_only || ((!$checksum_only && $offline_mode) || (!$checksum_only && !$offline_mode) ) ){
          $upcoming_orders[$key]['building_name'] = $uo['building_name'];
          $upcoming_orders[$key]['location'] = $uo['floor'].'-'.$uo['room'];
          $upcoming_orders[$key]['asset_tag'] = $uo['asset_symbol'].$uo['asset_number'];

          $sla_asset_categories = DB::table('contract_asset_categories')
                        ->where('asset_category_id', $uo['asset_category_id'])
                        ->where('contract_number', $uo['contract_number'])
                        ->orderBy('id', 'desc')
                        ->first();

          $response_time = 0;
          $service_window = 0;
          $response_time_type = 'hours';
          $service_window_type = 'minutes';
          if($uo['work_order_type'] == "reactive")
          {
            if(!empty($sla_asset_categories->priority_id)){
              $contract_priorities = DB::table('contract_priorities')
                    ->where('priority_id', $sla_asset_categories->priority_id)
                    ->where('contract_number', $uo['contract_number'])
                    ->orderBy('id', 'desc')
                    ->first();

            $response_time = isset($contract_priorities->response_time) ? $contract_priorities->response_time : 0;
            $service_window = isset($contract_priorities->service_window) ? $contract_priorities->service_window : '';
            $response_time_type = isset($contract_priorities->response_time_type) ? $contract_priorities->response_time_type:'hours';
            $service_window_type = isset($contract_priorities->service_window_type) ? $contract_priorities->service_window_type:'minutes';
            }
          }
          elseif($uo['work_order_type'] == "preventive" && $uo['priority_id'] != 0)
          {
            $contract_priorities = DB::table('contract_priorities')
                  ->where('priority_id', $uo['priority_id'])
                  ->where('contract_number', $uo['contract_number'])
                  ->orderBy('id', 'desc')
                  ->first();

            $response_time = isset($contract_priorities->response_time) ? $contract_priorities->response_time : 0;
            $service_window = isset($contract_priorities->service_window) ? $contract_priorities->service_window : '';
            $response_time_type = isset($contract_priorities->response_time_type) ? $contract_priorities->response_time_type:'hours';
            $service_window_type = isset($contract_priorities->service_window_type) ? $contract_priorities->service_window_type:'minutes';
          }
          else
          {
            $contract_frequencies = DB::table('frequencies_master')
                      ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                      ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                      ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                      ->where('frequencies_master.id', $uo['frequency_id'])
                      ->where('contract_frequencies.contract_number', $uo['contract_number'])
                      ->first();
            $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
            $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : '';
            $response_time_type = isset($contract_frequencies->response_time_type) ? $contract_frequencies->response_time_type:'hours';
            $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
          }
          $created_at = $uo['created_at'];
          $tdate = date('Y-m-d H:i:s');
          $datetime1 = strtotime($created_at);
          $datetime2 = strtotime($tdate);
          $interval  = abs($datetime2 - $datetime1);
          $minutes   = round($interval / 60);
          if($response_time_type == "days")
          {
            $response_time = $response_time * 1440;
          }
          elseif($response_time_type == "hours")
          {
            $response_time = $response_time * 60;
          }
          $time_left = $response_time - $minutes;

          if($uo['job_started_at'] == NULL)
          {
            $target_date = $uo['target_date'];
            $upcoming_orders[$key]['target_date'] = $target_date;
          }
          else
          {
            $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($uo['job_started_at'])));
            $upcoming_orders[$key]['target_date'] = $target_date;
          }
          //dd($service_window);
          if($uo['job_started_at'] != '')
          {
            if(strtotime($target_date) >= strtotime($tdate))
            {
              $upcoming_orders[$key]['pass_fail'] = 'pass';
            }
            else
            {
              $upcoming_orders[$key]['pass_fail'] = 'fail';
            }
          }
          if($uo['response_time'] == 'On time' || $uo['response_time'] == 'Late')
          {
            $upcoming_orders[$key]['response_time'] = $uo['response_time'];
          }
          else
          {
            if($time_left >= 0)
            {
              $upcoming_orders[$key]['response_time'] = $time_left;
            }
            else
            {
              $upcoming_orders[$key]['response_time'] = 'Late';
            }
          }
          if($uo['status'] == 2 && $uo['sp_reopen_status'] == 2 && $uo['worker_started_at'] == NULL && $uo['job_completion_date'] == NULL && $uo['job_submitted_at'] == NULL && ($uo['worker_id'] == $uo['old_worker_id'] || $uo['old_worker_id'] == 0))
          {
            unset($upcoming_orders[$key]);
          }

          if($uo['status'] == 2 && $uo['sp_reopen_status'] == 2 && $uo['sp_approve_job'] == 0 && $uo['bm_approve_job'] == 0 &&  ($uo['worker_id'] == $uo['old_worker_id'] || $uo['old_worker_id'] == 0))
          {
            unset($upcoming_orders[$key]);
          }
          if($offline_mode){
            $upcoming_orders[$key]['md5_checksum'] = $uo['has_checksum'][0]['md5_checksum'];
            unset($upcoming_orders[$key]['has_checksum']);
          }
        }elseif($checksum_only && $offline_mode){
          $upcoming_orders[$key]['md5_checksum'] = $uo['has_checksum'][0]['md5_checksum'];
          unset($upcoming_orders[$key]['has_checksum']);
        }
      }

      $upcoming_orders = array_values($upcoming_orders);
    }
    return $upcoming_orders;
  }


    public static function offline_upcoming_orders($worker_id, $search, $sort_by = 'desc', $filter_project_id, $filter_property_id, $filter_service_type, $filter_asset_name_id, $examine = NULL, $offline_mode = false, $checksum_only = false, $workOrderIds=null)
    {
        $tdate = date('Y-m-d');
        $loginuser_asset_categories = trim(Auth::user()->asset_categories) != "" ? Auth::user()->asset_categories : 0;
        $upcoming_orders = WorkOrders::with('workerTimings');
        if ($offline_mode && $checksum_only) {
            $upcoming_orders = $upcoming_orders->withOnly('hasChecksum')->select('work_orders.id');
        } else {
            if ($offline_mode) {
                $upcoming_orders = $upcoming_orders->with(
                    'assignedWorkers',
                    'contract',
                    'assetCategory', 'assetName', 'asset',
                    'priority', 'propertyBuilding', 'projectSettings',
                    'worker', 'createdByUser', 'chatMessages', 'itemRequests.requestedItems.contractUsableItem',
                    'serviceProviderItemRequests.requestedItems.contractUsableItem',
                    'relatedWorkOrders', 'NoChecklistSubtaskAction',
                    'relatedPMWorkOrders', 'slaAssetCategory', 'checklist', 'closedBy', 'createdBy',
                    'contractPriority', 'frequencyMaster', 'projectOwner', 'hasChecksum'
                );

            }
            $upcoming_orders->select('work_orders.id', 'work_orders.work_order_id',
                'work_orders.project_user_id','work_orders.wo_images', 'work_orders.maintanance_request_id',
                'work_orders.preventive_start_time', 'properties.property_type', 'properties.complex_name',
                'poa.project_id', 'work_orders.property_id', 'work_orders.service_type', 'assets.barcode_value',
                'work_orders.asset_name_id', 'work_orders.description', 'contract_type','work_orders.reason',
                'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'assets.barcode_value',
                'work_orders.end_date', 'work_orders.worker_started_at', 'target_date', 'wtf_start_time',
                'work_orders.status', 'workorder_journey', 'work_orders.created_at',
                'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag',
                'assets.asset_symbol', 'assets.asset_number', 'contracts.contract_number',
                'properties.latitude', 'properties.longitude', 'properties.location',
                'property_buildings.building_name', 'work_orders.asset_category_id',
                'work_orders.work_order_type', 'work_orders.frequency_id', 'work_orders.asset_number_id',
                'projects_details.project_name as project', 'work_orders.priority_id',
                'work_orders.response_time', 'work_orders.sp_approve_job', 'work_orders.bm_approve_job',
                'work_orders.sp_reopen_status', 'work_orders.worker_id', 'work_orders.old_worker_id',
                'work_orders.checklist_id', 'work_orders.examine_button_clicked_at','work_orders.place',
                'maintanance_request.app_type','maintanance_request.generated_from', 'maintanance_request.image1',
                'maintanance_request.image2', 'maintanance_request.image3','work_orders.schedule_start_time','work_orders.bm_approve_issue','work_orders.sla_service_window_priority');
        }
        $upcoming_orders = $upcoming_orders
            ->when(!empty($workOrderIds),function($query) use($workOrderIds){
                return $query->whereIn('work_orders.id',$workOrderIds);
            })
            ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
            ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
            ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
            ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
            ->join('users', 'users.id', '=', 'work_orders.created_by')
            ->leftjoin('users as poa', 'poa.id', '=', 'contracts.user_id')
            ->leftjoin('projects_details', 'projects_details.id', '=', 'poa.project_id')
            ->leftJoin('work_order_workers', 'work_order_workers.work_order_id', '=', 'work_orders.id')
            ->leftjoin('maintanance_request', 'maintanance_request.id', '=', 'work_orders.maintanance_request_id')
            ->whereIn('work_orders.asset_category_id', explode(',', $loginuser_asset_categories))
            ->where('job_status', '<>', 'active')
            ->where(fn($query) => $query->where(function ($query) use ($tdate, $examine) {

                          if($examine == NULL) {
                              // If $examine is null, apply based on work_orders.type
                              $query->where(function ($q) {
                                $q->where(function ($inner) {
                                    $inner->where('work_orders.work_order_type', 'reactive')
                                          ->where(function ($cond) {
                                              $cond->where('work_orders.start_date', '>=', date('Y-m-d'))
                                                  ->orWhere('work_orders.start_date', '<=', date('Y-m-d'));
                                          });
                                })
                                ->orWhere(function ($inner) {
                                    $inner->where('work_orders.work_order_type', 'preventive')
                                          ->where('work_orders.start_date', '<=', date('Y-m-d'));
                                });
                            });
                          }
                          else
                          {
                              // If $examine is not null, apply both conditions
                              $query->where(function ($q) {
                                $q->where('work_orders.start_date', '>=', date('Y-m-d'))
                                  ->orWhere('work_orders.start_date', '<=', date('Y-m-d'));
                            });
                          }
                          // $q->where('work_orders.start_date', '>=', date('Y-m-d'))
                          //       ->orWhere('work_orders.start_date', '<=', date('Y-m-d'));
                        })
                ->orWhere(fn($subQuery) => $subQuery->where('work_orders.assign_type', AssignType::Smart->value)
                    ->where('work_orders.job_started_at', '<=', date('Y-m-d') . ' 23:59:59')
                ));
        if ($examine == NULL) {
            $upcoming_orders = $upcoming_orders->whereRaw("( (work_orders.status = 1 and workorder_journey = 'job_execution') OR ((work_orders.status = 2 or work_orders.status = 3) and (workorder_journey = 'job_execution') and (work_orders.bm_approve_job != 1 and (work_orders.sp_approve_job = 0 OR work_orders.sp_approve_job = 1))))");
        } else {
            $upcoming_orders = $upcoming_orders->where('workorder_journey', 'job_execution')->whereRaw("(work_orders.status = 2 or work_orders.status = 3) and (work_orders.bm_approve_job != 1 and (work_orders.sp_approve_job = 0 OR work_orders.sp_approve_job = 1))");
        }

        $upcoming_orders = $upcoming_orders->where(function ($query) use ($worker_id) {
            $query->where('work_orders.worker_id', $worker_id)->whereNull('worker_started_at')
                ->orWhere(function ($query) use ($worker_id) {
                    $query->where('work_order_workers.worker_id', $worker_id)
                        ->where('work_orders.is_collaborative', 1);
                })
                ->where(function ($query) use ($worker_id) {
                    $query->whereDoesntHave('workerTimings', function ($query) use ($worker_id) {
                        $query->where('worker_id', $worker_id);
                    })
                        ->orWhereDoesntHave('workerTimings');
                });
        });
        if ($examine == NULL) {
            $upcoming_orders = $upcoming_orders->whereNull('examine_button_clicked_at');
        } else {
            $upcoming_orders = $upcoming_orders->whereNotNull('examine_button_clicked_at');
        }
        if ($search) {
            $upcoming_orders = $upcoming_orders->where('work_orders.work_order_id', 'like', '%' . $search . '%');
        }

        if ($filter_project_id != null && $filter_project_id != "") {
            $upcoming_orders = $upcoming_orders->whereIn('poa.project_id', explode(',', $filter_project_id));
        }

        if ($filter_property_id != null && $filter_property_id != "") {
            $upcoming_orders = $upcoming_orders->whereIn('work_orders.property_id', explode(',', $filter_property_id));
        }

        if ($filter_service_type != null && $filter_service_type != "") {
            $upcoming_orders = $upcoming_orders->whereIn('work_orders.service_type', explode(',', $filter_service_type));
        }

        if ($filter_asset_name_id != null && $filter_asset_name_id != "") {
            $upcoming_orders = $upcoming_orders->whereIn('work_orders.asset_name_id', explode(',', $filter_asset_name_id));
        }

        if ($sort_by == 'desc') {
            $upcoming_orders = $upcoming_orders->orderBy('id', 'desc');
        } elseif ($sort_by == 'asc') {
            $upcoming_orders = $upcoming_orders->orderBy('id', 'asc');
        } elseif ($sort_by == 'deadline') {
            $upcoming_orders = $upcoming_orders->orderBy('target_date', 'asc');
        } elseif ($sort_by == 'preventive') {
            $upcoming_orders = $upcoming_orders->orderBy('work_order_type', 'desc');
        } elseif ($sort_by == 'reactive') {
            $upcoming_orders = $upcoming_orders->orderBy('work_order_type', 'asc');
        }

        $upcoming_orders = $upcoming_orders->get();

        //$upcoming_orders = $upcoming_orders->orderBy('target_date', 'asc')->get();
        $upcoming_orders = json_decode(json_encode($upcoming_orders), true);
        if (!empty($upcoming_orders)) {

            foreach ($upcoming_orders as $key => $uo) {
                if (!$checksum_only || ((!$checksum_only && $offline_mode) || (!$checksum_only && !$offline_mode))) {

                    #region preparing data for offline response
                    $workOrderContract = Cache::remember('wo_contract_' . $uo['contract_number'], CachingTTL::TTL_ONE_HOUR->value, function () use ($uo) {
                        return Contracts::query()
                            ->where('contract_number', 'like', $uo['contract_number'])
                            ->with(['usableItems'])
                            ->first();
                    });
                    // Fetch usable items related to the contract
                    $selectableItems = $workOrderContract->usableItems->map(function (ContractUsableItem $item) {
                        return Cache::remember('ContractUsableItems_' . $item->id, CachingTTL::TTL_ONE_HOUR->value, function () use ($item) {
                            return $item->getItem();
                        });
                    });
                    // Filter out items where all fields are null and reset the array keys
                    $filteredItems = $selectableItems->filter(function ($item) {
                        // Check if any values in the item are not null
                        return collect($item)->filter()->isNotEmpty();
                    })->values(); // Reset array keys


                    $wo_images = [];

                    // Process images from wo_images field
                    if (!empty($uo['wo_images'])) {

                        foreach (explode(',', trim($uo['wo_images'])) as $image) {
                            if ((trim($image) != "") && (trim($image) != null))
                                $extension = pathinfo($image)['extension'];
                            if ($extension != 'pdf') {
                                if (filled(ImagesUploadHelper::displayImageMobileApp($image, 'uploads/workorder'))) {
                                    $wo_images[] = ImagesUploadHelper::displayImageMobileApp($image, 'uploads/workorder');
                                }

                            } else {
                                $wo_images[] = url('uploads/workorder/' . $image);
                            }
                        }
                    }
                    // Process images from maintanance_request

                    if (
                        isset($uo['maintanance_request_id']) &&
                        $uo['maintanance_request_id'] != null &&
                        (filled($uo['image1']) || filled($uo['image2']) || filled($uo['image3']))
                    ) {

                        if (filled($uo['image1'])) {
                            if (filled(ImagesUploadHelper::displayImageMobileApp($uo['image1'], 'uploads/maintanance_request'))) {
                                $wo_images[] = ImagesUploadHelper::displayImageMobileApp($uo['image1'], 'uploads/maintanance_request');
                            }
                        }
                        if (filled($uo['image2'])) {
                            if (filled(ImagesUploadHelper::displayImageMobileApp($uo['image2'], 'uploads/maintanance_request'))) {
                                $wo_images[] = ImagesUploadHelper::displayImageMobileApp($uo['image2'], 'uploads/maintanance_request');
                            }
                        }
                        if (filled($uo['image3'])) {
                            if (filled(ImagesUploadHelper::displayImageMobileApp($uo['image3'], 'uploads/maintanance_request'))) {
                                $wo_images[] = ImagesUploadHelper::displayImageMobileApp($uo['image3'], 'uploads/maintanance_request');
                            }
                        }
                    }
                    $upcoming_orders[$key]['wo_images']=$wo_images;




                                        $response_time = 0;
                    $service_window = 0;
                    $response_time_type = 'hours';
                    $service_window_type = 'minutes';
                    if ($uo['work_order_type'] == "reactive") {
                        if (!empty($sla_asset_categories->priority_id)  || !empty($uo['sla_service_window_priority'])) {
                            if ($uo['sla_service_window_priority'] != 0) {
                                $priority_id = $uo['sla_service_window_priority'];
                            } else {
                                $priority_id = $sla_asset_categories->priority_id;
                            }

                            $contract_priorities = DB::table('contract_priorities')
                                ->where('priority_id', $priority_id)
                                ->where('contract_number', $uo['contract_number'])
                                ->orderBy('id', 'desc')
                                ->first();

                            if (empty($contract_priorities)) {
                                $contract_priorities = DB::table('priorities')
                                    ->where('id', $priority_id)
                                    ->orderBy('id', 'desc')
                                    ->first();
                            }
                            if (!empty($uo['priority_id']) && $uo['priority_id'] != 0) {
                                $contract_priorities = DB::table('priorities')
                                    ->where('id', $uo['priority_id'])
                                    ->orderBy('id', 'desc')
                                    ->first();
                                $priority_id = $uo['priority_id'];
                            }
                            if (isset($contract_priorities)) {
                                $response_time = $contract_priorities->response_time;
                                $service_window = $contract_priorities->service_window;
                                $response_time_type = $contract_priorities->response_time_type;
                                $service_window_type = $contract_priorities->service_window_type;
                            }
//                            $response_time = isset($contract_priorities->response_time) ? $contract_priorities->response_time : 0;
//                            $service_window = isset($contract_priorities->service_window) ? $contract_priorities->service_window : '';
//                            $response_time_type = isset($contract_priorities->response_time_type) ? $contract_priorities->response_time_type : 'hours';
//                            $service_window_type = isset($contract_priorities->service_window_type) ? $contract_priorities->service_window_type : 'minutes';
                        }
                    } elseif ($uo['work_order_type'] == "preventive" && $uo['priority_id'] != 0) {
                        $priority_id = $uo['priority_id'];
                        if ($uo['sla_service_window_priority'] != 0) {
                            $priority_id = $uo['sla_service_window_priority'];
                        }
                        $contract_priorities = DB::table('contract_priorities')
                            ->where('priority_id', $priority_id)
                            ->where('contract_number', $uo['contract_number'])
                            ->orderBy('id', 'desc')
                            ->first();

//                        $response_time = $contract_priorities->response_time;
//                        $service_window = $contract_priorities->service_window;
//                        $response_time_type = $contract_priorities->response_time_type;
//                        $service_window_type = $contract_priorities->service_window_type;
                        if (isset($contract_priorities)) {
                            $response_time = $contract_priorities->response_time;
                            $service_window = $contract_priorities->service_window;
                            $response_time_type = $contract_priorities->response_time_type;
                            $service_window_type = $contract_priorities->service_window_type;
                        } else {
                            $contract_priorities = DB::table('priorities')
                                ->where('id', $priority_id)
                                ->orderBy('id', 'desc')
                                ->first();

                            if (!empty($details->priority_id) && $details->priority_id != 0) {
                                $contract_priorities = DB::table('priorities')
                                    ->where('id', $details->priority_id)
                                    ->orderBy('id', 'desc')
                                    ->first();
                            }
                            if (isset($contract_priorities)) {
                                $response_time = $contract_priorities->response_time;
                                $service_window = $contract_priorities->service_window;
                                $response_time_type = $contract_priorities->response_time_type;
                                $service_window_type = $contract_priorities->service_window_type;
                            }
                        }
                    } else {
                        $contract_frequencies = DB::table('frequencies_master')
                            ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                            ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                            ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                            ->where('frequencies_master.id', $uo['frequency_id'])
                            ->where('contract_frequencies.contract_number', $uo['contract_number'])
                            ->first();
                        $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
                        $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : '';
                        $response_time_type = isset($contract_frequencies->response_time_type) ? $contract_frequencies->response_time_type : 'hours';
                        $service_window_type = isset($contract_priorities->service_window_type) ? $contract_priorities->service_window_type : 'minutes';
                    }
                    $created_at = $uo['created_at'];
                    $tdate = date('Y-m-d H:i:s');
                    $datetime1 = strtotime($created_at);
                    $datetime2 = strtotime($tdate);
                    $interval = abs($datetime2 - $datetime1);
                    $minutes = round($interval / 60);
                    if ($response_time_type == "days") {
                        $response_time = $response_time * 1440;
                    } elseif ($response_time_type == "hours") {
                        $response_time = $response_time * 60;
                    }
                    $time_left = $response_time - $minutes;

////                    if ($uo['job_started_at'] == NULL) {
////                        $target_date = $uo['target_date'];
////                        $upcoming_orders[$key]['target_date'] = $target_date;
////                    } else {
////                        $target_date = date('Y-m-d H:i:s', strtotime('+' . $service_window . ' ' . $service_window_type, strtotime($uo['job_started_at'])));
////                        $upcoming_orders[$key]['target_date'] = $target_date;
////                    }
                    if ($uo['job_started_at'] == NULL || $uo['bm_approve_issue'] == 2) {
                        $target_date = $uo['target_date'];
                        $upcoming_orders[$key]['target_date'] = $target_date;
                    } else {

                        if ($uo['work_order_type'] == "preventive" && $uo['target_date'] != "" && $uo['target_date'] != "00:00:00") {
                            $target_date = $uo['target_date'];
                        } else {
                            $target_date = date('Y-m-d H:i:s', strtotime('+' . $service_window . ' ' . $service_window_type, strtotime($uo['job_started_at'])));
                        }
                        $upcoming_orders[$key]['target_date'] = $target_date;
                    }


                    $upcoming_orders[$key]['screen_status'] = '';
//                    $upcoming_orders[$key]['over_due'] = false;

                    if ($uo['status'] == 2 && ($uo['workorder_journey'] == 'job_execution' || $uo['workorder_journey'] == 'job_evaluation') && ($uo['bm_approve_job'] == 1 || $uo['sp_approve_job'] == 1)) {
                        $upcoming_orders[$key]['screen_status'] = 'Rejected';
                    } elseif ($uo['status'] == 2 && $uo['workorder_journey'] == 'job_execution' && $uo['sp_reopen_status'] == 2 && $uo['worker_started_at'] == NULL) {
                        $upcoming_orders[$key]['screen_status'] = 'Re Opened';
                    } elseif ($uo['status'] == 3 && ($uo['workorder_journey'] == 'job_execution' || $uo['workorder_journey'] == 'job_evaluation')) {
                        $upcoming_orders[$key]['screen_status'] = 'On Hold';
                    } elseif ($upcoming_orders[$key]['target_date'] < now() && ($uo['status'] == 2) && ($uo['workorder_journey'] == 'job_execution')) {
                        $upcoming_orders[$key]['screen_status'] = 'Over Due';
                    }

                    $upcoming_orders[$key]['over_due'] = $upcoming_orders[$key]['target_date'] < now() ?? false;

                    //that to make sure the over_due key return
                    if (!isset($upcoming_orders[$key]['over_due'])) {
                        $upcoming_orders[$key]['over_due'] = false;
                        $upcoming_orders[$key]['fixed_over_due'] = true;
                    }



//                    if ($examine) {
                        if (!empty($uo['item_requests']['requested_items'])) {
                            // Access the relationship data for itemRequests
                            foreach ($uo['item_requests']['requested_items'] as $key => $requestedItem) {
                                // Get the ContractUsableItem associated with the requestedItem
                                $contractUsableItem = $requestedItem['contract_usable_item'] ?? null;
                                // Set the name of the requestedItem based on the ContractUsableItem's item
                                if ($contractUsableItem)
                                    $uo['item_requests']['requested_items'][$key]['name'] = Cache::remember("contract_" . $workOrderContract->id . "_contract_usable_item_" . $contractUsableItem['id'], CachingTTL::TTL_ONE_HOUR->value, function () use ($contractUsableItem) {
                                        return optional(ContractUsableItem::query()->find($contractUsableItem['id'])->getItem())->name ?? null;
                                    });
                                // Missing quantity should be according the status
                                $uo['item_requests']['requested_items'][$key]['quantity_accepted'] = $uo['item_requests']['status'] == 'partially_given' ? $requestedItem['quantity'] - $requestedItem['quantity_accepted'] : $requestedItem['quantity_accepted'];
                            }
                        }
                    if (isset($uo['service_provider_item_requests']['requested_items'])) {
                        // Access the relationship data for serviceProviderItemRequests
                        foreach ($uo['service_provider_item_requests']['requested_items'] as $key => $requestedItem) {
                            // Get the ContractUsableItem associated with the requestedItem
                            $contractUsableItem = $requestedItem['contract_usable_item'];
                            // Set the name of the requestedItem based on the ContractUsableItem's item
                            $uo['service_provider_item_requests']['requested_items'][$key]['name'] = Cache::remember("contract_" . $workOrderContract->id . "_contract_service_provider__usable_item_" . $contractUsableItem['id'], CachingTTL::TTL_ONE_HOUR->value, function () use ($contractUsableItem) {
                                return optional(ContractUsableItem::query()->find($contractUsableItem['id'])->getItem())->name ?? null;
                            });
                        }
                    }
//                    }
                    $upcoming_orders[$key]['item_requests'] = $uo['item_requests'] ?? null;
                    $upcoming_orders[$key]['service_provider_item_requests'] = $uo['service_provider_item_requests'] ?? null;
                    $upcoming_orders[$key]['spare_items'] = $filteredItems ?? null;
                    $upcoming_orders[$key]['contract'] = $workOrderContract;

                    $upcoming_orders[$key]['place_for_maintenance'] = is_null($uo['place']) ? "" : trim($uo['place']);
                    $upcoming_orders[$key]['apartment_villa'] = '';
                    $upcoming_orders[$key]['tenant_phone_number'] = '';
                    $upcoming_orders[$key]['is_workorder_from_tenant'] = $uo['app_type'] == 'tenant' || $uo['generated_from'] == 'tenant' ? true : false;

                    if (isset($uo['maintanance_request_id']) &&
                        $uo['maintanance_request_id'] != null) {
                        $details_maintenance_request = Cache::remember('details_maintenance_request_' . $uo['maintanance_request_id'], CachingTTL::TTL_ONE_HOUR->value, function () use ($uo) {
                            return MaintenanceRequest::with('user')->where('id', $uo['maintanance_request_id'])->first();
                        });
                        if (isset($details_maintenance_request->user)) {
//                            $upcoming_orders[$key]['apartment_villa'] = $details_maintenance_request->user?->apartment ?? '';
                            $upcoming_orders[$key]['apartment_villa'] = $details_maintenance_request->user->apartment ?? '';

                            if ($uo['app_type'] == 'tenant' || $uo['generated_from'] == 'tenant') {
                                $upcoming_orders[$key]['tenant_phone_number'] = $details_maintenance_request->user->phone ?? '';
                            }
                        }
                    }


                    if (isset($uo['asset_category'])) {
                        $deleted = "";
                        $upcoming_orders[$key]['asset_category_name'] = $uo['asset_category']['asset_category'] . $deleted;
                    } else {
                        $upcoming_orders[$key]['asset_category_name'] = '';
                    }
                    $upcoming_orders[$key]['project_name'] = Helper::getProjectNameByWorkOrderID($uo['id']);
                    $upcoming_orders[$key]['current_time'] = date('Y-m-d H:i:s');

                    $upcoming_orders[$key]['zone']= $uo['floor'] ?? '';
                    $upcoming_orders[$key]['unit']= $uo['room'] ?? '';

                    $upcoming_orders[$key]['floor']= $uo['floor'] ?? '';
                    $upcoming_orders[$key]['room']= $uo['room'] ?? '';

                    $upcoming_orders[$key]['place'] = $uo['place'] ?? '';

                    $upcoming_orders[$key]['assets_files_data']= self::getAssetDetails($uo['asset_number_id'], $uo['property_id']);
//                    $upcoming_orders[$key]['assets_files']= $uo['asset'];
//                    if (isset($uo['asset']['files'])){
//                        foreach ($uo['asset']['files'] as $f => $h) {
//                            $uo['asset']['files'][$f]['uri']= trim($uo['asset']['files'][$f]['file_name']) != "" ? asset('uploads/asset_images/' . $uo['asset']['files'][$f]['file_name']) : $uo['asset']['files'][$f]['file_name'];
//                            $uo['asset']['files'][$f]['file_name']= trim($uo['asset']['files'][$f]['file_name']);
//
//                            $upcoming_orders[$key]['assets_files'][$f]= $uo['asset']['files'][$f];
//                            $upcoming_orders[$key]['assets_files'][$f]['uri']= trim($uo['asset']['files'][$f]['file_name']) != "" ? asset('uploads/asset_images/' . $uo['asset']['files'][$f]['file_name']) : $uo['asset']['files'][$f]['file_name'];
//                            $upcoming_orders[$key]['assets_files'][$f]['file_name']= trim($uo['asset']['files'][$f]['file_name']);
//                        }
//                    }

                    $wtfs = Cache::remember('work_time_frame_' . $uo['project_user_id'], CachingTTL::TTL_ONE_HOUR->value, function () use ($uo) {
                        return DB::table('work_time_frame')
                            ->select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
                            ->where('user_id', $uo['project_user_id'])
                            ->first();
                    });

                    if ($uo['wtf_start_time'] == '') {
                        if (!isset($wtfs)) //If work order time frame has added
                        {
                            $time = "00:00:00";
                        } else {
                            $time = $wtfs->start_time;
                        }
                    } else {
                        $time = $uo['wtf_start_time'];
                    }

                    $preventive_start_time = WorkorderHelper::getWorkorderstarttime($time, $uo['preventive_start_time']);
                    if ($uo['work_order_type'] == "preventive") //If work_order_type is preventive
                    {
                        $upcoming_orders[$key]['reported_at'] = $uo['start_date'] . ' ' . $preventive_start_time;
                    } else {
                        $upcoming_orders[$key]['reported_at'] = $uo['created_at'];
                    }


                    $upcoming_orders[$key]['building_name'] = $uo['building_name'];
                    $upcoming_orders[$key]['location'] = $uo['floor'] . '-' . $uo['room'];
                    $upcoming_orders[$key]['asset_tag'] = $uo['asset_symbol'] . $uo['asset_number'];
                    if(empty($upcoming_orders[$key]['asset_tag']))
                        $upcoming_orders[$key]['asset_tag']="";

                    $sla_asset_categories = DB::table('contract_asset_categories')
                        ->where('asset_category_id', $uo['asset_category_id'])
                        ->where('contract_number', $uo['contract_number'])
                        ->orderBy('id', 'desc')
                        ->first();

                    #region checklist tasks for WO
//              $active_task = self::active_task(Auth::user()->id);
//              $active_tasks = collect($upcoming_orders)->where('worker_id', '=', Auth::user()->id);
                    $upcoming_orders[$key]['has_active_task'] = false;
//              if (!empty((array)$active_tasks) && count($active_tasks) >= 3) {
//                  foreach ($active_tasks as $atkey => $at) {
//                      if ($at['id'] != $uo['id']) {
//                          $upcoming_orders[$key]['has_active_task'] = true;
//                      }
//                  }
//              }
                    $action = self::check_no_checklist_actions_completed($uo['id']);
                    if ($uo['checklist_id'] == 0) {

                        $upcoming_orders[$key]['has_checklist'] = false;
                        $upcoming_orders[$key]['total_checklists'] = 1;
                        $upcoming_orders[$key]['work_order_checklists'] = 1;

                        if ($action == 0) {
                            $upcoming_orders[$key]['checklist_actions_left'] = 1;
                            $upcoming_orders[$key]['work_order_checklists_pending'] = 1;
                        } else {
                            $upcoming_orders[$key]['checklist_actions_left'] = 0;
                            $upcoming_orders[$key]['work_order_checklists_pending'] = 0;
                        }
                    } else {
                        $checklist_tasks_count = count(self::checklist_tasks($uo['checklist_id'], $uo['id']));
                        $upcoming_orders[$key]['has_checklist'] = true;
                        $upcoming_orders[$key]['total_checklists'] = $checklist_tasks_count;
                        $checklist_actions_left = $checklist_tasks_count - $action;
                        $upcoming_orders[$key]['checklist_actions_left'] = $checklist_actions_left;
                        if ($checklist_actions_left <= 0) {
                            $upcoming_orders[$key]['checklist_actions_left'] = 0;
                        }
                        $checklist_tasksItems = ApiHelper::getWorkorderChecklists($uo['id']);
                        $upcoming_orders[$key]['checklist_tasks']= collect($checklist_tasksItems)->map(function($item) use($uo){
                            $checklist_completed_task_details = ApiHelper::getChecklistCompletedTaskDetails($uo['id'], $item['id']);
                            $isSubmitted= $checklist_completed_task_details ? 'yes' : 'no';
                            $subtask_checklists = collect(ApiHelper::getChecklistSubmittedSubtask($uo['id'], $item['id'], $isSubmitted));
                            $reformatSubtask= [];
                            foreach ($item['subtask_list'] as $key=>$subtask) {
                                $nestedsubtask_checklists= $subtask_checklists->firstWhere('id', $subtask['id']);
                                $reformatSubtask[]=Helper::Fetchsubtaskactionlist($subtask['id'], $uo['id']);
                                $reformatSubtask[$key]['id']= $subtask['id'] ?? 0;
                                $reformatSubtask[$key]['name']= $subtask['name'] ?? '-';
                                $reformatSubtask[$key]['check_count_checklist_action'] = $nestedsubtask_checklists['check_count_checklist_action'] ?? 0;
                                $reformatSubtask[$key]['is_actions_available']= $nestedsubtask_checklists['is_actions_available'] ?? false;
                                $reformatSubtask[$key]['check_user_by']= $nestedsubtask_checklists['check_user_by'] ?? null;
                                #todo just for testing
                                $reformatSubtask[$key]['checklist_completed_task_details']= $checklist_completed_task_details;
                                $reformatSubtask[$key]['subtask_checklists_details']= $subtask_checklists;
                            }
                            $item['subtask_list']=$reformatSubtask;
                            $item['task_photos'] = (!empty($checklist_completed_task_details)) ? (is_object($checklist_completed_task_details)) ? $checklist_completed_task_details->photos : $checklist_completed_task_details['photos'] : [];
                            $item['task_feedback_options'] = (!empty($checklist_completed_task_details)) ? (is_object($checklist_completed_task_details)) ? $checklist_completed_task_details->feedback_options : $checklist_completed_task_details['feedback_options'] : [];
                            $item['task_comment'] = (!empty($checklist_completed_task_details)) ? (is_object($checklist_completed_task_details)) ? $checklist_completed_task_details->comment : $checklist_completed_task_details['comment'] : "";
                            $item['task_checked_at'] = (!empty($checklist_completed_task_details)) ? (is_object($checklist_completed_task_details)) ? $checklist_completed_task_details->checked_at : $checklist_completed_task_details['checked_at'] : null;
                            return $item;
                        })->toArray();
                        $upcoming_orders[$key]['work_order_checklists'] = $checklist_tasks_count;
                        $upcoming_orders[$key]['work_order_checklists_pending'] = $checklist_actions_left;
                    }

                    #endrgion


                    //dd($service_window);
                    if ($uo['job_started_at'] != '') {
                        if (strtotime($target_date) >= strtotime($tdate)) {
                            $upcoming_orders[$key]['pass_fail'] = 'pass';
                        } else {
                            $upcoming_orders[$key]['pass_fail'] = 'fail';
                        }
                    }
                    if ($uo['response_time'] == 'On time' || $uo['response_time'] == 'Late') {
                        $upcoming_orders[$key]['response_time'] = $uo['response_time'];
                    } else {
                        if ($time_left >= 0) {
                            $upcoming_orders[$key]['response_time'] = $time_left;
                        } else {
                            $upcoming_orders[$key]['response_time'] = 'Late';
                        }
                    }


                    if ($uo['status'] == 2 && $uo['sp_reopen_status'] == 2 && $uo['worker_started_at'] == NULL && $uo['job_completion_date'] == NULL && $uo['job_submitted_at'] == NULL && ($uo['worker_id'] == $uo['old_worker_id'] || $uo['old_worker_id'] == 0)) {
                        unset($upcoming_orders[$key]);
                    }

                    if ($uo['status'] == 2 && $uo['sp_reopen_status'] == 2 && $uo['sp_approve_job'] == 0 && $uo['bm_approve_job'] == 0 && ($uo['worker_id'] == $uo['old_worker_id'] || $uo['old_worker_id'] == 0)) {
                        unset($upcoming_orders[$key]);
                    }
                    if ($offline_mode) {
                        $upcoming_orders[$key]['md5_checksum'] = $uo['has_checksum'][0]['md5_checksum'] ?? md5(json_encode($uo));
                        unset($upcoming_orders[$key]['has_checksum']);
                    }

                    #endregion preparing data for offline response
                } elseif ($checksum_only && $offline_mode) {
                    $upcoming_orders[$key]['md5_checksum'] = $uo['has_checksum'][0]['md5_checksum'] ?? md5(json_encode($uo));
                    unset($upcoming_orders[$key]['has_checksum']);
                }
            }
            //remove any object from the $upcoming_orders array that doesn't contain the id key
            $upcoming_orders = array_filter($upcoming_orders, function ($item) {
                return is_array($item) && array_key_exists('id', $item);
            });
            $upcoming_orders = array_values($upcoming_orders);
        }
        return $upcoming_orders;
    }

  public static function all_pending_orders($worker_id, $search, $count)
  {
    $tdate = date('Y-m-d H:i:s');
    $loginuser_asset_categories = trim(Auth::user()->asset_categories) != "" ? Auth::user()->asset_categories : 0;
    $pending_orders = WorkOrders::select('work_orders.id', 'work_order_id', 'work_orders.description', 'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag', 'contracts.contract_number', 'properties.latitude', 'properties.longitude', 'properties.location', 'work_orders.bm_approve_job', 'work_orders.sp_approve_job', 'work_orders.sp_reopen_status', 'worker_started_at', 'property_buildings.building_name')
                        ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                        ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
                        ->join('users', 'users.id', '=', 'work_orders.created_by')
                        ->whereIn('work_orders.asset_category_id', explode(',', $loginuser_asset_categories))
                        ->where(['worker_id' => $worker_id])
                        // ->whereRaw("((work_orders.status = 2 and (work_orders.workorder_journey = 'job_execution' OR work_orders.workorder_journey = 'job_evaluation') and (work_orders.bm_approve_job = 1 OR work_orders.sp_approve_job = 1))")
                        // ->OrwhereRaw("(work_orders.status = 2 and work_orders.workorder_journey = 'job_execution' and work_orders.sp_reopen_status = 2)")->WhereNull('worker_started_at')
                        // ->OrwhereRaw("(work_orders.status = 3 and (work_orders.workorder_journey = 'job_execution' or work_orders.workorder_journey = 'job_evaluation'))")
                        //->orwhereRaw("(work_orders.target_date < '$tdate' and (work_orders.status = 2) and (work_orders.workorder_journey = 'job_execution') and (work_orders.bm_approve_job != 1 AND work_orders.sp_approve_job != 1) and (work_orders.sp_reopen_status != 2)))")
                        ->orderBy('work_orders.id', 'desc');
    if($count > 0)
    {
      $pending_orders = $pending_orders->offset($count * 10)->limit(10);
    }
    else
    {
      $pending_orders = $pending_orders->limit(10);
    }
    if($search)
    {
      $pending_orders = $pending_orders->where('work_orders.work_order_id', 'like', '%' . $search . '%');
    }
    $pending_orders = $pending_orders->get();
    $pending_orders = json_decode(json_encode($pending_orders), true);
    if(!empty($pending_orders))
    {
      foreach($pending_orders as $key => $po)
      {
        $pending_orders[$key]['screen_status'] = '';
        if($po['status'] == 2 && ($po['workorder_journey'] == 'job_execution' || $po['workorder_journey'] == 'job_evaluation') && ($po['bm_approve_job'] == 1 || $po['sp_approve_job'] == 1))
        {
          $pending_orders[$key]['screen_status'] = 'Rejected';
        }
        elseif($po['status'] == 2 && $po['workorder_journey'] == 'job_execution' && $po['sp_reopen_status'] == 2 && $po['worker_started_at'] == NULL)
        {
          $pending_orders[$key]['screen_status'] = 'Re Opened';
        }
        elseif($po['status'] == 3 && ($po['workorder_journey'] == 'job_execution' || $po['workorder_journey'] == 'job_evaluation'))
        {
          $pending_orders[$key]['screen_status'] = 'On Hold';
        }
        // (work_orders.target_date < '$tdate' and (work_orders.status = 2) and (work_orders.workorder_journey = 'job_execution') and (work_orders.bm_approve_job != 1 AND work_orders.sp_approve_job != 1) and (work_orders.sp_reopen_status != 2))
        elseif(($po['target_date'] < now() && ($po['status'] == 2) && ($po['workorder_journey'] == 'job_execution') && ($po['bm_approve_job'] != 1 && $po['sp_approve_job'] != 1) && ($po['sp_reopen_status'] != 2)))
        {
          $pending_orders[$key]['screen_status'] = 'Over Due';
        }
      }
    }
    return $pending_orders;
  }

  public static function pending_orders($worker_id, $status, $search, $count)
  {
    $tdate = date('Y-m-d H:i:s');
    //echo $status;exit;
    $loginuser_asset_categories = trim(Auth::user()->asset_categories) != "" ? Auth::user()->asset_categories : 0;
    $pending_orders = WorkOrders::with('itemRequests.requestedItems.contractUsableItem')
                        ->select('work_orders.id', 'work_order_id', 'work_orders.sp_reopen_status', 'work_orders.reason', 'work_orders.worker_id', 'work_orders.old_worker_id', 'work_orders.description', 'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag', 'contracts.contract_number', 'properties.latitude', 'properties.longitude', 'properties.location', 'work_orders.bm_approve_job', 'work_orders.sp_approve_job', 'work_orders.sp_reopen_status', 'worker_started_at', 'property_buildings.building_name','work_orders.examine_button_clicked_at','work_orders.preventive_start_time')
                        ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                        ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
                        ->join('users', 'users.id', '=', 'work_orders.created_by')
                        ->whereIn('work_orders.asset_category_id', explode(',', $loginuser_asset_categories))
                        ->where(['worker_id' => $worker_id]);
    if($status == "rejected")
    {
      $status = "Rejected";
      $pending_orders = $pending_orders->whereRaw("(work_orders.status = 2 and work_orders.sp_reopen_status != 2 and (work_orders.workorder_journey = 'job_execution' OR work_orders.workorder_journey = 'job_evaluation') and (work_orders.bm_approve_job = 1 OR work_orders.sp_approve_job = 1 OR (work_orders.bm_approve_job = 0 AND work_orders.sp_approve_job = 0 AND work_orders.workorder_journey = 'job_execution' AND work_orders.reason != '')))");
    }
    elseif($status == "re_open")
    {
      $status = "Re Opened";
      $pending_orders = $pending_orders->whereRaw("(work_orders.status = 2 and work_orders.workorder_journey = 'job_execution' and work_orders.sp_reopen_status = 2)")->WhereNull('worker_started_at');
    }
    elseif($status == "on_hold")
    {
      $status = "On Hold";
      $pending_orders = $pending_orders->whereRaw("(work_orders.status = 3 and (work_orders.workorder_journey = 'job_execution' or work_orders.workorder_journey = 'job_evaluation'))");
    }
    elseif($status == "over_due")
    {
      $status = "Over Due";
      //$pending_orders = $pending_orders->whereRaw("(work_orders.target_date < '$tdate' and (work_orders.status = 2) and (work_orders.workorder_journey = 'job_execution'))");
      $pending_orders = $pending_orders->whereRaw("(work_orders.target_date < '$tdate' and (work_orders.status = 2) and (work_orders.workorder_journey = 'job_execution') and (work_orders.bm_approve_job != 1 AND work_orders.sp_approve_job != 1) and (work_orders.sp_reopen_status != 2))");
    }
    else
    {

      $rejected_order_id = WorkOrders::with('itemRequests.requestedItems.contractUsableItem')->select('work_orders.id', 'work_order_id', 'work_orders.description', 'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag', 'contracts.contract_number', 'properties.latitude', 'properties.longitude', 'properties.location', 'work_orders.bm_approve_job', 'work_orders.sp_approve_job', 'work_orders.sp_reopen_status', 'worker_started_at', 'property_buildings.building_name', 'work_orders.examine_button_clicked_at')
      ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
      ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
      ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
      ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
      ->join('users', 'users.id', '=', 'work_orders.created_by')
      ->whereIn('work_orders.asset_category_id', explode(',', $loginuser_asset_categories))
      ->where(['worker_id' => $worker_id])
      ->whereRaw("(work_orders.status = 2 and work_orders.sp_reopen_status != 2 and (work_orders.workorder_journey = 'job_execution' OR work_orders.workorder_journey = 'job_evaluation') and (work_orders.bm_approve_job = 1 OR work_orders.sp_approve_job = 1 OR (work_orders.bm_approve_job = 0 AND work_orders.sp_approve_job = 0 AND work_orders.workorder_journey = 'job_execution' AND work_orders.reason != '')))")
      ->get();
      if(isset($rejected_order_id) && !empty($rejected_order_id))
      {
        $rejected_order_id = json_decode(json_encode($rejected_order_id), true);
        $rejected_order_id = array_column($rejected_order_id,'id');
      }
      else
      {
        $rejected_order_id = array('0');
      }


      $re_open_order_id = WorkOrders::with('itemRequests.requestedItems.contractUsableItem')->select('work_orders.id', 'work_order_id', 'work_orders.description', 'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag', 'contracts.contract_number', 'properties.latitude', 'properties.longitude', 'properties.location', 'work_orders.bm_approve_job', 'work_orders.sp_approve_job', 'work_orders.sp_reopen_status', 'worker_started_at', 'property_buildings.building_name', 'work_orders.examine_button_clicked_at')
      ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
      ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
      ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
      ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
      ->join('users', 'users.id', '=', 'work_orders.created_by')
      ->whereIn('work_orders.asset_category_id', explode(',', $loginuser_asset_categories))
      ->where(['worker_id' => $worker_id])
      ->whereRaw("(work_orders.status = 2 and work_orders.workorder_journey = 'job_execution' and work_orders.sp_reopen_status = 2)")->WhereNull('worker_started_at')
      ->get();
      if(isset($re_open_order_id) && !empty($re_open_order_id))
      {
        $re_open_order_id = json_decode(json_encode($re_open_order_id), true);
        $re_open_order_id = array_column($re_open_order_id,'id');
      }
      else
      {
        $re_open_order_id = array('0');
      }

      $on_hold_order_id = WorkOrders::with('itemRequests.requestedItems.contractUsableItem')->select('work_orders.id', 'work_order_id', 'work_orders.description', 'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag', 'contracts.contract_number', 'properties.latitude', 'properties.longitude', 'properties.location', 'work_orders.bm_approve_job', 'work_orders.sp_approve_job', 'work_orders.sp_reopen_status', 'worker_started_at', 'property_buildings.building_name', 'work_orders.examine_button_clicked_at')
      ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
      ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
      ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
      ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
      ->join('users', 'users.id', '=', 'work_orders.created_by')
      ->whereIn('work_orders.asset_category_id', explode(',', $loginuser_asset_categories))
      ->where(['worker_id' => $worker_id])
      ->whereRaw("(work_orders.status = 3 and (work_orders.workorder_journey = 'job_execution' or work_orders.workorder_journey = 'job_evaluation'))")
      ->get();
      if(isset($on_hold_order_id) && !empty($on_hold_order_id))
      {
        $on_hold_order_id = json_decode(json_encode($on_hold_order_id), true);
        $on_hold_order_id = array_column($on_hold_order_id,'id');
      }
      else
      {
        $on_hold_order_id = array('0');
      }

      $over_due_order_id = WorkOrders::with('itemRequests.requestedItems.contractUsableItem')->select('work_orders.id', 'work_order_id', 'work_orders.description', 'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag', 'contracts.contract_number', 'properties.latitude', 'properties.longitude', 'properties.location', 'work_orders.bm_approve_job', 'work_orders.sp_approve_job', 'work_orders.sp_reopen_status', 'worker_started_at', 'property_buildings.building_name', 'work_orders.examine_button_clicked_at')
      ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
      ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
      ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
      ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
      ->join('users', 'users.id', '=', 'work_orders.created_by')
      ->whereIn('work_orders.asset_category_id', explode(',', $loginuser_asset_categories))
      ->where(['worker_id' => $worker_id])
      ->whereRaw("(work_orders.target_date < '$tdate' and (work_orders.status = 2) and (work_orders.workorder_journey = 'job_execution') and (work_orders.bm_approve_job != 1 AND work_orders.sp_approve_job != 1) and (work_orders.sp_reopen_status != 2))")
      ->get();
      if(isset($over_due_order_id) && !empty($over_due_order_id))
      {
        $over_due_order_id = json_decode(json_encode($over_due_order_id), true);
        $over_due_order_id = array_column($over_due_order_id,'id');
      }
      else
      {
        $over_due_order_id = array('0');
      }

      $all_order_id = array_merge($rejected_order_id,$re_open_order_id,$on_hold_order_id,$over_due_order_id);
      if(count($all_order_id) < 1)
      {
        $all_order_id = array('0');
      }
      $pending_orders = $pending_orders->whereIn('work_orders.id',$all_order_id);
    }
    if($search)
    {
      $pending_orders = $pending_orders->where('work_orders.work_order_id', 'like', '%' . $search . '%');
    }
    $pending_orders = $pending_orders->orderBy('work_orders.id', 'asc');

    if($count > 0)
    {
      $pending_orders = $pending_orders->offset($count * 10)->limit(10);
    }
    else
    {
      $pending_orders = $pending_orders->limit(10);
    }
    $pending_orders = $pending_orders->get();

    if(count($pending_orders) > 0) { // For Arabic the Status is not comming ... No reason why.
      foreach($pending_orders as $key=>$order) {
        if(trim($status) != "")
        {
          $pending_orders[$key]['screen_status'] = $status;
        }
        else
        {
          if(in_array($pending_orders[$key]['id'], $rejected_order_id))
          {
            $pending_orders[$key]['screen_status'] = "Rejected";
          }
          elseif(in_array($pending_orders[$key]['id'], $re_open_order_id))
          {
            $pending_orders[$key]['screen_status'] = "Re Opened";
          }
          elseif(in_array($pending_orders[$key]['id'], $on_hold_order_id))
          {
            $pending_orders[$key]['screen_status'] = "On Hold";
          }
          elseif(in_array($pending_orders[$key]['id'], $over_due_order_id))
          {
            $pending_orders[$key]['screen_status'] = "Over Due";
          }
          else
          {
            $pending_orders[$key]['screen_status'] = "";
          }
        }
        if($order['status'] == 2 && $order['sp_reopen_status'] == 2 && (($order['worker_id'] != $order['old_worker_id']) && $order['old_worker_id'] != 0))
        {
            unset($pending_orders[$key]);
        }

      }
    }
    $pending_orders = json_decode(json_encode($pending_orders), true);
    $pending_orders = array_values($pending_orders);
    return $pending_orders;
  }



  public static function countPendingOrders($worker_id, $status, $search, $count)
  {
    $tdate = date('Y-m-d H:i:s');
    $loginuser_asset_categories = trim(Auth::user()->asset_categories) != "" ? Auth::user()->asset_categories : 0;
    $pending_orders = WorkOrders::select('work_orders.id', 'work_order_id', 'work_orders.sp_reopen_status', 'work_orders.reason', 'work_orders.worker_id', 'work_orders.old_worker_id', 'work_orders.description', 'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag', 'contracts.contract_number', 'properties.latitude', 'properties.longitude', 'properties.location', 'work_orders.bm_approve_job', 'work_orders.sp_approve_job', 'work_orders.sp_reopen_status', 'worker_started_at', 'property_buildings.building_name')
                        ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                        ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
                        ->join('users', 'users.id', '=', 'work_orders.created_by')
                        ->whereIn('work_orders.asset_category_id', explode(',', $loginuser_asset_categories))
                        ->where(['worker_id' => $worker_id]);
    $all_rejected_orders_count = clone $pending_orders;
    $all_re_open_orders_count = clone $pending_orders;
    $all_on_hold_orders_count = clone $pending_orders;
    $all_over_due_orders_count = clone $pending_orders;

    $all_rejected_orders_count = $all_rejected_orders_count->whereRaw("(work_orders.status = 2 and work_orders.sp_reopen_status != 2 and (work_orders.workorder_journey = 'job_execution' OR work_orders.workorder_journey = 'job_evaluation') and (work_orders.bm_approve_job = 1 OR work_orders.sp_approve_job = 1 OR (work_orders.bm_approve_job = 0 AND work_orders.sp_approve_job = 0 AND work_orders.workorder_journey = 'job_execution' AND work_orders.reason != '')))")->count();
    $all_re_open_orders = $all_re_open_orders_count->whereRaw("(work_orders.status = 2 and work_orders.workorder_journey = 'job_execution' and work_orders.sp_reopen_status = 2)")->WhereNull('worker_started_at')->get()->toArray();

    if(count($all_re_open_orders) > 0) {
      foreach($all_re_open_orders as $key=>$order) {
        if($order['status'] == 2 && $order['sp_reopen_status'] == 2 && (($order['worker_id'] != $order['old_worker_id']) && $order['old_worker_id'] != 0))
        {
            unset($all_re_open_orders[$key]);
        }
      }
    }
    $all_re_open_orders_count = count($all_re_open_orders);
    $all_on_hold_orders_count = $all_on_hold_orders_count->whereRaw("(work_orders.status = 3 and (work_orders.workorder_journey = 'job_execution' or work_orders.workorder_journey = 'job_evaluation'))")->count();
    $all_over_due_orders_count = $all_over_due_orders_count->whereRaw("(work_orders.target_date < '$tdate' and (work_orders.status = 2) and (work_orders.workorder_journey = 'job_execution') and (work_orders.bm_approve_job != 1 AND work_orders.sp_approve_job != 1) and (work_orders.sp_reopen_status != 2))")->count();

    $all_pending_orders_count = $all_rejected_orders_count + $all_re_open_orders_count + $all_on_hold_orders_count + $all_over_due_orders_count;

    $pending_orders_count = [
                              ['type' => 'all', 'value' => $all_pending_orders_count],
                              ['type' => 'overdue', 'value' => $all_over_due_orders_count],
                              ['type' => 'onhold', 'value' => $all_on_hold_orders_count],
                              ['type' => 'reopen', 'value' => $all_re_open_orders_count],
                              ['type' => 'rejected', 'value' => $all_rejected_orders_count],
                            ];

    return $pending_orders_count;
  }

  public static function overdue_orders($worker_id, $count)
  {
    //DB::enableQueryLog();
    $upcoming_orders = WorkOrders::select('work_orders.id', 'work_order_id', 'work_orders.description', 'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag', 'contracts.contract_number', 'properties.latitude', 'properties.longitude', 'properties.location')
                        ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                        ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
                        ->join('users', 'users.id', '=', 'work_orders.created_by')
                        ->where(['worker_id' => $worker_id])
                        ->where('target_date', '<', now())
                        ->whereRaw("(work_orders.status = 1 or work_orders.status = 3)")
                        ->whereRaw("(work_orders.workorder_journey = 'submitted')")
                        ->orderBy('target_date', 'asc');

    if($count > 0)
    {
      $upcoming_orders = $upcoming_orders->offset($count * 10)->limit(10);
    }
    else
    {
      $upcoming_orders = $upcoming_orders->limit(10);
    }
    $upcoming_orders = $upcoming_orders->get();
    //dd(DB::getQueryLog());
    $upcoming_orders = json_decode(json_encode($upcoming_orders), true);
    return $upcoming_orders;
  }

  public static function on_hold_orders($worker_id, $count)
  {
    //DB::enableQueryLog();
    $upcoming_orders = WorkOrders::select('work_orders.id', 'work_order_id', 'work_orders.description', 'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag', 'contracts.contract_number', 'properties.latitude', 'properties.longitude', 'properties.location')
                        ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                        ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
                        ->join('users', 'users.id', '=', 'work_orders.created_by')
                        ->where(['worker_id' => $worker_id])
                        ->where('target_date', '>=', now())
                        ->whereRaw("(work_orders.status = 3)")
                        ->whereRaw("(work_orders.workorder_journey = 'submitted')")
                        ->orderBy('target_date', 'asc');

    if($count > 0)
    {
      $upcoming_orders = $upcoming_orders->offset($count * 10)->limit(10);
    }
    else
    {
      $upcoming_orders = $upcoming_orders->limit(10);
    }
    $upcoming_orders = $upcoming_orders->get();
    //dd(DB::getQueryLog());
    $upcoming_orders = json_decode(json_encode($upcoming_orders), true);
    return $upcoming_orders;
  }

  public static function re_opened_orders($worker_id, $count)
  {
    //DB::enableQueryLog();
    $upcoming_orders = WorkOrders::select('work_orders.id', 'work_order_id', 'work_orders.description', 'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag', 'contracts.contract_number', 'properties.latitude', 'properties.longitude', 'properties.location')
                        ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                        ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
                        ->join('users', 'users.id', '=', 'work_orders.created_by')
                        ->where(['worker_id' => $worker_id])
                        ->where('target_date', '>=', now())
                        ->whereRaw("(work_orders.status = 6)")
                        ->whereRaw("(work_orders.workorder_journey = 'submitted')")
                        ->orderBy('target_date', 'asc');

    if($count > 0)
    {
      $upcoming_orders = $upcoming_orders->offset($count * 10)->limit(10);
    }
    else
    {
      $upcoming_orders = $upcoming_orders->limit(10);
    }
    $upcoming_orders = $upcoming_orders->get();
    //dd(DB::getQueryLog());
    $upcoming_orders = json_decode(json_encode($upcoming_orders), true);
    return $upcoming_orders;
  }

  public static function rejected_orders($worker_id, $count)
  {
    //DB::enableQueryLog();
    $rejected_orders = WorkOrders::select('work_orders.id', 'work_order_id', 'work_orders.description', 'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag', 'contracts.contract_number', 'properties.latitude', 'properties.longitude', 'properties.location')
                        ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                        ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
                        ->join('users', 'users.id', '=', 'work_orders.created_by')
                        ->where(['worker_id' => $worker_id])
                        ->whereRaw("(work_orders.status = 1)")
                        ->whereRaw("(work_orders.workorder_journey = 'job_execution' or work_orders.workorder_journey = 'job_evaluation')")
                        ->whereRaw("(work_orders.bm_approve_job = 1 OR work_orders.sp_approve_job = 1)")
                        ->orderBy('target_date', 'asc');

    if($count > 0)
    {
      $rejected_orders = $rejected_orders->offset($count * 10)->limit(10);
    }
    else
    {
      $rejected_orders = $rejected_orders->limit(10);
    }
    $rejected_orders = $rejected_orders->get();
    //dd(DB::getQueryLog());
    $rejected_orders = json_decode(json_encode($rejected_orders), true);
    return $rejected_orders;
  }

  public static function details($id)
  {
    $details = WorkOrders::with('itemRequests.requestedItems.contractUsableItem', 'serviceProviderItemRequests.requestedItems.contractUsableItem','worker:id,name','teamleader:id,name')
                    ->select('work_orders.id','work_orders.place','work_orders.project_user_id', 'work_orders.wo_images', 'work_order_id', 'work_orders.description', 'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag','assets.barcode_value', 'assets.asset_symbol', 'assets.asset_number', 'contracts.contract_number', 'properties.latitude', 'properties.longitude', 'properties.location', 'properties.property_type', 'properties.complex_name', 'asset_names.asset_name', 'work_orders.worker_id', 'work_orders.team_leader_id', 'work_orders.worker_started_at', 'work_orders.checklist_id', 'work_orders.reason', 'property_buildings.building_name', 'work_orders.asset_category_id', 'work_orders.work_order_type', 'work_orders.frequency_id', 'sp_reopen_status', 'projects_details.project_name as project', 'work_orders.job_started_at', 'work_orders.priority_id', 'work_orders.created_by', 'poa.project_id','wtf_start_time','work_orders.property_id','work_orders.asset_name_id','work_orders.asset_number_id', 'maintanance_request.image1', 'maintanance_request.image2', 'maintanance_request.image3', 'work_orders.maintanance_request_id', 'work_orders.preventive_start_time','maintanance_request.app_type','maintanance_request.generated_from','work_orders.schedule_start_time','work_orders.bm_approve_issue','work_orders.sla_service_window_priority','work_orders.examine_button_clicked_at','work_orders.is_handle_by_team_leader','work_orders.team_leader_id')
                    ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                    ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                    ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
                    ->join('users', 'users.id', '=', 'work_orders.created_by')
                    ->leftjoin('users as poa', 'poa.id', '=', 'contracts.user_id')
                    ->leftjoin('projects_details', 'projects_details.id', '=', 'poa.project_id')
                    ->leftjoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
                    ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                    ->leftjoin('maintanance_request', 'maintanance_request.id', '=', 'work_orders.maintanance_request_id')
                    ->where(['work_orders.id' => $id])
                    ->first();
    if(isset($details))
    {
      $details->place_for_maintenance = is_null($details->place) ? "" : trim($details->place);
      $details->apartment_villa = '';
      $details->tenant_phone_number = '';

      $details->show_teamleader_button = false; // Show action on behalf of worker
      $details->show_reassign_worker_button = false; // Show reassign worker
      $details->show_worker_action_button = false; // Show actions on work orders

      if($details->is_handle_by_team_leader == 0)
      {
          if(Auth::user()->user_type == 'team_leader')
          {
              $details->show_teamleader_button = true;
          }
          else
          {
            $details->show_worker_action_button = true;
          }
      }


      if($details->is_handle_by_team_leader == 1)
      {
          if((Auth::user()->user_type == 'team_leader') && ($details->team_leader_id == Auth::user()->id))
          {
                $details->show_reassign_worker_button = true;
                $details->show_worker_action_button = true;
          }   
      }
      
      if($details->job_submitted_at != NULL)
      {
          $details->show_teamleader_button = false; // Show action on behalf of worker
          $details->show_reassign_worker_button = false; // Show reassign worker
          $details->show_worker_action_button = false; // Show actions on work orders
      }

      

      $details->examine_started = $details->examine_button_clicked_at != NULL ? true : false;
      $details->is_workorder_from_tenant = $details->app_type == 'tenant' || $details->generated_from == 'tenant' ? true:false;
      if(isset($details->maintanance_request_id) && $details->maintanance_request_id != null)
      {
        $details_maintenance_request = MaintenanceRequest::with('user')->where('id', $details->maintanance_request_id)->first();
        if(isset($details_maintenance_request->user))
        {
            $details->apartment_villa = isset($details_maintenance_request->user->apartment) ? $details_maintenance_request->user->apartment : '';

            if($details->app_type == 'tenant' || $details->generated_from == 'tenant')
            {
              $details->tenant_phone_number = isset($details_maintenance_request->user->phone) ? $details_maintenance_request->user->phone : '';
            }
        }
      }

      if (isset($details->itemRequests->requestedItems)) {
          // Access the relationship data for itemRequests
          foreach ($details->itemRequests->requestedItems as $requestedItem) {
              // Get the ContractUsableItem associated with the requestedItem
              $contractUsableItem = $requestedItem->contractUsableItem;

              // Set the name of the requestedItem based on the ContractUsableItem's item
              $requestedItem->name = optional($contractUsableItem->getItem())->name;

              // Missing quantity should be according the status
              $requestedItem->quantity_accepted = $details->itemRequests->status == 'partially_given' ? $requestedItem->quantity - $requestedItem->quantity_accepted : $requestedItem->quantity_accepted;
          }
      } elseif (isset($details->serviceProviderItemRequests->requestedItems)) {
          foreach ($details->serviceProviderItemRequests->requestedItems as $requestedItem) {
              $contractUsableItem = $requestedItem->contractUsableItem;

              // Check if contractUsableItem and getItem() are valid
              $requestedItem->name = optional(optional($contractUsableItem)->getItem())->name ?: '';
          }
      }

      $asset_categories_data = DB::table('asset_categories')
                    ->where('id', $details->asset_category_id)
                    ->first();

      $wo_images = [];

      // Process images from wo_images field
      if (!empty($details->wo_images)) {

          foreach (explode(',', trim($details->wo_images)) as $image) {
            if((trim($image) != "") && (trim($image) != null))
              $extension = pathinfo($image)['extension'];
              if ($extension != 'pdf') {
                if(filled(ImagesUploadHelper::displayImageMobileApp($image, 'uploads/workorder')))
                {
                  $wo_images[] = ImagesUploadHelper::displayImageMobileApp($image, 'uploads/workorder');
                }

              } else {
                  $wo_images[] = url('uploads/workorder/' . $image);
              }
          }
      }
      // Process images from maintanance_request

      if (
          isset($details->maintanance_request_id) &&
          $details->maintanance_request_id != null &&
          (filled($details->image1) || filled($details->image2) || filled($details->image3))
      ) {

          if (filled($details->image1)) {
            if(filled(ImagesUploadHelper::displayImageMobileApp($details->image1, 'uploads/maintanance_request')))
            {
                $wo_images[] = ImagesUploadHelper::displayImageMobileApp($details->image1, 'uploads/maintanance_request');
            }
          }
          if (filled($details->image2)) {
              if(filled(ImagesUploadHelper::displayImageMobileApp($details->image2, 'uploads/maintanance_request')))
              {
                  $wo_images[] = ImagesUploadHelper::displayImageMobileApp($details->image2, 'uploads/maintanance_request');
              }
          }
          if (filled($details->image3)) {
            if(filled(ImagesUploadHelper::displayImageMobileApp($details->image3, 'uploads/maintanance_request')))
              {
                  $wo_images[] = ImagesUploadHelper::displayImageMobileApp($details->image3, 'uploads/maintanance_request');
              }
          }
      }



      $details->wo_images = $wo_images;

      if(isset($asset_categories_data))
      {
        $deleted = "";
        // if($asset_categories_data->deleted_at != "")
        // {
        //   $deleted = " [".__('api.deleted')."]";
        // }
        $details->asset_category_name = $asset_categories_data->asset_category.$deleted;
      }
      else
      {
        $details->asset_category_name = '';
      }

      $details->project_name = Helper::getProjectNameByWorkOrderID($details->id);
      $wtfs = DB::table('work_time_frame')
                  ->select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
                  ->where('user_id', $details->project_user_id)
                  ->first();
      if($details->wtf_start_time == ''){
        if(!isset($wtfs)) //If work order time frame has added
        {
          $time = "00:00:00";
        }
        else {
          $time = $wtfs->start_time;
        }
      }
      else{
        $time = $details->wtf_start_time;
      }

      $preventive_start_time = WorkorderHelper::getWorkorderstarttime($time,$details->preventive_start_time);
      //dd($time);
      if($details->work_order_type == "preventive") //If work_order_type is preventive
      {
        $details->reported_at = $details->start_date.' '.$preventive_start_time;
      }
      else
      {
        $details->reported_at = $details->created_at;
      }
      if ($details->asset_category_id === null) {
        $sla_asset_categories = array();
    }
    else
    {
      $sla_asset_categories = DB::table('contract_asset_categories')
                    ->where('asset_category_id', $details->asset_category_id)
                    ->where('contract_number', $details->contract_number)
                    ->orderBy('id', 'desc')
                    ->first();
    }


    $response_time = 0;
    $service_window = 0;
    $response_time_type = 'hours';
    $service_window_type = 'minutes';
    if($details->work_order_type == "reactive")
    {
      if(!empty($sla_asset_categories->priority_id) || !empty($details->sla_service_window_priority)){
        if($details->sla_service_window_priority != 0)
                    {
                        $priority_id = $details->sla_service_window_priority;
                    }
                    else
                    {
                        $priority_id = $sla_asset_categories->priority_id;
                    }
        $contract_priorities = DB::table('contract_priorities')
              ->where('priority_id', $priority_id)
              ->where('contract_number', $details->contract_number)
              ->orderBy('id', 'desc')
              ->first();
              if(empty($contract_priorities))
              {
                  $contract_priorities = DB::table('priorities')
                      ->where('id', $priority_id)
                      //->where('contract_number', $contract_number)
                      ->orderBy('id', 'desc')
                      ->first();
              }
              if(!empty($details->priority_id) && $details->priority_id != 0)
              {
                  $contract_priorities = DB::table('priorities')
                      ->where('id', $details->priority_id)
                      ->orderBy('id', 'desc')
                      ->first();
                  $priority_id = $details->priority_id;
              }
              if(isset($contract_priorities)) {
                $response_time = $contract_priorities->response_time;
                $service_window = $contract_priorities->service_window;
                $response_time_type = $contract_priorities->response_time_type;
                $service_window_type = $contract_priorities->service_window_type;
              }
      }
    }
    elseif($details->work_order_type == "preventive" && $details->priority_id != 0)
    {
      $priority_id = $details->priority_id;
                if($details->sla_service_window_priority != 0)
                {
                    $priority_id = $details->sla_service_window_priority;
                }
      $contract_priorities = DB::table('contract_priorities')
            ->where('priority_id', $priority_id)
            ->where('contract_number', $details->contract_number)
            ->orderBy('id', 'desc')
            ->first();
      if(isset($contract_priorities)) {
        $response_time = $contract_priorities->response_time;
        $service_window = $contract_priorities->service_window;
        $response_time_type = $contract_priorities->response_time_type;
        $service_window_type = $contract_priorities->service_window_type;
      } else {
        $contract_priorities = DB::table('priorities')
            ->where('id', $priority_id)
            ->orderBy('id', 'desc')
            ->first();

        if(!empty($details->priority_id) && $details->priority_id != 0)
                {
                    $contract_priorities = DB::table('priorities')
                        ->where('id', $details->priority_id)
                        ->orderBy('id', 'desc')
                        ->first();
                }
        if(isset($contract_priorities)) {
          $response_time = $contract_priorities->response_time;
          $service_window = $contract_priorities->service_window;
          $response_time_type = $contract_priorities->response_time_type;
          $service_window_type = $contract_priorities->service_window_type;
        }
      }
    }
    else
    {
      $contract_frequencies = DB::table('frequencies_master')
                ->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                ->where('frequencies_master.id', $details->frequency_id)
                ->where('contract_frequencies.contract_number', $details->contract_number)
                ->first();
      $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
      $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : '';
      $response_time_type = isset($contract_frequencies->response_time_type) ? $contract_frequencies->response_time_type:'hours';
      $service_window_type = isset($contract_priorities->service_window_type)?$contract_priorities->service_window_type:'minutes';
    }
    $created_at = $details->created_at;
    $tdate = date('Y-m-d H:i:s');
    $datetime1 = strtotime($created_at);
    $datetime2 = strtotime($tdate);
    $interval  = abs($datetime2 - $datetime1);
    $minutes   = round($interval / 60);
    if($response_time_type == "days")
    {
      $response_time = $response_time * 1440;
    }
    elseif($response_time_type == "hours")
    {
      $response_time = $response_time * 60;
    }
    $time_left = $response_time - $minutes;

    if($details->job_started_at == NULL || $details->bm_approve_issue == 2)
    {
      $target_date = $details->target_date;
      $details->target_date = $target_date;
    }
    else
    {

      if($details->work_order_type == "preventive" && $details->target_date != ""  && $details->target_date != "00:00:00")
                {
                    $target_date = $details->target_date;
                }
                else
                {
                    $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($details->job_started_at)));
                }

      // if($details->work_order_type == "preventive")
      // {
      //   $job_started_at = !empty($details->job_started_at) ? date("Y-m-d", strtotime($details->job_started_at)) : date("Y-m-d");
      //   $job_started_at = $job_started_at.' '.$preventive_start_time;
      //   $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($job_started_at)));
      //  //$target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($details->job_started_at)));
      // }
      // else
      // {
      //   $target_date = date('Y-m-d H:i:s',strtotime('+'.$service_window.' '.$service_window_type, strtotime($details->job_started_at)));
      // }
      $details->target_date = $target_date;
    }
    //dd($service_window);
    if($details->job_started_at != '')
    {
      if(strtotime($target_date) >= strtotime($tdate))
      {
        $details->pass_fail = 'pass';
      }
      else
      {
        $details->pass_fail = 'fail';
      }
    }
    if($details->response_time == 'On time' || $details->response_time == 'Late')
    {
      $details->response_time = $details->response_time;
    }
    else
    {
      if($time_left >= 0)
      {
        $details->response_time = $time_left;
      }
      else
      {
        $details->response_time = 'Late';
      }
    }

    if($details->property_type == 'complex'){
      $details->building  = $details->complex_name.' - '.$details->building_name;
    }
    else{
      $details->building  = $details->building_name;
    }
    // $details->building = 'B'.$details->building_name.'-'.$details->floor.'-'.$details->room;

    $details->zone = $details->floor;
    $details->unit = $details->room;

    $details->asset_tag = $details->asset_symbol.$details->asset_number;
    $active_task = WorkOrders::active_task(Auth::user()->id);
    $details->has_active_task = false;
    if(!empty((array)$active_task) && count($active_task) >= 3)
    {
      foreach($active_task as $atkey => $at)
      {
        if($at['id'] != $details->id)
        {
          $details->has_active_task = true;
        }
      }
    }
    $action = WorkOrders::check_no_checklist_actions_completed($id);
    if($details->checklist_id == 0)
    {
      $details->has_checklist = false;
      $details->total_checklists = 1;
      if($action == 0)
      {
        $details->checklist_actions_left = 1;
      }
      else
      {
        $details->checklist_actions_left = 0;
      }
    }
    else
    {
      $checklist_tasks_count = count(WorkOrders::checklist_tasks($details->checklist_id, $id));
      $details->has_checklist = true;
      // if($checklist_tasks_count == 0)
      // {
      //   $details->has_checklist = false;
      // }
      $details->total_checklists = $checklist_tasks_count;
      $checklist_actions_left = $checklist_tasks_count - $action;
      $details->checklist_actions_left = $checklist_actions_left;
      if($checklist_actions_left <= 0)
      {
        $details->checklist_actions_left = 0;
      }
      $details->checklist_tasks = WorkOrders::checklist_tasks($details->checklist_id, $id);
    }
    //dd(now());
    // echo $details->target_date;
    // echo now();exit;
    $details->screen_status = '';
    if($details->status == 2 && ($details->workorder_journey == 'job_execution' || $details->workorder_journey == 'job_evaluation') && ($details->bm_approve_job == 1 || $details->sp_approve_job == 1))
    {
      $details->screen_status = 'Rejected';
    }
    elseif($details->status == 2 && $details->workorder_journey == 'job_execution' && $details->sp_reopen_status == 2 && $details->worker_started_at == NULL)
    {
      $details->screen_status = 'Re Opened';
    }
    elseif($details->status == 3 && ($details->workorder_journey == 'job_execution' || $details->workorder_journey == 'job_evaluation'))
    {
      $details->screen_status = 'On Hold';
    }
    elseif($details->target_date < now() && ($details->status == 2) && ($details->workorder_journey == 'job_execution'))
    {
      $details->screen_status = 'Over Due';
    }
    $details->over_due = false;
    if($details->target_date < now())
    {
      $details->over_due = true;
    }
    //$details->target_date = date('d/m/Y h:i A', strtotime($details->target_date));
    $details->current_time = date('Y-m-d H:i:s');
    }


    return $details;
  }

  public static function update_start_job($work_order_id,$isOfflineRequest=false,$offlinePayload=[])
  {
    $workOrder = WorkOrders::where('id', $work_order_id);
    $updatedata = $workOrder->update([
        'status' => 2,
        'workorder_journey' => 'job_execution',
        'worker_started_at' => ($isOfflineRequest) ? $offlinePayload['worker_started_at'] : date('Y-m-d H:i:s'),
        'sp_approve_job' => 0,
        'bm_approve_job' => 0
    ]);
    $workOrder = $workOrder->first();
    if($updatedata)
    {
        $SaveWorkerTimeTracking = WorkerWorkOrderTimeTracking::insert([
          'work_order_id' => $work_order_id,
          'worker_id' => Auth::user()->id,
          'start_datetime' => ($isOfflineRequest) ? Carbon::parse($offlinePayload['worker_started_at']): now(),
          'created_at' => ($isOfflineRequest) ? Carbon::parse($offlinePayload['worker_started_at']): now()
        ]);

      if($workOrder->is_collaborative == 1) {
        WorkOrderWorkerTiming::insert([
            'work_order_id' => $work_order_id,
            'worker_id' => Auth::user()->id,
            'start_time' => ($isOfflineRequest) ? Carbon::parse($offlinePayload['worker_started_at']): now(),
            'created_at' => ($isOfflineRequest) ? Carbon::parse($offlinePayload['worker_started_at']): now()
          ]);
      }

      $worker_name = Auth::user()->name;
      $workerId = Auth::user()->id;
      if(Auth::user()->user_type == 'team_leader')
      {
          $message = 'Team Leader <strong>'.$worker_name.'</strong> marked work order as started on behalf of worker';
          $message_ar = 'قام مشرف الفريق <strong>'.$worker_name.'</strong> ببدء أمر العمل نيابةً عن العامل';
      }
      else
      {
          $message = 'Worker <strong>'.$worker_name.'</strong> started working on the work order';
          $message_ar = 'العامل <strong>'.$worker_name.'</strong> بدأ تنفيذ أمر العمل';
      }

      //Notification
      DB::table('notifications')->insert(array('user_id' => $workerId, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $workOrder->property_id, 'notification_sub_type'=> 'wo_started_wo' , 'section_id' => $workOrder->id, 'created_at' => date('Y-m-d H:i:s'), 'is_timeline'=>'no'));
      //Timeline
      DB::table('notifications')->insert(array('user_id' => $workerId, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $workOrder->property_id, 'notification_sub_type'=> 'wo_started_wo' , 'section_id' => $workOrder->id, 'created_at' => date('Y-m-d H:i:s'), 'is_timeline'=>'yes'));
    }

    return true;
  }

  public static function update_job_on_hold($work_order_id, $worker_comment)
  {
    $check_status = WorkOrders::where(['id' => $work_order_id, 'status' => 3])->count();
    $wo = WorkOrders::where(['id' => $work_order_id])->first();
    if($check_status > 0)
    {
      WorkOrders::where('id', $work_order_id)->update(['status' => 2, 'worker_comment' => $worker_comment]);

      $SaveWorkerTimeTracking = WorkerWorkOrderTimeTracking::insert([
        'work_order_id' => $work_order_id,
        'worker_id' => Auth::user()->id,
        'start_datetime' => now(),
        'created_at' => now()
      ]);

      if(Auth::user()->user_type == 'team_leader')
      {
          $message = 'Team Leader <strong>'.Auth::user()->name.'</strong> has resumed the work order due to <strong>'.$worker_comment.'</strong>';
          $message_ar = 'استأنف مشرف الفريق <strong>'.Auth::user()->name.'</strong> أمر العمل بسبب: <strong>'.$worker_comment.'</strong>';
      }
      else
      {
        $message = 'Worker <strong>'.Auth::user()->name.'</strong> has resumes the work order due to <strong>'.$worker_comment.'</strong>';
        $message_ar = 'العامل <strong>'.Auth::user()->name.'</strong> قام باستئناف أمر العمل نظراً إلى <strong>'.$worker_comment.'</strong>';
      }
      
      //Notifiaction
      DB::table('notifications')->insert(array('user_id' => Auth::user()->id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id, 'notification_sub_type'=> 'wo_restarted_wo' , 'section_id' => $wo->id, 'created_at' => date('Y-m-d H:i:s'), 'is_timeline'=>'no'));
      //Timeline
      DB::table('notifications')->insert(array('user_id' => Auth::user()->id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id, 'notification_sub_type'=> 'wo_restarted_wo' , 'section_id' => $wo->id, 'created_at' => date('Y-m-d H:i:s'), 'is_timeline'=>'yes'));



    }
    else
    {
      WorkorderHelper::updateAndFetchWorkorderTracking($work_order_id,job_type: 'onhold');

      WorkOrders::where('id', $work_order_id)->update(['status' => 3, 'worker_comment' => $worker_comment]);

      if(Auth::user()->user_type == 'team_leader')
      {
          $message = 'Team Leader <strong>'.Auth::user()->name.'</strong> has paused the work order due to <strong>'.$worker_comment.'</strong>';
          $message_ar = 'قام مشرف الفريق <strong>'.Auth::user()->name.'</strong> بإيقاف أمر العمل مؤقتًا بسبب: <strong>'.$worker_comment.'</strong>';
      }
      else
      {
          $message = 'Worker <strong>'.Auth::user()->name.'</strong> has pauses the work order due to <strong>'.$worker_comment.'</strong>';
          $message_ar = 'العامل <strong>'.Auth::user()->name.'</strong> قام بإيقاف أمر العمل مؤقتاً نظراً إلى <strong>'.$worker_comment.'</strong>';
      }
      

      DB::table('notifications')->insert(array('user_id' => Auth::user()->id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id, 'notification_sub_type'=> 'wo_paused_wo' , 'section_id' => $wo->id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));
           //Timeline
      DB::table('notifications')->insert(array('user_id' => Auth::user()->id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id, 'notification_sub_type'=> 'wo_paused_wo' , 'section_id' => $wo->id, 'created_at' => date('Y-m-d H:i:s'), 'is_timeline'=>'yes'));
    }
    return true;
  }

  public static function check_no_checklist_actions_completed($work_order_id)
  {
    return NoChecklistAction::select('*')
                        ->where('work_order_id', $work_order_id)
                        ->where('worker_id', Auth::user()->id)
                        ->where('is_reopen_wo',0)
                        ->count();
  }

  public static function checklist_tasks($checklist_id, $work_order_id)
  {
    $work_order_checklists = WorkOrders::select('checklist_tasks.*')->selectRaw("(select count(id) from no_checklist_actions where work_order_id = '$work_order_id' and checklist_task_id = 'checklist_tasks.id' and is_reopen_wo = 0) as checklist_count")->join('checklists', 'checklists.id', '=', 'work_orders.checklist_id')->join('checklist_tasks', 'checklist_tasks.checklist_id', '=', 'checklists.list_id')->where('work_orders.id', $work_order_id)->where('checklist_tasks.is_deleted', "no")->get();
    $work_order_checklists = json_decode(json_encode($work_order_checklists), true);
    return $work_order_checklists;
  }

  public static function complete_job($work_order_id)
  {
    $wo = DB::table('work_orders')
    ->select('work_orders.*', 'contract_priorities.priority_id', 'contracts.contract_number', 'project_settings.use_smart_assigning', 'project_settings.skip_evaluation_stage', 'contracts.use_smart_assigning as contract_smart_assign','property_buildings.building_name')
    ->leftjoin('contract_asset_categories', 'contract_asset_categories.asset_category_id', '=', 'work_orders.asset_category_id')
    ->leftjoin('contract_priorities', 'contract_priorities.priority_id', '=', 'contract_asset_categories.priority_id')
    ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
    ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
    ->join('users', 'users.id', '=', 'work_orders.project_user_id')
    ->leftjoin('project_settings', 'project_settings.project_id', '=', 'users.project_id')
    ->where('work_orders.id', $work_order_id)
    ->first();

    if(Auth::user()->user_type == 'team_leader')
    {
          $message = 'Team Leader <strong>'.Auth::user()->name.'</strong> completed the work order on behalf of worker, pending for Service Provider approval';
          $message_ar = 'أتم مشرف الفريق <strong>'.Auth::user()->name.'</strong> أمر العمل نيابةً عن العامل، بانتظار موافقة مقدم الخدمة';
    }
    else
    {
          $message = 'Worker <strong>'.Auth::user()->name.'</strong> completed the work order, pending for Service Provider approval';
          $message_ar = 'العامل <strong>'.Auth::user()->name.'</strong> نفذ أمر العمل وغيّر الحالة الى مكتمل';
    }
    

    // @flip1@ chenges in notification content -> add additional param
    DB::table('notifications')->insert(array('user_id' => Auth::user()->id,'additional_param'=>Auth::user()->name, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id, 'notification_sub_type'=> 'wo_completed_wo' , 'section_id' => $wo->id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));
    DB::table('notifications')->insert(array('user_id' => Auth::user()->id,'additional_param'=>Auth::user()->name, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id, 'notification_sub_type'=> 'wo_completed_wo' , 'section_id' => $wo->id, 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));

    if($wo->is_collaborative == 1) {
      $count = WorkOrderWorkerTiming::where('work_order_id', $work_order_id)->where('end_time', '!=', NULL)->count();
      $totalCount = WorkOrderWorkers::where('work_order_id', $work_order_id)->count();
      WorkOrderWorkerTiming::where('work_order_id', $work_order_id)
          ->where('worker_id', Auth::user()->id)
          ->update(['end_time' => now()]);
      if($count < $totalCount - 1){
        return true;
      }
    }

    if(isset($wo->sp_approove) && $wo->sp_approove == 1)
    {
      $contractUseSmartAssigning = $wo->contract_smart_assign;
      $projectUseSmartAssigning = $wo->use_smart_assigning;
      $skipEvaluationStage = $wo->skip_evaluation_stage;

      if($contractUseSmartAssigning && $projectUseSmartAssigning && $skipEvaluationStage) 
      {
          $data['bm_approve_job'] = 0;
          $data['sp_approve_job'] = 2;
          $data['workorder_journey'] = 'job_approval';
          $data['reason'] = '';
          $data['job_submitted_at'] = date('Y-m-d H:i:s');
      }
      else 
      {
          $workorder_journey = 'job_evaluation';
          $work_order_id = $work_order_id;
          $data['status'] = 2;
          $data['workorder_journey'] = $workorder_journey;
          $data['job_completed_by'] = "worker";
          $data['sp_approve_job'] = "0";
          $data['bm_approve_job'] = "0";
          $data['job_submitted_at'] = date('Y-m-d H:i:s');
      }
    }

    // If Building manager does not want to approve :: bm_approve = 0 and sp admin does not wants to approve sp_approove =0

  elseif($wo->bm_approove == 0 && $wo->sp_approove == 0 )
  {
    $workorder_journey = 'finished';
    $work_order_id = $work_order_id;
    $data['status'] = 4;
    $data['workorder_journey'] = $workorder_journey;
    $data['job_completed_by'] = "SP";
    $data['rating'] = '';
    $data['score'] = '';
    $data['building_manager_comment']='';
    $data['job_completion_date'] = date('Y-m-d H:i:s');
    $data['sp_approve_job'] = "0";
    $data['bm_approve_job'] = "0";
    $data['job_submitted_at'] = date('Y-m-d H:i:s');
  }
// If Building manager  want to approve :: bm_approve = 1 and sp admin does not wants to approve sp_approove = 0
elseif($wo->bm_approove == 1 && $wo->sp_approove == 0 )
{
  $work_order_id = $work_order_id;
  $data['bm_approve_job'] = 0;
  $data['sp_approve_job'] = 2;
  $data['workorder_journey'] = 'job_approval';
  $data['reason'] = '';
  $data['job_submitted_at'] = date('Y-m-d H:i:s');
}

      $fetchTrackingDetails = WorkorderHelper::updateAndFetchWorkorderTracking($work_order_id,'complete');

      $total_time_spent = $fetchTrackingDetails['time_spent'];
      $total_minute_spent = $fetchTrackingDetails['time_spent_minutes'];
      $data['time_spent_by_worker'] = $total_time_spent;
      $data['job_submitted_at'] = date('Y-m-d H:i:s');

      if($wo->work_order_type == "preventive")
      {
          $frequency = DB::table('frequencies_master')->select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')->where('frequencies_master.id', $wo->frequency_id)->first();
          if($frequency->service_window_type == 'days')
          {
            $frequency_service_window = (int) $frequency->service_window * 1440;
          }
          else if($frequency->service_window_type == 'hours')
          {
              $frequency_service_window = (int) $frequency->service_window * 60;
          }
          else {
              $frequency_service_window = (int) $frequency->service_window;
          }

          if($total_minute_spent <= $frequency_service_window)
          {
              $data['pass_fail'] = "pass";
          }
          else
          {
              $data['pass_fail'] = "fail";
          }
      }
      else
      {
        $sla_asset_categories = ContractAssetCategories::where('asset_category_id', $wo->asset_category_id)
                ->where('contract_number', $wo->contract_number)
                ->orderBy('id', 'desc')
                ->first();
        if(!empty($sla_asset_categories->priority_id) || !empty($wo->sla_service_window_priority))
        {
            $wo_priority_id = $wo->sla_service_window_priority != 0 ? $wo->sla_service_window_priority : $sla_asset_categories->priority_id;

            $priorities = ContractPriority::where('priority_id', $wo_priority_id)
              ->where('contract_number', $wo->contract_number)
              ->orderBy('id', 'desc')
              ->first();

            if ($priorities)
            { // Check if $priorities is not null
                    if ($priorities->service_window_type == 'days') {
                        $priorities_service_window = (int) $priorities->service_window * 1440;
                    } elseif ($priorities->service_window_type == 'hours') {
                        $priorities_service_window = (int) $priorities->service_window * 60;
                    } else {
                        $priorities_service_window = (int) $priorities->service_window;
                    }

                    if ($total_minute_spent <= $priorities_service_window) {
                        $data['pass_fail'] = "pass";
                    } else {
                        $data['pass_fail'] = "fail";
                    }
            } else {
                // Handle the case when no priority is found
                $data['pass_fail'] = "fail"; // Default to "fail" if no priority is found
            }

        }
        else
        {
              // Handle the case when no priority is found
            $data['pass_fail'] = "fail"; // Default to "fail" if no priority is found
        }
      }

    $bm_name = '';
    $sp_name = '';
    $bmdetails = User::select('name')->where('id',$wo->created_by)->first();
    if(isset($bmdetails))
    {
        $bm_name = $bmdetails->name;
    }

    $spdetails = User::select('name')->where('id',$wo->service_provider_id)->first();
    if(isset($spdetails))
    {
        $sp_name = $spdetails->name;
    }
    
    $user_id = Auth::user()->id;
    if($wo->bm_approove == 0 && $wo->sp_approove == 0)
    {
      $message = 'Service Provider <strong>'.$sp_name.'</strong> has <strong><i>automatically</i></strong> approved the work order, pending for Building Manager approval';
      $message_ar = 'مقدم الخدمة <strong>'.$sp_name.'</strong> اعتمد أمر العمل <strong><i>تلقائيا،</i></strong> بانتظار اعتماد مدير المبنى';

        DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order','building_ids' => $wo->property_id, 'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));
        DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order','building_ids' => $wo->property_id, 'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));


        $message = 'Building Manager <strong>'.$bm_name.'</strong> has <strong><i>automatically</i></strong> approved the work order';
        $message_ar = 'مدير المبنى <strong>'.$bm_name.'</strong> اعتمد أمر العمل <strong><i>تلقائيا</i></strong>';
        $notification_sub_type = 'bm_has_automatically_approved_wo';
        //dd($message_ar);
        DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id,'notification_sub_type'=> $notification_sub_type ,'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));
        //Timeline
        DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id,'notification_sub_type'=> $notification_sub_type ,'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));
    }elseif($wo->bm_approove == 1 && $wo->sp_approove == 0 ){
      $message = 'Service Provider <strong>'.$sp_name.'</strong> has <strong><i>automatically</i></strong> approved the work order, pending for Building Manager approval';
      $message_ar = 'مقدم الخدمة <strong>'.$sp_name.'</strong> اعتمد أمر العمل <strong><i>تلقائيا،</i></strong> بانتظار اعتماد مدير المبنى';

        $notification_sub_type = 'sp_has_automatically_approved_wo';
        DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id,'notification_sub_type'=> $notification_sub_type ,'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));
        //Timeline
        DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id,'notification_sub_type'=> $notification_sub_type ,'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));
    }
    else {
      $contractUseSmartAssigning = $wo->contract_smart_assign;
      $projectUseSmartAssigning = $wo->use_smart_assigning;
      $skipEvaluationStage = $wo->skip_evaluation_stage;

      if($contractUseSmartAssigning && $projectUseSmartAssigning && $skipEvaluationStage) {
        $message = 'Service Provider <strong>'.$sp_name.'</strong> has <strong><i>automatically</i></strong> approved the work order, pending for Building Manager approval';
        $message_ar = 'مقدم الخدمة <strong>'.$sp_name.'</strong> اعتمد أمر العمل <strong><i>تلقائيا،</i></strong> بانتظار اعتماد مدير المبنى';

        $notification_sub_type = 'sp_has_automatically_approved_wo';
        DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id,'notification_sub_type'=> $notification_sub_type ,'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'no'));
        //Timeline
        DB::table('notifications')->insert(array('user_id' => $user_id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $wo->property_id,'notification_sub_type'=> $notification_sub_type ,'section_id' => $wo->id, 'is_automatic' => 'yes', 'created_at' => date('Y-m-d H:i:s'),'is_timeline'=>'yes'));
      }
    }

    $data['old_worker_id'] = $user_id;
    DB::table('work_orders')
                ->where('id', $work_order_id)
                ->update($data);

      $data = array(
        'wo_id'=> $work_order_id,
        'work_order_type'=>$wo->work_order_type,
        'status'=>'WO-Status-Updated-Third',
        'created_at'=>date('Y-m-d H:i:s'),
    );
    DB::table('work_orders_email_jobs')->insert($data);
    return true;
  }

  public static function get_privileges($worker_id)
  {
    $user_privileges = User::where('id', $worker_id)->pluck('user_privileges')->first();
    return $user_privileges;
  }

  public static function notifications($worker_id, $count, $selected_language)
  {
    $user_id = auth()->user()->id;
    $notifications = DB::table('notifications')->select('*')
            ->where('section_type','=','worker_app')
            ->whereRaw("find_in_set($worker_id, user_id)")
            ->where(function ($query) use ($timeline) {
              $query->where('notifications.is_timeline', 'no')->orWhere('notifications.is_timeline',null);
              })
            ->orderby('created_at','desc');
            //->where('user_id','=',$worker_id);
    if($count > 0)
    {
      $notifications = $notifications->offset($count * 10)->limit(10);
    }
    else
    {
      $notifications = $notifications->limit(10);
    }
    $notifications = $notifications->get();
    if(count($notifications) >0) {
      foreach($notifications as $key=>$row) {
        $wo_data = WorkOrders::select('*')->where('id',$row->section_id)->first();
        if(isset($wo_data))
        {
          $notifications[$key]->work_order_id = trim($wo_data->work_order_id);
          if($wo_data->status == 2 && ($wo_data->workorder_journey == 'job_execution' || $wo_data->workorder_journey == 'job_evaluation') && ($wo_data->bm_approve_job == 1 || $wo_data->sp_approve_job == 1))
          {
            $notifications[$key]->workorder_status = 'Rejected';
          }
          elseif($wo_data->status == 2 && $wo_data->workorder_journey == 'job_execution' && $wo_data->sp_reopen_status == 2 && $wo_data->worker_started_at == NULL)
          {
            $notifications[$key]->workorder_status = 'Re Opened';
          }
          elseif($wo_data->status == 3 && ($wo_data->workorder_journey == 'job_execution' || $wo_data->workorder_journey == 'job_evaluation'))
          {
            $notifications[$key]->workorder_status = 'On Hold';
          }
          elseif($wo_data->target_date < now() && ($wo_data->status == 2) && ($wo_data->workorder_journey == 'job_execution'))
          {
            $notifications[$key]->workorder_status = 'Over Due';
          }
          else
          {
            $notifications[$key]->workorder_status = 'Completed';
          }
        }
        else
        {
          $notifications[$key]->work_order_id = '';
          $notifications[$key]->workorder_status = '';
        }
        if(in_array($notifications[$key]->notification_type,array("maintenance_request_accepted","maintenance_request_rejected")))
        {
          $notifications[$key]->workorder_status = $notifications[$key]->notification_type == "maintenance_request_accepted" ? 'approved' : 'rejected';
        }

        if($selected_language == 'ar')
        {
          if(trim($notifications[$key]->message) == 'A work order rejected')
          {
              $notifications[$key]->message = 'تم رفض أمر العمل';
          }
          elseif(trim($notifications[$key]->message) == 'A new work order assigned')
          {
              $notifications[$key]->message = 'تم تعيين أمر عمل جديد';
          }
        }
        elseif($selected_language == 'ur')
        {
            if(trim($notifications[$key]->message) == 'A work order rejected')
            {
                $notifications[$key]->message = 'ورک آرڈر مسترد کر دیا گیا';
            }
            elseif(trim($notifications[$key]->message) == 'A new work order assigned')
            {
                $notifications[$key]->message = 'ایک نیا ورک آرڈر تفویض کیا گیا';
            }
        }

        if(in_array($notifications[$key]->notification_type,array("maintenance_request_accepted","maintenance_request_rejected")))
        {
          $notifications[$key]->message = $notifications[$key]->notification_type == "maintenance_request_accepted" ? 'A work order accepted' : 'A work order rejected';
        }
        $notifications[$key]->project_name = Helper::getProjectNameByWorkOrderID($row->section_id);
        $notifications[$key]->posted_date = date('d-m-Y h:i A',strtotime($row->created_at));
      }
    }
    return $notifications;
  }



  public static function notifications_api($worker_id, $count, $selected_language)
  {
    $user_id = auth()->user()->id;
    $notifications = DB::table('notifications')->select('*')
            ->where('section_type','=','worker_app')
            ->whereRaw("find_in_set($worker_id, user_id)")
            ->orderby('created_at','desc');
            //->where('user_id','=',$worker_id);
    if($count > 0)
    {
      $notifications = $notifications->offset($count * 10)->limit(10);
    }
    else
    {
      $notifications = $notifications->limit(10);
    }
    $notifications = $notifications->get();
    if(count($notifications) >0) {
      foreach($notifications as $key=>$row) {
        $wo_data = WorkOrders::select('*')->where('id',$row->section_id)->first();
        if(isset($wo_data))
        {
          $notifications[$key]->work_order_id = trim($wo_data->work_order_id);
          if($wo_data->status == 2 && ($wo_data->workorder_journey == 'job_execution' || $wo_data->workorder_journey == 'job_evaluation') && ($wo_data->bm_approve_job == 1 || $wo_data->sp_approve_job == 1))
          {
            $notifications[$key]->workorder_status = 'Rejected';
          }
          elseif($wo_data->status == 2 && $wo_data->workorder_journey == 'job_execution' && $wo_data->sp_reopen_status == 2 && $wo_data->worker_started_at == NULL)
          {
            $notifications[$key]->workorder_status = 'Re Opened';
          }
          elseif($wo_data->status == 3 && ($wo_data->workorder_journey == 'job_execution' || $wo_data->workorder_journey == 'job_evaluation'))
          {
            $notifications[$key]->workorder_status = 'On Hold';
          }
          elseif($wo_data->target_date < now() && ($wo_data->status == 2) && ($wo_data->workorder_journey == 'job_execution'))
          {
            $notifications[$key]->workorder_status = strtotime($wo_data->worker_started_at) > 0 ? 'Inprogress' : 'Over Due';
          }
          elseif((strtotime($wo_data->job_submitted_at) > 0) || ($wo_data->job_completed_by == 'worker' || $wo_data->job_completed_by == 'SP'))
          {
            $notifications[$key]->workorder_status = 'Completed';
          }
          elseif($wo_data->status == 2 && $wo_data->workorder_journey == 'job_execution' && $wo_data->sp_approve_job == 0 && $wo_data->bm_approve_job == 0 && (strtotime($wo_data->worker_started_at) > 0))
          {
            $notifications[$key]->workorder_status = 'Inprogress';
          }
          else
          {
            $notifications[$key]->workorder_status = '';
          }
        }
        else
        {
          $notifications[$key]->work_order_id = trim($notifications[$key]->section_id) != "" ? '#'.trim($notifications[$key]->section_id) : '';
          $notifications[$key]->workorder_status = '';
        }
        if(in_array($notifications[$key]->notification_type,array("maintenance_request_accepted","maintenance_request_rejected")))
        {
          $notifications[$key]->workorder_status = $notifications[$key]->notification_type == "maintenance_request_accepted" ? 'approved' : 'rejected';
        }

        if($selected_language == 'ar')
        {
          if(trim($notifications[$key]->message) == 'A work order rejected' || trim($notifications[$key]->message) == 'A work order has been rejected' || trim($notifications[$key]->message) == 'ورک آرڈر مسترد کر دیا گیا ہے' || trim($notifications[$key]->message) == 'تم رفض أمر العمل')
          {
              $notifications[$key]->message = 'تم رفض أمر العمل';
          }
          elseif(trim($notifications[$key]->message) == 'A new work order assigned' || trim($notifications[$key]->message) == 'A new work order has been assigned' || trim($notifications[$key]->message) == 'أمر عمل جديد تم تعيينه' || trim($notifications[$key]->message) == 'نیا ورک آرڈر دیا گیا ہے')
          {
              $notifications[$key]->message = 'أمر عمل جديد تم تعيينه';
          }
          elseif(trim($notifications[$key]->message) == 'A work order has been reopened' || trim($notifications[$key]->message) == 'تمت إعادة فتح أمر العمل' || trim($notifications[$key]->message) == 'ورک آرڈر دوبارہ کھول دیا گیا ہے')
          {
              $notifications[$key]->message = 'تمت إعادة فتح أمر العمل';
          }
        }
        elseif($selected_language == 'ur')
        {
            if(trim($notifications[$key]->message) == 'A work order rejected' || trim($notifications[$key]->message) == 'A work order has been rejected' || trim($notifications[$key]->message) == 'ورک آرڈر مسترد کر دیا گیا ہے' || trim($notifications[$key]->message) == 'تم رفض أمر العمل')
            {
                $notifications[$key]->message = 'ورک آرڈر مسترد کر دیا گیا ہے';
            }
            elseif(trim($notifications[$key]->message) == 'A new work order assigned' || trim($notifications[$key]->message) == 'A new work order has been assigned' || trim($notifications[$key]->message) == 'أمر عمل جديد تم تعيينه' || trim($notifications[$key]->message) == 'نیا ورک آرڈر دیا گیا ہے')
            {
                $notifications[$key]->message = 'نیا ورک آرڈر دیا گیا ہے';
            }
            elseif(trim($notifications[$key]->message) == 'A work order has been reopened' || trim($notifications[$key]->message) == 'تمت إعادة فتح أمر العمل' || trim($notifications[$key]->message) == 'ورک آرڈر دوبارہ کھول دیا گیا ہے')
            {
                $notifications[$key]->message = 'ورک آرڈر دوبارہ کھول دیا گیا ہے';
            }
        }
        else
        {
            if(trim($notifications[$key]->message) == 'A work order rejected' || trim($notifications[$key]->message) == 'A work order has been rejected' || trim($notifications[$key]->message) == 'ورک آرڈر مسترد کر دیا گیا ہے' || trim($notifications[$key]->message) == 'تم رفض أمر العمل')
            {
                $notifications[$key]->message = 'A work order has been rejected';
            }
            elseif(trim($notifications[$key]->message) == 'A new work order assigned' || trim($notifications[$key]->message) == 'A new work order has been assigned' || trim($notifications[$key]->message) == 'أمر عمل جديد تم تعيينه' || trim($notifications[$key]->message) == 'نیا ورک آرڈر دیا گیا ہے')
            {
                $notifications[$key]->message = 'A new work order has been assigned';
            }
            elseif(trim($notifications[$key]->message) == 'A work order has been reopened' || trim($notifications[$key]->message) == 'تمت إعادة فتح أمر العمل' || trim($notifications[$key]->message) == 'ورک آرڈر دوبارہ کھول دیا گیا ہے')
            {
                  $notifications[$key]->message = 'A work order has been reopened';
            }
        }

        if(in_array($notifications[$key]->notification_type,array("maintenance_request_accepted","maintenance_request_rejected")))
        {
          if($selected_language == 'ar')
          {
            $notifications[$key]->message = $notifications[$key]->notification_type == "maintenance_request_accepted" ? 'بلاغ الصيانة المرسل تم قبوله' : 'لاغ الصيانة المرسل تم رفضه';
          }
          elseif($selected_language == 'ur')
          {
            $notifications[$key]->message = $notifications[$key]->notification_type == "maintenance_request_accepted" ? 'مینٹیننس کی درخواست منظور کر دی گئی ہے' : 'مینٹیننس کی درخواست مسترد کر دی گئی ہے';
          }
          else
          {
            $notifications[$key]->message = $notifications[$key]->notification_type == "maintenance_request_accepted" ? 'The maintenance request has been Approved' : 'The maintenance request has been Rejected';
          }
        }
        if(trim($row->section_id) != "" && trim($row->section_id) != "null" || trim($row->section_id) != NULL)
        {

          $notifications[$key]->project_name = Helper::getProjectNameByWorkOrderID($row->section_id);

          if(isset($notifications[$key]->project_name) && $notifications[$key]->project_name != "")
          {

          }
          else
          {
                $notifications[$key]->project_name = Helper::getProjectNameByMaintenanceRequestID($row->section_id);
          }
        }
        else
        {
          $notifications[$key]->project_name = Helper::getProjectNameByProjectID($row->maintenance_section_id);
        }
        $notifications[$key]->posted_date = date('d-m-Y h:i A',strtotime($row->created_at));
      }
    }
    return $notifications;
  }

  public function assetCategoryName()
  {
    return $this->belongsTo('\App\Models\Asset','asset_number_id');
  }

  public static function getChecklistListDataSet($work_order_id)
  {
    $work_orders = DB::table('no_checklist_actions')
    ->select('no_checklist_actions.*','checklist_tasks.task_id','checklist_tasks.task_number','checklist_tasks.task_title','checklist_tasks.description','checklist_tasks.comment as result','checklist_tasks.multiple_options as ori_res')
    ->join('checklist_tasks', 'checklist_tasks.id', '=', 'no_checklist_actions.checklist_task_id')
    //->leftjoin('checklist_tasks', 'checklist_tasks.id', '=', 'no_checklist_actions.checklist_task_id')
    ->where('no_checklist_actions.work_order_id', '=', $work_order_id);
    $work_orders = $work_orders->orderBy('no_checklist_actions.id', 'desc')->paginate(10);
    $work_orders = json_decode(json_encode($work_orders), true);
    return $work_orders;
  }



  public static function getSendWoReminderemail($user_id,$wo_id)
  {
    $total_email_recivers_ids = $user_id;
    $work_order_number = $wo_id;
    $work_details = WorkOrders::where('id',$work_order_number)->first();
    \Log::info($total_email_recivers_ids);
    $users   = \Helper::getWorkOrderReciverEmails($total_email_recivers_ids);
    $sender_email = \Helper::getAdminContactMail();

    $work_details->assetCategory= DB::table('asset_categories')->where('id',$work_details->asset_category_id)->value('asset_category');

    if($work_details->asset_number_id == 0){
        $work_details->assetNumber=__('work_order.forms.label.asset_is_not_registered_in_the_system');
    }
    else{
        $work_details->assetNumber=DB::table('assets')->where('id',$work_details->asset_number_id)->value('asset_number');
    }
    $subject = " Reminder for Work order $work_details->work_order_id تذكير لأمر العمل رقم" ;
    $i = 1;
    if(count($users)>0) {
        foreach ($users as $user) {
            $has_sent = DB::table('work_orders_email_jobs')
                            ->select('wo_id')
                            ->where('wo_id', $work_order_number)
                            ->where('status','WO-Reminder')
                            ->whereRaw('FIND_IN_SET('.$user->id.',user_ids)')
                            ->count();
            \Log::info('count-'.$user->id);
            \Log::info('count-'.$has_sent);
           // if($has_sent == 0) {
                \Log::info('Comes inside');
                $to_name  = $user->name;
                $to_email = $user->email; //
                $mail_content['lang'] = 'ar';
                //Log::info('email-'.$to_email); //die;
                $default_lang = \Helper::getUserTypeLanguage('email_triggers',$to_email);
                //Log::info($to_email.$default_lang.'<br>'); //die;
                if(!empty($default_lang)) {
                    $mail_content['lang'] = $default_lang;
                    \App::setLocale($mail_content['lang']);
                    if($default_lang == 'en') {
                        $mail_content['dir'] = 'ltr';
                        $subject = " Reminder for Work order $work_details->work_order_id تذكير لأمر العمل رقم" ;
                        if($work_details->asset_number_id == 0){
                            $work_details->assetNumber="Asset is not registered in the system";
                        }
                    } else {
                        $subject = " Reminder for Work order $work_details->work_order_id تذكير لأمر العمل رقم" ;
                        if($work_details->asset_number_id == 0){
                            $work_details->assetNumber="الأصل غير مسجل في النظام";
                        }
                        $mail_content['dir'] = 'rtl';
                    }
                } else {
                    $subject = " Reminder for Work order $work_details->work_order_id تذكير لأمر العمل رقم" ;

                    $mail_content['dir'] = 'rtl';
                    $mail_content['lang'] = 'ar';
                }

                $mail_content['email'] = $user->email;
                $mail_content['name'] = $user->name;
                $mail_content['work_id'] = $work_details->work_order_id;
                $mail_content['id'] = $work_details->id;
                $mail_content['status'] = $work_details->job_status;
                $mail_content['description'] = $work_details->description;
                $mail_content['category'] = $work_details->assetCategory;
                $mail_content['asset_number'] = $work_details->assetNumber;
                $mail_content['floor'] = $work_details->floor;
                $mail_content['room'] = $work_details->room;
                $mail_content['work_order_link'] =  route('workorder.show', \Crypt::encryptString($work_details->id));
                $mail_content['subject'] = $subject;

                if ($to_email != '' && $i == 1 ) {
                    //Log::info($to_email);
                    \Mail::send('mail.work_order_reminder_email', ['mail_content' => $mail_content]
                    , function ($message) use ($to_name, $to_email, $subject, $sender_email) {
                        $message->to($to_email, $to_name)
                            ->subject($subject);
                        $message->from($sender_email,'Osool Team');
                    });

                }
                //$i++;
            //}

        }
    }
  }


  public static function WorkorderChecklistDetails($id)
    {
      $work_orders = DB::table('no_checklist_actions')
                      ->select('no_checklist_actions.*','checklist_tasks.task_id','checklist_tasks.task_number','checklist_tasks.task_title','checklist_tasks.description','checklist_tasks.comment as result','checklist_tasks.photos as is_photos','checklist_tasks.multiple_options as ori_res')
                      ->join('checklist_tasks', 'checklist_tasks.id', '=', 'no_checklist_actions.checklist_task_id')
                      //->leftjoin('checklist_tasks', 'checklist_tasks.id', '=', 'no_checklist_actions.checklist_task_id')
                      ->where('no_checklist_actions.id', '=', $id)->first();

        if(isset($work_orders) && !empty($work_orders))
        {
            $work_orders = json_decode(json_encode($work_orders), true);
            $photos= json_decode($work_orders['photos']);
            if(isset($photos) && !empty($photos))
            {
                        foreach($photos as $key=>$val)
                        {
                            if(trim(array_reverse(explode('.',url('storage/'.$val)))[0]) != 'pdf')
                            {
                                $work_orders['image_data'][$key] = ImagesUploadHelper::displayImage($val, 'actions');
                            }
                            else {
                                $work_orders['image_data'][$key] = url('storage/'. $val);
                            }
                        }

                        $work_orders['photos'] = $work_orders['image_data'];
                        unset($work_orders['image_data']);
            }

            if($work_orders['checklist_task_id'] != "")
            {
                $get_subtask_list = DB::table('checklist_subtasks')->where('checklist_task_id',$work_orders['checklist_task_id'])->get();
                if(isset($get_subtask_list) && !empty($get_subtask_list))
                {
                    $get_subtask_list = json_decode(json_encode($get_subtask_list), true);
                    foreach($get_subtask_list as $k1 => $v1)
                    {
                          $fetch_media = DB::table('no_checklist_subtask')
                                        ->select('*')
                                        ->where('checklist_subtask_id', $v1['id'])
                                        ->where('no_checklist_actions_id', $work_orders['id'])
                                        ->first();

                          if(isset($fetch_media) && !empty($fetch_media))
                          {

                              if($fetch_media->photos != "" && $fetch_media->photos != "[]")
                              {
                                  $photos= json_decode($fetch_media->photos);
                                  foreach($photos as $key=>$val)
                                  {

                                        if(trim(array_reverse(explode('.',url('storage/'.$val)))[0]) != 'pdf')
                                        {
                                            $image_data[$key] = ImagesUploadHelper::displayImage($val, 'actions');
                                        }
                                        else {
                                            $image_data[$key] = url('storage/'. $val);
                                        }
                                  }

                                  $fetch_media->photos = $image_data;
                                  unset($image_data);
                              }

                              $get_subtask_list[$k1]['uploaded_photos'] = $fetch_media->photos;
                              $get_subtask_list[$k1]['saved_comment'] = $fetch_media->comment;
                              $get_subtask_list[$k1]['checked_at'] = $fetch_media->created_at;
                          }
                          else
                          {
                            $get_subtask_list[$k1]['uploaded_photos'] = '';
                            $get_subtask_list[$k1]['saved_comment'] = '';
                            $get_subtask_list[$k1]['checked_at'] = '';
                          }
                          $get_subtask_actionlist = DB::table('subtask_actions_list')
                                          ->where('checklist_task_id',$work_orders['checklist_task_id'])
                                          ->where('checklist_subtask_id',$v1['id'])
                                          ->get();
                          if(isset($get_subtask_actionlist) && !empty($get_subtask_actionlist))
                          {
                                $get_subtask_actionlist = json_decode(json_encode($get_subtask_actionlist), true);
                                  foreach($get_subtask_actionlist as $k=>$v)
                                  {

                                      $action_data = DB::table('no_checklist_subtask_actions')
                                                          ->select('toggle_value')
                                                          ->where('no_checklist_actions_id', $work_orders['id'])
                                                          ->where('action_id', $v['id'])
                                                          ->first();
                                      if(isset($action_data) && !empty($action_data))
                                      {
                                          $get_subtask_actionlist[$k]['selected_toggle_value'] = $action_data->toggle_value;
                                      }
                                      else
                                      {
                                          $get_subtask_actionlist[$k]['selected_toggle_value'] = '';
                                      }
                                  }


                                $get_subtask_list[$k1]['action_list'] = $get_subtask_actionlist;
                          }
                          else
                          {
                               $get_subtask_list[$k1]['action_list'] = array();
                          }
                    }
                }
                else
                {
                    $get_subtask_list = array();
                }


            }
            else
            {
              $get_subtask_list = array();
            }

            $work_orders['subtask_list'] = $get_subtask_list;
        }
        return $work_orders;
    }



    public static function save_app_language($language)
    {
      $user_id = auth()->user()->id;
      DB::table('users')->where('id', $user_id)->update(['selected_app_langugage' => $language]);
      return true;
    }


    public static function get_reopen_wo_checklist_data($id)
    {
        $photos = DB::table('no_checklist_actions')
                    ->select('no_checklist_actions.photos')
                    ->where('reopen_wo_id', $id)
                    ->orderBy('id', 'asc')
                    ->get();
        $photos = json_decode(json_encode($photos), true);
        $images = [];
        if(!empty($photos))
        {
          foreach($photos as $row)
          {
            $json = json_decode($row['photos']);
            foreach($json as $prow)
            {
              $images[] = $prow;
            }
          }
        }
        return $images;
    }



    public static function getAssetNames($asset_category_id)
    {
        $asset_names_id = AssetNameAssetCategory::select('asset_name_id')->where('asset_category_id',$asset_category_id)->get()->toArray();
        if(isset($asset_names_id) && !empty($asset_names_id))
        {
            $asset_names_id = array_column($asset_names_id,'asset_name_id');
        }
        else {
            $asset_names_id = array('0');
        }

        $asset_names_id = implode(',',$asset_names_id);
        $data = AssetName::where('is_deleted', 'no')
                    ->whereRaw("(asset_category_id = $asset_category_id OR id IN ($asset_names_id))")
                    //->whereIn('asset_category_id', $asset_category_id)
                    //->orWhereIn('id',$asset_names_id)
                    ->get()->toArray();
        return $data;
    }


    public static function getAssetNamesmultiple($asset_cateogry_id)
    {
        $asset_names_id = AssetNameAssetCategory::select('asset_name_id')->whereIn('asset_category_id',$asset_cateogry_id)->get()->toArray();
        if(isset($asset_names_id) && !empty($asset_names_id))
        {
            $asset_names_id = array_column($asset_names_id,'asset_name_id');
        }
        else {
            $asset_names_id = array('0');
        }

        $asset_cateogry_id = implode(',',$asset_cateogry_id);
        $asset_names_id = implode(',',$asset_names_id);

        $data = AssetName::where('is_deleted', 'no')
                    ->whereRaw("(asset_category_id IN ($asset_cateogry_id) OR id IN ($asset_names_id))")
                    //->whereIn('asset_category_id', $asset_cateogry_id)
                    //->orWhereIn('id',$asset_names_id)
                    ->get();
        return $data;
    }


    public static function getAssetDetails($asset_number_id, $property_id)
    {
        // Get the logged-in user
        $logedin_user = auth()->user();

        // Retrieve the asset's data
        // $row_data = Asset::with(['assetCategories'  => function ($query) {
        //     $query->withTrashed();
        // }])->where('id', $asset_number_id)->first();

        $row_data = Asset::where('id', $asset_number_id)->first();
        if(isset($row_data) && !empty($row_data))
        {
          $assetnamedata = AssetName::select('asset_name')->where('id',$row_data->asset_name_id)->first();
          $row_data->asset_name = isset($assetnamedata) && !empty($assetnamedata) ? $assetnamedata->asset_name : "";
              // Check if $row_data->assetCategories exists and has items
              // if ($row_data->assetCategories && count($row_data->assetCategories) > 0) {
              //   $row_data->asset_categories = $row_data->assetCategories->pluck('id')->toArray();
              // } else {
              //     $row_data->asset_categories = [$row_data->asset_category_id];
              // }
              $data['row_data'] = $row_data;

              // Parse damage images
              // if (!empty($data['row_data']->damage_images)) {
              //     $data['row_data']->damage_images = explode(',', $row_data->damage_images);
              //     $data['damage_images'] = $data['row_data']->damage_images;
              // } else {
              //     $data['damage_images'] = [];
              // }

              // Get asset names with related asset categories
              // $data['asset_category'] = AssetName::with(['categories' => function ($query) use ($logedin_user) {
              //     $query->where('service_type', 'hard')->where('is_deleted', 'no')->where('user_id', $logedin_user['project_user_id']);
              // }])
              // ->with(['directCategory' => function ($query) use ($logedin_user) {
              //     $query->where('service_type', 'hard')->where('is_deleted', 'no');
              // }]) // Load the direct category
              // ->where('is_deleted', 'no')
              // ->where('user_id', $logedin_user['project_user_id'])
              // ->where('id', $row_data->asset_name_id)
              // ->orderBy('id', 'asc')
              // ->first();

              // Get asset names with related asset categories
              // $data['asset_name'] = AssetName::with(['categories' => function ($query) use ($logedin_user) {
              //     $query->where('is_deleted', 'no')->where('user_id', $logedin_user['project_user_id']);
              // }])
              // ->with('directCategory') // Load the direct category
              // ->where('is_deleted', 'no')
              // ->where('user_id', $logedin_user['project_user_id'])
              // ->orderBy('id', 'asc')
              // ->get();

              // Get asset files
              $data['assets_files'] = DB::table('assets_files')->select('*')
              ->where('assets_id', $asset_number_id)
              ->get()->toArray();
              foreach($data['assets_files'] as $k=>$v)
              {
                $data['assets_files'][$k]->uri = trim($data['assets_files'][$k]->file_name) != "" ? asset('uploads/asset_images/'.$data['assets_files'][$k]->file_name) : $data['assets_files'][$k]->file_name;
                $data['assets_files'][$k]->file_name = trim($data['assets_files'][$k]->file_name);
              }
              // Retrieve floor and room information
              if ($row_data->unit_id != 0) {
                  $row_data->floor = DB::table('room_types_floors')->where('id', $row_data->unit_id)
                      ->value('floor');
                  $row_data->room_id = $row_data->unit_id;
              }

              // Retrieve floor data based on conditions
              // $floor_data = RoomsTypeFloors::where(function ($query) use ($row_data) {
              //     $query->where('id', $row_data->unit_id)
              //         ->orWhereNull('deleted_at');
              // })
              // ->where([
              //     ['building_id', $row_data->building_id],
              //     ['floor', $row_data->floor]
              // ])
              // ->withTrashed()
              // ->get();

              // Get unique floor zones
              // $zones = DB::table('room_types_floors')->where('building_id', $row_data->building_id)
              //     ->groupBy('floor')->pluck('floor');
              // $data['asset_floor'] = $zones;
              // $data['asset_room'] = $floor_data;
              $data['id'] = $asset_number_id;
              $data['property_id'] = $property_id;

              // Retrieve property data
              //$property_data = Property::where('id', $property_id)->first();
              $data['builiding_name'] = DB::table('property_buildings')->where('id', $row_data->building_id)->value('building_name');
              $data['building_id'] = $row_data->building_id;
             // $data['property_data'] = $property_data;
              $data['barcode_value'] = $row_data->barcode_value;
              return $data;
        }
        else
        {
          return null;
        }

    }



    public static function getQrAssetDetails($asset_barcode_id,$is_bardcode)
    {
        // Get the logged-in user
        $logedin_user = auth()->user();
        // Retrieve the asset's data
        if($is_bardcode == 'yes')
        {
          $row_data = Asset::where('barcode_value', $asset_barcode_id)->first();
        }
        else
        {
          $row_data = Asset::where('id', $asset_barcode_id)->first();
        }
        
        if(isset($row_data) && !empty($row_data))
        {
          $assetnamedata = AssetName::select('asset_name')->where('id',$row_data->asset_name_id)->first();
          $row_data->asset_name = isset($assetnamedata) && !empty($assetnamedata) ? $assetnamedata->asset_name : "";
          $row_data->zone = $row_data->floor;
          $row_data->unit = $row_data->room;
          $data['row_data'] = $row_data;

              // Get asset files
              $data['assets_files'] = DB::table('assets_files')->select('*')
              ->where('assets_id', $row_data->id)
              ->get()->toArray();
              foreach($data['assets_files'] as $k=>$v)
              {
                $data['assets_files'][$k]->uri = trim($data['assets_files'][$k]->file_name) != "" ? asset('uploads/asset_images/'.$data['assets_files'][$k]->file_name) : $data['assets_files'][$k]->file_name;
                $data['assets_files'][$k]->file_name = trim($data['assets_files'][$k]->file_name);
                $isImage = in_array(strtolower(pathinfo($data['assets_files'][$k]->uri, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif']);

                      if ($isImage) {
                          $images[] = [
                              'uri' => trim($data['assets_files'][$k]->file_name) != "" ? asset('uploads/asset_images/'.str_replace(' ', '%20', $data['assets_files'][$k]->file_name)) : $data['assets_files'][$k]->file_name,
                              'file_name' => trim($data['assets_files'][$k]->file_name),
                              'extension' => pathinfo($data['assets_files'][$k]->uri, PATHINFO_EXTENSION),
                          ];
                      } else {
                          $pdfs[] = [
                              'uri' => trim($data['assets_files'][$k]->file_name) != "" ? asset('uploads/asset_images/'.str_replace(' ', '%20', $data['assets_files'][$k]->file_name)) : $data['assets_files'][$k]->file_name,
                              'file_name' => trim($data['assets_files'][$k]->file_name),
                              'extension' => pathinfo($data['assets_files'][$k]->uri, PATHINFO_EXTENSION),
                          ];
                      }
              }
              // Retrieve floor and room information
              if ($row_data->unit_id != 0) {
                  $row_data->floor = DB::table('room_types_floors')->where('id', $row_data->unit_id)
                      ->value('floor');
                  $row_data->room_id = $row_data->unit_id;
              }

              $data['service_type'] = Asset::with(['assetCategory', 'assetCategories'  => function ($query) {
                $query->withTrashed();
            }])
            //->with(['assetName'])
            ->where('id', $row_data->id)
            ->first()->toArray();

            if(isset($data['service_type']['asset_categories']))
            {
              $data['service_type'] = implode(array_column($data['service_type']['asset_categories'],'asset_category'));
            }
            else
            {
              $data['service_type'] = "";
            }

              $data['id'] = $row_data->id;
              $data['property_id'] = $row_data->property_id;
              $result['type'] = 'asset';
              // Retrieve property data
              $result['basic_information']['id'] = $row_data->id;
              $result['basic_information']['property_name'] = DB::table('property_buildings')->where('id', $row_data->building_id)->value('building_name');
              $result['basic_information']['zone'] = $row_data->floor;
              $result['basic_information']['unit'] = $row_data->room;
              $result['basic_information']['asset_name'] = $row_data->asset_name;
              $result['basic_information']['service_type'] = $data['service_type'];
              $result['basic_information']['asset_number'] = $row_data->asset_number;
              $result['basic_information']['asset_tag'] = $row_data->asset_tag;

              //General Information
              $result['general_information']['purchase_date'] = $row_data->purchase_date;
              $result['general_information']['model_number'] = $row_data->model_number;
              $result['general_information']['manufacturer_name'] = $row_data->manufacturer_name;
              $result['general_information']['asset_status'] = $row_data->asset_status;
              $result['general_information']['general_information'] = $row_data->general_information;


              // Now you have separate arrays for images and pdfs:
              $result['files']['images'] = isset($images) ? $images : []; // Handle potential empty array
              $result['files']['pdfs'] = isset($pdfs) ? $pdfs : [];


              $data['building_id'] = $row_data->building_id;
              $data['barcode_value'] = $row_data->barcode_value;

              unset($data);

              return $result;
        }
        else
        {
          return null;
        }

    }




    public static function getQrUnitDetails($barcode_id)
    {
        // Get the logged-in user
        $logedin_user = auth()->user();

        // Retrieve the data
        $UnitData = RoomsTypeFloors::with('roomType')->where('id', $barcode_id)->first();
        if(isset($UnitData) && !empty($UnitData))
        {

          $result['type'] = 'unit';
          $result['details']['id'] = $UnitData->id;
          $result['details']['unit_type'] = isset($UnitData->roomType->room_type) ? $UnitData->roomType->room_type : '';
          $result['details']['zone'] = isset($UnitData->floor) ? $UnitData->floor : '';
          $result['details']['unit'] = isset($UnitData->room) ? $UnitData->room : '';return $result;
        }
        else
        {
          return null;
        }

    }



    public static function getQrPropertyDetails($barcode_id)
    {
        // Get the logged-in user
        $logedin_user = auth()->user();

        // Retrieve the data
        $propertyResultdata = PropertyQrCodes::where('qr_code_value', $barcode_id)->first();
        if(isset($propertyResultdata) && !empty($propertyResultdata))
        {

          $selected_property_id = $propertyResultdata->building_id;

          $PropertyBuildings= PropertyBuildings::query()
            ->where('id', '=',$selected_property_id)
            ->with(['property'=>function ($subQuery)use($selected_property_id) {
                $subQuery->select('properties.*','cities.name_en as city_name_en','cities.name_ar  as city_name_ar','regions.name as region_name','regions.name_ar as region_name_ar','location_type')
                    ->leftJoin("cities",function($join){
                        $join->on("cities.id","=","properties.city_id");
                    })
                    ->leftJoin("regions",function($join){
                        $join->on("regions.id","=","properties.region_id");
                    });
            }])
            ->first();
            if(isset($PropertyBuildings) && !empty($PropertyBuildings))
            {
              $result['type'] = 'property';
              $result['details']['id'] = $PropertyBuildings->id;
              $result['details']['building_name'] = $PropertyBuildings->building_name;
              $result['details']['property_type'] = isset($PropertyBuildings->property->property_type) ? $PropertyBuildings->property->property_type : '';
              $result['details']['region_en'] = isset($PropertyBuildings->property->region_name) ? $PropertyBuildings->property->region_name : '';
              $result['details']['region_ar'] = isset($PropertyBuildings->property->region_name_ar) ? $PropertyBuildings->property->region_name_ar : '';
              $result['details']['city_en'] = isset($PropertyBuildings->property->city_name_en) ? $PropertyBuildings->property->city_name_en : '';
              $result['details']['city_ar'] = isset($PropertyBuildings->property->city_name_ar) ? $PropertyBuildings->property->city_name_ar : '';
              return $result;
            }
            else
            {
              return null;
            }
        }
        else
        {
          return null;
        }

    }



    public static function getAssetCategoriesusingPropertyId($property_id)
    {
      $contract_ids = ContractPropertyBuildings::where('contract_property_buildings.property_building_id',$property_id)->get()->toArray();
        if(isset($contract_ids) && !empty($contract_ids))
        {
            $contract_ids = array_values(array_unique(array_column($contract_ids, 'contract_id')));
        }
        else
        {
            $contract_ids = array(0);
        }

        $contract_number_data = Contracts::whereIn('id',$contract_ids)->get()->toArray();

        if(isset($contract_number_data) && !empty($contract_number_data))
        {
            $contract_number_data = array_values(array_unique(array_column($contract_number_data, 'contract_number')));
        }
        else
        {
            $contract_number_data = array(0);
        }

        $contract_assetcategories = ContractAssetCategories::select('asset_category_id')->whereIn('contract_number',$contract_number_data)->get()->toArray();
        if(isset($contract_assetcategories) && !empty($contract_assetcategories))
        {
            $contract_asset_category_id = array_values(array_unique(array_column($contract_assetcategories, 'asset_category_id')));
        }
        else
        {
            $contract_asset_category_id = array(0);
        }


        $data = AssetCategory::select('asset_categories.id', 'asset_categories.asset_category as category_name', 'asset_categories.deleted_at as asset_cat_deleted_at')
                ->whereNull('asset_categories.deleted_at')
                ->whereIn('asset_categories.id', $contract_asset_category_id)
                ->orderBy('asset_categories.id', 'asc')
                ->groupBy('asset_categories.asset_category')
                ->get()
                ->toArray();


        return $data;
    }
    public function newEloquentBuilder($query): WorkOrderQueryBuilder
    {
        return new WorkOrderQueryBuilder($query);
    }
}
