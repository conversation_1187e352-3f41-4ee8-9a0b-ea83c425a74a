<?php

namespace App\Services\Finance;

use App\Services\Contracts\DashCrmInterface;
use Symfony\Component\HttpFoundation\Response;

class FinanceProposalService
{
    protected $crmApiService;
    protected $workspaceSlug;

    public function __construct(DashCrmInterface $crmApiService)
    {
        $this->crmApiService = $crmApiService;
        $this->workspaceSlug = auth()->user()->workspace;
    }

    /**
     * Get all proposals for a specific customer.
     *
     * @param int $customerId
     * @param array $data
     * @return array
     */
    public function getProposals(array $data = []): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/accounting/proposals", $data);
    }

    /**
     * Get all proposals.
     *
     * @param array $data
     * @return array
     */
    public function list(array $data = []): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/accounting/proposals", $data);
    }

    /**
     * Get dropdown data for creating proposals.
     *
     * @return array
     */
    public function dropdownlist(): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/accounting/proposals/create");
    }

    /**
     * Create a new proposal.
     *
     * @param array $data
     * @return array
     */
    public function create(array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/accounting/proposals", $data);
    }

    /**
     * Delete a proposal.
     *
     * @param int $id
     * @return array
     */
    public function delete(int $id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/accounting/proposals/delete/{$id}");
    }

    /**
     * Get proposal data for editing.
     *
     * @param int $id
     * @return array
     */
    public function edit(int $id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/accounting/proposals/{$id}/edit");
    }

    /**
     * Update a proposal.
     *
     * @param int $id
     * @param array $data
     * @return array
     */
    public function update(int $id, array $data): array
    {
        return $this->crmApiService->put("/api/{$this->workspaceSlug}/accounting/proposals/{$id}", $data);
    }

    /**
     * View a specific proposal.
     *
     * @param int $id
     * @return array
     */
    public function view(int $id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/accounting/proposals/view/{$id}");
    }

    /**
     * Convert proposal to invoice.
     *
     * @param int $id
     * @return array
     */
    public function convertToInvoice(int $id): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/accounting/proposals/{$id}/convert-to-invoice");
    }

    /**
     * Export proposals.
     *
     * @return Response
     */
    public function export(): Response
    {
        return $this->crmApiService->downloadBlob("/api/{$this->workspaceSlug}/accounting/proposals-export");
    }
}
