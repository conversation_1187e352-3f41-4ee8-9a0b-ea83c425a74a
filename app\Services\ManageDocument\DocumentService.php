<?php


namespace App\Services\ManageDocument;

use App\Jobs\ProcessCrmLogin;
use App\Services\Contracts\DashCrmInterface;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Session;
use Mpdf\Http\Exception\RequestException;

class DocumentService
{
    protected $crmApiService;
    protected $workspaceSlug;

    public function __construct(DashCrmInterface $crmApiService)
    {
        $this->crmApiService = $crmApiService;
        $this->workspaceSlug = auth()->user()->workspace;
    }

    public function list($data): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/manage-documents/documents", $data);
    }


    public function create(): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/manage-documents/documents/create");
    }

    public function store(array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/manage-documents/documents", $data);
    }

    public function edit($id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/manage-documents/documents/$id/edit");
    }

    public function update(int $id, array $data): array
    {
        return $this->crmApiService->put("/api/{$this->workspaceSlug}/manage-documents/documents/{$id}", $data);
    }

    public function updateDescription(int $id, array $data): array
    {
        return $this->crmApiService->put("/api/{$this->workspaceSlug}/manage-documents/document-update-description/{$id}", $data);
    }

    public function delete(int $id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/manage-documents/documents/{$id}");
    }

    public function view($id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/manage-documents/documents/$id");
    }

    public function uploadAttachment(int $id, $filePath, $fileName): array
    {
        try {

            $client = new Client(['base_uri' => config('crm.base_url'), 'verify' => false]);

            $response = $client->post("/api/{$this->workspaceSlug}/manage-documents/document-upload-attachment/{$id}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . auth()->user()->crm_api_token,
                    'Accept' => 'application/json',
                ],
                'multipart' => array_filter([
                    $filePath ? [
                        'name'     => 'file',
                        'contents' => fopen($filePath, 'r'),
                        'filename' => $fileName,
                    ] : null
                ])
            ]);

            if ($response->getStatusCode() == 401) {
                auth()->user()->crm_api_token = null;
                auth()->user()->workspace_slug = null;
                auth()->user()->save();
                ProcessCrmLogin::dispatch(auth()->user()->email, Session::get('plain_user_password'));
                if (request()->ajax() && (auth()->user()->workspace != 'none' || !auth()->user()->workspace_slug)) {
                    abort(401);
                }
            }
            return json_decode($response->getBody(), true);
        } catch (RequestException $e) {
            return [
                'error' => true,
                'message' => $e->getMessage(),
            ];
        }
    }

    public function download(int $id, $data): array
    {
        try {

            $client = new Client(['base_uri' => config('crm.base_url'), 'verify' => false]);

            $response = $client->post("/api/{$this->workspaceSlug}/manage-documents/documents-export/{$id}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . auth()->user()->crm_api_token,
                    'Accept' => 'application/json',
                ],
            ]);

            if ($response->getStatusCode() == 401) {
                auth()->user()->crm_api_token = null;
                auth()->user()->workspace_slug = null;
                auth()->user()->save();
                ProcessCrmLogin::dispatch(auth()->user()->email, Session::get('plain_user_password'));
                if (request()->ajax() && (auth()->user()->workspace != 'none' || !auth()->user()->workspace_slug)) {
                    abort(401);
                }
            }
            return json_decode($response->getBody(), true);
        } catch (RequestException $e) {
            return [
                'error' => true,
                'message' => $e->getMessage(),
            ];
        }
    }

    public function deleteAttachment(int $id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/manage-documents/document-delete-attachment/{$id}");
    }

    public function storeComment($id, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/manage-documents/document-comment-store/$id", $data);
    }

    public function deleteComment(int $id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/manage-documents/document-comment-delete/{$id}");
    }

    public function storeNote($id, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/manage-documents/document-note-store/$id", $data);
    }

    public function deleteNote(int $id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/manage-documents/document-note-delete/{$id}");
    }

    public function updateStatus($id, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/manage-documents/document-update-status/$id", $data);
    }
}
