<?php


namespace App\Services\ManageDocument;

use App\Jobs\ProcessCrmLogin;
use App\Services\Contracts\DashCrmInterface;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Session;
use Mpdf\Http\Exception\RequestException;

class TypeService
{
    protected $crmApiService;
    protected $workspaceSlug;

    public function __construct(DashCrmInterface $crmApiService)
    {
        $this->crmApiService = $crmApiService;
        $this->workspaceSlug = auth()->user()->workspace;
    }

    public function list($data): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/manage-documents/document-types", $data);
    }

    public function store(array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/manage-documents/document-types", $data);
    }

    public function update(int $id, array $data): array
    {
        return $this->crmApiService->put("/api/{$this->workspaceSlug}/manage-documents/document-types/{$id}", $data);
    }

    public function delete(int $id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/manage-documents/document-types/{$id}");
    }
}
