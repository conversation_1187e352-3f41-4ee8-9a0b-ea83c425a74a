$gray-600:#6c757d;
$gray-500:#adb5bd;
$light-blue-1: #e<PERSON>ffe;
$primary: #5f63f2;
$text-light-1: #868eae;
$text-color: #272b41;
$btn-height: 48px;
$bg-draft: #ECECEC;
$bg-opacity-issued: #E4E4F5;
$bg-issued: #565887;
$lightBlue: #5a92cb;
$bgInvoiced: #016395;
$lightBlue1: #00A9F3;
$bgOpacityInvoiced: rgba(1, 99, 149, .1);
$bgRedLight:#f0e1ea;
$bgGreenLight:#d9eae6;
$bgOrangeLight:#f3e1c9;
$bgRed:#f78eac;
$bgGreen:#0CAF60;
$bgOrange:#FFA21D;
$newPrimary:#01A9F3;
$bgHold:#6C757D;
$bgHoldLight:#8a95b7;
$bgLoss:#D81B60;
$bgWin:#0CAF60;
$bgOsool:#152B70;
$bgDelete:#D81B60;
$bgSecondary:#475466;
$lightBlue2:#acc4cb;
$bgClose:#64748B;
$bgReOpen:#A91CD4;
$bgInProgress:#5eb1ff;
$bgNewSecondary:#566068;
$cyan-800: #055160;
$cyan-700: #00aca9;
$text-cyan: $cyan-800;
$bgExport: rgba(21, 43, 112, 0.05);
$blueNew:#ACB4D1;
$siteNewBlue:#1f64ff;
.bgRedLight{
    background:$bgRedLight;
}
.bgGreenLight{
    background:$bgGreenLight;
}
.bgOrangeLight{
    background:$bgOrangeLight;
}
.bgRed{
    background:$bgRed;
}
.bgGreen{
    background:$bgGreen;
}
.bgOrange{
    background:$bgOrange;
}.colorRed{
    color:$bgRed;
}.colorGreen{
    color:$bgGreen;
}.colorOrange{
    color:$bgOrange;
}
.bg-new-primary{
    background:$newPrimary;
    &.btn{
        color:#ffffff;
    }
}
.bg-hold{
    background:$bgHold;
}
.bg-loss{
    background:$bgLoss;
}
.bg-win{
    background:$bgWin;
}
.bg-hold-light{
    background:$bgHoldLight;
}
.text-hold-light{
    color:$bgHoldLight;
}
.text-new-primary{
    color:$newPrimary;
}
.bg-osool-new{
    background:$bgOsool;
}
.text-osool-new{
    color:$bgOsool !important;
}
.bg-delete{
    background:$bgDelete;
}
.text-delete{
    color:$bgDelete;
}
.text-loss{
    color:$bgLoss;
}
.bg-second{
    background:$bgSecondary;
}

$bgBlueLight:rgba(62, 201, 214, 0.1);
$bgBlue:#3EC9D6;
.bgBlue{
    background:$bgBlue;
}
.bgBlueLight{
    background:$bgBlueLight;
}
.colorBlue{
    color:$bgBlue;
}
.btn-export{
    background:$bgExport;
    &:hover{
        background:rgba(21, 43, 112, 0.08);
    }
}
.btn.bg-package2{
    &:hover{
        color:#fff;
        filter: brightness(90%);
    }
}
.bg-opacity-osool{
    background:rgba(21, 43, 112, 0.1);
}
.bg-opacity-osool-05{
    background:rgba(21, 43, 112, 0.05);
}
.bg-opacity-loss{
    background:rgba(216, 27, 96, 0.1)
}
.bg-opacity-new-primary{
    background:rgba(1, 169, 243, 0.2) !important;
}
.bg-opacity-new-primary-05{
    background:rgba(1, 169, 243, 0.05) !important;
}
.table{
    &.th-osool{
        th{
            color:$bgOsool;
        }
    }
    &.td-osool{
        td{
            color:$bgOsool !important;
        }
    }
}
.text-new-blue{
    color:$blueNew;
}
.text-win{
    color:$bgWin;
}
  .text-price-new{
    color:rgba(21, 43, 112, 0.5);
  }
  .bg-new-blue{
    background:$siteNewBlue;
    &.btn{
        color:#fff;
    }
  }
.bg-export{
    background:$bgExport;
}
.bg-opacity-win{
    background:$bgGreenLight;
}
