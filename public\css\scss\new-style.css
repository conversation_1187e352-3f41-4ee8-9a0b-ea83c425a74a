@charset "UTF-8";
@import url("https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@100;200;300;400;500;600;700&family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap");
.bgRedLight {
  background: #f0e1ea;
}

.bgGreenLight {
  background: #d9eae6;
}

.bgOrangeLight {
  background: #f3e1c9;
}

.bgRed {
  background: #f78eac;
}

.bgGreen {
  background: #0CAF60;
}

.bgOrange {
  background: #FFA21D;
}

.colorRed {
  color: #f78eac;
}

.colorGreen {
  color: #0CAF60;
}

.colorOrange {
  color: #FFA21D;
}

.bg-new-primary {
  background: #01A9F3;
}
.bg-new-primary.btn {
  color: #ffffff;
}

.bg-hold {
  background: #6C757D;
}

.bg-loss {
  background: #D81B60;
}

.bg-win {
  background: #0CAF60;
}

.bg-hold-light {
  background: #8a95b7;
}

.text-hold-light {
  color: #8a95b7;
}

.text-new-primary {
  color: #01A9F3;
}

.bg-osool-new {
  background: #152B70;
}

.text-osool-new {
  color: #152B70 !important;
}

.bg-delete {
  background: #D81B60;
}

.text-delete {
  color: #D81B60;
}

.text-loss {
  color: #D81B60;
}

.bg-second {
  background: #475466;
}

.bgBlue {
  background: #3EC9D6;
}

.bgBlueLight {
  background: rgba(62, 201, 214, 0.1);
}

.colorBlue {
  color: #3EC9D6;
}

.btn-export {
  background: rgba(21, 43, 112, 0.05);
}
.btn-export:hover {
  background: rgba(21, 43, 112, 0.08);
}

.btn.bg-package2:hover {
  color: #fff;
  filter: brightness(90%);
}

.bg-opacity-osool {
  background: rgba(21, 43, 112, 0.1);
}

.bg-opacity-osool-05 {
  background: rgba(21, 43, 112, 0.05);
}

.bg-opacity-loss {
  background: rgba(216, 27, 96, 0.1);
}

.bg-opacity-new-primary {
  background: rgba(1, 169, 243, 0.2) !important;
}

.bg-opacity-new-primary-05 {
  background: rgba(1, 169, 243, 0.05) !important;
}

.table.th-osool th {
  color: #152B70;
}
.table.td-osool td {
  color: #152B70 !important;
}

.text-new-blue {
  color: #ACB4D1;
}

.text-win {
  color: #0CAF60;
}

.text-price-new {
  color: rgba(21, 43, 112, 0.5);
}

.bg-new-blue {
  background: #1f64ff;
}
.bg-new-blue.btn {
  color: #fff;
}

.bg-export {
  background: rgba(21, 43, 112, 0.05);
}

.bg-opacity-win {
  background: #d9eae6;
}

.fw-bold {
  font-weight: bold;
}

.m-h-150 {
  min-height: 150px;
}

.m-h-250 {
  min-height: 250px;
}

.m-h-200 {
  min-height: 200px;
}

.min-h-48 {
  min-height: 48px;
}

.max-h-350 {
  max-height: 350px;
}

.h-350 {
  height: 350px;
}

.h-565 {
  height: 565px;
}

.h-300px {
  height: 300px;
}

.w-300px {
  width: 300px;
}

.min-w-150 {
  min-width: 150px;
}

.w-150px {
  width: 150px;
}

.min-w-180 {
  min-width: 180px;
}

.max-w-180 {
  max-width: 180px;
}

.min-w-200 {
  min-width: 200px;
}

.w-200px {
  width: 200px;
}

.min-w-300 {
  min-width: 300px;
}

.min-w-250 {
  min-width: 250px;
}

.max-width-50p {
  max-width: 50%;
}

.w-10 {
  width: 10px;
}

.w-16px {
  width: 16px;
}

.w-80px {
  width: 80px;
}

.min-h-0 {
  min-height: 0px !important;
}

.w-0 {
  width: 0%;
}

.min-w-100px {
  min-width: 100px;
}

.w-250px {
  width: 250px;
}

.w-20 {
  width: 20%;
}

.w-30 {
  width: 30%;
}

.m-h-80vh {
  min-height: 80vh;
}

.fs-2rem {
  font-size: 2rem;
}

.fs-1-3rem {
  font-size: 1.3rem;
}

.w-2rem {
  width: 2rem;
}

.max-w-400 {
  max-width: 400px;
}

.max-w-100p {
  max-width: 100%;
}

.min-w-50 {
  min-width: 50px;
}

.w-120px {
  width: 120px;
}

.max-w-0 {
  max-width: 0px;
}

.mt-51px {
  margin-top: 50px;
}

.ml-12p {
  margin-left: 12%;
}

.ml-38p {
  margin-left: 38%;
}

.mt-m7px {
  margin-top: -7px;
}

.min-h-30 {
  min-height: 30px;
}

.m-w-25p {
  min-width: 25% !important;
}

.h-50px {
  height: 50px !important;
}

.h-0p {
  height: 0%;
}

.max-w-0 {
  max-width: 0px;
}

.min-w-0 {
  min-width: 0px;
}

.h-80px {
  height: 80px;
}

.max-w-50 {
  max-width: 50px;
}

.fs-4em {
  font-size: 4em;
}

.min-h-40 {
  min-height: 40px;
}

.min-h-50 {
  min-height: 50px;
}

.min-h-50i {
  min-height: 50px !important;
}

.h-81p {
  height: 56% !important;
}

.w-10px {
  width: 10px;
}

.max-w-360 {
  max-width: 360px;
}

.max-w-55px {
  max-width: 55px;
}

.min-h-66px {
  min-height: 66px;
}

.max-w-400 {
  max-width: 400px;
}

.min-h-150 {
  min-height: 150px;
}

.min-h-150 {
  max-height: 150px;
}

.max-w-210 {
  max-width: 210px;
}

.mb-6px {
  margin-bottom: 6px;
}

.w-30px {
  width: 30px;
}

.h-100px {
  height: 100px;
}

.h-180px {
  height: 180px;
}

.w-42px {
  width: 42px;
}

.fs-38 {
  font-size: 38px;
}

.max-w-130 {
  max-width: 130px;
}

.max-w-0 {
  max-width: 0px;
}

.min-w-0 {
  min-width: 0px;
}

.h-80px {
  height: 80px;
}

.max-w-50 {
  max-width: 50px;
}

.min-h-40 {
  min-height: 40px;
}

.min-h-50 {
  min-height: 50px;
}

.min-h-50i {
  min-height: 50px !important;
}

.h-81p {
  height: 56% !important;
}

.w-10px {
  width: 10px;
}

.max-w-360 {
  max-width: 360px;
}

.max-w-55px {
  max-width: 55px;
}

.min-h-66px {
  min-height: 66px;
}

.max-w-400 {
  max-width: 400px;
}

.min-h-150 {
  min-height: 150px;
}

.min-h-150 {
  max-height: 150px;
}

.max-w-210 {
  max-width: 210px;
}

.mb-6px {
  margin-bottom: 6px;
}

.w-30px {
  width: 30px;
}

.h-100px {
  height: 100px;
}

.h-180px {
  height: 180px;
}

.w-42px {
  width: 42px;
}

.max-w-130 {
  max-width: 130px;
}

.min-h-80vh {
  min-height: 80vh;
}

.min-h-85vh {
  min-height: 85vh;
}

.min-h-100vh {
  min-height: 100vh;
}

.min-h-90vh {
  min-height: 100vh;
}

.min-w-130 {
  min-width: 130px;
}

.min-h-40 {
  min-height: 40px;
}

.max-w-150 {
  max-width: 150px;
}

.max-w-100px {
  max-width: 100px;
}

.wh-50 {
  width: 50px;
  height: 50px;
}

.wh-45 {
  width: 45px;
  height: 45px;
}

.badge-new {
  padding: 3px 10px;
  display: inline-block;
}

.table.tp-0 td {
  padding: 0;
}

.h-20 {
  height: 20px;
}

.radius-15 {
  border-radius: 15px;
}

.fs-30 {
  font-size: 30px;
}

.fs-26 {
  font-size: 26px;
}

.iti {
  --iti-path-flags-1x: url("../../img/intlflags/flags.webp");
  --iti-path-flags-2x: url("../../img/intlflags/<EMAIL>");
  --iti-path-globe-1x: url("../../img/intlflags/globe.webp");
  --iti-path-globe-2x: url("../../img/intlflags/<EMAIL>");
}


.display-none {
  display: none;
}

.error {
  color: red;
}

.cursor-move {
  cursor: move;
}

.ml-15p {
  margin-left: 15%;
}

.text-icon {
  color: #666d92;
}

.pointer-events-none {
  pointer-events: none;
}

.fw-normal {
  font-weight: normal;
}

.error {
  color: red;
}

.cursor-move {
  cursor: move;
}

.ml-15p {
  margin-left: 15%;
}

.text-icon {
  color: #666d92;
}

.pointer-events-none {
  pointer-events: none;
}

.fw-normal {
  font-weight: normal;
}

.fs-4em {
  font-size: 4em;
}

.fs-38 {
  font-size: 38px;
}

.error {
  color: red;
}

.job-category .job-category-li {
  padding: 0.3rem;
}
.job-category .job-category-li h6 {
  font-weight: normal;
  font-size: 0.8rem;
  max-width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.job-category .job-category-li span {
  font-size: 0.7rem;
  display: inline-block;
}
.job-category .job-category-li span.percent:before {
  content: "";
  height: 8px;
  width: 8px;
  background: #5f63f2;
  border-radius: 50%;
  /* Arabic */
  margin-right: 5px;
  display: inline-block;
}
.job-category .job-category-li .progress-wrap .progress {
  height: 6px;
}
.job-category .scrollable-container {
  overflow-y: auto;
}
.job-category .scrollable-container.bm {
  max-height: 380px;
}
.job-category .scrollable-container.poa {
  max-height: 480px;
}

.osool-pagination .page-list span {
  height: 30px;
  width: 30px;
  display: block;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
}
.osool-pagination .page-list span:hover {
  background: #efeffe;
  color: #5f63f2;
}
.osool-pagination .page-list.active span {
  background: #efeffe;
  color: #5f63f2;
}

.popup-card .execution-time {
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text_info_new {
  color: #01A9F3;
}

.text_info_bg_new {
  background-color: #01A9F3;
}

.text-light-1 {
  color: #868eae;
}

.bg_gray_badge {
  background-color: rgba(21, 43, 112, 0.5019607843);
  color: #FFF;
  height: 28px;
  font-weight: 400;
}

.bg_gray_badge_light {
  background-color: rgba(21, 43, 112, 0.1);
  color: #152B70;
  height: 28px;
  font-weight: 400;
}

.fs-28 {
  font-size: 28px;
}

.tbody-fs-14 td {
  font-size: 14px !important;
}

.expand_all_btn {
  height: 28px;
  width: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.properties_table tr:nth-child(even) {
  background: #f8f9fb;
}

/*# a href link with button class */
a.btn-outline-danger {
  color: #ff4d4f;
}
a.btn-outline-danger:hover {
  color: #ffffff;
}

.filter-bar .btn-group {
  padding: 2px 0;
}

.files-section .col-6:odd {
  background: black;
}

.tenants-section .content-list {
  position: absolute;
  width: 100%;
  background: #FFF;
  float: left;
  border: 1px solid #E1E1E1;
  border-color: rgba(0, 0, 0, 0.2);
  border-left: 0;
  border-right: 0;
  border-top: 0;
  -moz-box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.25);
  -webkit-box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.25);
  box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.25);
  margin-bottom: 8px;
  display: none;
  top: 100%;
  left: 0;
  border-radius: 20px;
  padding-bottom: 20px;
  z-index: 1;
}

.tenant-list-page .user-member .action-btn a {
  padding: 8px !important;
}

.nav-tabs.site-tabs li a.nav-link.active {
  border-bottom: 2px solid var(--primary);
}
.nav-tabs.site-tabs li a.nav-link.active h6 {
  color: var(--primary);
}

.lh-0 {
  line-height: 0;
}

.hover-2-img .img-2 {
  display: none;
}
.hover-2-img:hover .img-1 {
  display: none;
}
.hover-2-img:hover .img-2 {
  display: block;
}

.filter-popup .nav-pills {
  min-width: 10.5rem;
  padding-bottom: 1rem;
}
.filter-popup .nav-pills .nav-link {
  padding: 0.5rem;
  position: relative;
  color: #272b41;
  border-radius: 0.5rem 0 0 0.5rem;
}
.filter-popup .nav-pills .nav-link.active {
  background: #fff;
  color: #272b41;
}
.filter-popup .nav-link {
  padding: 0.3rem 1rem;
}

html[dir=rtl] .filter-popup .nav-link {
  border-radius: 0 0.5rem 0.5rem 0;
}

.all-filters .modal-body {
  min-height: 28rem;
}

.tenants-section .container_circle {
  position: relative;
  max-width: 60px;
  box-shadow: 0px 0px 4px 4px rgba(146, 153, 184, 0.**********);
  border-radius: 50%;
  margin: 0 auto;
}
.tenants-section .bottom_content_each {
  float: left;
}
.tenants-section .buildings-list .card-body {
  text-align: center;
}
.tenants-section .provider_bottom_container {
  border-top: 1px solid #f1f2f6;
}
.tenants-section .performance_each_card {
  padding-left: 5px;
  padding-right: 5px;
  padding-bottom: 10px;
}

.switch-btn .switch {
  position: relative;
  display: inline-block;
  width: 49px;
  height: 21px;
  margin-bottom: 2px;
}
.switch-btn .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.switch-btn .switch input:checked + .slider {
  background-color: #5F63F2;
}
.switch-btn .switch input:checked + .slider:before {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
}
.switch-btn .switch input:focus + .slider {
  box-shadow: 0 0 1px #5F63F2;
}
.switch-btn .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.switch-btn .slider:before {
  position: absolute;
  content: "";
  height: 15px;
  width: 15px;
  right: 30px;
  bottom: 3px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.switch-btn .slider.round {
  border-radius: 34px;
}
.switch-btn .slider.round:before {
  border-radius: 50%;
}

#workerdata .select-active {
  min-height: 40px;
  position: relative;
}
#workerdata .select-active .feather {
  position: absolute;
  content: "";
  top: 8px;
  right: 2px;
  color: #20c997;
}
#workerdata .select-active select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

html[lang=ar] #workerdata .select-active .feather {
  left: 2px;
  right: auto;
}

.bg-light-warning {
  background: #FFFBEF;
}

.btn-height {
  height: 48px;
}

.site-scrollbar::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px transparent;
  background-color: #f5f5f5;
}
.site-scrollbar::-webkit-scrollbar {
  width: 3px;
  background-color: #f5f5f5;
}
.site-scrollbar::-webkit-scrollbar-thumb {
  background-color: #e3e6ef;
  border: 3px solid #e3e6ef;
  -webkit-border-radius: 4px;
  border-radius: 4px;
}

.overflow-y-auto {
  overflow-y: auto;
}

textarea.form-control.textarea {
  min-height: 150px;
}

.job-category .nav-tabs .nav-item .nav-link {
  padding: 20px 6px;
  color: #272b41;
}
.job-category .nav-tabs .nav-item .nav-link.active {
  border-bottom: 1px solid var(--primary);
  background: none;
  color: var(--primary);
}
.job-category .nav-tabs .nav-item.show .nav-link {
  border-bottom: 1px solid var(--primary);
  background: none;
  color: var(--primary);
}
.job-category .select2-container .select2-selection--single {
  min-height: 35px;
}

#showSelected span.text::before {
  content: "Show selected work orders";
}
html[dir=rtl] #showSelected span.text::before {
  content: "عرض أوامر العمل المحددة";
}
#showSelected.active {
  color: red;
}
#showSelected.active .feather {
  display: block;
  color: red;
}
#showSelected.active span.text::before {
  content: "Close selected list";
}
html[dir=rtl] #showSelected.active span.text::before {
  content: "اغلاق قائمة أوامر العمل المحددة";
}
#showSelected.btn {
  box-shadow: none !important;
}
#showSelected .feather {
  display: none;
}

.local-dev {
  height: 70px;
  position: relative;
}

.local-dev:before {
  content: "";
  position: absolute;
  width: 10px;
  height: calc(50% - 10px);
  left: 9px;
  top: calc(50% + 10px);
  border-left: 1px dashed #676D8F;
}
html[dir=rtl] .local-dev:before {
  left: auto;
  right: 0;
}

.local-dev:after {
  content: "";
  position: absolute;
  width: 10px;
  height: calc(50% - 10px);
  left: 9px;
  top: 0;
  border-left: 1px dashed #676D8F;
}
html[dir=rtl] .local-dev:after {
  left: auto;
  right: 0;
}

.job-category .local-dev.first:after {
  display: none;
}
.job-category .local-dev:last-child:before {
  display: none;
}
.job-category .local-dev img + span {
  font-size: 10px;
}
.job-category .local-dev label {
  font-size: 9px;
  width: 100%;
}
.job-category .local-dev .progress {
  background: var(--primary);
  height: 10px;
  border-radius: 0;
}
.job-category .local-dev .progress-bar {
  background-color: #009b13;
}
.job-category .local-dev > div:first-child {
  min-width: 70px;
}
.job-category .local-dev > div:last-child {
  min-width: 140px;
}
.job-category .local-dev .local-tooltip {
  font-size: 12px;
  display: flex;
  align-items: center;
}
.job-category .local-dev .local-tooltip span {
  color: #ffffff;
}
.job-category .local-dev .local-tooltip span:first-child {
  height: 10px;
  width: 10px;
  border-radius: 2px;
}
.job-category .local-dev .local-tooltip span.bg-green {
  background: #009b13;
}
.job-category .local-dev .tooltip-dark {
  background: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  position: absolute;
  display: none;
}
.job-category .local-dev > div:last-child:hover .tooltip-dark {
  display: block;
  width: 100%;
}
.job-category .local-dev > div:last-child:hover .tooltip-dark:before {
  content: "";
  position: absolute;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid rgba(0, 0, 0, 0.7);
  top: -5px;
  left: 20px;
}

.localize-bar {
  height: 10px;
}

.green-bar {
  background-color: #009b13;
}

.text-count > div {
  margin-top: -21px;
  margin-right: 10px;
}
html[dir=rtl] .text-count > div {
  margin-right: 0;
  margin-left: 10px;
}

.pi-row .row .col-md-4:nth-child(3n) {
  border-right: none !important;
}

.notification_time_container span.time-posted a {
  white-space: nowrap;
}

.payment-invoice__btn a .feather.text-white {
  color: #fff !important;
}

.performance-indicator .chart {
  display: block;
  height: 30px;
  width: 230px;
}
.performance-indicator .chart h4 {
  color: #000;
  font-size: 15px;
  margin-bottom: 20px;
}
.performance-indicator .bar-1-container .bar-1 {
  height: 10px;
  background-color: #5f63f2;
  transition: width 0.5s;
  margin-top: 12px;
  border-radius: 10px;
  margin-left: 10px;
}
.performance-indicator .bar-1-container p {
  color: #5f63f2;
  font-size: 25px;
  font-weight: bold;
  margin-bottom: 0;
}
.performance-indicator .bar-2-container .bar-2 {
  height: 10px;
  background-color: #d17da0;
  transition: width 0.5s;
  margin-top: 12px;
  border-radius: 10px;
  margin-left: 10px;
}
.performance-indicator .bar-2-container p {
  color: #d17da0;
  font-size: 25px;
  font-weight: bold;
  margin-bottom: 0;
}
.performance-indicator .bar-3-container .bar-3 {
  height: 10px;
  background-color: #fa8b0b;
  transition: width 0.5s;
  margin-top: 12px;
  border-radius: 10px;
  margin-left: 10px;
}
.performance-indicator .bar-3-container p {
  color: #fa8b0b;
  font-size: 25px;
  font-weight: bold;
  margin-bottom: 0;
}
.performance-indicator .bar-4-container .bar-4 {
  height: 10px;
  background-color: #30cc9d;
  transition: width 0.5s;
  margin-top: 12px;
  border-radius: 10px;
  margin-left: 10px;
}
.performance-indicator .bar-4-container p {
  color: #30cc9d;
  font-size: 25px;
  font-weight: bold;
  margin-bottom: 0;
}
.performance-indicator .overall_card_wrap h3 {
  color: #5B5F7B;
  font-size: 16px;
  margin-bottom: 10px;
  margin-top: 20px;
}
.performance-indicator .overall_card_wrap h4 {
  color: #272B40;
  font-size: 20px;
  padding-bottom: 20px;
}
.performance-indicator .overall_card_wrap h5 {
  color: #272B40;
  font-size: 16px;
  margin-top: 10px;
  margin-bottom: 5px;
}
.performance-indicator .overall_card_wrap h6 {
  color: #272B40;
  font-size: 16px;
  margin-top: 10px;
  margin-bottom: 5px;
  text-align: center;
}
.performance-indicator .cc-progress {
  height: 8px;
}
.performance-indicator .current-status-graph {
  display: flex;
  justify-content: center;
  align-items: center;
  object-fit: cover;
}
.performance-indicator .status-div {
  width: 100%;
  height: auto;
  position: relative;
  aspect-ratio: 1;
}

.dashboard:not(.performance-indicator) .device-pieChart-wrap .pie-chart-legend {
  top: 50%;
}
.device-pieChart-wrap.no-heading .pie-chart-legend {
  top: 53%;
}

.hide {
  display: none !important;
}

.mh-auto {
  min-height: auto;
}

.table-social.mh-auto {
  min-height: auto;
}
.table-social.mh-auto th,
.table-social.mh-auto td {
  height: 80px;
  text-align: center;
  background: none;
  border-top: 1px solid #f1f2f6;
}
.table-social th {
  text-align: center;
}

.performance-indicator .overall_card_wrap h3 {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin: 10px 0 3px 0;
  min-height: 1.5rem;
}
.performance-indicator .overall_card_wrap h3 span:first-child {
  font-size: 14px;
  font-weight: 500;
}
.performance-indicator .overall_card_wrap h3 span.fs-20 {
  min-width: 1.5rem;
  text-align: right;
}
html[lang=ar] .performance-indicator .overall_card_wrap h3 span.fs-20 {
  text-align: left;
}
.performance-indicator .overall_card_wrap ul {
  /*display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  -webkit-border-radius: 0.25rem;
  border-radius: 0.25rem;*/
}
.performance-indicator .overall_card_wrap ul li {
  display: flex;
  justify-content: space-between;
  align-items: start;
  position: relative;
  padding: 0.1rem 0rem 0.1rem 1rem;
  background-color: #fff;
  /*&:first-child{
  -webkit-border-top-left-radius: inherit;
  border-top-left-radius: inherit;
  -webkit-border-top-right-radius: inherit;
  border-top-right-radius: inherit;
  }*/
}
html[lang=ar] .performance-indicator .overall_card_wrap ul li {
  padding: 0.1rem 1rem 0.1rem 0rem;
}
.performance-indicator .overall_card_wrap ul li:before {
  content: "";
  position: absolute;
  top: 8px;
  left: 0;
  background: #111111;
  height: 5px;
  width: 5px;
}
html[lang=ar] .performance-indicator .overall_card_wrap ul li:before {
  left: auto;
  right: 0;
}
.performance-indicator .overall_card_wrap ul li p {
  margin-bottom: 0;
  color: #9299b8;
}
.performance-indicator .overall_card_wrap ul li span.fs-20 {
  font-size: 18px;
  line-height: 21px;
}
.performance-indicator .overall_card_wrap ul li + li {
  border-top-width: 0;
}

.select-akaunting .select2-selection.select2-selection--single {
  display: flex;
  align-items: center;
}
.select-akaunting span.select2-selection__arrow {
  top: 50% !important;
}

.w-10p {
  width: 10px;
}

.h-10p {
  height: 10px;
}

.h-fix-content {
  height: fit-content;
}

.c_property_name_div .select2-container--default .select2-selection--multiple .select2-selection__rendered li {
  display: none;
}
.c_property_name_div .select2-container--default .select2-selection--multiple .select2-selection__rendered li:nth-child(-n+6) {
  display: flex;
}

.penalty_input_group .select2-selection {
  border: 0;
}

.table_td_baseline td {
  vertical-align: baseline;
}

.pr_3 {
  padding-right: 1.5rem !important;
}

.d-o-none {
  display: none;
}

.bg-modal2 {
  background: rgba(0, 0, 0, 0.3);
}

.visibility-hidden {
  visibility: hidden;
}

.cursor-auto {
  cursor: auto !important;
}

.sbn-bar-code {
  vertical-align: middle;
  display: table-cell;
}

.contract-show .h-56 {
  height: 56% !important;
}
.contract-show .h-32 {
  height: 32% !important;
}
.contract-show .timeline-box--3.basic-timeline .timeline > li .timeline-single__buble {
  background-color: #ffffff;
  width: 12px;
  height: 12px;
  border: none;
  margin-left: -5px !important;
  top: 0;
  border-radius: 50%;
}
.contract-show .timeline-box--3.basic-timeline .timeline li .timeline-single {
  margin-top: -5px;
  padding-right: 45%;
}
.contract-show .timeline-box--3.basic-timeline .timeline li .timeline-single {
  margin-top: -5px;
  padding-right: 10%;
}
.contract-show .table--default td {
  font-size: 12px !important;
}
.contract-show .card_content th {
  padding-left: 0;
}
.contract-show .card_content td {
  color: #272b41;
  font-size: 14px !important;
  font-weight: 700;
  padding-left: 0;
}
.contract-show .table_text_primary th, .contract-show .table_text_primary td {
  font-weight: 600 !important;
}
.contract-show .table_text_primary th:not(:text-warning), .contract-show .table_text_primary td:not(:text-warning) {
  color: #152B70 !important;
}
.contract-show .card-overview-progress .card-progress__summery strong {
  font-size: 21px !important;
}
.contract-show .card-overview-progress .card-progress__summery {
  margin-bottom: 13px !important;
}
.contract-show .card-overview-progress .card-progress {
  padding-top: 15px !important;
  padding-bottom: 15px !important;
}
.contract-show .image-zoom img {
  border-radius: 30px;
}

.contracts-list-page .bg-opacity-grey {
  background: none !important;
  border: 1px solid rgba(178, 178, 178, 0.15);
}

.upload-label {
  border: 2px dashed #e3e6ef;
}
.upload-label .upload-icon {
  height: 35px;
  width: 35px;
  max-width: 35px;
}

.tenant-section label#file_upload-error {
  color: red;
}

.worktime-section .userDatatable table thead tr th:first-child,
.userdt-container .userDatatable table thead tr th:first-child {
  border-left: 0px;
  border-radius: 0px;
}
.worktime-section .userDatatable table thead tr th:last-child,
.userdt-container .userDatatable table thead tr th:last-child {
  border-right: 0px;
  border-radius: 0px;
}
.worktime-section .userDatatable table thead tr th,
.userdt-container .userDatatable table thead tr th {
  color: #4f5369;
  background: none;
  border-top: 0px;
  border-bottom: 0px;
  font-weight: 400;
}
.worktime-section #official_vacation_days_count,
.userdt-container #official_vacation_days_count {
  color: #272b41 !important;
}
.worktime-section .time-picker .input-container,
.userdt-container .time-picker .input-container {
  width: 98% !important;
}
.worktime-section .input-color,
.userdt-container .input-color {
  color: var(#272b41);
}

.add-checklist .dropdown-item-checked::before {
  position: absolute;
  left: 0.4rem;
  content: "✓";
  color: black;
  font-weight: 600;
}
.add-checklist label {
  display: inline-block;
  width: 110px;
  color: #777777;
}
.add-checklist input {
  padding: 5px 10px;
}

.maintenance-requests-page .users-list-body__title span span {
  font-weight: 400 !important;
}
.maintenance-requests-page .dropdown-item-checked::before {
  position: absolute;
  right: 1.4 rem;
  content: "✔";
  font-weight: 600;
}
.maintenance-requests-page .view-img {
  padding: 10px 0px;
  display: inline-block;
}
.maintenance-requests-page #searchbar::placeholder {
  color: #808497 !important;
}
.maintenance-requests-page #searchbar {
  color: #808497 !important;
}

.akaunting-debug-bar {
  position: fixed;
  bottom: 0;
  right: 0;
  width: 400px;
  height: 100px;
  background: #fff;
  border: 1px solid #ccc;
  z-index: 9999;
}
.akaunting-debug-bar .akaunting-debug-exit {
  position: absolute;
  top: 0;
  right: 0;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  text-align: center;
  line-height: 30px;
  font-size: 20px;
  color: #000;
}
.akaunting-debug-bar .akaunting-debug-bar-content {
  display: flex;
  flex-direction: row;
  padding: 10px;
}
.akaunting-debug-bar .akaunting-debug-bar-content .logic-info {
  margin-left: 10px;
}
.akaunting-debug-bar .akaunting-debug-bar-content p {
  margin: 0;
  padding: 0;
  font-size: 12px;
  font-weight: bold;
  color: #000;
}
.akaunting-debug-bar .akaunting-debug-bar-content ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.akaunting-debug-bar .akaunting-debug-bar-content ul li {
  margin: 0;
  padding: 0;
  font-size: 10px;
  color: #000;
}
.akaunting-debug-bar .akaunting-debug-exit i {
  color: #ff1a1d;
}

.border-radius-10 {
  border-radius: 10px;
}

.border-radius-8 {
  border-radius: 8px;
}

.login-module .vcenter-item {
  display: flex;
  align-items: center;
}
.login-module .edit-profile__body .form-control-sm {
  height: 26px !important;
}
.login-module .edit-profile__body .form-control {
  height: 42px;
}
.login-module .edit-profile__body .form-group label {
  font-size: 13px;
  line-height: 1.4285714286;
  font-weight: 400;
  color: #5f5f5f;
}
.login-module .edit-profile__body .form-group .field-icon {
  top: 24px;
}
.login-module iframe#launcher {
  display: block;
}

.super-admin-page .edit-profile__body .form-control {
  height: 40px !important;
}

.workspace-pages .show-radio-div.active {
  display: block !important;
}

.sp-list-page .loading-container {
  position: relative;
}
.sp-list-page .loading-indicator {
  position: absolute;
  top: 30%;
  left: 82%;
  margin-left: 5px;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  animation: rotate 2s linear infinite;
  display: none;
}
.sp-list-page .loadingButton.loading .loading-indicator {
  display: block;
}
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.property-details-page #prevBtn {
  display: none !important;
}
.property-details-page .disable-click a {
  /* pointer-events:none; */
  cursor: not-allowed;
}
.property-details-page .form-control[readonly] {
  background-color: white;
  opacity: 1;
}
.property-details-page input.select2-search__field {
  width: 100% !important;
}
.property-details-page .image-zoom .wo-img {
  border: 10px solid #ffffff;
}

.contracts-pages .c_property_name_div .select2-container--default .select2-selection--multiple .select2-selection__rendered {
  max-height: 50px !important;
}
.contracts-pages .c_region_name_div .select2-container--default .select2-selection--multiple .select2-selection__rendered {
  max-height: 70px !important;
}
.contracts-pages .c_city_name_div .select2-container--default .select2-selection--multiple .select2-selection__rendered {
  max-height: 70px !important;
}
.contracts-pages .select2-container--default .select2-selection--multiple .select2-selection__rendered li + li.select2-search {
  width: auto !important;
}
.contracts-pages .select2-container--default .select2-selection--multiple .select2-selection__rendered li.select2-search,
.contracts-pages .select2-container .select2-search--inline .select2-search__field {
  display: flex !important;
  width: 100% !important;
}

.new-scrollbar::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px transparent;
  background-color: #fff;
}
.new-scrollbar::-webkit-scrollbar {
  width: 3px;
  background-color: #fff;
}
.new-scrollbar::-webkit-scrollbar-thumb {
  background-color: #e3e6ef;
  border: 3px solid #e3e6ef;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  height: 148px;
}

.new-scrollbar-hidden::-webkit-scrollbar-track {
  display: none;
}
.new-scrollbar-hidden::-webkit-scrollbar {
  display: none;
}
.new-scrollbar-hidden::-webkit-scrollbar-thumb {
  display: none;
}

.complaints-page .timeline-box--3.scroll-timeline {
  height: 50vh !important;
}
.complaints-page p.tool-last {
  margin-bottom: 0;
  padding-bottom: -3timeline-box- !important;
}

.contract-items .userDatatable {
  padding: 0 !important;
  border: none !important;
  box-shadow: none;
}

.site-config .wizard10 .edit-profile__body .form-group .form-control {
  border: 1px solid #e3e6ef;
  color: #5e5e5f;
  font-size: 15px;
  line-height: 1.**********;
  font-weight: 400;
  background: #fff;
}

.sp-list-page .action_icon {
  color: #212529 !important;
}
.sp-list-page .view-account {
  line-height: 15px !important;
  white-space: nowrap;
  font-size: 12px;
}

.asset-tag-section .select2-container--default .select2-selection--single,
.asset-tag-section .select2-container--default .select2-selection--multiple {
  min-height: 0;
  background: none;
  border: none;
}
.asset-tag-section .select2-container--default .select2-selection--single .select2-selection__rendered,
.asset-tag-section .select2-container--default .select2-selection--multiple .select2-selection__rendered {
  padding-left: 5px;
  padding-right: 40px;
  font-size: 13px;
}
html[lang=ar] .asset-tag-section .select2-container--default .select2-selection--single .select2-selection__rendered,
html[lang=ar] .asset-tag-section .select2-container--default .select2-selection--multiple .select2-selection__rendered {
  padding-right: 5px !important;
  padding-left: 40px !important;
  font-size: 12px;
}
.asset-tag-section .select2-container--default .select2-selection--single .select2-selection__arrow,
.asset-tag-section .select2-container--default .select2-selection--multiple .select2-selection__arrow {
  height: auto;
  right: -10px;
}
html[lang=ar] .asset-tag-section .select2-container--default .select2-selection--single .select2-selection__arrow,
html[lang=ar] .asset-tag-section .select2-container--default .select2-selection--multiple .select2-selection__arrow {
  left: -10px;
  right: auto;
}
.asset-tag-section .cutom-input {
  width: 60%;
  color: #5F63F2;
}

.tag-section .tag-dropdown {
  padding-top: 7px;
  padding-bottom: 7px;
}

@media screen and (max-width: 576px) {
  .assets-filter .filter-btn,
  .assets-filter .sort-btn,
  .assets-filter .history-btn {
    width: calc(50% - 6px);
  }
  .assets-filter .tag-section {
    width: 100%;
  }
  .assets-filter .tag-section .tag-dropdown {
    padding-top: 7px;
    padding-bottom: 7px;
  }
}
.purchase_request .header-recap {
  background-color: #F8F9FB;
  padding: 2rem 0;
}
.purchase_request .header-recap .amount {
  font-size: 13px;
  font-weight: 400;
  line-height: 19.36px;
  text-align: left;
  text-transform: uppercase;
}

.table-items table th.width {
  min-width: 150px;
}

.table-price td,
.table-price th {
  padding: 7px 10px;
}

.company-info .company-logo {
  width: 150px;
  height: 150px;
}

.pur-req-details h4 {
  font-size: 16px;
}

.bg-draft {
  background: #ECECEC;
}

.bg-opacity-issued {
  background: #E4E4F5;
}

.bg-issued {
  background: #565887;
}

.bg-light-blue {
  background: #5a92cb;
}

.bg-auth {
  background: url("/img/imgpsh_fullsize_anim.jpg") no-repeat center center fixed;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
}

.text-issued {
  color: #565887;
}

.order-bg-opacity-danger a.remove:hover,
.order-bg-opacity-danger button.remove:hover,
.orderDatatable_actions li a.remove:hover,
.orderDatatable_actions li button.remove:hover {
  background: rgba(255, 77, 79, 0.1);
}

.job-category-tool {
  position: relative;
}

@media screen and (max-width: 576px) {
  .release-notes .notification_right_contentbox {
    margin: 0;
  }
  .version-name {
    border-radius: 30px;
  }
}
.navbar-right__menu .nav-author__info h6 {
  max-width: 170px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.asset-imgage-empty-img {
  max-width: 40%;
}

.fc-day-holiday {
  background: #FFF9E7;
  border-top: 2px solid #FFC926;
}
.fc-day-holiday .fc-daygrid-day-holidayname {
  position: absolute;
  bottom: 5px;
  right: 5px;
}

.asset-tag-container {
  position: relative;
  display: inline-block;
  cursor: default;
}
.asset-tag-container .asset-tag {
  display: inline-block;
  max-width: 200px;
  /* Ajustez la largeur maximale selon vos besoins */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.asset-tag-container .tooltip {
  width: 200px;
  /* Ajustez la largeur selon vos besoins */
  background-color: #8fa5fa;
  color: #fff;
  text-align: center;
  border-radius: 5px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  /* Positionne le tooltip au-dessus du texte */
  left: 50%;
  margin-left: -100px;
  /* Centre le tooltip */
  transition: opacity 0.3s, visibility 0.3s;
}
.asset-tag-container:hover .tooltip {
  visibility: visible;
  opacity: 0.9;
}

.add-property tr.room_row_each td {
  border-bottom: none !important;
}
.add-property tr.userDatatable-header th {
  font-weight: normal;
}
.add-property .wizard10 .select2-container--default .select2-selection--single,
.add-property .wizard10 .select2-container--default .select2-selection--multiple {
  border: 1px solid #e3e6ef !important;
}
.add-property .generated-qr img {
  opacity: 0.3;
}

.assets_managements .dropdown-default .dropdown-item.active,
.assets_managements .dropdown-menu .dropdown-item.active {
  background: rgba(95, 99, 242, 0.06);
  color: #5f63f2;
}
.assets_managements .file-view {
  display: flex;
  align-items: center;
  height: 95px;
  border: 1px solid rgb(232, 223, 223);
  border-radius: 5px;
  width: auto;
  padding: 5px;
  box-sizing: border-box;
  margin: 2px;
}
.assets_managements .file-view .img-section {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40%;
  height: 90%;
  background-color: #f7f6f6;
  padding: 5px;
  border-radius: 5px;
  flex-shrink: 0;
  /* Prevent shrinking */
  min-width: 40%;
  /* Ensure minimum width */
}
.assets_managements .file-view .img-section img {
  max-width: 100%;
  max-height: 100%;
}
.assets_managements .img-section-transparent {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40%;
  height: 90%;
  padding: 5px;
  border-radius: 5px;
  flex-shrink: 0;
  /* Prevent shrinking */
  min-width: 40%;
  /* Ensure minimum width */
}
.assets_managements .img-section-transparent img {
  cursor: pointer;
}
.assets_managements .info-section {
  flex: 1;
  padding-left: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  /* Prevent text overflow */
  white-space: nowrap;
  /* Prevent text wrapping */
  text-overflow: ellipsis;
  /* Show ellipsis for overflow text */
}
.assets_managements .info-section h6 {
  font-size: 12px !important;
}

.bg-opacity-success-hover:hover {
  background: rgba(32, 201, 151, 0.15);
}

.bg-opacity-danger-hover:hover {
  background: rgba(255, 77, 79, 0.15);
}

.color-invoiced {
  color: #016395;
}

.bg-invoiced {
  background: #016395;
}

.bg-opacity-invoiced {
  background: rgba(1, 99, 149, 0.1);
}

.wh-10 {
  width: 10px;
  height: 10px;
}

.color-dropdown .color-dot {
  height: 20px;
  width: 20px;
  border-radius: 50%;
}

.table-account {
  display: table;
}
.table-account tr td,
.table-account tr th {
  padding: 0.5rem 0;
}

.expandable {
  max-height: 50px;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}
.expandable.active {
  height: auto;
  max-height: 100px;
  overflow-y: auto;
}
.expandable.active.overflow-hidden {
  overflow-y: auto !important;
}
.expandable.active .expand-bottom {
  display: none;
}
.expandable .expand-bottom {
  height: 50px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgb(238, 140, 97);
  background: linear-gradient(180deg, rgba(238, 140, 97, 0) 0%, rgb(255, 255, 255) 100%);
}

.show-more i {
  transition: all 0.3s ease-in-out;
}
.show-more::before {
  content: "Show More";
}
.show-more.show-less::before {
  content: "Show Less";
}
.show-more.show-less i {
  transform: rotate(180deg);
}

.v-align-c {
  vertical-align: middle;
}

.table thead th.v-align-c {
  vertical-align: middle;
}

.bg-light-blue1 {
  background: #00A9F3;
}

html[lang=ar] .tenant-list-page .pagination .pagination-fa i {
  transform: rotate(180deg);
}

@media screen and (max-width: 767px) {
  .border-top-sm {
    border-top: 1px solid #e3e6ef;
    border-left: none !important;
    border-right: none !important;
  }
  .border-bottom-sm {
    border-bottom: 1px solid #e3e6ef;
    border-left: none !important;
    border-right: none !important;
  }
}
.gap-10 {
  gap: 10px;
}

.gap-20 {
  gap: 20px;
}

.gap-50 {
  gap: 50px;
}

.property-right > *, .property-right a {
  max-height: 49px;
  min-height: 49px;
}

.bg-opacity-yellow {
  background: rgba(243, 201, 41, 0.1215686275);
}

.text-yellow {
  color: #F3C929;
}

.bg-yellow {
  background: #F3C929;
}

.table.table-per-thre td, .table.table-per-thre th {
  vertical-align: middle;
}
.table.table-per-thre .form-control {
  max-height: 34px;
  min-height: 0px;
}
.table.table-per-thre .input-group-text {
  max-height: 34px;
  padding: 0 5px;
}

@media only screen and (max-width: 678px) {
  .table-per-thre th.penalty {
    min-width: 100px;
  }
  .table-per-thre th.percentage {
    min-width: 150px;
  }
  .table-per-thre th.percentage .select2-container--default .select2-selection--single {
    min-height: 35px;
    max-height: 35px;
  }
}
.list-group.booking-system {
  flex-direction: row;
}
.list-group.booking-system .list-group-item {
  display: inline-block;
  border-radius: 5px;
  margin-right: 10px;
}
.list-group.booking-system .list-group-item + .list-group-item {
  border-top-width: 1px;
}

.projectDatatable.userDatatable .table td.py-0 {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

.sp-logo {
  height: 100px;
  width: 100px;
}

.card .card-header h6.fs-14 {
  font-size: 14px;
}

.rater-worker .jq-star {
  height: auto !important;
  width: auto !important;
  background: none;
  border: none;
  cursor: default;
}
.rater-worker .jq-star .jq-star-svg {
  width: 10px;
  cursor: default;
}

.no-before:before {
  display: none;
}

.files-multiple .image-list {
  width: 100%;
}
.files-multiple .thumb-Images li {
  width: 100%;
  display: inline-block;
  vertical-align: top;
  margin-bottom: 10px;
  position: relative;
}
.files-multiple .thumb-Images li img {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}
html[lang=ar] .files-multiple .thumb-Images li img {
  margin-left: 10px;
}
.files-multiple .thumb-Images li .FileNameCaptionStyle {
  font-size: 12px;
  position: absolute;
  top: 32%;
  /* z-index: 99999; */
  left: 70px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 60%;
}
.files-multiple .thumb-Images li .img-wrap {
  position: relative;
  display: inline-block;
  font-size: 0;
  border: 1px solid #b9b6d1;
  width: 100%;
  border-radius: 10px;
  /* padding-right: 20px;*/
}
.files-multiple .thumb-Images li .img-wrap .close {
  position: absolute;
  top: 20%;
  right: 10px;
  z-index: 100;
  padding: 5px 2px 2px;
  color: #999;
  font-weight: 100;
  height: 30px;
  width: 30px;
  border-radius: 30px;
  border: 1px solid #999;
  cursor: pointer;
  opacity: 0.5;
  font-size: 23px;
  line-height: 16px;
  border-radius: 50%;
  text-align: center;
  opacity: 1 !important;
}
.files-multiple .thumb-Images li .img-wrap .thumb {
  width: 50px;
  height: 50px;
  border-radius: 10px;
}

.contracts-dashboard .card .card-header {
  padding-right: 1rem;
  padding-left: 1rem;
}
.contracts-dashboard .card .card-header h6 {
  font-size: 15px;
}
.contracts-dashboard .card .overview-content .chart-desc h3.h3 {
  font-size: 20px;
}

.pip {
  position: relative;
  display: inline-block;
}
.pip img {
  height: 80px;
}
.pip .remove {
  height: 25px;
  display: block;
  width: 25px;
  line-height: 22px;
  text-align: center;
  border: 1px solid #ccc;
  border-radius: 50%;
  position: absolute;
  top: -6px;
  right: -10px;
  background: #fff;
  cursor: pointer;
}

.scroll-only-table .table-responsive {
  overflow-x: hidden;
}
.scroll-only-table .table-responsive .table-scroll {
  overflow-x: auto;
}
.scroll-only-table .table-responsive .dataTables_wrapper .bottom {
  margin-top: 10px;
}

.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}

.rotate-active {
  transition: all 0.5s ease-in-out;
}
a.active .rotate-active {
  transform: rotate(180deg);
}

.modal .body-color p {
  color: #666d92 !important;
}

.criteria-li .icon {
  font-size: 1.6rem;
}
.criteria-li i:not(.icon) {
  display: none;
}
.criteria-li label, .criteria-li p, .criteria-li i {
  color: #999999;
}
.criteria-li.active i:not(.icon) {
  display: block;
}
.criteria-li.active label, .criteria-li.active p, .criteria-li.active i {
  color: #5f63f2;
}
.criteria-li.active.border {
  border-color: #5f63f2 !important;
}

.text-overflow-hidden {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.profile-email-icon {
  height: 15px;
  width: 15px;
  position: absolute;
  right: -6px;
}

.scroll-height {
  max-height: 300px;
  overflow-y: auto;
}

.fc-more-link {
  display: none !important;
}

.fc-more-link-wo {
  border: 1px solid var(--primary) !important;
  color: var(--primary) !important;
  font-weight: bold !important;
}

.vendor-form .btn-lg {
  height: 55px;
}
.vendor-form .btn {
  border: 1px solid !important;
}
.vendor-form .form-control {
  min-height: 48px;
  font-size: 14px;
}
.vendor-form .form-control:focus {
  border-color: #2a2a69;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1) !important;
}
.vendor-form .form-label {
  color: #696f79;
  font-weight: 500;
}

.list-square li {
  list-style-type: square;
}

div:where(.swal2-container) {
  z-index: 9999 !important;
}

@keyframes sweetAlertAnimation {
  0% {
    transform: scale(0.7);
    opacity: 0;
  }
  45% {
    transform: scale(1.05);
    opacity: 1;
  }
  80% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}
.sweet-modal {
  animation: sweetAlertAnimation 0.4s ease-out;
}

.cancel-alert {
  display: none;
}

.force-enable.form-control[readonly] {
  background-color: #ffffff;
}
.force-enable.form-control[readonly][disabled] {
  background-color: #f4f5f7;
}

.months-selection .card-header .btn {
  padding: 0;
}
.months-selection .month-picker {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}
.months-selection .month-picker .month {
  padding: 5px 10px;
  text-align: center;
  border: 1px solid #ccc;
  cursor: pointer;
  border-radius: 5px;
  font-size: 14px;
}
.months-selection .month-picker .month.selected {
  border: 1px solid #5f63f2;
  color: white;
  color: #5f63f2;
}
.months-selection .month-picker .month.selected.current-month {
  border: 1px solid #5f63f2;
  color: white;
  color: #5f63f2;
}
.months-selection .month-picker .month.current-month {
  border-color: #ffc107 !important; /* Highlight color */
  color: #ffc107 !important;
}
.months-selection .month-picker .month.disabled {
  background-color: #f8f9fa;
  color: #ccc;
  cursor: not-allowed;
}
.months-selection .dropdown-menu {
  min-width: 300px;
  padding: 0;
  right: auto !important;
  top: 100% !important;
}
[lang=ar] .months-selection .dropdown-menu {
  left: auto !important;
  right: none;
}

.list-group.crm li {
  background: none;
  border: none;
  padding: 0;
}
.list-group.crm li a {
  padding: 0.8rem 1.25rem;
  color: #666d92;
}
.list-group.crm li.active a {
  background: #01A9F3;
  color: #ffffff;
  border-radius: 10px;
}

.user-type h1 {
  font-size: 1.5rem;
}
.user-type h1.main-heading {
  font-weight: bold;
  border-bottom: 1px solid #e1dfdf;
  padding-bottom: 1rem;
}
.user-type .field-icon {
  position: absolute;
  z-index: 2;
  top: 23px;
  right: 10px;
  transform: translate(-50%, -50%);
  color: inherit;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
[lang=ar] .user-type .field-icon {
  left: 20px;
  right: auto;
}
.user-type .list-group-item {
  border: 1px solid #2a2a69;
  background: #f5f9ff;
}
.user-type .list-group-item h5 {
  color: #2a2a69;
}
.user-type .list-group-item .text-muted {
  transition: all 0.3s ease-in-out;
  display: inline-block;
  height: 35px;
  width: 35px;
  background: #f5f9ff;
  border-radius: 50%;
  text-align: center;
  line-height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
}
.user-type .list-group-item .pentagon {
  width: 50px;
  height: 50px;
  min-width: 50px;
  background: #2a2a69;
  clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%);
  transition: all 0.3s ease-in-out;
}
.user-type .list-group-item .pentagon i,
.user-type .list-group-item .pentagon .feather {
  color: #ffffff;
}
.user-type .list-group-item .pentagon.active a {
  background: #01A9F3;
  color: #ffffff;
  border-radius: 10px;
}
.user-type .list-group-item.active, .user-type .list-group-item:hover {
  transform: scale(1.02);
  transition: all 0.1s ease-in-out !important;
  background: #ffffff;
}
.user-type .list-group-item.active .text-muted, .user-type .list-group-item:hover .text-muted {
  margin-right: -35px;
  background: #ffffff;
  border: 1px solid #2a2a69;
}
[lang=ar] .user-type .list-group-item.active .text-muted, [lang=ar] .user-type .list-group-item:hover .text-muted {
  margin-right: 0;
  margin-left: -35px;
}
.user-type .list-group-item.active .pentagon, .user-type .list-group-item:hover .pentagon {
  background: #eeeeee;
}
.user-type .list-group-item.active .pentagon i,
.user-type .list-group-item.active .pentagon .feather, .user-type .list-group-item:hover .pentagon i,
.user-type .list-group-item:hover .pentagon .feather {
  color: #2a2a69;
}

.login-image {
  background: url("../../img/vendor/login-image.png") no-repeat bottom center;
  background-size: 80%;
  width: 100%;
  height: 100%;
  background-color: #2a2a69;
  min-height: 350px;
}
.login-image h1,
.login-image p {
  color: #fff;
}

.d-center {
  display: flex;
  align-items: center;
}

.color-grey-1 {
  color: #8692a6;
}

.fill-gray-1 {
  fill: #8692a6;
}

.cursor-pointer {
  cursor: pointer;
}

.border-10 {
  border-radius: 10px;
}

.or-div:before {
  content: "";
  position: absolute;
  height: 1px;
  width: 100%;
  top: calc(50% - 1px);
  left: 0;
  width: 100%;
  background: #dddddd;
}
.or-div span {
  background: #fff;
  padding: 0px;
  padding: 5px 10px;
  position: relative;
  display: inline-block;
}

/*Main button Animation*/
.button-animation {
  pointer-events: auto;
  cursor: pointer;
  background: #e7e7e7;
  border: none;
  padding: 0.8rem 3rem;
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  position: relative;
  display: inline-block;
}

.button-animation::before,
.button-animation::after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.button--calypso {
  overflow: hidden;
  font-size: 1rem;
  border-radius: 0.85rem;
  color: #fff;
}

.button--calypso span {
  display: block;
  position: relative;
  z-index: 10;
}

.button--calypso:hover span {
  animation: MoveScaleUpInitial 0.3s forwards, MoveScaleUpEnd 0.3s forwards 0.3s;
}

@keyframes MoveScaleUpInitial {
  to {
    transform: translate3d(0, -105%, 0) scale3d(1, 2, 1);
    opacity: 0;
  }
}
@keyframes MoveScaleUpEnd {
  from {
    transform: translate3d(0, 100%, 0) scale3d(1, 2, 1);
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
.button--calypso::before {
  content: "";
  background: #17174b;
  width: 120%;
  height: 0;
  padding-bottom: 120%;
  top: -110%;
  left: -10%;
  border-radius: 50%;
  transform: translate3d(0, 68%, 0) scale3d(0, 0, 0);
}

.button--calypso:hover::before {
  transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
  transition: transform 0.4s cubic-bezier(0.1, 0, 0.3, 1);
}

.button--calypso::after {
  content: "";
  background: #17174b;
  transform: translate3d(0, -100%, 0);
  transition: transform 0.4s cubic-bezier(0.1, 0, 0.3, 1);
}

.button--calypso:hover::after {
  transform: translate3d(0, 0, 0);
  transition-duration: 0.05s;
  transition-delay: 0.4s;
  transition-timing-function: linear;
}

.z-index-999 {
  z-index: 999;
}

.flags-scroll ul {
  max-height: 250px;
  overflow-y: auto;
}
.flags-scroll input.form-control {
  min-height: 0;
}

.sign-btn i {
  transition: all 0.2s ease-in-out;
}
.sign-btn:hover i {
  margin-left: 5px;
}

.otp-inputs {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 1.5rem;
}
.otp-inputs input {
  width: 60px;
  height: 60px;
  text-align: center;
  font-size: 1.5rem;
  border: none;
  border: none;
  border-bottom: 3px solid #ddd;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}
.otp-inputs input:focus {
  border-color: #152b70;
  outline: none;
  color: #152b70;
  font-weight: bold;
}

.list-group.crm li {
  background: none;
  border: none;
  padding: 0;
}
.list-group.crm li a {
  padding: 0.8rem 1.25rem;
  color: #666d92;
}
.list-group.crm li.active a {
  background: #01A9F3;
  color: #ffffff;
  border-radius: 10px;
}

.crm .projectDatatable.project-table .table th:first-child, .crm .projectDatatable.project-table .table td:first-child {
  padding-left: 20px !important;
}
.crm .projectDatatable.project-table .table.radius-0 th {
  border-radius: 0px !important;
}
.crm .image-upload {
  padding: 0px;
  border: none;
  border-radius: 10px;
}
.crm .image-upload .upload-bg:before {
  height: 100%;
  width: 100%;
  background: rgba(1, 169, 243, 0.2);
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: absolute;
  border-radius: 50%;
}
.crm .image-upload .upload-bg:after {
  height: calc(100% + 20px);
  width: calc(100% + 20px);
  background: rgba(1, 169, 243, 0.2);
  content: "";
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  position: absolute;
  border-radius: 50%;
  transition: all 0.3s ease-in-out;
  animation: zoomInOut 2s infinite;
}
.crm .userDatatable table.new-header thead tr th {
  border-radius: 0 !important;
  border: none;
}
.crm .btn-primary {
  background: #01A9F3;
}
.crm .btn-primary.btn {
  border: 1px solid #10a2e3;
}
.crm .modal .close {
  border: 2px solid #152B70;
  border-radius: 8px;
  height: 25px;
  width: 25px;
  color: #152B70;
  opacity: 1;
  align-items: center;
  display: flex;
  justify-content: center;
}
.crm .modal .close .feather {
  color: #152B70;
}
.crm .modal .modal-footer .btn.btn-default {
  background: #8a95b7;
  color: #ffffff;
  border: 1px solid #a9b1c9;
}

.workforce_table td {
  vertical-align: top;
}

.workforce_table .v-align-middle {
  vertical-align: middle;
}

.draggable {
  cursor: grab; /* Hand open when idle */
}
.draggable:active {
  cursor: grabbing; /* Hand closed when dragging */
}

.crm .profile-group {
  display: flex;
  align-items: center;
}
.crm .profile-group .profile {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid white;
  overflow: hidden;
  margin-left: -10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
.crm .profile img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.crm .profile-group .extra {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid white;
  background-color: #F1F1F1;
  color: black;
  font-size: 14px;
  margin-left: -10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.mx-m10 {
  margin-right: -10px;
  margin-left: -10px;
}

.mx-m8 {
  margin-right: -8px;
  margin-left: -8px;
}

.btn.svg-20 svg {
  width: 20px;
  height: 20px;
}

.crm-leads .dropdown .dropdown-menu {
  top: 20px !important;
}
.crm-leads .dropdown .dropdown-menu a.dropdown-item {
  padding: 10px;
}

.dash-crm-logo {
  height: 200px; /* Adjust the height as needed */
  /* background: linear-gradient(to right, #01A9F3, #152B70);*/
  background: url("../../img/crm/crm-bg.jpg") 100%;
  background-position: 50% 0%;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
}
.dash-crm-logo .crm-logo:before {
  height: 120px;
  width: 120px;
  border-radius: 50%;
  background: rgba(1, 169, 243, 0.2);
  content: "";
  position: absolute;
  top: -15px;
  left: -15px;
  animation: zoomInOut 2s infinite;
  transform-origin: center; /* Ensure transform scales from the center */
}
@keyframes zoomInOut {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1); /* Adjust scale factor as needed */
  }
  100% {
    transform: scale(0.8);
  }
}
.dash-crm-logo .crm-logo .logo-inner {
  height: 90px;
  width: 90px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
}
.dash-crm-logo .crm-logo .logo-inner img {
  width: 60px;
}

.d-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.d-flex-center.d-inline-flex {
  display: inline-flex;
}

.crm-dashboard .dash-card {
  min-height: 180px;
}
.crm-dashboard .dash-card:before {
  height: 25px;
  width: 25px;
  position: absolute;
  top: 55px;
  left: 60px;
  content: "";
  border-radius: 50%;
  opacity: 0.5;
}
.crm-dashboard .dash-card:after {
  height: 15px;
  width: 15px;
  background: rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 70px;
  left: 90px;
  content: "";
  border-radius: 50%;
  opacity: 0.5;
}
.crm-dashboard .dash-card.bgGreenLight:before, .crm-dashboard .dash-card.bgGreenLight:after {
  background: #0CAF60;
}
.crm-dashboard .dash-card.bgRedLight:before, .crm-dashboard .dash-card.bgRedLight:after {
  background: #f78eac;
}
.crm-dashboard .dash-card.bgOrangeLight:before, .crm-dashboard .dash-card.bgOrangeLight:after {
  background: #FFA21D;
}
.crm-dashboard .dash-circle {
  bottom: -20px;
  right: -20px;
  border: 4px solid rgba(255, 255, 255, 0.9);
}
[lang=ar] .crm-dashboard .dash-circle {
  left: -20px;
  right: auto;
}

.new-calendar .fc-theme-standard, .new-calendar .fc-view-harness {
  height: 100%;
}
.new-calendar .fc-timegrid-slots .fc-timegrid-slot .fc-timegrid-slot-label-frame {
  top: 0 !important;
}
.new-calendar .fc-col-header-cell .fc-scrollgrid-sync-inner {
  text-align: center;
}
.new-calendar .fc-daygrid-day-frame {
  background: #ffffff;
}
.new-calendar .fc-daygrid-day-frame .fc-daygrid-day-events {
  margin: 0.5rem 0;
  max-width: 98%;
}
.new-calendar .fc-daygrid-day-frame .fc-daygrid-day-events .fc-daygrid-event {
  padding: 0.8rem 12px;
}
.new-calendar .fc-daygrid-day-frame .fc-daygrid-day-events .fc-daygrid-event-harness {
  margin-bottom: 0.5rem;
}
.new-calendar .fc-daygrid-day-frame .fc-daygrid-day-events .fc-daygrid-event-harness .fc-event-title {
  width: 100%;
  text-align: center;
  color: #01A9F3;
}
.new-calendar .fc-daygrid-day-frame .fc-daygrid-day-events .fc-daygrid-event-harness .fc-daygrid-event {
  background: rgba(4, 169, 241, 0.2) !important;
  border-color: #01A9F3 !important;
}
.new-calendar .fc-daygrid-day-frame .fc-daygrid-day-events .fc-daygrid-event-harness + .fc-daygrid-event-harness .fc-daygrid-event {
  background: rgba(255, 162, 29, 0.2) !important;
  border-color: #FFA21D !important;
}
.new-calendar .fc-daygrid-day-frame .fc-daygrid-day-events .fc-daygrid-event-harness + .fc-daygrid-event-harness .fc-event-title {
  width: 100%;
  text-align: center;
  color: #FFA21D;
}
.new-calendar .fc-toolbar-chunk {
  display: flex;
}
.new-calendar .fc-toolbar-chunk .fc-today-button {
  background: #01A9F3 !important;
  border: none;
}
.new-calendar .fc-toolbar-chunk > div {
  background: #01A9F3;
  display: flex;
  border-radius: 5px;
  align-items: center;
}
.new-calendar .fc-toolbar-chunk > div.fc-button-group {
  background: #e6f6fe !important;
}
.new-calendar .fc-toolbar-chunk > div.fc-button-group button.fc-button {
  color: #00003b;
  display: block !important;
  padding: 7px;
}
.new-calendar .fc-toolbar-chunk > div.fc-button-group button.fc-button.fc-button-active, .new-calendar .fc-toolbar-chunk > div.fc-button-group button.fc-button:hover {
  background: #01A9F3;
  color: #ffffff;
}
.new-calendar .fc-toolbar-chunk > div button {
  background: none;
  border: none;
  padding-right: 0;
  padding-left: 0;
}
.new-calendar .fc-toolbar-chunk > div button:hover {
  background: rgba(0, 0, 0, 0.1);
}
.new-calendar .fc-toolbar-chunk > div .fc-toolbar-title {
  font-size: 0.8rem;
  font-weight: normal;
  color: #ffffff;
  padding: 0 5px;
}

.labels-list .form-check {
  padding-left: 0;
}
.labels-list .form-check .form-check-input {
  display: none;
}
.labels-list .form-check label span {
  height: 30px;
  width: 30px;
  border-radius: 3px;
  display: inline-block;
  /*&:before{
      content: "";
      height:15px;
      width:15px;
      border:2px solid $newPrimary;
      border-radius:5px;
      left: -10px;
      top: 7px;
      position:absolute;
      background:#F9F5FF;
  }*/
}
.labels-list .form-check .form-check-input:checked + label:before {
  content: "";
  height: 5px;
  width: 2px;
  left: 12px;
  top: 14px;
  position: absolute;
  background: #ffffff;
  z-index: 9;
  border-radius: 5px;
  transform: rotate(-45deg);
}
.labels-list .form-check .form-check-input:checked + label:after {
  content: "";
  height: 8px;
  width: 2px;
  left: 16px;
  top: 11px;
  position: absolute;
  background: #ffffff;
  z-index: 9;
  border-radius: 5px;
  transform: rotate(45deg);
}

.switch-new .switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 25px;
  margin-bottom: 2px;
}
.switch-new .switch input:checked + .slider:before {
  position: absolute;
  content: "";
  height: 19px;
  width: 19px;
  right: 29px;
  bottom: 3px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.switch-new .switch input:checked + .slider {
  background: #01A9F3;
}
.switch-new .switch input + .slider:before {
  right: 22px;
  height: 19px;
  width: 19px;
}

@media only screen and (max-width: 575px) {
  .leads-details .card-header {
    flex-flow: row;
  }
}
.new-popup .close {
  border: 2px solid #152B70;
  border-radius: 8px;
  height: 25px;
  width: 25px;
  color: #152B70;
  opacity: 1;
  align-items: center;
  display: flex;
  justify-content: center;
}
.new-popup .close .feather {
  color: #152B70;
}

[lang=ar] .nav-stages li a i {
  transform: rotate(180deg);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .user-dropdown-wrapper {
    margin-left: 0;
    margin-top: 10px;
  }
  .user-dropdown-menu {
    position: static;
    box-shadow: none;
    border: 1px solid #eee;
  }
}
.user-dropdown-wrapper {
  position: relative;
  margin-left: 20px;
}

.user-dropdown-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #2b2b69;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  padding: 0.8rem;
}

.user-dropdown-trigger:hover {
  background-color: #3d405b;
}

.welcome-text {
  font-size: 14px;
  font-weight: 500;
}

.user-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 200px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: none;
  z-index: 1000;
  margin-top: 4px;
}

.user-dropdown-menu.active {
  display: block;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  color: #333;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.dropdown-item i {
  width: 16px;
  color: #666;
}

.phone-container {
  width: 100%;
  padding: 20px;
  background: white !important;
  border-radius: 10px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}

.iti {
  width: 100%;
}

.iti--separate-dial-code .iti__selected-flag {
  background-color: white !important;
}

.success-btn {
  background: #17a400;
  color: #fff;
}

.success-btn:hover {
  background: rgba(23, 164, 0, 0.6) !important;
}

.z-20 {
  z-index: 20;
}

.status-badge {
  padding: 6px 12px;
  font-weight: 500;
  border-radius: 30px;
  font-size: 14px;
  display: inline-block;
}
@media screen and (max-width: 576px) {
  .status-badge {
    font-size: 13px;
  }
}

small.status-badge {
  font-size: 80%;
}

.projects_list .status-badge {
  font-size: 12px;
  padding: 3px 15px;
  width: 80px;
  text-align: center;
}

small.status-badge {
  font-size: 80%;
}

.projects_list .status-badge {
  font-size: 12px;
  padding: 3px 15px;
  width: 80px;
  text-align: center;
}

.info-badge {
  background-color: #e6f1f6;
  color: #016395;
}

.warning-badge {
  background: #FAF6E6;
  color: #AB8600;
}

.error-badge {
  background: #FAE6E6;
  color: #B90000;
}

.success-badge {
  background: #F1F6EE;
  color: #63914A;
}

.update-badge {
  background: #EEEFF6;
  color: #4A4F91;
} /* Responsive Styles */
@media (max-width: 768px) {
  .user-dropdown-wrapper {
    margin-left: 0;
    margin-top: 10px;
  }
  .user-dropdown-menu {
    position: static;
    box-shadow: none;
    border: 1px solid #eee;
  }
}
.user-dropdown-wrapper {
  position: relative;
  margin-left: 20px;
}

.user-dropdown-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #2b2b69;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  padding: 0.8rem;
}

.user-dropdown-trigger:hover {
  background-color: #3d405b;
}

.welcome-text {
  font-size: 14px;
  font-weight: 500;
}

.user-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 200px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: none;
  z-index: 1000;
  margin-top: 4px;
}

.user-dropdown-menu.active {
  display: block;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  color: #333;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.dropdown-item i {
  width: 16px;
  color: #666;
}

.phone-container {
  width: 100%;
  padding: 20px;
  background: white !important;
  border-radius: 10px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}

.iti {
  width: 100%;
}

.iti--separate-dial-code .iti__selected-flag {
  background-color: white !important;
}

.success-btn {
  background: #17a400;
  color: #fff;
}

.success-btn:hover {
  background: rgba(23, 164, 0, 0.6) !important;
}

.z-20 {
  z-index: 20;
}

.status-badge {
  padding: 6px 12px;
  font-weight: 500;
  border-radius: 30px;
  font-size: 14px;
  display: inline-block;
}

.info-badge {
  background-color: #e6f1f6;
  color: #016395;
}

.warning-badge {
  background: #FAF6E6;
  color: #AB8600;
}

.error-badge {
  background: #FAE6E6;
  color: #B90000;
}

.success-badge {
  background: #F1F6EE;
  color: #63914A;
}

.update-badge {
  background: #EEEFF6;
  color: #4A4F91;
}

.chat-list .chat-item {
  color: #666d92;
  padding: 10px;
  background: #ffffff;
  transition: all 0.3s ease-in-out;
}
.chat-list .chat-item:hover {
  transform: scale(1.02);
  border-radius: 100px;
}
.chat-list .chat-item:hover span + span {
  color: #ffffff;
}
.chat-list .chat-item:hover.email {
  background: #01A9F3;
}
.chat-list .chat-item:hover.email .icon {
  color: #01A9F3;
}
.chat-list .chat-item:hover.whatsapp {
  background: #25D366;
}
.chat-list .chat-item:hover.whatsapp .icon {
  color: #25D366;
}
.chat-list .chat-item:hover.facebook {
  background: #2366b8;
}
.chat-list .chat-item:hover.facebook .icon {
  color: #2366b8;
}
.chat-list .chat-item:hover.instagram {
  background-image: linear-gradient(45deg, #feda75, #fa7e1e, #d62976, #962fbf, #4f5bd5);
}
.chat-list .chat-item:hover.instagram .icon {
  color: #d62976;
}
.chat-list .chat-item:hover.messanger {
  background-image: linear-gradient(45deg, #0099ff, #0077ff, #005eff);
}
.chat-list .chat-item:hover.messanger .icon {
  color: #005eff;
}
.chat-list .chat-item:hover .icon {
  background: #ffffff;
  border-radius: 100px;
}
.chat-list .chat-item.active {
  background: #152B70;
}
.chat-list .chat-item.active span:not(.icon) {
  color: #ffffff;
}
.chat-list .chat-item .icon {
  width: 30px;
  height: 30px;
  transition: all 0.3s ease-in-out;
  color: #ffffff;
}

.d-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.table-border-bottom tr:not(:last-child) td {
  border-bottom: 1px solid #F1F1F1;
}

.email-text {
  max-width: 350px;
}

.email-table {
  color: #00003B;
}
.email-table .form-check {
  padding: 0;
}

.bg-light-crm {
  background: #f0f5ff;
}

.bg-light-nprimary {
  background: rgba(1, 169, 243, 0.2);
}

.crm-form .form-group label {
  color: #293240;
}

.whatsapp-chatbox .chat-header {
  background: #f0f5ff;
  border-radius: 10px 10px 0 0;
  min-height: 53px;
  position: relative;
  z-index: 9;
}
.whatsapp-chatbox .chat-text-box__subtitle p {
  font-size: 13px;
}
.whatsapp-chatbox .chat-type-text__write {
  background: #ffffff;
}
.whatsapp-chatbox .chat-type-text__btn .btn-attachement {
  padding-top: 9px !important;
}
.whatsapp-chatbox .right-panel a, .whatsapp-chatbox .right-panel .nav-link {
  color: #152B70;
  font-size: 12px;
  padding-left: 0.5rem;
  padding-right: 0;
}
.whatsapp-chatbox .right-panel a i, .whatsapp-chatbox .right-panel .nav-link i {
  color: #01A9F3;
}
.whatsapp-chatbox .right-panel a.active, .whatsapp-chatbox .right-panel .nav-link.active {
  background: #01A9F3;
  color: #ffffff;
  border-radius: 10px;
  padding: 0.5rem;
}
.whatsapp-chatbox .right-panel a.active i, .whatsapp-chatbox .right-panel .nav-link.active i {
  color: #ffffff;
}
.whatsapp-chatbox .chatbox-list {
  max-height: 560px;
}
.whatsapp-chatbox .chatbox-list .last-message {
  max-width: 180px;
}
.whatsapp-chatbox .chatbox-list .unseen-count {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 1px 5px;
}
[lang=ar] .whatsapp-chatbox .chatbox-list .unseen-count {
  right: auto;
  left: 10px;
}

.rotate-90 {
  transform: rotate(90deg);
}

.whats-form .with-icon {
  position: relative;
}
.whats-form .with-icon .form-control {
  min-height: 40px;
}
.whats-form .select2-container--default .select2-selection--single, .whats-form .select2-container--default .select2-selection--multiple {
  min-height: 40px;
}
.whats-form .field-icon {
  top: 19px;
}

.fc-col-header, .fc-scrollgrid-sync-table, .fc-scrollgrid table, .fc, .fc .fc-daygrid-body {
  max-width: 100%;
}

.fb-panel {
  background: linear-gradient(-45deg, #f8fbff 0%, #ffffff 50%, #dce9fa 100%);
  min-height: 500px;
  position: relative;
}
.fb-panel > div {
  position: relative;
  width: 300px;
}
.fb-panel > div .icon-container {
  position: relative;
}
.fb-panel > div .btn-connect {
  position: relative;
  transition: box-shadow 0.3s ease-in-out, transform 0.2s ease-in-out;
}
.fb-panel > div .btn-connect:hover {
  animation: shadow-pulse 1s infinite alternate;
  transform: scale(1.05);
}
@keyframes shadow-pulse {
  0% {
    box-shadow: 0px 0px 10px rgba(0, 123, 255, 0.1);
  }
  100% {
    box-shadow: 0px 0px 25px rgba(0, 123, 255, 0.3);
  }
}
.fb-panel:has(.btn-connect:hover) .circle {
  animation: rotate360 10s linear infinite;
}
.fb-panel:has(.btn-connect:hover) .fb-card img {
  animation: bounce-up 1s ease-in-out infinite alternate;
}
.fb-panel:has(.btn-connect:hover) .osool-card img {
  animation: bounce-down 1s ease-in-out infinite alternate;
}
.fb-panel .fb-card, .fb-panel .osool-card {
  height: 100px;
  width: 100px;
}
.fb-panel .fb-card img, .fb-panel .osool-card img {
  margin-top: -25px;
}
.fb-panel .circle {
  width: 240px;
  height: 240px;
  border-radius: 50%;
  border: 2px solid #007bff;
  position: absolute;
  top: -40px;
  left: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.9s ease-in-out;
  transform: rotate(30deg);
}
.fb-panel .circle .arrow {
  position: absolute;
  width: 20px;
  height: 20px;
  border-top: 2px solid #007bff;
  border-right: 2px solid #007bff;
}
.fb-panel .circle .arrow-top {
  bottom: -9px;
  transform: rotate(220deg);
}
.fb-panel .circle .arrow-bottom {
  top: -10px;
  transform: rotate(43deg);
}

@keyframes rotate360 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes bounce-up {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-10px);
  }
}
@keyframes bounce-down {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(10px);
  }
}
.text-editor .tox-tinymce {
  max-height: 250px;
}
.text-editor .tox-statusbar__text-container {
  display: none;
}

.image-list ul.thumb-Images li {
  width: 100%;
  display: inline-block;
  vertical-align: top;
  margin-bottom: 10px;
  position: relative;
}
.image-list .img-wrap {
  position: relative;
  display: inline-block;
  font-size: 0;
  border: 1px solid #b9b6d1;
  width: 100%;
  border-radius: 10px;
  /* padding-right: 20px;*/
}
.image-list .img-wrap .thumb {
  height: 50px;
  width: 50px;
  border-radius: 10px;
}
.image-list .img-wrap .close {
  position: absolute;
  top: 20%;
  right: 10px;
  z-index: 100;
  padding: 5px 2px 2px;
  color: #999;
  font-weight: 100;
  height: 30px;
  width: 30px;
  border-radius: 30px;
  border: 1px solid #999;
  cursor: pointer;
  opacity: 0.5;
  font-size: 23px;
  line-height: 16px;
  border-radius: 50%;
  text-align: center;
  opacity: 1 !important;
}
.image-list .FileNameCaptionStyle {
  font-size: 12px;
  position: absolute;
  top: 32%;
  /* z-index: 99999; */
  left: 70px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 60%;
}

@media only screen and (min-width: 768px) {
  .crm-sidebar {
    position: sticky;
    top: 75px;
  }
}
.fc-col-header, .fc-scrollgrid-sync-table, .fc-scrollgrid table, fc {
  max-width: 100%;
}

.table.td-pl-0 td:first-child {
  padding-left: 0 !important;
}
.table.td-pl-0.table-address td {
  padding: 5px;
}

.btn-xs i.fs-18 {
  font-size: 18px !important;
}

.sales-section .delete-btn {
  position: absolute;
  top: 5px;
  right: 5px;
}
.sales-section .delete-btn .dust {
  position: absolute;
  top: -5px;
  left: 13px;
  width: 6px;
  height: 6px;
  background: #ffffff;
  border-radius: 50%;
  opacity: 0;
  transform: translateX(-50%) scale(0);
  transition: all 0.4s ease-in-out;
}
.sales-section .delete-btn:hover .dust {
  opacity: 1;
  transform: translate(-20%, 18px) scale(1);
}

.address-box, .new-shadow {
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.bg-nprimary-opacity {
  background: rgba(1, 169, 243, 0.5);
}

[lang=ar] .dataTables_paginate .pagination .page-item.previous .pagination-fa .fa-chevron-left {
  transform: rotate(180deg);
}
[lang=ar] .dataTables_paginate .pagination .page-item.next .pagination-fa .fa-chevron-right {
  transform: rotate(180deg);
}

.projectDatatable-title.fw-600 {
  font-weight: 600 !important;
}

@media screen and (max-width: 576px) {
  .nav-pills.site-pills.market-tabs .nav-link, .nav-tabs.site-pills.market-tabs .nav-link {
    font-size: 12px;
    padding: 10px 8px;
  }
}
.nav-pills.site-pills .nav-link, .nav-tabs.site-pills .nav-link {
  border: none;
  background: none;
}
.nav-pills.site-pills .nav-link.active, .nav-tabs.site-pills .nav-link.active {
  background: #01A9F3;
  color: #ffffff;
}

.market-item .market-img {
  height: 150px;
}
.details .market-item .market-img {
  height: 200px;
}
.market-item .market-img .rating {
  background: #acc4cb;
}
.market-item.details .market-img {
  height: 250px;
}
.market-item .top-content .labels {
  max-height: 90px;
  overflow-y: hidden;
}
.market-item .new-alert {
  position: absolute;
  width: calc(100% - 1rem);
  bottom: 47px;
  min-height: 30px;
  padding: 10px;
}
.market-item .new-alert .close-alert {
  position: absolute;
  right: 10px;
}
.market-item .new-alert:not(.active) {
  width: 40px;
  right: 0.5rem;
  transition: all 0.3s ease-in-out;
}
.market-item .new-alert:not(.active) .close-alert, .market-item .new-alert:not(.active) .message {
  display: none;
}

.gap-5 {
  gap: 5px;
}

.percent_icon {
  align-items: center;
  display: flex !important;
  height: 50px;
  padding-left: 7px;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  border-left: 1px solid #ddd;
}

.market-ratings .stars-rating .star-icon {
  font-size: 20px;
}
.market-ratings .stars-rating span.active, .market-ratings .stars-rating .star-icon.active {
  color: #01A9F3;
}
.market-ratings .comment .stars-rating .star-icon {
  font-size: 18px;
}
.market-ratings .comment .stars-rating span.active, .market-ratings .comment .stars-rating .star-icon.active {
  color: #152B70;
}
.market-ratings h1 {
  font-size: 50px;
}

.blurred-bg {
  background: rgba(255, 255, 255, 0.2); /* Light translucent background */
  backdrop-filter: blur(10px); /* Applies blur effect */
  -webkit-backdrop-filter: blur(10px); /* For Safari support */
}

.chat-header .search-bar {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9;
}
.chat-header .search-bar.active {
  display: block;
}
.chat-header .search-bar .close-btn {
  position: absolute;
  top: 15px;
  right: 10px;
}

.text-inprogress {
  color: #5eb1ff;
}

.bg-inprogress {
  background: #5eb1ff;
}

.bg-close {
  background: #64748B;
}

.text-close {
  color: #64748B;
}

.bg-reopen {
  background: #A91CD4;
}

.text-reopen {
  color: #A91CD4;
}

.bids-list .card {
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 5px;
  transition: all 0.3s ease-in-out;
}
.bids-list .card:hover {
  box-shadow: 0 5px 30px rgba(146, 153, 184, 0.5);
}
.bids-list .card .card-header {
  padding: 0;
  min-height: 0;
}
.bids-list .card .card-header button {
  min-height: 60px;
}

.image-gallery {
  display: flex;
  gap: 10px;
}

.image-gallery img {
  cursor: pointer;
  border-radius: 12px;
}

.lightbox {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  justify-content: center;
  align-items: center;
  z-index: 99999;
}
.lightbox img {
  max-width: 90%;
  max-height: 80vh;
  border-radius: 12px;
}
.lightbox .close {
  position: absolute;
  color: white;
  font-size: 24px;
  cursor: pointer;
  background: none;
  box-shadow: none;
  border: none;
  top: 20px;
  right: 20px;
  opacity: 1;
}
[lang=ar] .lightbox .close {
  left: 20px;
  right: auto;
}
.lightbox .download {
  position: absolute;
  color: white;
  font-size: 24px;
  cursor: pointer;
  background: none;
  box-shadow: none;
  border: none;
  top: 20px;
  right: 50px;
  line-height: 25px;
}
[lang=ar] .lightbox .download {
  left: 50px;
  right: auto;
}
.lightbox .prev {
  position: absolute;
  color: white;
  font-size: 24px;
  cursor: pointer;
  background: none;
  box-shadow: none;
  border: none;
  top: 50%;
  transform: translateY(-50%);
  left: 20px;
}
.lightbox .next {
  position: absolute;
  color: white;
  font-size: 24px;
  cursor: pointer;
  background: none;
  box-shadow: none;
  border: none;
  right: 20px;
}

.search-click input {
  position: absolute;
  right: 0;
  left: auto;
  width: auto;
}
[lang=ar] .search-click input {
  right: auto;
  left: 0;
}
.search-click .input-container {
  display: none;
}
.search-click .close-search {
  right: 15px;
  top: 12px;
  z-index: 9;
}

.new-pagination {
  /* .new-pagination-section > span{
       &:not(:first-child):not(:last-child){
           display:none;
       }
   } */
}
.new-pagination span button {
  height: 30px;
  min-width: 30px;
  border-radius: 5px;
  margin: 2.5px;
}
.new-pagination span button.current-page {
  background: #F7F7FE;
  color: #01A9F3;
}

.market-dropdown.dropdown-menu .sort-list .dropdown-item {
  padding: 5px 0;
}
.market-dropdown.dropdown-menu .sort-list .dropdown-item:hover {
  background: none;
}
.market-dropdown.dropdown-menu .sort-list .dropdown-item.active {
  font-weight: bold;
  background: none;
}
.market-dropdown.dropdown-menu.filter-dropdown {
  width: 300px;
}
.market-dropdown.dropdown-menu .filter-list {
  max-height: 150px;
  overflow-y: auto;
}
.market-dropdown.dropdown-menu .filter-list a {
  padding: 5px 10px;
}
.market-dropdown.dropdown-menu .filter-list a.active {
  background: #152B70;
  color: #fff;
}
.market-dropdown.dropdown-menu .filter-list label {
  cursor: pointer;
}
.market-dropdown.dropdown-menu .filter-list input {
  display: none;
}
.market-dropdown.dropdown-menu .filter-list input:checked + label {
  background: #152B70;
  color: #fff;
}

.eligibility-page ul li {
  color: rgba(37, 55, 63, 0.7);
}
.eligibility-page h1, .eligibility-page h2, .eligibility-page h3, .eligibility-page h4, .eligibility-page h5, .eligibility-page h6 {
  margin-bottom: 1rem;
}

@media only screen and (max-width: 576px) {
  .user-type.container {
    margin: 1rem;
  }
}
[lang=ar] .rotate-ar-y {
  transform: rotate(180deg);
}

.border-osool {
  border: 1px solid #152B70;
}

.cards-view {
  display: grid;
  grid-template-columns: calc(25% - 20px) calc(25% - 20px) calc(25% - 20px) 25%;
}

@media only screen and (max-width: 768px) {
  .cards-view {
    grid-template-columns: calc(50% - 10px) 50%;
  }
}
@media only screen and (max-width: 567px) {
  .cards-view {
    grid-template-columns: 100%;
  }
}
/* Custom CSS for slide-in dropdown */
.slide-dropdown .dropdown-menu {
  display: block;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, transform 0.3s ease;
  transform: translateX(-100%);
}

.slide-dropdown.show .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

.table-search input, .table-search button {
  height: 40px;
  min-height: 0;
}
.table-search .field-icon {
  top: 19px;
}

.status-badge {
  display: inline-block;
}

.filter-dropdown .form-control, .filter-dropdown .select2-container--default .select2-selection--single {
  min-height: 40px;
}

.filter-button {
  width: 45px;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}
.filter-button .filter-click {
  opacity: 0;
  width: 120px;
  transition: all 0.3s ease-in-out;
  transform: translateX(100%); /* Move it to the left */
  position: absolute;
}
.filter-button.active {
  width: 120px;
  background: #fff;
  box-shadow: none !important;
}
.filter-button.active > .iconsax {
  display: none;
}
.filter-button.active .filter-click {
  opacity: 1;
  transform: translateX(0);
}

.pieChart .pie-chart-legend {
  position: absolute;
  top: calc(50% - 20px);
  left: 50%;
  text-align: center;
  /* width: 200px; */
  margin-bottom: 0px;
  display: inline-block;
  transform: translate(-50%);
}

.table-project-dashboard tr td {
  height: 90px;
}

.table-duplicate .form-check-input {
  margin-top: 0.2rem;
}
.table-duplicate thead th {
  border-radius: 5px 5px 0 0;
  border-right: none;
}
.table-duplicate th {
  background: #f8f9fb;
  border-bottom: 1px solid #f1f2f6;
  border-right: 1px solid #f1f2f6;
  border-top: none;
}
.table-duplicate > td {
  border-bottom: 1px solid #f1f2f6;
  border-top: none;
}

.gantt-controls .btn-group .btn-info.active {
  background-color: #585555;
  border-color: #585555;
}

.upload-box {
  border: 2px dashed #01A9F3;
  padding: 20px;
  margin: 50px auto;
  text-align: center;
  position: relative;
  font-family: sans-serif;
}
.upload-box input {
  display: none;
}
.upload-box label span {
  color: #7f6fff;
  cursor: pointer;
}
.upload-box p {
  margin-top: 8px;
  color: #666;
  font-size: 14px;
}

.import-wrapper .progress-wrapper {
  margin-top: 15px;
  display: none;
}
.import-wrapper .progress-wrapper .file-details #file-name {
  font-weight: 600;
}
.import-wrapper .progress-wrapper .file-details #file-size {
  font-size: 14px;
  color: #999;
}
.import-wrapper .progress-wrapper .progress-bar {
  width: 100%;
  height: 8px;
  background: #eee;
  border-radius: 4px;
  overflow: hidden;
}
.import-wrapper .progress-wrapper .progress-bar .progress-fill {
  height: 100%;
  width: 0;
  background-color: #7f6fff;
  transition: width 0.2s ease-in-out;
}
.import-wrapper .button-group {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 10px;
}
.import-wrapper .button-group button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}
.import-wrapper .button-group .cancel-btn {
  background-color: #f2f2f2;
  color: #333;
}
.import-wrapper .button-group #import-btn {
  background-color: #7f6fff;
  color: white;
}
.import-wrapper .button-group #import-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.proposal-timeline .timeline-icons {
  position: relative;
}
.proposal-timeline .timeline-icons:before, .proposal-timeline .timeline-icons:after {
  right: -32px;
}
[lang=ar] .proposal-timeline .timeline-icons:before, [lang=ar] .proposal-timeline .timeline-icons:after {
  right: auto;
  left: -32px;
}
.proposal-timeline .timeline-icons:after {
  left: -32px;
}
[lang=ar] .proposal-timeline .timeline-icons:after {
  left: auto;
  right: -32px;
}
.proposal-timeline .timeline-icons:before, .proposal-timeline .timeline-icons:after {
  content: "";
  height: 2px;
  background: #e4e8f7;
  width: calc(50% + 5px);
  position: absolute;
  top: 22px;
  z-index: 1;
}
.proposal-timeline .timeline-icons.active.primary:after {
  display: none;
}
.proposal-timeline .timeline-icons.active.primary:before {
  background: var(--primary);
  top: 23px;
}
.proposal-timeline .timeline-icons.active.warning:before, .proposal-timeline .timeline-icons.active.warning:after {
  background: var(--warning);
}
.proposal-timeline .timeline-icons.active.osool:after {
  background: #152B70;
}
.proposal-timeline .timeline-icons.active.osool:before {
  display: none;
}
.proposal-timeline .timeline-icons .timeline-dot {
  position: absolute;
  top: 22px;
  width: 50px;
}

.bg_circle_shape1 {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  position: absolute;
  top: 20%;
  left: 22%;
}
[lang=ar] .bg_circle_shape1 {
  right: 22%;
  left: auto;
}

.bg_circle_shape2 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  position: absolute;
  top: 30%;
  left: 35%;
}
[lang=ar] .bg_circle_shape2 {
  right: 35%;
  left: auto;
}

.market-content .date-target {
  position: absolute;
  left: calc(50% - 11px);
  padding: 2px;
  top: 10px;
}
.market-content .description {
  max-height: 100px;
  overflow: hidden;
  display: block;
}

.projects-list .description {
  display: -webkit-box;
  -webkit-line-clamp: 2; /* Number of lines to show */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text_black {
  color: #272b41;
}

@media screen and (max-width: 576px) {
  .company_info_btns a {
    padding: 0px 8px;
    font-size: 12px;
  }
  .company_info_btns button {
    padding: 0 8px;
    font-size: 12px;
    white-space: nowrap;
  }
}

.opacity-1 {
  opacity: 0.1;
}

.opacity-2 {
  opacity: 0.2;
}

.opacity-3 {
  opacity: 0.3;
}

.opacity-4 {
  opacity: 0.4;
}

.opacity-5 {
  opacity: 0.5;
}

.opacity-6 {
  opacity: 0.6;
}

.opacity-7 {
  opacity: 0.7;
}

.opacity-8 {
  opacity: 0.8;
}

.opacity-9 {
  opacity: 0.9;
}

.gap-15 {
  gap: 15px;
}

.gap-20 {
  gap: 20px;
}

.paging-option .select2-container--default .select2-selection--single, .paging-option .select2-container--default .select2-selection--multiple {
  min-height: 40px;
}
.paging-option .select2-container--default .select2-selection--single .select2-selection__arrow {
  top: 22px;
}

/*========NHC Integration=========*/
.new-radio {
  position: relative;
}
.new-radio input {
  display: none;
}
[lang=ar] .new-radio input {
  left: auto;
  right: 10px;
}
[lang=ar] .new-radio input + label {
  padding: 0.7em 1em 0.7em 1em;
}
.new-radio input + label {
  padding: 0.5em 1em 0.5em 1em;
  display: flex;
  align-items: center;
  font-weight: 600;
  min-width: 160px;
}
.new-radio input + label i {
  height: 20px;
  width: 20px;
  background: #ffffff;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  border: 1px solid #00aca9;
  border-radius: 50%;
}
.new-radio input:checked + label {
  border-color: #00aca9 !important;
  color: #00aca9;
}
.new-radio input:checked + label i {
  display: inline-flex;
  background: #00aca9;
  color: #ffffff;
}

.nhc .vide-background video {
  position: absolute;
  top: 50%;
  left: 50%;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  z-index: 0;
  transform: translate(-50%, -50%);
  object-fit: cover;
}

.blurred-glass {
  background: rgba(255, 255, 255, 0.3); /* semi-transparent white */
  backdrop-filter: blur(5px); /* applies blur to background behind the element */
  -webkit-backdrop-filter: blur(5px); /* for Safari */
  /*border: 1px solid rgba(255, 255, 255, 0.2);*/
}

.bg-subscription:before {
  content: "";
  position: absolute;
  inset: 0;
  background: #E9F8FF;
  background: linear-gradient(46deg, rgb(233, 248, 255) 0%, rgb(255, 255, 255) 100%);
  /* background: url("/home/<USER>/home/<USER>") no-repeat center center
      fixed; */
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  z-index: -1;
  transform: scaleX(-1); /* default */
}
[lang=ar] .bg-subscription:before {
  transform: scaleX(1); /* default */
}

.radius-xxl {
  border-radius: 20px;
}

.bg-package2, .package-2 {
  background: #0d8193;
  color: #fff;
}

.text-package2 {
  color: #0d8193;
}

.bg-gray-700 {
  background: #6c757d;
  color: #fff;
}

.bg-gray-500 {
  background: #adb5bd;
  color: #fff;
}

@media only screen and (min-width: 991px) {
  div.bg-package2-outer {
    margin-top: -3rem;
  }
}

.bg-package2-outer {
  background: #00aca9;
  color: #fff;
}
.bg-package2-outer.border-2 {
  border: 2px solid #00aca9;
}
.bg-package2-outer.btn {
  border-color: #00aca9 !important;
}
.bg-package2-outer.btn:hover {
  background: #078b88;
}

.text-cyan {
  color: #055160;
}

.package ul li {
  margin-bottom: 1rem;
}
[lang=en] .package ul li {
  font-size: 0.9rem;
}
.package .btn i {
  transition: all 0.5s ease-in-out;
}
.package .card-title {
  height: 5rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.typing {
  width: 22ch; /* Adjust to match text length */
  white-space: nowrap;
  overflow: hidden;
  font-size: 2rem;
  animation: typing 2s steps(22);
}
[lang=ar] .typing {
  line-height: 40px;
}

/* Typing animation */
@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 22ch;
  }
}
.pricing-table thead th {
  border: 0;
  padding: 1rem 0.5rem;
  white-space: nowrap;
}
.pricing-table tbody td {
  padding: 0.8rem;
}

.vendor-form input::placeholder {
  color: #aaaaaa;
}
.vendor-form .form-label {
  font-size: 14px;
}

.subscription .bg-light-blue {
  background-color: #E0F5FE;
}
.subscription .number-input button {
  font-size: 1.25rem;
  border: none;
  background: none;
  cursor: pointer;
  outline: none;
}
.subscription .payment-card {
  overflow: hidden;
}
.subscription .payment-card input.form-control {
  border: none;
  border-radius: 0 !important;
}
.subscription .payment-card input.form-control.border-top:first-child {
  border-bottom-left-radius: 15px;
}
.subscription .payment-card input.form-control.border-top:last-child {
  border-bottom-right-radius: 15px;
}
.subscription .checkout-form input.form-control {
  min-height: 48px;
}
.subscription .checkout-form .wpwl-control {
  display: inline-block;
  width: 100%;
  min-height: 40px;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.subscription .checkout-form .wpwl-control:not(select) {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.subscription .checkout-form .wpwl-wrapper-cardHolder {
  display: flex;
}
.subscription .checkout-form .wpwl-wrapper > .wpwl-icon {
  top: 0.45em;
}
[lang=ar] .subscription .checkout-form .wpwl-wrapper > .wpwl-icon {
  right: auto;
  left: 0.5625em;
}
.subscription .checkout-form .wpwl-label {
  font-size: 14px;
}
.subscription .checkout-form .wpwl-button {
  width: 100%;
  border-radius: 0.5rem;
}
.subscription .checkout-form .wpwl-button.wpwl-button-pay {
  min-height: 48px;
  background: #00aca9;
}
.subscription .checkout-form .wpwl-button.wpwl-button-pay:hover {
  opacity: 0.9;
}

.countries .dropdown-toggle::after {
  vertical-align: middle;
  margin-left: 10px;
  right: 20px;
  position: absolute;
  top: 23px;
  display: none;
}
.countries .btn-country {
  min-height: 48px;
  border-color: #dee2e6;
}

.package-1 {
  background: rgba(0, 172, 169, 0.75);
}

.text-package-1 {
  color: #7C6990;
}

.package-btn .btn.bg-package2.active {
  background: #01A9F3;
  box-shadow: none;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.package-3 {
  background: rgba(0, 172, 169, 0.75);
}

.btn.nhc-back {
  border: 2px solid #26313D;
  color: #26313D;
  transition: all 0.3s ease-in-out;
}
.btn.nhc-back i {
  transition: all 0.3s ease-in-out;
  display: inline-block;
}
.btn.nhc-back:hover {
  background: #26313D;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

body.nhc-partnership, .nhc-partnership, .nhc {
  background: #ffffff;
  font-family: "IBM Plex Sans Arabic", sans-serif !important;
  font-weight: normal;
  font-style: normal;
}
[lang=ar] body.nhc-partnership, [lang=ar] .nhc-partnership, [lang=ar] .nhc {
  font-family: "IBM Plex Sans Arabic", sans-serif !important;
}
[lang=ar] body.nhc-partnership h1, [lang=ar] body.nhc-partnership h2, [lang=ar] body.nhc-partnership h3, [lang=ar] body.nhc-partnership h4, [lang=ar] body.nhc-partnership h5, [lang=ar] body.nhc-partnership h6, [lang=ar] body.nhc-partnership p, [lang=ar] body.nhc-partnership a, [lang=ar] .nhc-partnership h1, [lang=ar] .nhc-partnership h2, [lang=ar] .nhc-partnership h3, [lang=ar] .nhc-partnership h4, [lang=ar] .nhc-partnership h5, [lang=ar] .nhc-partnership h6, [lang=ar] .nhc-partnership p, [lang=ar] .nhc-partnership a, [lang=ar] .nhc h1, [lang=ar] .nhc h2, [lang=ar] .nhc h3, [lang=ar] .nhc h4, [lang=ar] .nhc h5, [lang=ar] .nhc h6, [lang=ar] .nhc p, [lang=ar] .nhc a {
  font-family: "IBM Plex Sans Arabic", sans-serif !important;
}

.checkout-card {
  position: relative;
}
.checkout-card:before {
  content: "";
  height: 70%;
  width: 100%;
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  position: absolute;
  bottom: 0;
  left: 0;
}
.checkout-card .checkout-payment h6 {
  font-size: 14px;
}

.colorBackDark {
  color: #26313D;
}

.lh-15 {
  line-height: 15px;
}

.lh-50 {
  line-height: 50px;
}

.table.valign-middle thead th {
  vertical-align: middle;
}
.table.table-ranges tr td, .table.table-ranges tr th {
  padding: 0px;
  text-align: center;
  color: #152B70;
}
.table.table-ranges tr td > span, .table.table-ranges tr th > span {
  padding: 10px;
  display: inline-block;
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  width: 100%;
  line-height: 25px;
  min-height: 50px;
  color: #152B70;
}
.table.table-ranges tr td:first-child > span, .table.table-ranges tr th:first-child > span {
  border-left: 1px solid #ddd;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}
.table.table-ranges tr td:last-child > span, .table.table-ranges tr th:last-child > span {
  border-right: 1px solid #ddd;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}
.table.table-ranges tr td {
  padding: 5px 0;
}
.table.table-ranges tr td > span {
  border-left: 1px solid #ddd;
  border-right: 1px solid #ddd;
  border-radius: 10px;
}
.table.table-ranges tr td input.form-control {
  border: none !important;
  text-align: center;
  min-height: 20px;
  height: 26px;
}
.table.table-ranges tr .select2-container--default .select2-selection--single, .table.table-ranges tr .select2-container--default .select2-selection--multiple {
  min-height: 20px;
  border: none;
}

.panalty .form-control {
  min-height: 20px;
  height: 33px;
  border: 0 !important;
}

.z-999 {
  z-index: 999;
}

.accordion_header {
  background-color: #f8f9fb;
  padding: 10px;
  border-radius: 10px;
}

.Kpi_popup_checkbox .form-check-label {
  margin-bottom: 4px;
  margin-left: 8px;
  margin-top: 3px;
}
.Kpi_popup_checkbox .form-check-input {
  width: 15px;
  height: 15px;
}

.property_form_container .form-group .form-control.border-0 {
  border: none !important;
}

.select-borderless .select2-container--default .select2-selection--single {
  border: none;
  min-height: 20px;
}
.select-borderless .select2-container--default .select2-selection--single .select2-selection__rendered {
  padding: 0 !important;
}
.select-borderless .form-control {
  border: none;
  min-height: 20px;
  height: 30px;
}

.accordion button:not(.collapsed) i {
  transform: rotate(180deg);
}

.text-nhc {
  color: #00aca9;
}

.skeleton-text {
  font-size: 1.2rem;
  background: linear-gradient(90deg, #cccccc 25%, #eeeeee 50%, #aaaaaa 75%);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
.project-description {
  max-height: 150px;
  overflow-y: auto;
}

.bg-nhc {
  background-color: #00aca9;
}

.bg-thankyou {
  background: rgba(211, 211, 211, 0.3);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.thankyou-page .btn i {
  position: absolute;
  left: 0;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
[lang=ar] .thankyou-page .btn i {
  left: auto;
  right: 0;
}
.thankyou-page .btn span {
  transition: all 0.3s ease-in-out;
}
.thankyou-page .btn:hover i {
  left: 20px;
  opacity: 1;
}
[lang=ar] .thankyou-page .btn:hover i {
  left: auto;
  right: 20px;
}
.thankyou-page .btn:hover span {
  margin-left: 20px;
}
[lang=ar] .thankyou-page .btn:hover span {
  margin-left: 0;
  margin-right: 20px;
}
.thankyou-page .btn.btn-contact {
  color: #ffffff;
}
.thankyou-page .btn.btn-contact:hover {
  background: #fff;
  color: #404040 !important;
}
[lang=ar] .thankyou-page .btn.btn-next i {
  transform: rotate(180deg);
}
.thankyou-page .btn.btn-next:hover {
  background: #01A9F3 !important;
  color: #ffffff !important;
}

.sticky-sidebar {
  position: sticky;
  top: 80px; /* Adjust based on your layout */
  z-index: 1;
}

.file-upload-new .image-box {
  padding-right: 10px;
  min-height: 55px;
}
.file-upload-new .image-box img {
  width: 50px;
  border-radius: 10px;
}

.bg-gray-light {
  background: rgba(21, 43, 112, 0.5);
}

.ui-widget-content .ui-datepicker-unselectable.ui-state-disabled.undefined .ui-state-default {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  margin: 4px;
  line-height: 2rem;
}

.advance-tab .months-selection .dropdown-menu {
  top: auto !important;
  bottom: 100%;
}

.proposal-timeline.timeline-new .timeline-div:first-child .timeline-icons:after {
  display: none;
}
.proposal-timeline.timeline-new .timeline-div:last-child .timeline-icons:before {
  display: none;
}

.req-img-section {
  max-height: 350px;
  overflow-y: auto;
}
.req-img-section .req-image {
  overflow: hidden;
}
.req-img-section .req-image img {
  opacity: 0;
}

/*# sourceMappingURL=new-style.css.map */
