function showLoader() {
    document.getElementById("overlayer").style.display = "block";
    let loaderElements = document.getElementsByClassName("loader-overlay");
    if (loaderElements.length > 0) {
        loaderElements[0].style.display = "block";
    }
}

function hideLoader() {
    document.getElementById("overlayer").style.display = "none";

    let loaderElements = document.getElementsByClassName("loader-overlay");
    if (loaderElements.length > 0) {
        loaderElements[0].style.display = "none";
    }
}

window.addEventListener('show-loader', event => {
    showLoader();
});


window.addEventListener('hide-loader', event => {
    setTimeout(() => {
        hideLoader();
    }, 1000);
});



document.addEventListener('DOMContentLoaded', function() {
    if (!window.toastrEventRegistered) {
        window.toastrEventRegistered = true;
        window.addEventListener("show-toastr", event => {
            toastr.options = {
                closeButton: true,
                progressBar: true,
                positionClass: "toast-top-right",
                timeOut: "3000"
            };

            if (event.detail.type === "success") {
                toastr.success(event.detail.message);
            } else if (event.detail.type === "error") {
                toastr.error(event.detail.message);
            }
        });
    }
})

function toggleModal(modalId, action) {
    if (action === 'show') {
        $('#' + modalId).modal('show');
    } else if (action === 'hide') {
        $('#' + modalId).modal('hide');
    }
}
window.addEventListener('open-modal', event => {
    toggleModal(event.detail.modalId, 'show');
});

window.addEventListener('close-modal', event => {
    toggleModal(event.detail.modalId, 'hide');
});




