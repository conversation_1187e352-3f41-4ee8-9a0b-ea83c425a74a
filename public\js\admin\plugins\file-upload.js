//////////////////// HTML //////////////////

// <input type="file" id="imageInput" multiple accept="image/*" class="d-none">
// <label class="btn btn-default bg-new-primary" for="imageInput">
//     <i class="las la-plus fs-16"></i> Upload Files </label>
// <div id="imagePreview" class="preview-container"></div>


//////////////////// CSS //////////////////
// .preview-container {
//   display: flex;
//   flex-wrap: wrap;
//   gap: 10px;
//   margin-top: 10px;
// }

// .image-box {
//     position: relative;
//     width: 100%;
//     padding: 5px;
// }

// .image-box img {
//     width: 40px;
//     height: 40px;
//     object-fit: cover;
//     border: 1px solid #ccc;
//     border-radius: 5px;
// }

// .remove-btn {
//     color: #cccccc;
//     border: none;
//     border-radius: 50%;
//     font-size: 20px;
//     width: 20px;
//     height: 20px;
//     cursor: pointer;
//     border: 1px solid #ccc;
//     background: #ffffff;
// }

////////////////// JS //////////////////

$(document).ready(function () {
  const maxFiles = 1;               // Max number of files allowed
  const maxSizeMB = 2;              // Max file size in MB
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']; // Allowed MIME types

  $('#imageInput').on('change', function () {
    const files = Array.from(this.files);
    $('#imagePreview').empty(); // Clear previous previews

    if (files.length > maxFiles) {
      alert(`You can upload only up to ${maxFiles} images.`);
      this.value = ''; // Clear input
      return;
    }

    files.forEach((file, index) => {
      if (!allowedTypes.includes(file.type)) {
        alert(`File type not allowed: ${file.name}`);
        return;
      }

      if (file.size > maxSizeMB * 1024 * 1024) {
        alert(`File too large (${file.name}). Max size is ${maxSizeMB}MB.`);
        return;
      }

      const reader = new FileReader();
      reader.onload = function (e) {
        const imgBox = $(`
        <div class="image-box d-flex justify-content-between align-items-center border radius-xl" data-name="${file.name}" data-size="${file.size}">
          <div class="d-flex align-items-center gap-10">
            <img src="${e.target.result}" alt="Image Preview">
            <span>${file.name}</span>
          </div>
          <div class="d-flex gap-10">
            <a class="download-btn d-center p-2 bg-new-primary text-white rounded-circle text-light cursor-pointer"
               href="${e.target.result}" download="${file.name}">
              <i class="iconsax" icon-name="download-1"></i>
            </a>
            <a class="remove-btn d-center p-2 bg-loss text-white rounded-circle text-light cursor-pointer">
              <i class="iconsax" icon-name="x"></i>
            </a>
          </div>
        </div>
      `);
        $('#imagePreview').append(imgBox);
      };
      reader.readAsDataURL(file);
    });
  });

  $('#imagePreview').on('click', '.remove-btn', function () {
    $(this).closest('.image-box').remove();
  });
});