$(document).ready(function () {
    // Scroll to section on sidebar link click with 50px gap before top
    $('.nav-stages a').on('click', function (e) {
        e.preventDefault();
        const target = $($(this).attr('href'));
        if (target.length) {
            const offsetGap = 50; // adjust the gap from top
            $('html, body').animate({
                scrollTop: target.offset().top - offsetGap
            }, 500);
        }
    });

    // Change active link on scroll
    const sections = $('.scroll-right > div'); // your right side sections
    const navLinks = $('.nav-stages li');

    $(window).on('scroll', function () {
    const scrollPos = $(document).scrollTop();
    const windowHeight = $(window).height();
    const docHeight = $(document).height();
    let currentId = "";

    sections.each(function () {
        const $section = $(this);
        const sectionTop = $section.offset().top - 50; // offset for early activation
        const sectionHeight = $section.outerHeight();

        if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
            currentId = $section.attr('id');
            return false; // exit loop
        }
    });

    // If user is at the very bottom of the page, force last section to be active
    if ($(window).scrollTop() + windowHeight >= docHeight - 1) {
        currentId = sections.last().attr('id');
    }

    if (currentId) {
        navLinks.removeClass('active');
        $('.nav-stages a[href="#' + currentId + '"]').parent().addClass('active');
    }
});
});


// HTML

// div class="row">
//             <div class="col-md-3 pr-md-0 mb-3 mb-md-0">
//     <div class="card  sticky-sidebar">
//         <ul class="list-group crm nav-stages">
//             <li class="list-group-item active">
//                 <a href="#info-section" class="d-flex justify-content-between align-items-center"> Info <i class="las la-angle-right"></i> </a>
//             </li>
//             <li class="list-group-item">
//                 <a href="#attachments-section" class="d-flex justify-content-between align-items-center"> Attachements <i class="las la-angle-right"></i> </a>
//             </li>
//             <li class="list-group-item">
//                 <a href="#comments-section" class="d-flex justify-content-between align-items-center"> Comments <i class="las la-angle-right"></i> </a>
//             </li>
//             <li class="list-group-item">
//                 <a href="#note-section" class="d-flex justify-content-between align-items-center"> Notes <i class="las la-angle-right"></i> </a>
//             </li>
//         </ul>
//     </div>
// </div>


// <div class="col-md-9 mb-md-0 mb-30 scroll-right">
//     <div class="card info-section" id="info-section">
//         <div>
//         <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
//             <h6>Info</h6>
//         </div>
//         <div class="card-body">
//             <p class="mb-0">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam</p>
//         </div>
//         <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
//             <h6>RFx Requirement</h6>
//         </div>
//         <div class="card-body">
//             <p class="mb-0">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam</p>
//         </div>
//     </div>
//     </div>
//     <div class="card mt-3 attachments-section" id="attachments-section">
//         <div>
//         <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
//             <h6>Attachments</h6>
//         </div>
//         <div class="card-body px-0">
//             <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
//     <div class="table-responsive">
//         <table class="table mb-0 radius-0">
//             <thead>
//                 <tr class="userDatatable-header">
//                     <th>
//                         Item Type
//                     </th>
//                     <th>
//                         Item Name
//                     </th>
//                     <th>
//                         Description
//                     </th>
//                 </tr>
//             </thead>
//             <tbody class="sort-table ui-sortable">
//                 <tr class="ui-sortable-handle">
//                     <td>
//                         <div class="d-flex userDatatable-content mb-0 align-items-center">
//                             <span>Product</span>
//                         </div>
//                     </td>
//                     <td>
//                         <div class="d-flex userDatatable-content mb-0 align-items-center">
//                             <span>Test</span>
//                         </div>
//                     </td>
//                     <td>
//                         <div class="d-flex userDatatable-content mb-0 align-items-center">
//                             <span>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam </span>
//                         </div>
//                     </td>
//                 </tr>
//             </tbody>
//         </table>
//         <!-- Livewire Component wire-end:cleVBdTUlPo7PO22RPs2 -->
//     </div>
// </div>

//         </div>
//     </div>
//     </div>


//     <div class="card comments-section mt-3" id="comments-section">
//         <div>
//         <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
//             <h6>Info</h6>
//         </div>
//         <div class="card-body">
//             <p class="mb-0">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam</p>
//         </div>
//         <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
//             <h6>RFx Requirement</h6>
//         </div>
//         <div class="card-body">
//             <p class="mb-0">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam</p>
//         </div>
//     </div>
//     </div>


//     <div class="card note-section mt-3" id="note-section">
//         <div>
//         <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
//             <h6>Info</h6>
//         </div>
//         <div class="card-body">
//             <p class="mb-0">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam</p>
//         </div>
//         <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
//             <h6>RFx Requirement</h6>
//         </div>
//         <div class="card-body">
//             <p class="mb-0">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam</p>
//         </div>
//     </div>
//     </div>



// </div>


//         </div>