//alert('test')
// @flip2@ adding validation for worker id
let inp_user_type = $("#inp_user_type").val();
let inp_field_validation_rules = {
    emp_id: {},
};
switch (inp_user_type) {
    case "sp_worker":
        inp_field_validation_rules.emp_id.required = true;
        break;

    default:
        inp_field_validation_rules.emp_id.required = true;
        break;
}
$("#user_create_form").validate({
    ignore: "input[type=hidden]",
    rules: {
        user_type: {
            required: true,
        },
        name: {
            required: true,
            minlength: 2,
            maxlength: 50,
        },
        nationality_id: {
            required: function () {
                return !$(".nationality_select").is(":hidden");
            },
        },
        profession_id: {
            required: function () {
                return !$(".profession_select").is(":hidden");
            },
        },
        emp_dept: {
            maxlength: 20,
        },
        emp_id: {
            required: {
                //@flip1@ add validation for workorder id
                depends: function (element) {
                    return $("#user_type").val() == "sp_worker";
                },
            },
            minlength: 2,
            maxlength: 10,
            alphanumeric: true,
            remote: {
                url: $("#ajax_check_employee_id_unique").val(),
                type: "post",
                data: {
                    email: function () {
                        return $("#emp_id").val();
                    },
                    project_id: $("#project_id").val(),
                    user_type: function () {
                        return $("#user_type").val();
                    },
                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        email: {
            required: true,
            email: true,
            maxlength: 50,
            remote: {
                url: $("#ajax_check_useremail_unique").val(),
                type: "post",
                data: {
                    email: function () {
                        return $("#email").val();
                    },
                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        phone: {
            number: true,
            minlength: 9,
            maxlength: 9,

            remote: {
                url: $("#ajax_check_userphone_unique").val(),
                type: "post",
                data: {
                    phone: function () {
                        return $("#phone").val();
                    },
                    user_type: function () {
                        return $("#user_type").val();
                    },
                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        salary: {
            number: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                }
            },
            min: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                },
                param: 0
            }
        },
        attendance_target: {
            number: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                }
            },
            min: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                },
                param: 0
            }
        },
        phone2: {},
        service_provider: {},
        sp_admin_id: {},
        building_admin: {},
        supervisor_id: {},
   
    },
    messages: {
        user_type: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        name: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            minlength:
                translations.general_sentence.validation
                    .Please_enter_at_least_2_characters,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_50_characters,
            //lettersandspace: true,
        },
        country_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        nationality_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        profession_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        emp_dept: {
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_20_characters,
        },
        emp_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            alphanumeric:
                translations.general_sentence.validation
                    .Letters_numbers_and_only_these_characters_special_are_allowed,
            minlength:
                translations.general_sentence.validation
                    .Please_enter_at_least_2_characters,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_10_characters,
            remote: translations.general_sentence.validation
                .Employee_ID_already_exist_Enter_different_one,
        },
        city_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        email: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            email: translations.general_sentence.validation
                .Email_format_not_Valid,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_50_characters,
            remote: translations.general_sentence.validation
                .Email_already_exist_Enter_different_email,
        },
        salary: {
            required: translations.general_sentence.validation.This_field_is_required,
            number: translations.general_sentence.validation.Please_enter_a_valid_number,
            min: 'Salary should be  equal or greater than 0',
        },
        attendance_target: {
            required: translations.general_sentence.validation.This_field_is_required,
            number: translations.general_sentence.validation.Please_enter_a_valid_number,
            min: 'Attendance Target should be  equal or greater than 0',
        },
        role: {
            required: translations.general_sentence.validation.This_field_is_required,
        },
        admin_level: {
            required: translations.general_sentence.validation.This_field_is_required,
        },
        phone: {
            // required: translations.general_sentence.validation.This_field_is_required,
            number: translations.general_sentence.validation
                .Please_enter_a_valid_number,
            minlength:
                translations.general_sentence.validation.Please_enter_9_numbers,
            maxlength:
                translations.general_sentence.validation.Please_enter_9_numbers,
            remote: translations.general_sentence.validation
                .Phone_number_already_exist_Enter_different_number,
        },
        service_provider: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        sp_admin_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        building_admin: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        supervisor_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        store_keeper_name: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        store_keeper_email: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            email: translations.general_sentence.validation
                .Email_format_not_Valid,
        },
    },
    errorPlacement: function (error, element) {
        error.addClass("invalid-feedback");

    
        if (element.attr("id") == "sp_admin_id") {
            error.appendTo($("#sp_admin_id_error"));
        }
       
        if (element.attr("name") == "city_id") {
            error.appendTo($("#city_option_erro"));
        } else if (element.attr("name") == "country_id") {
            error.appendTo($("#country_id_error"));
        } else if (element.attr("name") == "building_admin") {
            error.appendTo($("#building_admin-error"));
        } else if (element.attr("name") == "service_provider") {
            error.appendTo($("#service_provider-error"));
        } else if (element.attr("id") == "sp_admin_id") {
            error.appendTo($("#sp_admin_id-error"));
        } else if (element.attr("id") == "supervisor_id") {
            error.appendTo($("#supervisor_id-error"));
        } else if (element.attr("id") == "user_type") {
            error.appendTo($("#user-type-error"));
        } else if (element.attr("id") == "nationality_id") {
            error.appendTo($("#nationality-id-error"));
        } else if (element.attr("id") == "profession_id") {
            error.appendTo($("#profession-id-error"));
        } else if (element.attr("id") == "store_keeper_name") {
            error.appendTo($("#store_keeper_name-error"));
        } else if (element.attr("id") == "store_keeper_email") {
            error.appendTo($("#store_keeper_email-error"));
        } else if (element.attr("id") == "role") {
            error.appendTo($("#role-level-error"));
        } else if (element.attr("id") == "admin_level") {
            error.appendTo($("#admin-level-error"));
        } else {
            error.insertAfter(element);
        }
    },
    highlight: function (element, errorClass, validClass) {
        $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
        $(element).removeClass("is-invalid");
        var elem = $(element);
        if (elem.hasClass("select2-offscreen")) {
            $("#s2id_" + elem.attr("id") + " ul").removeClass(errorClass);
        } else {
            elem.removeClass(errorClass);
        }
    },
    submitHandler: function (form) {
        //$.LoadingOverlay("show");
        //alert('ad');
        console.log($("#user_type").val())
        if($("#user_type").val() === 'building_manager' ) {
          if($('.unique_area_manager_error').is(':visible')){
              alert(`${translations.user_management_module.user_validation.unique_area_manager}`);
              return;
          }
        }
        form.submit();
    },
});

$("#user_type").on("change", function () {
    var user_type = $(this).val();

    var spWorkerSection = $("#sp_worker_section");
    var extraInfoFields = $("#extra_info_fields");
    var showExtraInfoCheckbox = $("#show_extra_info");

    // New logic: show/hide extra info for sp_worker
    if (user_type === 'sp_worker') {
        spWorkerSection.show();
    } else {
        spWorkerSection.hide();
        extraInfoFields.hide();
        showExtraInfoCheckbox.prop('checked', false);
    }
}).trigger("change");

// Checkbox toggle for showing extra info fields
$("#show_extra_info").on("change", function () {
    if ($(this).is(":checked")) {
        $("#extra_info_fields").show();
    } else {
        $("#extra_info_fields").hide();
    }
});

$(document).ready(function () {
    $("#role, #admin_level").select2({
            // placeholder: translations.user_management_module.user_forms.label.choose_a_favorite_language,
            dropdownCssClass: "tag",
            language: {
                noResults: () => translations.general_sentence.validation.No_results_found,
            }
    });
});



// SALARY
$('#salary').on('keyup', function () {
    sessionStorage.setItem('createuser_salary', JSON.stringify($(this).val()));
});
let salaryStored = sessionStorage.getItem('createuser_salary');
if (salaryStored) {
    $('#salary').val(JSON.parse(salaryStored));
}

// ATTENDANCE TARGET
$('#attendance_target').on('keyup', function () {
    sessionStorage.setItem('createuser_attendance_target', JSON.stringify($(this).val()));
});
let attendanceStored = sessionStorage.getItem('createuser_attendance_target');
if (attendanceStored) {
    $('#attendance_target').val(JSON.parse(attendanceStored));
}

// ROLE
$('#role').on('change', function () {
    sessionStorage.setItem('createuser_role', JSON.stringify($(this).val()));
});
let roleStored = sessionStorage.getItem('createuser_role');
if (roleStored) {
    $('#role').val(JSON.parse(roleStored)).change();
}

// ADMIN LEVEL
$('#admin_level').on('change', function () {
    sessionStorage.setItem('createuser_admin_level', JSON.stringify($(this).val()));
});
let adminLevelStored = sessionStorage.getItem('createuser_admin_level');
if (adminLevelStored) {
    $('#admin_level').val(JSON.parse(adminLevelStored)).change();
}

// ATTENDANCE MANDATORY
$('input[name="attendance_mandatory"]').on('change', function () {
    sessionStorage.setItem('createuser_attendance_mandatory', JSON.stringify($(this).val()));
});
let attendanceMandatoryStored = sessionStorage.getItem('createuser_attendance_mandatory');
if (attendanceMandatoryStored !== null) {
    $('input[name="attendance_mandatory"][value="' + JSON.parse(attendanceMandatoryStored) + '"]').prop('checked', true);
}

// OPTIONAL: Also persist the toggle state of "Show extra information"
$('#show_extra_info').on('change', function () {
    sessionStorage.setItem('createuser_show_extra_info', $(this).is(':checked'));
    toggleExtraInfoFields(); // call UI toggle
});

let showExtraInfo = sessionStorage.getItem('createuser_show_extra_info');
if (showExtraInfo === 'true') {
    $('#show_extra_info').prop('checked', true);
    toggleExtraInfoFields(); // trigger UI change on load
}

function toggleExtraInfoFields() {
    if ($('#show_extra_info').is(':checked')) {
        $('#extra_info_fields').show();
    } else {
        $('#extra_info_fields').hide();
    }
}

$("#user_create_form_worker").validate({
    ignore: "input[type=hidden]",
    rules: {
        user_type: {
            required: true,
        },
        name: {
            required: true,
            minlength: 2,
            maxlength: 50,
            //lettersandspace: true,
        },
        emp_dept: {
            maxlength: 20,
        },
        emp_id: {
            required: {
                //@flip1@ add validation for workorder id
                depends: function (element) {
                    return $("#user_type").val() == "sp_worker";
                },
            },
            minlength: 2,
            maxlength: 10,
            alphanumeric: true,
            remote: {
                url: $("#ajax_check_employee_id_unique").val(),
                type: "post",
                data: {
                    email: function () {
                        return $("#emp_id").val();
                    },
                    project_id: $("#project_id").val(),
                    user_type: function () {
                        return $("#user_type").val();
                    },
                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        email: {
            required: true,
            email: true,
            maxlength: 50,
            remote: {
                url: $("#ajax_check_useremail_unique").val(),
                type: "post",
                data: {
                    email: function () {
                        return $("#email").val();
                    },
                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        phone: {
            required: {
                depends: function (element) {
                    return $("#user_type").val() != "sp_worker"; //phone validation not required for worker user type
                },
            },
            number: true,
            minlength: 9,
            maxlength: 9,

            remote: {
                url: $("#ajax_check_userphone_unique").val(),
                type: "post",
                data: {
                    phone: function () {
                        return $("#phone").val();
                    },
                    user_type: function () {
                        return $("#user_type").val();
                    },
                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        phone2: {},
        service_provider: {},
        sp_admin_id: {},
        building_admin: {},
        "supervisor_id[]": {
            required: true,
        },
    },
    messages: {
        user_type: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        name: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            minlength:
                translations.general_sentence.validation
                    .Please_enter_at_least_2_characters,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_50_characters,
            //lettersandspace: true,
        },
        country_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        emp_dept: {
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_20_characters,
        },
        emp_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            alphanumeric:
                translations.general_sentence.validation
                    .Letters_numbers_and_only_these_characters_special_are_allowed,
            minlength:
                translations.general_sentence.validation
                    .Please_enter_at_least_2_characters,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_10_characters,
            remote: translations.general_sentence.validation
                .Employee_ID_already_exist_Enter_different_one,
        },
        city_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        email: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            email: translations.general_sentence.validation
                .Email_format_not_Valid,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_50_characters,
            remote: translations.general_sentence.validation
                .Email_already_exist_Enter_different_email,
        },
        phone: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            number: translations.general_sentence.validation
                .Please_enter_a_valid_number,
            minlength:
                translations.general_sentence.validation.Please_enter_9_numbers,
            maxlength:
                translations.general_sentence.validation.Please_enter_9_numbers,
            remote: translations.general_sentence.validation
                .Phone_number_already_exist_Enter_different_number,
        },
        service_provider: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        sp_admin_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        building_admin: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        "supervisor_id[]": {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
    },
    errorPlacement: function (error, element) {
        error.addClass("invalid-feedback");
        if (element.attr("name") == "city_id") {
            error.appendTo($("#city_option_erro"));
        } else if (element.attr("name") == "country_id") {
            error.appendTo($("#country_id_error"));
        } else if (element.attr("name") == "building_admin") {
            error.appendTo($("#building_admin-error"));
        } else if (element.attr("name") == "service_provider") {
            error.appendTo($("#service_provider-error"));
        } else if (element.attr("id") == "sp_admin_id") {
            error.appendTo($("#sp_admin_id-error"));
        } else if (element.attr("id") == "supervisor_id[]") {
            error.appendTo($("#supervisor_id_error"));
        } else if (element.attr("id") == "user_type") {
            error.appendTo($("#user-type-error"));
        } else {
            error.insertAfter(element);
        }
    },
    highlight: function (element, errorClass, validClass) {
        $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
        $(element).removeClass("is-invalid");
        var elem = $(element);
        if (elem.hasClass("select2-offscreen")) {
            $("#s2id_" + elem.attr("id") + " ul").removeClass(errorClass);
        } else {
            elem.removeClass(errorClass);
        }
    },
    submitHandler: function (form) {
        //$.LoadingOverlay("show");
        //alert('ad');
        form.submit();
    },
});

$("#user_create_form_edit").validate({
    ignore: "input[type=hidden]",
    ignore: "select[id=supervisor_id]", // @fli1@ ignore when edit supervisor
    rules: {
        name: {
            required: true,
            minlength: 2,
            maxlength: 50,
            //lettersandspace: true,
        },
        country_id: {
            required: true,
        },
        emp_dept: {
            maxlength: 20,
        },
        emp_id: {
            required: {
                //@flip1@ add validation for workorder id
                depends: function (element) {
                    return $("#user_type").val() == "sp_worker";
                },
            },
            minlength: 2,
            maxlength: 10,
            alphanumeric: true,
            remote: {
                url: $("#ajax_check_employee_id_unique_edit").val(),
                type: "post",
                data: {
                    user_type: function () {
                        return $("#user_type").val();
                    },
                    email: function () {
                        return $("#emp_id").val();
                    },
                    project_id: $("#project_id").val(),
                    user_id: $("#user_id").val(),

                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        city_id: {
            required: true,
        },
        email: {
            required: true,
            email: true,
            maxlength: 50,
            remote: {
                url: $("#ajax_check_useremail_unique").val(),
                type: "get",
                data: {
                    email: function () {
                        return $("#email").val();
                    },
                    user_id: $("#user_id").val(),
                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
       salary: {
            number: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                }
            },
            min: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                },
                param: 0
            }
        },
        attendance_target: {
            number: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                }
            },
            min: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                },
                param: 0
            }
        },
        phone: {
            // @flip1@ eremove from require
            // required: {
            //     depends: function (element) {
            //         return $("#user_type").val() != 'sp_worker'; //phone validation not required for worker user type
            //     }
            // },
            number: true,
            minlength: 9,
            maxlength: 9,

            remote: {
                url: $("#ajax_check_userphone_unique_edit").val(),
                type: "post",
                data: {
                    phone: function () {
                        return $("#phone").val();
                    },
                    user_type: function () {
                        return $("#user_type").val();
                    },
                    'user_id':$('#user_id').val(),
                     locale: "en",
                     _token: $('meta[name="csrf-token"]').attr("content"),
                 },
             },
        },
        phone2: {},
    },
    messages: {
        name: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            minlength:
                translations.general_sentence.validation
                    .Please_enter_at_least_2_characters,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_50_characters,
        },
        country_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        emp_dept: {
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_20_characters,
        },
        emp_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            alphanumeric:
                translations.general_sentence.validation
                    .Letters_numbers_and_only_these_characters_special_are_allowed,
            minlength:
                translations.general_sentence.validation
                    .Please_enter_at_least_2_characters,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_10_characters,
            remote: translations.general_sentence.validation
                .Employee_ID_already_exist_Enter_different_one,
        },
        city_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        email: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            email: translations.general_sentence.validation
                .Email_format_not_Valid,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_50_characters,
            remote: translations.general_sentence.validation
                .Email_already_exist_Enter_different_email,
        },
        phone: {
            // required: translations.general_sentence.validation.This_field_is_required,
            number: translations.general_sentence.validation
                .Please_enter_a_valid_number,
            minlength:
                translations.general_sentence.validation.Please_enter_9_numbers,
            maxlength:
                translations.general_sentence.validation.Please_enter_9_numbers,
            remote: translations.general_sentence.validation
                .Phone_number_already_exist_Enter_different_number,
        },
    },
    errorPlacement: function (error, element) {
        console.log(error, element, 123);
        error.addClass("invalid-feedback");
        if (element.attr("name") == "city_id") {
            error.appendTo($("#city_option_erro"));
        } else if (element.attr("name") == "country_id") {
            error.appendTo($("#country_id_error"));
        } else {
            error.insertAfter(element);
        }
    },
    highlight: function (element, errorClass, validClass) {
        $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
        $(element).removeClass("is-invalid");
        var elem = $(element);
        if (elem.hasClass("select2-offscreen")) {
            $("#s2id_" + elem.attr("id") + " ul").removeClass(errorClass);
        } else {
            elem.removeClass(errorClass);
        }
    },
    submitHandler: function (form) {
        //$.LoadingOverlay("show");
        //alert('HII');
        console.log(form);
        form.submit();
    },
});

$("#user_create_form_edit_worker").validate({
    ignore: "input[type=hidden]",
    rules: {
        name: {
            required: true,
            minlength: 2,
            maxlength: 50,
            //lettersandspace: true,
        },
        "supervisor_id[]": {
            required: true,
        },
        nationality_id: {
            required: true,
        },
        profession_id: {
            required: function () {
                return !$(".profession_select").is(":hidden");
            },
        },
        emp_dept: {
            maxlength: 20,
        },
        emp_id: {
            required: true /*ajax_check_employee_id_unique*/,
            minlength: 2,
            maxlength: 10,
            alphanumeric: true,
            remote: {
                url: $("#ajax_check_employee_id_unique_edit").val(),
                type: "post",
                data: {
                    user_type: function () {
                        return $("#user_type").val();
                    },
                    email: function () {
                        return $("#emp_id").val();
                    },
                    project_id: $("#project_id").val(),
                    user_id: $("#user_id").val(),

                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        salary: {
            number: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                }
            },
            min: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                },
                param: 0
            }
        },
        attendance_target: {
            number: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                }
            },
            min: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                },
                param: 0
            }
        },
        phone: {
            required: {
                depends: function (element) {
                    return $("#user_type").val() != "sp_worker" &&  $("#user_type").val() != "team_leader"; //phone validation not required for worker user type
                },
            },
            number: true,
            minlength: 9,
            maxlength: 9,

             remote: {
                 url: $("#ajax_check_userphone_unique_edit").val(),
                 type: "post",
                 data: {
                     phone: function () {
                         return $("#phone").val();
                     },
                     user_type: function () {
                        return $("#user_type").val();
                     },
                     'user_id':$('#user_id').val(),
                     locale: "en",
                     _token: $('meta[name="csrf-token"]').attr("content"),
                 },
             },
        },
        phone2: {},
    },
    messages: {
        name: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            minlength:
                translations.general_sentence.validation
                    .Please_enter_at_least_2_characters,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_50_characters,
        },
        "supervisor_id[]": {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        nationality_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        profession_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        emp_dept: {
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_20_characters,
        },
        emp_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            alphanumeric:
                translations.general_sentence.validation
                    .Letters_numbers_and_only_these_characters_special_are_allowed,
            minlength:
                translations.general_sentence.validation
                    .Please_enter_at_least_2_characters,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_10_characters,
            remote: translations.general_sentence.validation
                .Employee_ID_already_exist_Enter_different_one,
        },
        phone: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            number: translations.general_sentence.validation
                .Please_enter_a_valid_number,
            minlength:
                translations.general_sentence.validation.Please_enter_9_numbers,
            maxlength:
                translations.general_sentence.validation.Please_enter_9_numbers,
            remote: translations.general_sentence.validation
                .Phone_number_already_exist_Enter_different_number,
        },
        salary: {
            required: translations.general_sentence.validation.This_field_is_required,
            number: translations.general_sentence.validation.Please_enter_a_valid_number,
            min: 'Salary should be  equal or greater than 0',
        },
        attendance_target: {
            required: translations.general_sentence.validation.This_field_is_required,
            number: translations.general_sentence.validation.Please_enter_a_valid_number,
            min: 'Attendance Target should be  equal or greater than 0',
        },
    },
    errorPlacement: function (error, element) {
        error.addClass("invalid-feedback");
        if (element.attr("name") == "city_id") {
            error.appendTo($("#city_option_erro"));
        } else if (element.attr("name") == "country_id") {
            error.appendTo($("#country_id_error"));
        } else if (element.attr("name") == "supervisor_id[]") {
            error.appendTo($("#supervisor_id_error"));
        } else if (element.attr("id") == "profession_id") {
            error.appendTo($("#profession-id-error"));
        } else if (element.attr("id") == "nationality_id") {
            error.appendTo($("#nationality-id-error"));
        } else {
            error.insertAfter(element);
        }
    },
    highlight: function (element, errorClass, validClass) {
        $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
        $(element).removeClass("is-invalid");
        var elem = $(element);
        if (elem.hasClass("select2-offscreen")) {
            $("#s2id_" + elem.attr("id") + " ul").removeClass(errorClass);
        } else {
            elem.removeClass(errorClass);
        }
    },
    submitHandler: function (form) {
        //$.LoadingOverlay("show");
        //alert('HII');
        form.submit();
    },
});

$("#file_upload").on("change", function (event) {
    var files = document.getElementById("file_upload").files;

    if (files.length) {
        var file_size_error = false;
        var file_type_error = false;
        var file_size_in_kb = files[0].size / 2048;
        var file_type = files[0].type;

        if (file_size_in_kb > 2048) {
            file_size_error = true;
        }

        var supported_types = ["image/jpeg", "image/png", "image/jpg"];

        if (!supported_types.includes(file_type)) {
            file_type_error = true;
        }

        if (file_size_error == true || file_type_error == true) {
            document.getElementById("file_upload").value = "";

            var error_message = "";

            if (file_size_error == true && file_type_error == true) {
                error_message =
                    translations.general_sentence.validation
                        .Please_upload_only_jpg_jpeg_png_image_of_max_size_1mb;
            } else if (file_size_error == true && file_type_error == false) {
                error_message =
                    translations.general_sentence.validation
                        .File_size_should_not_be_more_than_1_mb;
            } else {
                error_message =
                    translations.general_sentence.validation
                        .Please_upload_only_jpg_jpeg_png_image;
            }

            swal({
                title: error_message,
                //text: "Hello",
                icon: "warning",
                buttons: true,
                dangerMode: true,
            });
        } else {
            var reader = new FileReader();
            reader.onload = function () {
                var output = document.getElementById("output_pic");
                output.src = "";
                output.src = reader.result;
                console.log(reader.result);
            };
            reader.readAsDataURL(event.target.files[0]);
            $("[name='isImageRemove']").val(0);
            $(".remove-img").removeClass("hide");
        }
    } else {
        document.getElementById("file_upload").value = "";
        let app_url = $("#app_url").val();
        //$("#file_upload").attr('value','');

        $("#output_pic").attr("src", app_url + "/img/upload.png");

        $("[name='isImageRemove']").val(1);
        $(".remove-img").addClass("hide");
    }
});

$(".confirm_remove_photo").click(function () {
    document.getElementById("file_upload").value = "";
    let app_url = $("#app_url").val();
    //$("#file_upload").attr('value','');

    $("#output_pic").attr("src", app_url + "/img/upload.png");

    $("[name='isImageRemove']").val(1);
    $(".remove-img").addClass("hide");
});

$("#country_id").on("change", function () {
    let value_cnt = $(this).val();
    //alert(language);
    $.ajax({
        url: $(this).data("url"),
        method: "GET",
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
            id: value_cnt,
        },
        dataType: "json",
        beforeSend: function () {},
        success: function (data) {
            $("#city_id").empty();
            var language = current_locale;
            // @flip1@ add blank option
            $("#city_id").append(
                $("<option></option>")
                    .attr("selected", true)
                    .attr("disabled", true)
                    .text(
                        translations.user_management_module.user_forms
                            .place_holder.emp_city
                    )
            );
            //alert(language);
            $.each(data, function (key, value) {
                if (language == "en") {
                    $("#city_id").append(
                        $("<option></option>")
                            .attr("value", value.id)
                            .text(value.name_en)
                    );
                } else {
                    $("#city_id").append(
                        $("<option></option>")
                            .attr("value", value.id)
                            .text(value.name_ar)
                    );
                }
            });
        },
        error: function (data) {
            var errors = $.parseJSON(data.responseText);
            toastr.error(data, translations.general_sentence.validation.Error, {
                timeOut: 5000,
                positionClass: "toast-top-center",
                progressBar: true,
            });
        },
    });
});

/***************sp admin list and sup list by company change **********/

/***************sp admin list and sup list by company change **********/
$("#user_type").on("change", function () {
    let user_type_tag = $(this).val();
    let app_url = $("#app_url").val();
    $.ajax({
        url: app_url + "/user/ajax/get_userrole_list/",
        method: "GET",
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
            user_type_tag: user_type_tag,
        },
        dataType: "json",
        beforeSend: function () {},
        success: function (data) {
            $("#user_role").empty();
            $.each(data, function (key, value) {
                $("#user_role").append(
                    $("<option></option>")
                        .attr("value", value.slug)
                        .text(value.user_role_tag)
                );
            });
        },
        error: function (data) {
            var errors = $.parseJSON(data.responseText);
            toastr.error(data, translations.general_sentence.validation.Error, {
                timeOut: 5000,
                positionClass: "toast-top-center",
                progressBar: true,
            });
        },
    });
});

/**************employee admin id ****************************/
$("#building_admin").on("change", function () {
    let id = $(this).val();
    $("#employee_admin_id").val(id);
});

$("#spga_admin").on("change", function () {
    let id = $(this).val();
    $("#employee_admin_id").val(id);
});

$("#sp_admin_id").on("change", function () {
    let id = $(this).val();
    $("#employee_admin_id").val(id);
});


$(document).ready(function() {
    $('#emp_dept').attr('maxlength', 20); // Set the new max-length attribute
      $('.profession_select').hide();
      $('.nationality_select').hide();
      $('.favorite_language_select').hide();
      //$('#emp_dept').prop('required', false); // Make input not required
      if(user_type_val!='')
      {
         if(user_type_val=='sp_worker')
         {
            //$('.profession').hide();
            $('.profession_select').show();
            $('.nationality_select').show();
            $('.favorite_language_select').show();
         }
         else
         {
            $('.profession_select').hide();
            $('.nationality_select').hide();
            $('.favorite_language_select').hide();
         }
      }

      $('#profession_id').on('change', function() {
         var selectedOption = $(this).val();

         if (selectedOption === '10') {
               $('.profession').show();
               $('#emp_dept').prop('required', true); // Make input required
               $('#emp_dept').attr('maxlength', 15); // Set the new max-length attribute
               $('#emp_dept').val('');
         } else {
               $('.profession').hide();
               $('#emp_dept').prop('required', false); // Make input not required
               $('#emp_dept').attr('maxlength', 20); // Set the new max-length attribute
               $('#emp_dept').val('');
         }
      });
    });