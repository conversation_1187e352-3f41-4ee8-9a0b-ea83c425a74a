$(document).ready(function() {
            var images = $(".image-gallery img");
            var currentIndex = 0;
            var lightbox = $(".lightbox");
            var lightboxImg = $(".lightbox img");

            images.click(function() {
                currentIndex = images.index(this);
                var imgSrc = $(this).attr("src");
                lightbox.addClass("d-flex").hide().fadeIn();
                lightboxImg.attr("src", imgSrc).hide().fadeIn();
            });

            $(".lightbox .close").click(function() {
                lightbox.fadeOut(function() {
                    lightbox.removeClass("d-flex");
                });
            });

            $(".lightbox .prev").click(function(e) {
                e.stopPropagation();
                currentIndex = (currentIndex > 0) ? currentIndex - 1 : images.length - 1;
                switchImage();
            });

            $(".lightbox .next").click(function(e) {
                e.stopPropagation();
                currentIndex = (currentIndex < images.length - 1) ? currentIndex + 1 : 0;
                switchImage();
            });

            $(".lightbox").click(function(e) {
                if ($(e.target).is(".lightbox, .close")) {
                    lightbox.fadeOut(function() {
                        lightbox.removeClass("d-flex");
                    });
                }
            });

            function switchImage() {
                lightboxImg.fadeOut(function() {
                    var imgSrc = images.eq(currentIndex).attr("src");
                    lightboxImg.attr("src", imgSrc).fadeIn();
                });
            }

            $(".lightbox .download").click(function (e) {
                e.stopPropagation();

                var imgSrc = $(".lightbox img").attr("src");
                var fileName = imgSrc.split('/').pop().split('?')[0];

                // Fetch image as blob
                fetch(imgSrc, { mode: 'cors' }) // CORS required
                    .then(response => response.blob())
                    .then(blob => {
                        var link = document.createElement('a');
                        link.href = window.URL.createObjectURL(blob);
                        link.download = fileName;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                    })
                    .catch(err => {
                        console.error('Download failed:', err);
                        alert("Download failed. File might be hosted without proper permissions.");
                    });
            });

        });