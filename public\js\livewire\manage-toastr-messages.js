document.addEventListener("DOMContentLoaded", function() {
    // Ensure event listener is not added multiple times
    if (!window.toastrEventRegistered) {
        window.toastrEventRegistered = true; // Set flag

        window.addEventListener("show-toastr", event => {
            toastr.remove(); // Clear previous toasts

            toastr.options = {
                closeButton: true,
                progressBar: true,
                positionClass: "toast-top-right",
                timeOut: "3000"
            };

            if (event.detail.type === "success") {
                toastr.success(event.detail.message);
            } else if (event.detail.type === "error") {
                toastr.error(event.detail.message);
            }
        });
    }
});
