const { event } = require("jquery");

function submitImportForm(event, text1, text2) {
    var errorMessage = null;
    const fileInput = document.getElementById("file");
    const format = ['xlsx', 'csv', 'xlsm', 'xlsb', 'xl', 'xls']; 

    if (fileInput.value == "") {
        errorMessage = text1;
    }

    else if(!format.includes(fileInput.value.split('.').pop())){
        errorMessage = text2;
    }
   
    else{
        errorMessage = null;
    }

    if(errorMessage != null){
        event.preventDefault();
        
        swal({
            title: errorMessage,
            icon: "warning",
            buttons: true,
            dangerMode: true,            
        })
    }

    else{
        var filename = getFilename(fileInput);
        var filenameWithoutExtension = getFilenameWitoutExtension(filename);
        var extension = getFileExtension(filename);
        var file = fileInput.files[0];
        var fileSizeBytes = file.size;
        var formattedSize = formatFileSize(fileSizeBytes);
        showBulkImportConfirmQuestion(filenameWithoutExtension, extension, formattedSize, event);
    }
}

function getFilename(file) {
    return file.value.split('\\').pop() || file.value.split('/').pop();
}

function getFilenameWitoutExtension(filename) {
    return filename.split('.').slice(0, -1).join('.');
}

function getFileExtension(filename) {
    return filename.split('.').pop();
}

function getFileSize(file) {
    return file.size;
}

function formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    const index = Math.floor(Math.log(bytes) / Math.log(1024));
    const formattedSize = (bytes / Math.pow(1024, index)).toFixed(2) + ' ' + units[index];
    return formattedSize;
}

function showBulkImportConfirmQuestion(filenameWithoutExtension, extension, fileSizeInKilobytes, event) {
    event.preventDefault();

    swal({
        title: translations.import.confirmation,
        text: translations.import.question_first_step + "\n" + translations.import.file_name + filenameWithoutExtension + "\n" + translations.import.extension_title + extension + "\n" + translations.import.size + fileSizeInKilobytes,
        icon: "warning",
        buttons: true,
        dangerMode: true,
        showCancelButton: true,
        confirmButtonText:  translations.general_sentence.swal_buttons.confirm,
        cancelButtonText:  translations.general_sentence.swal_buttons.cancel,
    },
    function(willDelete) {
        if (willDelete) {
            var element = document.getElementById("spinner");
            element.classList.remove("d-none");
            var btn = document.getElementById("submit_btn");
            btn.setAttribute("disabled", true);
            document.getElementById("bulk-upload-form").submit();
        }
    });
}

function getFirstLetterByNumber(word, number) {
    return word.substring(0, number);
}

function validateNumber(input) {
    input.value = input.value.replace(/[^0-999]/g,'');
}

function validateConfgurationForm(event, requiredField, numberField) {
    var code = document.getElementById('code');
    var codeError = document.getElementById('codeError');
    var valueInput = document.getElementById('value');  
    var valueError = document.getElementById('valueError');  
    var platform = document.getElementById('platform');
    var platformError = document.getElementById('platformError');
    var name = document.getElementById('name');
    var nameError = document.getElementById('nameError');
    var status = document.getElementById('status');
    var statusError = document.getElementById('statusError');
    var description = document.getElementById('description');
    var descriptionError = document.getElementById('descriptionError');
    var btn = document.getElementById('submit');

    if(code.value.trim() === ""){
        event.preventDefault();
        codeError.innerHTML = requiredField;
        code.classList.add("is-invalid");
    }

    else if(isNaN(code.value.trim())){
        event.preventDefault();
        codeError.innerHTML = numberField;
        code.classList.add("is-invalid");
    }

    else if(valueInput.value.trim() === ""){
        event.preventDefault();
        valueError.innerHTML = requiredField;
        value.classList.add("is-invalid");
    }

    else if(platform.value.trim() === ""){
        event.preventDefault();
        platformError.innerHTML = requiredField;
        platform.classList.add("is-invalid");
    }

    else if(name.value.trim() === ""){
        event.preventDefault();
        nameError.innerHTML = requiredField;
        name.classList.add("is-invalid");
    }

    else if(status.value.trim() === ""){
        event.preventDefault();
        statusError.innerHTML = requiredField;
        status.classList.add("is-invalid");
    }

    else if(description.value.trim() === ""){
        event.preventDefault();
        descriptionError.innerHTML = requiredField;
        description.classList.add("is-invalid");
    }

    else{
        codeError.innerHTML = '';
        code.classList.remove("is-invalid");
        valueError.innerHTML = '';
        value.classList.remove("is-invalid");
        platformError.innerHTML = '';
        platform.classList.remove("is-invalid");
        nameError.innerHTML = '';
        name.classList.remove("is-invalid");
        statusError.innerHTML = '';
        status.classList.remove("is-invalid");
        descriptionError.innerHTML = '';
        description.classList.remove("is-invalid");
        btn.setAttribute("disabled", true);
        $('#bulk-upload-form').submit();
    }
}

function removeError(input) {
    input.classList.remove("is-invalid");
    $("#"+input.name + "Error").text("");
}

function hideModal(modal) {
    $(modal).modal('hide');
}

function alertResulAction(data) {
    if (data == 1) {
        toastr.success("", translations.general_sentence.tostr_lang.status_success_updated, {
            timeOut: 2500,
            positionClass: "toast-top-center"
        });
    } 
    
    else {
        toastr.error(translations.general_sentence.tostr_lang.internal_server_error, translations.general_sentence.tostr_lang.error, {
            timeOut: 5000
        });
    }
}

function alertStoreAction(data) {
    if (data == 1) {
        toastr.success("", translations.general_sentence.s_global.added_success, {
            timeOut: 2500,
            positionClass: "toast-top-center"
        });
    } 
    
    else {
        toastr.error(translations.general_sentence.tostr_lang.internal_server_error, translations.general_sentence.tostr_lang.error, {
            timeOut: 5000
        });
    }
}

function alertUpdateAction(data) {
    if (data == 1) {
        toastr.success("", translations.general_sentence.s_global.updated_success, {
            timeOut: 2500,
            positionClass: "toast-top-center"
        });
    } 
    
    else {
        toastr.error(translations.general_sentence.tostr_lang.internal_server_error, translations.general_sentence.tostr_lang.error, {
            timeOut: 5000
        });
    }
}

function confirmDestroyConfiguration() {
    swal({
        title: translations.configuration.delete_modal_title,
        text: translations.configuration.delete_modal_desc,
        icon: "warning",
        buttons: true,
        dangerMode: true,
        showCancelButton: true,
        confirmButtonText:  translations.general_sentence.swal_buttons.confirm,
        cancelButtonText:  translations.general_sentence.swal_buttons.cancel,
    },
    function(willDelete) {
        if (willDelete) {
            Livewire.emit('destroyConfiguration')
        }
    });
}

function alertDeleteAction(data) {
    if (data == 1) {
        toastr.success("", translations.general_sentence.s_global.delete_success, {
            timeOut: 2500,
            positionClass: "toast-top-center"
        });
    } 
    
    else {
        toastr.error(translations.general_sentence.tostr_lang.internal_server_error, translations.general_sentence.tostr_lang.error, {
            timeOut: 5000
        });
    }
}

function displayBeneficiaryImage(event) {
    const file = event.target.files[0];
    Livewire.emit('startLoading');

    let fileType = file.type;
    let fileTypeError = false;
    var fileSizeError = false;
    var fileSizeInKb = file.size / 2048;
    var supportedTypes = ["image/jpeg", "image/png", "image/jpg"];
    var errorMessage = null;

    if (fileSizeInKb > 2048) {
        fileSizeError = true;
    }

    if (!supportedTypes.includes(fileType)) {
        fileTypeError = true;
    }

    if (fileSizeError == true || fileTypeError == true) {
        if (fileSizeError == true && fileTypeError == true) {
            errorMessage = translations.general_sentence.validation.Please_upload_only_jpg_jpeg_png_image_of_max_size_1mb;
        } 

        else if (fileSizeError == true && fileTypeError == false) {
            errorMessage = translations.general_sentence.validation.File_size_should_not_be_more_than_1_mb;
        } 

        else {
            errorMessage = translations.general_sentence.validation.Please_upload_only_jpg_jpeg_png_image;
        }

        swal({
            title: errorMessage,
            icon: "warning",
            buttons: true,
            dangerMode: true,
        }) 
    }

    else{
        const fileReader = new FileReader();
        fileReader.onload = (e) => {
            output.src = e.target.result;
        };
        fileReader.readAsDataURL(file);
    }
}

function confirmSubmitImport() {
    swal({
        title: translations.import.confirmation,
        text: translations.import.bulk_import_modal_question,
        icon: "warning",
        buttons: true,
        dangerMode: true,
        showCancelButton: true,
        confirmButtonText:  translations.general_sentence.swal_buttons.confirm,
        cancelButtonText:  translations.general_sentence.swal_buttons.cancel,
    },
    function(willDelete) {
        if (willDelete) {
            Livewire.emit('importFile')
        }
    });
}

function showModal(modal) {
    $(modal).modal('show');
}

function showExcelFileDiv(input) {
    const file = input.files[0];
    const fileInfoDiv = document.getElementById("file-info");
    const fileNameSpan = fileInfoDiv.querySelector(".file-name");

    if (file) {
        fileNameSpan.textContent = file.name;
        fileInfoDiv.style.display = "block";
    } 
    
    else {
        fileInfoDiv.style.display = "none";
    }
}

function showModalConfirmDeleteData(type) {
    swal({
        title: translations.import.confirm_message, 
        text: translations.general_sentence.modal.warning_title + " " + translations.general_sentence.modal.delete_warning,
        icon: "warning",
        buttons: true,
        dangerMode: true,
        showCancelButton: true,
        confirmButtonText:  translations.general_sentence.swal_buttons.confirm,
        cancelButtonText:  translations.general_sentence.swal_buttons.cancel,
    },
    function(willDelete) {
        if (willDelete) {
            Livewire.emit('deleteDataList', type)
        }
    });
}

function filterByRatings2(input) {
    var id = input.id;

    if (id == "5stars_tab") {
        $("#5stars").prop("checked", true);
    } 
    
    else if (id == "4stars_tab") {
        $("#4stars").prop("checked", true);
    }
    
    else if (id == "3stars_tab") {
        $("#3stars").prop("checked", true);
    }

    else if (id == "2stars_tab") {
        $("#2stars").prop("checked", true);
    }

    else if (id == "1stars_tab") {
        $("#1stars").prop("checked", true);
    }

    var selectedValues = $("[name='jratings']:checked").val();
    Livewire.emit('setPermanentRating', selectedValues);
}

function filterByWorkerId() {
    var countInputs = $("[name='workers[]']").length;
	var countChecked = $("[name='workers[]']:checked").length;
    var checkedInputs = $("[name='workers[]']:checked");
    var selectedValues = [];

    if(countChecked == countInputs){
        $('#all-workers').prop('checked', true);
    }

    else if(countChecked == 0){
        $('#all-workers').prop('checked', true);
    }

    else if(countChecked < countInputs){
        $('#all-workers').prop('checked', false);
    }    

    checkedInputs.each(function() {
        selectedValues.push($(this).val());
    });

    Livewire.emit('setPermanentWorker', selectedValues);
}

function resetWorkersFilter() {
    $('#all-workers').prop('checked', true);
    $("[name='workers[]']").prop('checked', false);
    Livewire.emit('setPermanentWorker', "");
}

function filterByResponseTime() {
    var countInputs = $("[name='rs-time[]']").length;
	var countChecked = $("[name='rs-time[]']:checked").length;
    var checkedInputs = $("[name='rs-time[]']:checked");
    var selectedValues = [];

    if(countChecked == countInputs){
        $('#all-response').prop('checked', true);
    }

    else if(countChecked == 0){
        $('#all-response').prop('checked', true);
    }

    else if(countChecked < countInputs){
        $('#all-response').prop('checked', false);
    }    

    checkedInputs.each(function() {
        selectedValues.push($(this).val());
    });

    Livewire.emit('setPermanentResponseTime', selectedValues);
}

function resetResponseTimeFilter() {
    $('#all-response').prop('checked', true);
    $("[name='rs-time[]']").prop('checked', false);
    Livewire.emit('setPermanentResponseTime', "");
}

function filterByExecusionTime() {
    var countInputs = $("[name='ex-time[]']").length;
	var countChecked = $("[name='ex-time[]']:checked").length;
    var checkedInputs = $("[name='ex-time[]']:checked");
    var selectedValues = [];

    if(countChecked == countInputs){
        $('#all-execusion').prop('checked', true);
    }

    else if(countChecked == 0){
        $('#all-execusion').prop('checked', true);
    }

    else if(countChecked < countInputs){
        $('#all-execusion').prop('checked', false);
    }    

    checkedInputs.each(function() {
        selectedValues.push($(this).val());
    });

    Livewire.emit('setPermanentExecusionTime', selectedValues);
}

function resetExecusionTimeFilter() {
    $('#all-execusion').prop('checked', true);
    $("[name='ex-time[]']").prop('checked', false);
    Livewire.emit('setPermanentExecusionTime', "");
}

function filterByProperty() {
    var countInputs = $("[name='buildings[]']").length;
	var countChecked = $("[name='buildings[]']:checked").length;
    var checkedInputs = $("[name='buildings[]']:checked");
    var selectedValues = [];

    if(countChecked == countInputs){
        $('#ap').prop('checked', true);
    }

    else if(countChecked == 0){
        $('#ap').prop('checked', true);
    }

    else if(countChecked < countInputs){
        $('#ap').prop('checked', false);
    }    

    checkedInputs.each(function() {
        selectedValues.push($(this).val());
    });

    Livewire.emit('setPermanentProperty', selectedValues);
}

function resetPropertiesFilter() {
    $('#ap').prop('checked', true);
    $("[name='buildings[]']").prop('checked', false);
    Livewire.emit('setPermanentProperty', "");
}

function filterBySupervisor() {
    var countInputs = $("[name='supervisors[]']").length;
	var countChecked = $("[name='supervisors[]']:checked").length;
    var checkedInputs = $("[name='supervisors[]']:checked");
    var selectedValues = [];
    
    if(countChecked == countInputs){
        $('#all_supervisors').prop('checked', true);
    }

    else if(countChecked == 0){
        $('#all_supervisors').prop('checked', true);
    }

    else if(countChecked < countInputs){
        $('#all_supervisors').prop('checked', false);
    }    

    checkedInputs.each(function() {
        selectedValues.push($(this).val());
    });

    Livewire.emit('setPermanentSupervisor', selectedValues);
}

function resetSupervisorsFilter() {
    $('#all_supervisors').prop('checked', true);
    $("[name='supervisors[]']").prop('checked', false);
    Livewire.emit('setPermanentSupervisor', "");
}

function filterByContract() {
    var countInputs = $("[name='contract[]']").length;
	var countChecked = $("[name='contract[]']:checked").length;
    var checkedInputs = $("[name='contract[]']:checked");
    var selectedValues = [];
    
    if(countChecked == countInputs){
        $('#all_contracts').prop('checked', true);
    }

    else if(countChecked == 0){
        $('#all_contracts').prop('checked', true);
    }

    else if(countChecked < countInputs){
        $('#all_contracts').prop('checked', false);
    }    

    checkedInputs.each(function() {
        selectedValues.push($(this).val());
    });

    Livewire.emit('setPermanentContract', selectedValues);
}

function resetContractsFilter() {
    $('#all_contracts').prop('checked', true);
    $("[name='contract[]']").prop('checked', false);
    Livewire.emit('setPermanentContract', "");
}

function resetAllRatingWorkOrdersSelection() {
    resetWorkersFilter();
    resetResponseTimeFilter();
    resetExecusionTimeFilter();
    resetPropertiesFilter();
    resetSupervisorsFilter();
    resetContractsFilter();
}

function searchServiceProviderList(input) {
    var filter = $(input).val().toLowerCase();

    $('.service-provider').each(function() {
        var name = $(this).data('name').toLowerCase();
        $(this).toggle(name.includes(filter));
    });
}

function sendDeletedServiceProvider(input) {
    if ($(input).is(':checked')) { 
        Livewire.emit('setShowDeletedServiceProviders', true);
    } 
    
    else {
        Livewire.emit('setShowDeletedServiceProviders', false);
    }
}

function initCalendar(startDate) {
    var start = new Date(startDate);
    var end = moment();

    $("#calender_filter_workorder").daterangepicker(
        {
            startDate: start,
            endDate: end,
            locale: {
                format: 'DD/MM/YYYY'
            },
            ranges: {
                //'Select dates' : [moment().subtract(5, 'year'), moment().add(5, 'year')],
                "Reset": [start, moment()],
                Today: [moment(), moment()],
                Yesterday: [
                    moment().subtract(1, "days"),
                    moment().subtract(1, "days"),
                ],
                "Last 7 Days": [moment().subtract(6, "days"), moment()],
                "Last 30 Days": [moment().subtract(29, "days"), moment()],
                "This Month": [
                    moment().startOf("month"),
                    moment().endOf("month"),
                ],
                "Last Month": [
                    moment().subtract(1, "month").startOf("month"),
                    moment().subtract(1, "month").endOf("month"),
                ],
            },
        }
    );

    if (window.current_locale == "ar") {
        var arr = [
            translations.dashboard.bread_crumbs.reset,
            translations.dashboard.bread_crumbs.today,
            translations.dashboard.bread_crumbs.yesterday,
            translations.dashboard.bread_crumbs.last_7_days,
            translations.dashboard.bread_crumbs.last_30_day,
            translations.dashboard.bread_crumbs.this_month,
            translations.dashboard.bread_crumbs.last_month
        ];
        $(".ranges ul li").each(function (i) {
            // var get = $(this).attr('data-range-key'); // This is your rel value
            $(this).empty();
            $(this).append(arr[i]);
            if ($(this).attr("data-range-key") == "Custom Range") {
                $(this).append(
                    translations.dashboard.bread_crumbs.custom_range
                );
            }
        });
    }
}

function selectServiceProvider(input) {
    if ($(input).is(':checked')) { 
        $('input[class="sp_list_check"]').prop('checked', true); 
    } 
    
    else {
        $('input[class="sp_list_check"]').prop('checked', false); 
    }
}

function selectSingleServiceProvider() {
    if($('input[class="sp_list_check"]:checked').length == 0){
        $('#check-all-sp-list').prop('checked', true);
        $('input[class="sp_list_check"]').prop('checked', true);
    }

    else{
        var allChecked = $('input[class="sp_list_check"]:checked').length === $('input[class="sp_list_check"]').length;
        $('#check-all-sp-list').prop('checked', allChecked);
    }
}

function filterByCalendar() {
    $("#calender_filter_workorder").on("change", function () {
        wDateRange = [];
        var startDate = $(this).data("daterangepicker").startDate._d;
        var endDate = $(this).data("daterangepicker").endDate._d;
        startDate = moment(startDate).format("YYYY-MM-DD");
        endDate = moment(endDate).format("YYYY-MM-DD");
        wDateRange.push(startDate, endDate);
        Livewire.emit('setDateRange', wDateRange);
    });
}

function filterByFrequencyList(input) {
    const selectedValues = [];

    if ($(input).data('value') === 0) {
        $(input).data('check', true);
        $('#filter_id_section a').removeClass('dropdown-item-checked'); 
        selectedValues.length = 0; 
        selectedValues.push(0);
    } 
    
    else {
        $(input).data('check', !$(input).data('check')); 
        $(input).toggleClass('dropdown-item-checked'); 

        selectedValues.length = 0; 
        $('#filter_id_section a.dropdown-item-checked').each(function() {
            var element = $(this).data('value')
            selectedValues.push(element);
        });
    }
    
    Livewire.emit('setFrequency', selectedValues);
}

function searchWOPropertiesList(input) {
    var filter = $(input).val().toLowerCase();

    $('.property-filter').each(function() {
        var name = $(this).data('name').toLowerCase();
        $(this).toggle(name.includes(filter));
    });

    
    $('.service-filter').each(function() {
        var name = $(this).data('name').toLowerCase();
        $(this).toggle(name.includes(filter));
    });
}

function selectProperties(input) {
    if ($(input).is(':checked')) { 
        $('input[name = "buildings[]"]').prop('checked', true);
        $('input[name = "complex[]"]').prop('checked', true);
    }

    else{
        $('input[name = "buildings[]"]').prop('checked', false);
        $('input[name = "complex[]"]').prop('checked', false);
    }
}

function selectServices(input) {
    if ($(input).is(':checked')) { 
        $('input[name = "service_types[]"]').prop('checked', true);
    }

    else{
        $('input[name = "service_types[]"]').prop('checked', false);
    }
}

function selectSingleProperties() {
    var allChecked = $('input[class="mr-2 prop_field"]:checked').length === $('input[class="mr-2 prop_field"]').length;
    $('#all_properties').prop('checked', allChecked);
}

function filterByProperty() {
    const selectedValues = [];
    $(".multi-field").parent().removeClass("show");

    $.each($("input[name='buildings[]']:checked"), function () {
        selectedValues.push($(this).val());
    });

    Livewire.emit('setProperty', selectedValues);
}

function filterByService() {
    const selectedValues = [];
    $(".multi-field").parent().removeClass("show");

    $.each($("input[name='service_types[]']:checked"), function () {
        selectedValues.push($(this).val());
    });
    console.log("Selected Service Types:", selectedValues); // Debugging

    Livewire.emit('setService', selectedValues);
}

    function resetProperty() {
        $(".multi-field").parent().removeClass("show");
        const buildings = document.querySelectorAll('input[name="buildings[]"');

        buildings.forEach(function(checkbox) {
            checkbox.checked = true;
        });

        const complex = document.querySelectorAll('input[name="complex[]"');

        complex.forEach(function(checkbox2) {
            checkbox2.checked = true;
        });

        Livewire.emit('setProperty', []);
    }
    
function resetService() {
    $(".multi-field").parent().removeClass("show");
    const services = document.querySelectorAll('input[name="service_types[]"');

    services.forEach(function(checkbox) {
        checkbox.checked = true;
    });

    Livewire.emit('setService', []);
}

function confirmDestroyPmWorkOrder(input) {
    swal({
        title: translations.general_sentence.modal.warning_title_workorder_pm,
        text: translations.general_sentence.modal.delete_warning_message_pm,
        icon: "warning",
        buttons: true,
        dangerMode: true,
        showCancelButton: true,
        confirmButtonText:  translations.general_sentence.swal_buttons.confirm,
        cancelButtonText:  translations.general_sentence.swal_buttons.cancel,
    },
    function(willDelete) {
        if (willDelete) {
            var uniqueId = $(input).data("unique_id");
            Livewire.emit('destroyPmWorkOrders', uniqueId);
        }
    });
}

function openFailedModalForDeleteWorkOrder() {
    toastr.error(
        translations.general_sentence.tostr_lang
            .internal_server_error,
        translations.general_sentence.tostr_lang.error,
        { timeOut: 2000 }
    );
}

function openSuccessModalForDeleteWorkOrder() {
    toastr.success(
        "",
        translations.general_sentence.tostr_lang
            .success_deleted,{
            timeOut: 2000,
            positionClass: "toast-top-center",
        }
    );
}

function filterBySortType(input) {
    const selectedValues = [];

    switch ($(input).data('value')) {
        case 'sort_by_date_asc':
            selectedValues.push('submission_date', 'ASC');
        break;

        case 'sort_by_date_desc':
            selectedValues.push('submission_date', 'DESC');
        break;

        case 'sort_by_status_asc':
            selectedValues.push('status_html', 'ASC');
        break;

        case 'sort_by_status_desc':
            selectedValues.push('status_html', 'DESC');
        break;

        case 'sort_by_name_asc':
            selectedValues.push('assigned_worker', 'ASC');
        break;

        case 'sort_by_name_desc':
            selectedValues.push('assigned_worker', 'DESC');
        break;
    
        default:
            selectedValues.push('submission_date', 'ASC');
        break;
    }

    $("#filter_id_section_sort a").removeClass("active");
    $(input).addClass("active");
    Livewire.emit('setSortBy', selectedValues);
}

function showSelectedRowsByCheckBox(value) {
    Livewire.emit('setShowSelectedRows', value);
}

function openSuccessModalForExport() {
    setTimeout(function() {
        $('#export-confirm-modal').modal('show');
    }, 800);
}

function openFailedModalForExport() {
    setTimeout(function() {
        $('#export-fail-modal').modal('show');
    }, 800);
}

function filterByServiceProvider() {
    const selectedValues = [];

    if($('#check-all-sp-list').is(':checked')){
        $.each($(".sp_list_check"), function () {
            selectedValues.push($(this).val());
        });
    }

    else{
        $.each($(".sp_list_check:checked"), function () {
            selectedValues.push($(this).val());
        });
    }

    Livewire.emit('setExplodedServiceProviders', selectedValues);
}

function filterByRatings(input) {
    var id = input.id;

    if (id == "5stars_tab") {
        $("#5stars").prop("checked", true);
    } 
    
    else if (id == "4stars_tab") {
        $("#4stars").prop("checked", true);
    }
    
    else if (id == "3stars_tab") {
        $("#3stars").prop("checked", true);
    }

    else if (id == "2stars_tab") {
        $("#2stars").prop("checked", true);
    }

    else if (id == "1stars_tab") {
        $("#1stars").prop("checked", true);
    }
}

function filterByStatus(input) {
    $('#filter_id_section a').attr('data-check', 'false'); 
    checkedStatus = $(input).data("value");

    checkedStatus === '' ? $('#all_status_filter').addClass("dropdown-item-checked") : $('#all_status_filter').removeClass("dropdown-item-checked");
    checkedStatus === 1 ? $('#open_status_filter').addClass("dropdown-item-checked") : $('#open_status_filter').removeClass("dropdown-item-checked");
    checkedStatus === 2 ? $('#in_progress_status_filter').addClass("dropdown-item-checked") : $('#in_progress_status_filter').removeClass("dropdown-item-checked");
    checkedStatus === 3 ? $('#on_hold_status_filter').addClass("dropdown-item-checked") : $('#on_hold_status_filter').removeClass("dropdown-item-checked");
    checkedStatus === 4 ? $('#closed_status_filter').addClass("dropdown-item-checked") : $('#closed_status_filter').removeClass("dropdown-item-checked");
    checkedStatus === 6 ? $('#re_open_status_filter').addClass("dropdown-item-checked") : $('#re_open_status_filter').removeClass("dropdown-item-checked");
    $(input).attr('data-check', 'true');
}

function filterByViewBy(input) {
    var id = input.id;

    if(id == "pass-tab"){
        $("#pass").prop("checked", true);
    }

    else if(id == "fail-tab"){
        $("#fail").prop("checked", true); 
    }

    else if(id == "pass_fail_tab"){
        $("#all_pass_fail").prop("checked", true);
    }
}

function filterByType(input) {
    var id = input.id;

    if(id == "reactive-tab"){
        $("#reactive").prop("checked", true);
    }

    else if(id == "preventive-tab"){
        $("#preventive").prop("checked", true);
    }

    else if(id == "all-type"){
        $("#type_all").prop("checked", true);
    }
}

function searchAssetNumbersList(input) {
    var filter = $(input).val().toLowerCase();

    $('.asset_number_li').each(function() {
        var number = $(this).data('number').toLowerCase();
        $(this).toggle(number.includes(filter));
    });
}

function selectAllAssetNumber(input) {
    if ($(input).is(':checked')) { 
        $('.as_num').prop('checked', true); 
    } 
    
    else {
        $('.as_num').prop('checked', false); 
    }
}

function selectSingleAssetNumber() {
    var allChecked = $('input[class="mr-2 as_num"]:checked').length === $('input[class="mr-2 as_num"]').length;
    $('#assets-ap').prop('checked', allChecked);
}

function clearAssetsNumber() {
    $('.as_num').prop("checked", false);
}

function searchServicesList(input) {
    var filter = $(input).val().toLowerCase();

    $('.asset_li').each(function() {
        var number = $(this).data('service').toLowerCase();
        $(this).toggle(number.includes(filter));
    });
}

function selectAllAssets(input) {
    if ($(input).is(':checked')) { 
        $('.service').prop('checked', true); 
    } 
    
    else {
        $('.service').prop('checked', false); 
    }
}

function selectSingleAssets() {
    var allChecked = $('input[name="service_types[]"]:checked').length === $('input[name="service_types[]"]').length;
    $('#service_all').prop('checked', allChecked);
}

function clearServices() {
    $('.service').prop("checked", false);
}

function searchWorkersList(input) {
    var filter = $(input).val().toLowerCase();

    $('.worker_li').each(function() {
        var name = $(this).data('name').toLowerCase();
        $(this).toggle(name.includes(filter));
    });
}

function selectAllWorkers(input) {
    if ($(input).is(':checked')) { 
        $('.workers').prop('checked', true); 
    } 
    
    else {
        $('.workers').prop('checked', false); 
    }
}

function selectSingleWorkers() {
    var allChecked = $('input[name="workers[]"]:checked').length === $('input[name="workers[]"]').length;
    $('#all_workers').prop('checked', allChecked);
}

function clearWorkers() {
    $('.workers').prop("checked", false);
}

function searchSupervisorsList(input) {
    var filter = $(input).val().toLowerCase();

    $('.supervisor_li').each(function() {
        var name = $(this).data('name').toLowerCase();
        $(this).toggle(name.includes(filter));
    });
}

function selectAllSupervisors(input) {
    if ($(input).is(':checked')) { 
        $('.supervisor').prop('checked', true); 
    } 
    
    else {
        $('.supervisor').prop('checked', false); 
    }
}

function selectSingleSupervisors() {
    var allChecked = $('input[name="supervisors[]"]:checked').length === $('input[name="supervisors[]"]').length;
    $('#all_supervisors').prop('checked', allChecked);
}

function clearSupervisors() {
    $('.supervisor').prop("checked", false);
}

function searchPropertiesList(input) {
    var filter = $(input).val().trim().toLowerCase();
    if (filter !== "") {
        $('#properties_div li').each(function() {
            var labelText = $(this).find('label').text().trim().toLowerCase();
            $(this).toggle(labelText.includes(filter));
        });
    } else {
        $('#properties_div li').show(); // Show all if input is empty
    }
}

function searchbmeList(input) {
    var filter = $(input).val().toLowerCase();
    if (filter !== "") {
        $('#bme_div li').each(function() {
            var labelText = $(this).find('label').text().trim().toLowerCase();
            $(this).toggle(labelText.includes(filter));
        });
    } else {
        $('#bme_div li').show(); // Show all if input is empty
    }

    // $('.bme_li').each(function() {
    //     var building = $(this).data('building').toLowerCase();
    //     $(this).toggle(building.includes(filter));
    // });
}

function selectAllProperties(input) {
    if ($(input).is(':checked')) { 
        $('.property').prop('checked', true); 
    } 
    
    else {
        $('.property').prop('checked', false); 
    }
}

function selectaallbme(input) {
    if ($(input).is(':checked')) { 
        $('.bme').prop('checked', true); 
    } 
    
    else {
        $('.bme').prop('checked', false); 
    }
}

function selectSingleProperties() {
    var allChecked = $('input[name="buildings[]"]:checked').length === $('input[name="buildings[]"]').length;
    var allCheckedService = $('input[name="service_types[]"]:checked').length === $('input[name="service_types[]"]').length;
    $('#all_properties').prop('checked', allChecked);
    $('#service_all').prop('checked', allCheckedService);
}


function clearbme() {
    $('.bme').prop("checked", false);
}

function clearProperties() {
    $('.property').prop("checked", false);
}

function filterByAll() {
    const selectedRatingsValues = $('input[name="jratings"]:checked').map((index, element) => $(element).val()).get();
    const selectedHiddenColumns = $('input[name="colums"]:not(:checked)').map((index, element) => $(element).val()).get();
    const selectedStatusValues = $('a[data-check="true"]').map((index, element) => $(element).data('value')).get();
    const selectedPassFailValues = $('input[name="pass_fail"]:checked').map((index, element) => $(element).val()).get();
    const selectedTypeValues = $('input[name="type"]:checked').map((index, element) => $(element).val()).get();
    const selectedAssetNumberValues = $('input[name="assets[]"]:checked').map((index, element) => $(element).val()).get();
    const selectedAssetsValues = $('input[name="service_types[]"]:checked').map((index, element) => $(element).val()).get();
    const selectedWorkersValues = $('input[name="workers[]"]:checked').map((index, element) => $(element).val()).get();
    const selectedSupervisorsValues = $('input[name="supervisors[]"]:checked').map((index, element) => $(element).val()).get();
    const selectedPropertiesValues = $('input[name="buildings[]"]:checked').map((index, element) => $(element).val()).get();
    const selectedBuildingMangerEmployee = $('input[name="buildingMangerEmployee[]"]:checked').map((index, element) => $(element).val()).get();
    const selectedBuildingManger = $('input[name="buildingManger[]"]:checked').map((index, element) => $(element).val()).get();

    const selectedValues = {
        'ratings' : selectedRatingsValues == '' ? [] : selectedRatingsValues,
        'columns' : selectedHiddenColumns == '' ? [] : selectedHiddenColumns,
        'status' : selectedStatusValues == '' ? [] : selectedStatusValues,
        'passFail' : selectedPassFailValues == '' ? [] : selectedPassFailValues,
        'type' : selectedTypeValues == '' ? [] : selectedTypeValues,
        'assets' : selectedAssetNumberValues == '' ? [] : selectedAssetNumberValues,
        'services' : selectedAssetsValues == '' ? [] : selectedAssetsValues,
        'workers' : selectedWorkersValues == '' ? [] : selectedWorkersValues,
        'supervisors' : selectedSupervisorsValues == '' ? [] : selectedSupervisorsValues,
        'properties' : selectedPropertiesValues == '' ? [] : selectedPropertiesValues,
        'buildingMangerEmployee' : selectedBuildingMangerEmployee == '' ? [] : selectedBuildingMangerEmployee,
        'buildingManger' : selectedBuildingManger == '' ? [] : selectedBuildingManger
    };

    Livewire.emit('setFilters', selectedValues);
}

function resetAllFilters() {
    const selectedValues = {
        'ratings' : [],
        'columns' : [],
        'status' : [],
        'passFail' : [],
        'type' : [],
        'assets' : [],
        'services' : [],
        'workers' : [],
        'supervisors' : [],
        'properties' : [],
        'buildingMangerEmployee' : [],
        'buildingManger' : []
    };

    Livewire.emit('setFilters', selectedValues);
    clearAssetsNumber();
    clearServices();
    clearWorkers();
    clearSupervisors();
    clearProperties();
}

function showImportFileQuestion(event) {
    event.preventDefault();

    swal({
        title: translations.import.confirmation,
        text: translations.import.confirm_question_bulk_import,
        icon: "warning",
        buttons: true,
        dangerMode: true,
        showCancelButton: true,
        confirmButtonText:  translations.general_sentence.swal_buttons.confirm,
        cancelButtonText:  translations.general_sentence.swal_buttons.cancel,
    },
    function(willDelete) {
        if (willDelete) {
            var btn = document.getElementById("submit_btn");
            btn.setAttribute("disabled", true);
            document.getElementById("bulk-upload-form").submit();
        }
    });
}

function showSelectSheetsQuestion(event) {
    event.preventDefault();
    var users = $('#checked_users').is(':checked');  
    var priorities = $('#checked_priorities').is(':checked'); 
    var services = $('#checked_services').is(':checked'); 
    var properties = $('#checked_properties').is(':checked');  
    var buildings = $('#checked_buildings').is(':checked'); 
    var assets = $('#checked_assets').is(':checked'); 

    if(!users && !priorities && !services && !properties && !buildings && !assets){
        swal({
            title: translations.import.validation,
            text: translations.import.select_one_sheet,
            icon: "danger",
            buttons: false,
            dangerMode: true,
            showCancelButton: false,
            confirmButtonText:  translations.general_sentence.swal_buttons.cancel,
        });
    }

    else{
        swal({
            title: translations.import.confirmation,
            text: translations.import.confirm_question_select_sheets,
            icon: "warning",
            buttons: true,
            dangerMode: true,
            showCancelButton: true,
            confirmButtonText:  translations.general_sentence.swal_buttons.confirm,
            cancelButtonText:  translations.general_sentence.swal_buttons.cancel,
        },
        function(willDelete) {
            if (willDelete) {
                var btn = document.getElementById("btn-next");
                btn.setAttribute("disabled", true);
                document.getElementById("select-sheets-form").submit();
            }
        });
    }
}

function manageShowMoreTable() {
    if($('#assets-table thead th').hasClass('d-none')){
        $('.extra-col').removeClass('d-none');
        $('.extra-row').removeClass('d-none');
        $('#show-action-btn').text(translations.import.less);
    }

    else{
        $('.extra-col').addClass('d-none');
        $('.extra-row').addClass('d-none');
        $('#show-action-btn').text(translations.import.more);
    }
}

function openConfirmModalForMapView() {
    swal({
        title: translations.import.confirmation,
        text: translations.import.confirm_map_question,
        icon: "question",
        buttons: true,
        dangerMode: true,
        showCancelButton: true,
        confirmButtonText:  translations.general_sentence.swal_buttons.confirm,
        cancelButtonText:  translations.general_sentence.swal_buttons.cancel,
    },
    function(willDelete) {
        if (willDelete) {
            startJobs();
            myodal = new bootstrap.Modal(document.getElementById('progress_bar_modal'));
            myodal.show();
        }
    });
}

function startJobs() {
    Livewire.emit('startJobs');
}

function manageShowMoreUsersTable() {
    if($('#users-table thead th').hasClass('d-none')){
        $('.extra-col').removeClass('d-none');
        $('.extra-row').removeClass('d-none');
        $('#show-action-btn').text(translations.import.less);
        $('#users-table').addClass('scroll');
    }

    else{
        $('.extra-col').addClass('d-none');
        $('.extra-row').addClass('d-none');
        $('#show-action-btn').text(translations.import.more);
        $('#users-table').removeClass('scroll');
    }
}

function manageShowMorePrioritiesTable() {
    if($('#priorities-table thead th').hasClass('d-none')){
        $('.extra-col').removeClass('d-none');
        $('.extra-row').removeClass('d-none');
        $('#show-action-btn2').text(translations.import.less);
        $('#priorities-table').addClass('scroll');
    }

    else{
        $('.extra-col').addClass('d-none');
        $('.extra-row').addClass('d-none');
        $('#show-action-btn2').text(translations.import.more);
        $('#priorities-table').removeClass('scroll');
    }
}

function manageShowMoreServicesTable() {
    if($('#services-table thead th').hasClass('d-none')){
        $('.extra-col').removeClass('d-none');
        $('.extra-row').removeClass('d-none');
        $('#show-action-btn3').text(translations.import.less);
        $('#services-table').addClass('scroll');
    }

    else{
        $('.extra-col').addClass('d-none');
        $('.extra-row').addClass('d-none');
        $('#show-action-btn3').text(translations.import.more);
        $('#services-table').removeClass('scroll');
    }
}

function manageShowMorePropertiesTable() {
    if($('#properties-table thead th').hasClass('d-none')){
        $('.extra-col').removeClass('d-none');
        $('.extra-row').removeClass('d-none');
        $('#show-action-btn4').text(translations.import.less);
        $('#properties-table').addClass('scroll');
    }

    else{
        $('.extra-col').addClass('d-none');
        $('.extra-row').addClass('d-none');
        $('#show-action-btn4').text(translations.import.more);
        $('#properties-table').removeClass('scroll');
    }
}

function manageShowMoreBuildingsTable() {
    if($('#buildings-table thead th').hasClass('d-none')){
        $('.extra-col').removeClass('d-none');
        $('.extra-row').removeClass('d-none');
        $('#show-action-btn5').text(translations.import.less);
        $('#buildings-table').addClass('scroll');
    }

    else{
        $('.extra-col').addClass('d-none');
        $('.extra-row').addClass('d-none');
        $('#show-action-btn5').text(translations.import.more);
        $('#buildings-table').removeClass('scroll');
    }
}

function manageShowMoreAssetsTable() {
    if($('#assets-table thead th').hasClass('d-none')){
        $('.extra-col').removeClass('d-none');
        $('.extra-row').removeClass('d-none');
        $('#show-action-btn6').text(translations.import.less);
        $('#assets-table').addClass('scroll');
    }

    else{
        $('.extra-col').addClass('d-none');
        $('.extra-row').addClass('d-none');
        $('#show-action-btn6').text(translations.import.more);
        $('#assets-table').removeClass('scroll');
    }
}

function manageAffectationButtonState() {
    if($("#checked_users").is(':checked')){
        $('#users_affectation_btn').prop('disabled', false);
    }

    else{
        $('#users_affectation_btn').prop('disabled', true);
    }
}

function setSelectedBMA(input, email) {
    Livewire.emit('manageSelectedBMA', input.value, email);
}

function resetSelect2() {
    $('.select2-select').val('').trigger('change');
}

function setSelectedCompany(input, email) {
    Livewire.emit('manageSelectedSPACompany', input.value, email); 
}

function setSelectedCompanySPS(input, email) {
    Livewire.emit('manageSelectedSPSCompany', input.value, email); 
}

function updadeSPASelect2(item) {
    const email = item.email;
    const data = item.data;
    const selectEmail = 'sp_admin_' + email;
    const select = $('select[name="'+selectEmail+'"]');

    clearSelect2(selectEmail);

    data.forEach(option => {
        const newOption = new Option(option.name, option.id, false, false);
        select.append(newOption);
    });
}

function setSelectedSPAForSPS(input, email) {
    Livewire.emit('manageSelectedSPSData', input.value, email); 
}

function clearSelect2(input) {
    $('select[name="'+input+'"] option:not(:first)').remove();
}

function setSelectedCompanyWorker(input, email) {
    Livewire.emit('manageSelectedWorkerCompany', input.value, email); 
}

function updadeSPSSelect2(item) {
    const email = item.email;
    const data = item.data;
    const selectEmail = 'sp_supervisor_' + email;
    const select = $('select[name="'+selectEmail+'"]');

    clearSelect2(selectEmail);

    data.forEach(option => {
        const newOption = new Option(option.name, option.id, false, false);
        select.append(newOption);
    });
}

function setSelectedSPAForSPW(input, email) {
    Livewire.emit('manageSelectedSPWData', input.value, email); 
}

function setSelectedAllRows(list, input) {
    if ($(input).is(':checked')) { 
        Livewire.emit('updatedSelectedAllRows', list); 
    } 
    
    else {
        Livewire.emit('updatedSelectedAllRows', null); 
    }
}