<?php

return array(
    'enable_booking_system' => 'Enable Booking System',
    'booking_system_default_interval'=> 'Booking System Default Interval',
    'enable_booking_system_tooltip' => 'Enable the appointment management system to streamline tenant move-ins. This feature allows tenants to book, view, and cancel appointments, while Project owners and building managers can manage bookings and receive notifications for all changes. Key issuance confirmation completes the Tenant’s unit receival process.',
    'enable_booking_system_caution_title' => 'Are you sure you want to do this?',
    'enable_booking_system_caution_message' => 'Disabling this feature will lead to Cancelling all upcoming bookings and notify Tenants, also this will hide the pages related. You can enable any time later',
    'enable_booking_system_confirm' => 'Confirm',
    'enable_booking_system_cancel' => 'Cancel',
    'enable_multiple_priority_level_to_single_service_caution_title' => 'Are you sure you want to do this?',
    'enable_multiple_priority_level_to_single_service_caution_message' => 'Disabling this feature will affect current services with multiple priorities they will be restricted to a single priority which may affect workflow. You can re-enable later',
    'All' => 'All',
    'view' => 'View',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'admin_panel' => 'Osool Platform Admin Panel',
    'no_access' => 'No access',
    'click_to_upload' => 'Click to Upload',
    'use_unit_receival_form_for_tenants' => 'Use unit receival form for tenants',
    'all_existing_and_new_tenants_receives_form' => 'All existing and new tenants receives form',
    'only_newly_added_tenants' => 'Only newly added tenants',
    'set_period_for_form_expiration' => 'Set period for form expiration (Days)',
    'this_service_will_be_used_for_tenant_form' => 'This service will be used for Tenant form',
    'days' => 'Days',
    'enable_warranty' => 'Enable warranty',
    'inside_unit_request' => 'Enable Inside Unit Request for Tenants',
    'inside_unit_request_tooltip' => 'Allows tenants to request services for inside units from registered vendors in Osool, with separate work orders outside project contracts and negotiable agreements.',
    'warranty_duration' => 'Warranty Duration (Years)',
    'show' => 'Show',
    'results' => 'Results',
    'length_menu' => "Show _MENU_ Results",
    'breadcrumbs' =>
        array(
            'add' => 'Add',
            'arabic' => 'Arabic',
            'calendar' => 'Calendar',
            'city' => 'City',
            'complaints' => 'Complaints',
            'contracts' => 'Contracts',
            'country' => 'Country',
            'create' => 'Create',
            'dashboard' => 'Dashboard',
            'details' => 'Details',
            'edit' => 'Edit',
            'email' => 'Email',
            'emergency_service' => 'Emergency Service',
            'english' => 'English',
            'location' => 'Location',
            'messages' => 'Messages',
            'notifications' => 'Notifications',
            'properties' => 'Properties',
            'property_types' => 'Property Types',
            'quotations' => 'Quotation Management',
            'sent_messages' => 'Sent Messages',
            'services' => 'Services',
            'settings' => 'Settings',
            'shared_service_orders' => 'Shared Service Orders',
            'shared_services' => 'Shared Services',
            'sms_templates' => 'Sms Templates',
            'spare_part_orders' => 'Spare Part Orders',
            'spare_parts' => 'Spare Parts',
            'state' => 'State',
            'statuses' => 'Statuses',
            'translations' => 'Translations',
            'unit' => 'Unit',
            'user_groups' => 'User Groups',
            'users' => 'Users',
            'work_order' => 'Work Order',
            'task' => 'Task',
        ),
    'button_and_links' =>
        array(
            'active' => 'Active',
            'add_file' => 'Add Files',
            'add_note' => 'Add Note',
            'add_service' => 'Add Service',
            'assign_labour_task' => 'Assign Labour Task',
            'attachment' => 'Attachment',
            'back' => 'Back',
            'cancel' => 'Cancel',
            'close' => 'Close',
            'complaints' => 'Complaints',
            'compose' => 'Compose',
            'create' => 'Create',
            'create_city' => 'Create City',
            'create_complaint' => 'Create Complaint',
            'create_contract' => 'Create Contract',
            'create_country' => 'Create Country',
            'create_group' => 'Create Group',
            'create_property' => 'Create Property',
            'create_property_type' => 'Create Property Type',
            'create_services' => 'Find translation in files',
            'create_shared_service' => 'Create Shared Service',
            'create_spare_part' => 'Create Spare Part',
            'create_state' => 'Create State',
            'create_status' => 'Create Status',
            'create_unit' => 'Create Unit',
            'create_user' => 'Create User',
            'create_work_order' => 'Create Work Order',
            'edit_work_order' => 'Edit Work Order',
            'created_at' => 'Created At',
            'delete' => 'Delete',
            'details' => 'Details',
            'discard' => 'Discard',
            'download_invoice' => 'Download Invoice',
            'download_report' => 'Download Report',
            'edit' => 'Edit',
            'import_groups' => 'Import Groups',
            'inbox' => 'Inbox',
            'more_info' => 'More Info',
            'next' => 'Next',
            'ok' => 'Ok',
            'previous' => 'Previous',
            'publish_all' => 'Publish All',
            'save_and_next' => 'Save & Next',
            'search' => 'Search',
            'select_all' => 'Select All',
            'send' => 'Send',
            'sent' => 'Sent',
            'status' => 'Status',
            'submit' => 'Submit',
            'update' => 'Update',
            'update_service' => 'Update Service',
            'view_all' => 'View All',
            'reset' => "Reset"
        ),
    'create_task' => 'Create Task',
    'daily' => 'Daily',
    'dashboard' => 'Dashboard',
    'day' => 'Day',
    'days' => 'Day(s)',
    'first' => 'First',
    'for' => 'For',
    'fourth' => 'Fourth',
    'free' => 'Free',
    'inactive' => 'Inactive',
    'maintenance' => 'Maintenance',
    'monthly' => 'Monthly',
    'months' => 'Month(s)',
    'months_array' =>
        array(
            'January' => 'January',
            'February' => 'February',
            'March' => 'March',
            'April' => 'April',
            'May' => 'May',
            'June' => 'June',
            'July' => 'July',
            'August' => 'August',
            'September' => 'September',
            'October' => 'October',
            'November' => 'November',
            'December' => 'December',
        ),
    'no_properties_found' => 'No properties found',
    'no_data_found' => 'No Data Found!',
    'not_available' => 'Not available',
    'on_demand' => 'On Demand',
    'search' => 'Search',
    'no_performance_data_available' => 'Due to the absence of work orders or enough data for the search criteria, the performance data is not available.',
    'second' => 'Secound',
    'status_button' =>
        array(
            'complain' => 'Complain',
            'completed' => 'Completed',
            'emergency' => 'Emergency',
            'overdue' => 'Overdue',
            'pending' => 'Pending',
            'rating_and_view' => 'Rating and view',
            'view' => 'View',
            'warning' => 'Warning',
            'complete_now' => 'Complete Now'
        ),
    'third' => 'Third',
    'week_days' =>
        array(
            'Sunday' => 'Sunday',
            'Monday' => 'Monday',
            'Tuesday' => 'Tuesday',
            'Wednesday' => 'Wednesday',
            'Thurseday' => 'Thurseday',
            'Friday' => 'Friday',
            'Saturday' => 'Saturday',
        ),
    'week_days_cal' =>
        array(
            'Sunday' => 'Su',
            'Monday' => 'Mo',
            'Tuesday' => 'Tu',
            'Wednesday' => 'We',
            'Thurseday' => 'Th',
            'Friday' => 'Fr',
            'Saturday' => 'Sa',
        ),
    'weekly' => 'Weekly',
    'weeks' => 'Week(s)',
    'yearly' => 'Yearly',
    'years' => 'Year(s)',
    'minutes' => 'Minute(s)',
    'navigation' =>
        array(
            'osool_administrator' => 'Osool Administrator',
            'application' => 'Application',
            'services_parts' => 'Services & Parts',
            'coming_soon' => 'Soon',
        ),

    'swal_buttons' => array(
        'confirm' => 'Confirm',
        'cancel' => 'Cancel',

        'work_order_delete' => 'Delete',
        'work_order_cancel' => 'Cancel',
        'ok' => 'OK',
        'save_continue' => 'Save & Continue',
    ),
    'messages_s' => array(
        'sent_messsage_title' => 'Sent Messages',
    ),

    /**
     * General translate for pupup window
     */

    'modal' => array(
        'added_new_records' => 'Record has been added',
        'deleted_successfully' => 'Record has been deleted',

        'records_updated' => 'Record has been updated',
        'warning_title' => 'Are you sure to delete this?',
        'delete_warning' => 'Once deleted, you will not be able to recover this!',
        'delete_warning_sp' => 'Caution: Deleting this Service Provider would delete all users (Service Provider Admin, Workers and Supervisors) associated with it as well',
        'delete_asset_cat_warning' => 'Caution: Deleting this Service Provider would delete all users associated with it as well',

        'property_warning_title' => 'Are you sure you want to delete this property?',
        'property_warning_message' => 'Caution: Deleting this property will delete all of its associated Units, Tenants, files and Assets as well this will affect the Work Orders, Contracts, etc. ',

        'contract_warning_title' => 'Are you sure you want to delete this contract?',
        'contract_warning_message' => 'Caution: Deleting this Contract will affect the Work Orders associated with this Contract and won’t be able to generate report for it.
    Work Orders associated with this Contract will have [Deleted] next to the Contract name in the Work Order details',


        'beneficiary_warning_title' => 'Are you sure you want to delete this beneficiary?',
        'beneficiary_warning_message' => 'Caution: Deleting this beneficiary will remove from associated properties.',

        'tenant_warning_title' => 'Are you sure you want to delete this tenant?',
        'tenant_warning_message' => 'Caution: Deleting this tenant will delete all the private files, and will not be able to access app',
        'shared_file_warning_title' => 'Are you sure you want to delete this file?',
        'shared_file_warning_message' => 'Caution: Once deleted, you will not be able to recover this!',
        'sp_warning_title' => 'Are you sure you want to delete this service provider?',
        'public_sp_warning_title' => 'Are you sure you want to delete this public service provider?',
        'sp_warning_message' => 'Caution: Deleting this Service Provider would delete all associated users (Service Provider Admin, Supervisors and Workers), you will not be able to recover this!',
        'public_sp_warning_message' => 'Caution: Deleting this Service Provider would delete it from your project only, you will not be able to recover this!',


        'user_warning_title' => 'Are you sure you want to delete this user?',
        'submit_all_warning_title' => 'Submitting all entered work orders',
        'submit_all_warning_desc' => 'Note: Submitting all will proceed with all filled work orders, anything that left empty it will be ignored',
        'No_images_uploaded' => 'No images uploaded',
        // 'sp_warning_message' => 'Caution: Deleting this Service Provider would delete all associated users (Service Provider Admin, Supervisors and Workers), you will not be able to recover this!',


        'delete_success_message' => 'Successfully deleted',
        'you_want_to' => 'You want to',
        'sms_send_warning_title' => 'Are you sure?',
        'sms_send_success_message' => 'SMS has been sent to the tenant successfully',
        'sms_send_warning_desc' => 'Are you sure you want to resend a SMS for this tenant?',
        'sms_send_warning_for_all_building_all_tenant' => 'Are you sure you want to send SMS for All not registered tenants in All properties?',
        'sms_send_warning_for_one_building_all_tenant' => 'Are you sure you want to send SMS for not registered tenants?',
        'project_delete_warning_title' => 'Are you sure you want to delete this project?',
        'project_delete_warning' => 'Caution: deleting a project will remove all of its related data',

        'edit_warning_title' => 'Are you sure to edit the user?',
        'edit_warning_sp' => 'Caution: Editing this Service Provider will update all users (Workers) [Region, Contract, Building] associated with it as well',

        'want_to_save_changes_title' => 'Do you want to save changes?',

        'warning_title_sps' => 'Are you sure to delete this user?',
        'delete_warning_sps' => 'Caution: deleting this user will delete all associated workers, unless workers manually assigned to other Supervisor before deleting.',

        'edit_warning_bm' => 'Caution: Editing this Building Manager will update all users (BME) [Region, Contract, Building] associated with it as well',

        'delete_warning_bm' => 'Caution: deleting this user will delete its associated Employees',

        'delete_warning_sp_admin' => 'Caution: deleting this user will delete all associated (Supervisors and workers)',
        'delete_warning_worker' => 'Caution: deleting this user will stop him from receiving work orders and will not able to access app, you have to manually reassign work orders to other worker after deleting.',


        'warning_title_priority' => 'Are you sure to delete this priority?',
        'delete_warning_priority' => 'Caution: Deleting priority will affect the existing contracts and work orders associated with it.',

        'warning_title_assetcat' => 'Are you sure you want to delete this asset category?',
        'delete_warning_assetcat' => 'Caution: Deleting asset category will affect all associated Work orders, Checklists, unit assets, contracts, etc. Once deleted you will not be able to recover this!',

        'warning_title_servicetype' => 'Are you sure you want to delete this service type?',
        'delete_warning_servicetype' => 'Caution: Deleting service type will affect all associated Work orders, Checklists, unit assets, contracts, etc. Once deleted you will not be able to recover this!',


        'warning_title_assetname' => 'Are you sure you want to delete this asset name?',
        'delete_warning_assetname' => 'Caution: Deleting asset name will affect all associated Work orders, Checklists, unit assets, contracts, etc. Once deleted you will not be able to recover this!',

        'warning_title_email_trigger' => 'Are you sure you want to delete this email trigger?',
        'delete_warning_email_trigger' => 'Caution: Deleting triggers will stop associated user from receiving new email triggers.',

        'warning_title_email_escalation' => 'Are you sure you want to delete this email escalation?',
        'delete_warning_email_escalation' => 'Caution: Deleting escalation will stop associated user from receiving new escalation emails.',
        'warning_title_room' => 'Are you sure you want to delete this unit?',
        'delete_warning_room' => 'Caution: Deleting Unit Type will affect the existing properties and work orders associated with it.',

        'warning_title_unit_recevial' => 'Are you sure you want to delete this unit recevial place?',
        'delete_warning_unit_recevial' => 'Caution: Deleting unit recevial Type will affect the existing properties and work orders associated with it.',
        'not_unique_unit_recevial' => 'Please enter an unique unit recevial place',

        'warning_title_checklist' => 'Are you sure you want to delete this checklist?',
        'delete_warning_checklist' => 'Caution: Deleting checklist will affect the Work orders associated with it.',
        'delete_warning_space_nut' => 'Caution: Deleting unit will be affecting associated Assets and Work Orders.',

        'delete_warning_assetno' => 'Caution: Deleting asset will be affecting associated Work Orders.',
        'deleted' => ' [Deleted]',

        'delete_warning_qrcode' => 'Caution: Deleting QR code will disable maintenance portal users from raising work orders.',
        'warning_title_workorder' => 'Are you sure you want to delete this work order?',
        'update_wtf_title' => 'Are you sure you want to save changes?',
        'update_wtf_message' => 'Caution: changing work time frame and adding vacation days will apply on newly created work orders, but earlier created work order need to be edited to apply the change on them.',
        'warning_title_workorder_pm' => 'Are you sure you want to delete this preventive work order?',
        'delete_warning_message_pm' => 'Caution: Deleting a preventive work order will delete all associated work orders.',


        'email_varification_resend_title' => 'Are you sure you want to resend an email verification for this user?',

        'place_added_successfully' => 'Place has been added',
        'place_removed_successfully' => 'Place has been Removed',
        'place_edited_successfully' => 'Place has been Edited',
    ),

    /**
     * General Status translate
     */
    'g_status' => array(
        'overdue' => 'Overdue',
        'pending' => 'Pending',
        'ongoing' => 'Ongoing',
        'emergency' => 'Emergency',
        'reschedule_request' => 'Reschedule Request',
        'first_slot' => 'First Slot',
        'second_slot' => 'Second Slot',
        'third_slot' => 'Third Slot',
        'fourth_slot' => 'Fourth Slot',
        'fifth_slot' => 'Fifth Slot',
    ),

    /**calender button */
    'calander' => array(
        'apply' => 'Apply',
        'cancel' => 'Cancel',
    ),
    'quotos' => array(
        'quots_deleted' => 'Quotation successfully deleted.',
        'quots_updated' => 'Quotation status successfully updated.'
    ),

    /**
     * commonly usage larnguage
     */
    's_global' => array(
        'sms_send_success' => 'SMS has been sent to the tenant successfully',
        'no_eligible_tenant_found' => 'No eligible tenant found',
        'no_output' => 'No output format selected.',
        'added_success' => 'Successfully created',
        'added_error' => 'An error occurred while adding the',
        'updated_success' => 'Successfully updated',
        'error_update' => 'An error occurred while updating',
        'status_updated' => 'Status updated successfully',
        'status_updated_error' => 'Something went wrong',
        'delete_success' => 'successfully deleted',
        'activate' => 'Activate',
        'deactivate' => 'Deactivate',
        'role_cannot_delete' => 'There are active users with this group. You can not delete this group. To delete this group assign the users to other group and try again.',
        'goup_success' => 'Group successfully',
        'service_success' => 'Service successfully',
        'delete_state_first' => 'Please delete states related to this country!',
        'service_cannot_delete' => 'There are active contracts with this service. You cannot delete this service.',
        'setting_updated' => 'Setting successfully updated',
        'cancel' => 'Cancel',
        'save' => 'Save',
        'auth' => array(
            'invalid_passwrod' => 'Invalid Password',
            'not_authrized_user' => 'You are not an authorized user',
            'error_email_sent' => 'An error occurred while sending you the email containing the password',
            'email_sent' => 'Password Recovery Link has been sent to your email',
            'email_not_register' => 'This email id is not registered',
            'new_password_updated' => 'Your new password successfully updated',
            'something_wrong_try_again' => 'Something went wrong. Please try again later',
            'password_not_match' => 'Current password does not matched',
            'password_changed_success' => 'Password successfully changed',
        ),
        'contract_section' => array(
            'service_already_assign' => 'Service already assigned by the service provider. You can not edit the service now',
            'no_date_found' => 'No dates found within this recurrence pattern provided',
            'last_data_recurrence' => 'Last date for your recurrence exceeds end date of the contract',
            'recurrence_exceeds_contract' => 'Last date for your recurrence exceeds end date of the contract',
            'atleast_one_service' => 'There should be atleast one service for the contract',
            'contract_service_sts' => 'Contract service successfully',
            'at_least_one_service' => 'Please add atleast one service for the contract',
            'cannot_delete' => 'You can not delete this Contract. This contract is used somewhere in the panel.',
            'attachement_file_deleted' => 'Attachement file successfully deleted',
            'contract_info_update' => 'Contract info updated.'
        ),
        'emergency_service_management' => array(
            'assigned_emg_work' => 'assigned you an Emergency Work Order.',
            'modified_emg_work_order' => 'modified the Emergency Work Order which had been assigned to previously.',
        ),
        'labour_module' => array(
            'already_leave' => 'Already Leave adde for this user on',
            'labour_leave_sucess_msg' => 'labour leave successfully',
        ),
        'module_management' => array(
            'functionality_updated' => 'Functionality has been updated successfully',
            'error_while_update' => 'An error occurred while updating the Functionality',
            'error_occurred_delete_module' => 'An error occurred while deleting the Module list',
            'error_occurred_delete_function' => 'An error occurred while deleting the Functionality list',
            'error_occurrd_adding_functionality' => 'An error occurred while adding the Functionality',
        ),
        'spcompany'=>[
            'updated_success'=>'the verification team will respond to your request within 7 working days.'
        ]


    ),

    /**toster updated */
    'tostr_lang' => array(
        'success_deleted' => 'Successfully deleted',
        'invalid_url' => 'Invalid URL',
        'internal_server_error' => 'Internal server error',
        'status_success_updated' => 'Status successfully updated',
        'success' => 'Success',
        'error' => 'Error',

        'sms_success_send' => 'SMS has been sent to the tenant successfully',
        'sms_success' => 'Success',

    ),
    'payment_lang' => array(
        'installment_paid' => 'Installment successfully paid',
        'installment_already_paid' => 'This installment already paid',
        'contract_paid' => 'Contract amount successfully paid'
    ),

    'validation' => [
        'email_dns_failed' => 'Our system has detected, that the email address connected to this account is invalid and did not pass our DNS check. Please contact your system administrator to update your email address.',
        'asset_item_owner_sp_admin' => 'The asset item owner will be the service provider admin',
        'asset_item_owner_admin' => 'The asset item owner will be the project owner admin',
        'checklist_title_already_exist' => 'Checklist title already exist',
        'data_added_successfully' => 'Added new task',
        'added_new_records' => 'Record has been added',
        'deleted_successfully' => 'Record has been deleted',


        'max_length_is_ten' => 'Please do not enter more than 10 letters or numbers.',
        'Admin_name_is_invalid' => 'The Admin name format is invalid.',
        'Phone_number_is_invalid' => 'The Admin phone must be a number.',
        'please_enter_at_least_one_task' => 'Please Enter One Task At Least',
        'min_five' => 'Please enter at least 5 characters',
        'there_are_no_workorders' => 'There are no work orders within the selected period',
        'there_are_no_mr' => 'No maintenance requests within applied options',
        'there_are_no_qr' => 'No QR codes within entries, please try again',
        'service_provider_name_already_exists' => 'Service provider name already exists in the system, please enter new one',

        'emailNotSent' => 'Apologies Email will not be sent, due to an issue',
        'smsNotSent' => 'Apologies SMS will not be sent, due to an issue',

        'notice' => 'Notice ',

        'Please_enter_alphanumeric_value' => 'Please enter alphanumeric value',
        'Please_enter_no_more_than_model_number' => 'Please enter no more than 40 number',
        'Please_enter_no_more_than_manufacturer_name' => 'Please enter no more than 40 number',
        'Please_enter_no_more_than_general_information' => 'Please enter no more than 150 number',
        'Tags_Mode' => 'Tags Mode',
        'Beneficiary_already_exist_Enter_different_ID' => 'Beneficiary already exist. Enter different ID',
        'Please_upload_only_jpg_jpeg_png_pdf_image_of_max_size_1mb' => 'Please upload only jpg/jpeg/png/pdf image of max size 1mb',
        'Please_upload_only_jpg_jpeg_png_image_of_max_size_1mb' => 'Please upload only jpg/jpeg/png image of max size 1mb',
        'Please_upload_only_jpg_jpeg_png_image_of_max_size_10mb' => 'Please upload only jpg/jpeg/png image of max size 1mb',

        'File_size_should_not_be_more_than_1_mb' => 'File size should not be more than 1 mb',
        'File_size_should_not_be_more_than_10_mb' => 'File size should not be more than 10 mb',

        'File_max_upload_not_more_than_10' => 'Maximum 10 files can be uploaded',
        'File_max_upload_not_more_than_9' => 'Maximum 9 files can be uploaded',
        'File_max_upload_not_more_than_8' => 'Maximum 8 files can be uploaded',
        'File_max_upload_not_more_than_7' => 'Maximum 7 files can be uploaded',
        'File_max_upload_not_more_than_6' => 'Maximum 6 files can be uploaded',
        'File_max_upload_not_more_than_5' => 'Maximum 5 files can be uploaded',
        'File_max_upload_not_more_than_4' => 'Maximum 4 files can be uploaded',
        'File_max_upload_not_more_than_3' => 'Maximum 3 files can be uploaded',
        'File_max_upload_not_more_than_2' => 'Maximum 2 files can be uploaded',
        'File_max_upload_not_more_than_1' => 'Maximum 1 files can be uploaded',

        'Please_upload_only_jpg_jpeg_png_pdf_image' => 'Please upload only jpg/jpeg/png/pdf file',
        'Please_upload_only_jpg_jpeg_png_image' => 'Please upload only jpg/jpeg/png image',
        'Please_upload_only_jpg_jpeg_png_imagezip' => 'Please upload only jpg/jpeg/png/zip/pdf Files',

        'Please_upload_only_jpg_jpeg_png_image_tenant' => 'Please upload only jpg/jpeg/png/pdf image',

        'Error' => 'Error',
        'Success' => 'Success',
        'warning' => 'Warning',
        'Updated' => 'Updated',
        'Asset_Category' => 'Service Type',
        'Percentage' => 'Percentage',
        'Osool' => 'Osool',
        'Welcome_to_osool' => 'Welcome to Osool',
        'Your_admin_has_created_an_account_for_you' => 'Your admin has created an account for you Click on the button to log-in and set your password',
        'thank_you' => 'Thank You',
        'Hi' => 'Hi',
        'Account_created_success_fully' => 'Account created success fully',
        'label' => 'label',
        'average' => 'average',
        'Project_already_exist_Enter_different_project_name' => 'Project already exist. Enter different project name',
        'Please_enter_exactly_3_digits' => 'Three characters minimum ',
        'Building_already_exist_Enter_different_building_name' => 'Building already exist. Enter different building name',
        'Provider_ID_already_exist_Enter_different_ID' => 'Provider ID already exist. Enter different ID',
        'Contact_email_already_exist_Enter_different_email' => 'Contact email already exist. Enter different email',
        'Contact_number_already_exist_Enter_different_number' => 'Contact number already exist. Enter different number',
        'Please_select_at_least_one_checkbox_of_Work_Order_Approving' => 'Please select at least one checkbox of Work Order Approving.',
        'Please_select_at_least_one_checkbox_of_Work_Order' => 'Please select at least one checkbox of Work Order',
        'Please_select_at_least_one_checkbox_of_Worker' => 'Please select at least one option for “Take action on behalf of workers”',
        'Please_select_at_least_one_checkbox_of_use_qr' => 'Please select at least one checkbox of the QR code.',
        'Please_select_at_least_one_checkbox_of_purchases' => 'Please select at least one checkbox of Purchase',
        'Please_select_at_least_one_checkbox_of_inventory' => 'Please select at least one checkbox of Inventory',
        'Please_select_at_least_one_checkbox_of_vendors' => 'Please select at least one checkbox of Vendor',
        'Please_select_at_least_one_checkbox_of_warehouses' => 'Please select at least one checkbox of Warehouse',
        'Please_select_at_least_one_checkbox_of_report_maintenance' => 'Please select at least one checkbox of Report Maintenance.',
        'Please_select_at_least_one_checkbox_of_Assets' => 'Please select at least one checkbox of Assets.',
        'Please_select_at_least_one_checkbox_of_contracts' => 'Please select at least one checkbox of contracts.',
        'Please_select_at_least_one_checkbox_of_tenant' => 'Please select at least one checkbox of Tenant Management.',
        'Please_select_at_least_one_checkbox_of_warehouse_privilege' => 'Please select at least one checkbox of Warehouse Privileges.',

        'Please_select_at_least_one_checkbox_of_Service_Provider' => 'Please select at least one checkbox of Service Provider.',
        'Please_select_at_least_one_checkbox_of_beneficiary' => 'Please select at least one checkbox of beneficiary.',
        'Please_select_at_least_one_checkbox_of_property' => 'Please select at least one checkbox of property.',
        'Phone_no_alredy_exist_Enter_different_number' => 'Phone no. already exist. Enter different number',
        'Worker_ID_alredy_exist_Enter_different_one' => 'The ID is already taken please use another ID',
        'Email_already_exist_Enter_different_email' => 'Email already exist. Enter different email',
        'Phone_number_already_exist_Enter_different_number' => 'Phone number already exist. Enter different number',
        'Employee_ID_already_exist_Enter_different_one' => 'The ID is already taken please use another ID',
        'No_users_found' => 'No users found',
        'Email_alredy_exist_Enter_different_email' => 'Email already exist. Enter different email',
        'Email_format_not_Valid' => 'Email format not Valid',
        'Existing_password_is_not_matching' => 'Existing password is not matching',
        'Password_and_confirm_password_does_not_match' => 'Password and confirm password does not match',
        'Data_successfully_validated' => 'Data successfully validated.',
        'Please_check_all' => 'Please check all',
        'Letters_numbers_and_only_these_characters_special_are_allowed' => 'Letters, numbers, and only these characters -  _  / \\  are allowed',
        'Sorry_Multiple_Option_is_not_less_than_1' => 'Sorry Multiple Option is not less than 1',
        'Sorry_Multiple_Option_must_be_1' => 'Sorry Multiple Option must be 1',
        'Sorry_Multiple_Option_is_Limit_5' => 'Sorry Multiple Option is Limit 5',
        'Please_Select_5_Days_minimum' => 'Please Select 5 Days minimum',
        'Please_Enter_Asset_Category' => 'Please Enter Asset Category',
        'Please_Select_Service_Type' => 'Please Select Service Type',
        'Please_Select_Priority' => 'Please Select Priority',
        'Letters_underscores_only_please' => 'Letters underscores only please',
        'Please_enter_no_more_than_4_number' => 'Please enter no more than 4 number',
        'Please_enter_no_more_than_3_number' => 'Please enter no more than 3 number',
        'Please_enter_3_numbers' => 'Please enter between 3 to 45 numbers',

        'only_char_allow' => 'Please enter only characters',
        'Details' => 'Details',
        'Employee_ID' => 'Employee ID',
        'E_mail' => 'E-mail',
        'Sign_in_to_Osool' => 'Sign in to Osool',
        'Have_a_blessed_day' => 'Have a blessed day',
        'Support_Team' => 'Support Team.',
        'No_results_found' => 'No results found',
        'load_more' => 'Load More',
        'load_less' => 'Load Less',
        'No_contracts_found1' => 'No contracts for selected service',


        'No_contracts_found' => 'No contracts found',
        'Geocoder_failed_due_to' => 'Geocoder failed due to:',
        'Please_enter_a_message_in_the_chat_box' => 'Please enter a message in the chat box',
        'Message_submitted_Successfully' => 'Message submitted Successfully!',
        'Warranty_Closed_Successfully' => 'Warranty Closed Successfully!',
        'Please_enter_Re_open_reason' => 'Please enter Re-open reason!',
        'Job_Re_opened_Successfully' => 'Job Re-opened Successfully!',
        'Please_select_the_Proposed_Date' => 'Please select the Proposed Date!',
        'Please_select_worker' => 'Please select worker!',
        'Status_updated_Successfully' => 'Status updated Successfully!',
        'Please_enter_reason' => 'Please Enter reason!',
        'Your_account_has_been_deactivated' => 'Your account has been deactivated',
        'Your_account_has_been_deleted' => 'Your account has been deleted',
        'Please_Enter_Suggested_Date' => 'Please Enter Suggested Date!',
        'Please_select_a_Worker' => 'Please select a Worker!',
        'Please_enter_a_reason' => 'Please enter a reason!',
        'Please_confirm_your_password_before_continuing' => 'Please confirm your password before continuing.',
        'Send_Password_Reset_Link' => 'Send Password Reset Link',
        'This_field_is_required' => 'This field is required.',
        'field_is_required' => 'This field is required',
        'atleaseOne' => 'At least select one to be required',
        'Please_enter_no_more_than_1_number' => 'Please enter at least 1 characters.',
        'Please_enter_no_more_than_200_number' => 'Please enter at least 200 characters.',
        'Please_enter_no_more_than_150_number' => 'Please enter no more than 150 characters.',
        'max_3_pictures' => 'Max 3 picture',
        'Please_enter_at_least_2_characters' => 'Please enter at least 2 characters.',
        'Please_enter_at_least_2_numbers' => 'Please enter at least 2 characters or numbers.',

        'Please_enter_at_least_10_characters' => 'Please enter at least 10 characters.',
        'Please_enter_at_least_6_characters' => 'Please enter at least 6 characters.',
        'Please_enter_no_more_than_50_characters' => 'Please enter no more than 50 characters.',
        'Please_enter_no_more_than_80_characters' => 'Please enter no more than 80 characters.',
        'Please_enter_no_more_than_30_characters' => 'Please enter no more than 30 characters.',
        'Please_enter_no_more_than_8_characters' => 'Please enter no more than 8 characters.',
        'Please_enter_no_more_than_60_characters' => 'Please enter no more than 60 characters.',
        'Please_enter_no_more_than_20_characters' => 'Please enter no more than 20 characters.',
        'Please_enter_no_more_than_40_characters' => 'Please enter no more than 40 characters.',
        'latter_no_space_only_please' => 'Letters, Numbers And Space Only Please.',
        'Please_enter_no_more_than_4_characters' => 'Please enter no more than 4 characters.',

        'Please_enter_no_more_than_12_characters' => 'Please enter no more than 12 characters.',
        'Please_enter_no_more_than_10_characters' => 'Please enter no more than 10 characters.',
        'Please_enter_no_more_than_15_characters' => 'Please enter no more than 15 characters.',
        'Please_enter_no_more_than_100_characters' => 'Please enter no more than 100 characters.',
        'Please_enter_no_more_than_500_characters' => 'Please enter no more than 500 characters.',
        'Please_enter_no_more_than_500_characters2' => 'The maximum number of characters is 500 characters',

        'Please_enter_a_valid_email_address' => 'Please enter a valid email address.',
        'Please_enter_at_least_9_characters' => 'Please enter at least 9 numbers.',
        'Please_enter_at_max_9_characters' => 'Please enter no more than 9 numbers.',
        'Please_enter_at_least_9_numbers' => 'Please enter at least 9 numbers.',

        'Please_enter_9_numbers' => 'Please enter 9 numbers',

        'Please_enter_a_valid_number' => 'Please enter a valid number.',
        'Please_enter_no_more_than_9_characters' => 'Please enter no more than 9 characters.',
        'Property_Name_is_required_field' => 'Property_Name_is_required_field',
        'Floor_is_required_field' => 'Floor is required field',
        'Room_is_required_field' => 'Room is required field',
        'Asset_category_is_required_field' => 'Asset category is required field',
        'The_asset_name_define_field_is_required' => 'The asset name define field is required.',
        'The_asset_number_define_field_is_required' => 'The asset number define field is required.',
        'The_Contracts_field_is_required' => 'The Contracts field is required.',
        'Description_is_required_field' => 'Description is required field',
        'Please_check_all_errors' => 'Please check all errors',
        'Asset_name_is_required' => 'Asset name is required',
        'Asset_number_is_required' => 'Asset number is required',
        'Contract_is_required' => 'Contract is required',
        'Frequency_is_required' => 'Frequency is required',
        'Start_date_is_required_field' => 'Start date is required field',
        'End_date_is_required_field' => 'End date is required field',
        'The_Start_Date_must_be_a_date_before_or_equal_to_End_Date' => 'The Start Date must be a date before or equal to End Date.',
        'The_End_Date_must_be_a_date_after_or_equal_to_Start_Date' => 'The End Date must be a date after or equal to Start Date.',
        'Please_Enter_Asset_Category' => 'Please Enter Service Type',
        'Letters_numbers_and_only_these_characters_are_allowed' => 'Letters, numbers, and only these characters - _ / \ are allowed',
        'Please_enter_unique_check_list_title' => 'Please enter unique check list title',
        'Please_enter_no_more_than_200_characters' => 'Please enter no more than 200 characters.',
        'Please_enter_no_more_than_400_characters' => 'Please enter no more than 400 characters.',
        'Contract_number_is_required_field' => 'Contract number is required field',
        'Service_Provider_is_required_field' => 'Service Provider is required field',
        'Contract_type_is_required_field' => 'Contract type is required field',
        'Region_is_required_field' => 'Region is required field',
        'City_is_required_field' => 'City is required field',
        'Property_name_is_required_field' => 'Property name is required field',
        'Letters_numbers_and_space_only_please' => 'Special characters are not allowed in this field',
        'Contract_number_maximum_allow_only_20_char' => 'Contract number maximum allow only 20 char',
        'The_Contract_number_must_be_at_least_6_characters' => 'The Contract number must be at least 6 characters.',
        'Number_of_worker_is_required_field' => 'Number of worker is required field',
        'Number_of_supervisor_is_required_field' => 'Number of supervisor is required field',
        'Number_of_worker_allow_max_200' => 'Number of worker allow max 200',
        'The_Number_of_supervisor_may_not_be_greater_than_50' => 'The Number of supervisor may not be greater than 50.',
        'Number_of_administrator_is_required_field' => 'Number of administrator is required field',
        'The_Number_of_administrator_may_not_be_greater_than_50' => 'The Number of administrator may not be greater than 50.',
        'Number_of_engineer_is_required_field' => 'Number of engineer is required field',
        'The_Number_of_engineer_may_not_be_greater_than_50' => 'The Number of engineer may not be greater than 50.',
        'Asset_already_exist_Enter_different_asset' => 'Asset already exist. Enter different asset',
        'Please_enter_only_digits' => 'Please enter only digits',

        'pass_fail_min_length' => 'Minimum 1',
        'pass_fail_max_length' => 'Please enter a valid number (0-100)',

        'target_response_min_length' => 'Minimum 1',
        'target_response_max_length' => 'Maximum 999',

        'workorder_reopen_min_length' => 'Minimum 1',
        'workorder_reopen_max_length' => 'Please enter a valid number (0-999)',
        'workorder_reminder_max_length' => 'Please enter a valid number (1-999)',

        'project_name_already_exist' => 'This name is already exist!',
        'it_should_be_anumber' => 'It should be a number',

        'facility_management_number' => 'Allow number only',
        'facility_management_min_length' => 'Minimum 9',
        'facility_management_max_length' => 'Maximum 9',

        'service_provider_number' => 'Allow number only',
        'service_provider_min_length' => 'Minimum 9',
        'service_provider_max_length' => 'Maximum 9',
        'contact_info_success' => 'Contact Information updated Successfully!',
        'the_client_industry_field_required' => 'The Client industry field is required',
        'the_client_name_field_required' => 'Client name is required field',

        'please_enter_at_least_5_characters' => 'Please enter at least 5 characters',

        'login_email_field_is_required' => 'The Email Address field is required.',
        'login_password_field_is_required' => 'The password field is required.',

        'tenant_facility_management_please_enter_9_characters' => 'Please enter 9 characters.',

        'tenant_service_provider_please_enter_9_characters' => 'Please enter 9 characters.',
        'alphanumeric' => 'Letters, Numbers And Space Only Please',
        'minimum_1_value' => 'Please enter a value greater than or equal to 1',
        'please_enter_number_for_cost' => 'Please enter number for cost',
        'max_no_of_sub_task' => 'Maximum number of sub tasks is 15',
        'min_no_of_sub_task' => 'Minimum number of sub tasks is 1',

        'please_select_at_least_one_checkbox_of_public_service_provider' => 'Please select at least one checkbox of Public Service Providers.',
        'please_select_at_least_one_checkbox_of_client_requests' => 'Please select at least one checkbox of Client Requests.',
        'please_select_at_least_one_checkbox_of_edit_project' => 'Please select at least one checkbox of Edit Project.',
        'please_select_at_least_one_checkbox_of_subscribers' => 'Please select at least one checkbox of Subscribers.',
        'please_select_at_least_one_checkbox_of_registration_requests' => 'Please select at least one checkbox of Registration requests.',
        'please_select_atleast_one_option' => 'Please select at least one option',
        'please_enter_a_number_between_1_999' => 'Please enter a number between 1-999',
        'please_enter_a_number_between_1_5' => 'Please enter a number between 1-5',
        'please_enter_a_number_between_1_10' => 'Please enter a number between 1-10',
        'Please_enter_number_equal_or_greater_than_1' => 'Please enter number equal or greater than 1.',
    ],

    'empty_ui' => [
        'No_Units_Here_heading' => 'No Units Here',
        'No_Units_Here_para' => 'This Property has no space Units Add new Unit',
        'no_workorders_avaliable' => 'No workorders available',
        'sub_title_no_workorders_avaliable' => 'This table has no workorders to show',
        'no_documents_avaliable' => 'No documents available',
        'sub_title_no_documents_avaliable' => 'This table has no documents to show',
        'no_payrolls_avaliable' => 'No payrolls available',
        'sub_title_no_payrolls_avaliable' => 'This table has no payrolls to show',
        'no_inspection_reports_avaliable' => 'No Inspection reports available',
        'sub_title_no_inspection_reports_avaliable' => 'This table has no Inspection reports to show',
        'no_mr_found' => 'No maintenance requests found',
        'The_maintenance_list_will_appear_here' => 'Maintenance request list will appear here',
        'No_maintenance_yet' => 'No maintenance requests yet',
        'no_beneficiary_yet' => 'No beneficiaries yet',
        'no_contracts_linked' => 'No contracts linked',
        'No_contracts_yet' => 'No contracts yet',
        'No_notifications_yet' => 'No notifications yet',
        'No_properties_yet' => 'No properties yet',
        'No_service_provider_yet' => 'No service provider yet',
        'No_users_yet' => 'No users yet',
        'No_leave_requests_yet' => 'No leave requests yet',
        'No_workorders_yet' => 'No workorders yet',
        'select_sp_admin' => 'Select primary admin of company',
        'No_reports_yet' => 'No Reports yet',
        'No_exports_yet' => 'No Exports yet',

        'No_prev_reports_yet' => 'No previous reports yet',
        'No_failed_reports_yet' => 'No failed reports yet',
        'previous_reports_show_here' => 'The previous reports will show here',
        'failed_reports_show_here' => 'The failed reports will show here',
        'No_recent_reports_yet' => 'No recent reports yet',
        'recent_reports_show_here' => 'The recent reports will show here',

        'The_beneficiary_list_will_appear_here' => 'The beneficiary list will appear here',
        'The_contracts_list_will_appear_here' => 'The contracts list will appear here',
        'The_properties_list_will_appear_here' => 'The properties list will appear here',
        'The_service_provider_list_will_appear_here' => 'The service provider list will appear here',
        'The_users_list_will_appear_here' => 'The users list will appear here',
        'The_workorder_list_will_appear_here' => 'The workorder list will appear here',
        'Your_received_notifications_will_appear_here' => 'Your received notifications will appear here',
        'Your_received_notifications_will' => 'Your received notifications will',
        'appear_here' => 'appear here',
        'This_property_has_no_contract_linked_to_it' => 'This property has no contract linked to it',
        'This_property_has_no_assets_defined' => 'This property has no assets defined',
        'No_Assets_here' => 'No Assets here',
        'No_photos_submitted' => 'No photos submitted',
        'There_is_no_photos_uploaded_for_this_workorder' => 'There is no photos uploaded for this workorder',
        'No_Attachments_uploaded' => 'No Attachments uploaded',
        'There_are_no_attachments_uploaded_for_this_workorder' => 'There are no attachments uploaded for this work order',
        'The_workorder_list_will_appear_here' => 'The workorder list will appear here',
        'The_report_list_will_appear_here' => 'The generated reports will show here',

        'processing' => 'Processing...',
        'No_closed_workorders_yet' => 'No closed work orders yet',
        'The_closed_workorder_list_will_appear_here' => 'The closed work orders will appear here',
    ],
    'not_selected' => 'Not Selected',
    'powered_by' => 'Powered by',
    'available' => 'Available',
    'sold' => 'Sold',
    'rented' => 'Rented',
    'booked' => 'Booked',
    'Create_Document' => 'Create Document',
        'Edit_Document' => 'Edit Document',


);
