<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ (Session::get('locale')=='ar' ? 'rtl' : 'ltr') }}">
<style>
        .quotation .class-cancelled-status {
            background: rgba(255, 77, 79, 0.15);
            color: #ff4d4f;
        }

        .quotation .class-open-status {
            background: rgba(32, 201, 151, 0.15);
            color: #20c997;
        }
    
        .quotation-link {
            padding-left: 150px;
            padding-right: 150px;
            padding-top: 50px;
            padding-bottom: 80px;
        }
   
</style>


<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title @if(App::getLocale() == 'en') lang = "en" @else lang = "ar" @endif>@lang('import.osool')</title>

    <meta name="description" content="@yield('page_description', $pageDescription ?? '')"/>

    @livewireStyles

   <link rel="stylesheet" href="{{ asset('new_files/new_font.css') }}?v={{ filemtime(public_path('new_files/new_font.css')) }}">

    @include('layouts.partials._styles')

    @yield('styles')

    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('favicon/apple-touch-icon.png')}}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('favicon/favicon-32x32.png')}}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('favicon/favicon-16x16.png')}}">
    <link rel="shortcut icon" type="image/png" sizes="32x32" href="{{ asset('favicon/favicon-32x32.png')}}">
    <link rel="shortcut icon" type="image/png" sizes="16x16" href="{{ asset('favicon/favicon-16x16.png')}}">
    <link rel="manifest" href="{{ asset('favicon/site.webmanifest')}}">
    <link rel="mask-icon" href="{{ asset('favicon/safari-pinned-tab.svg')}}" color="#5bbad5">

    <script src="{{ asset('new_files/axios.min.js') }}?v={{ filemtime(public_path('new_files/axios.min.js')) }}"></script>

    @if(App::getLocale()=='ar')
        <style>
            .toggle-icon {
                transform: rotate(180deg);
            }
        </style>
    @endif
</head>
   <body>
    <div class="quotation-link">

        <div class="container-fluid">
            <div class="row">

                <div class="col-lg-12">
                    <div class="project-progree-breadcrumb">
                        <div class="breadcrumb-main user-member justify-content-sm-between ">
                            <div class=" d-flex flex-wrap justify-content-center breadcrumb-main__wrapper">
                                <div class="d-flex flex-column user-member__title mr-sm-25">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title page-title__left">
                                        {{ __('quotation.common.quotation') }} 
                                    </h4>


                                </div>

                            </div>
                            <div class="action-btn">
                                <div class="d-flex align-items-center">
                                    <span class="fw-600 text-dark">{{ __('quotation.forms.labels.status') }}: </span>
                                    @switch($aka_quotation['status'])
                                        @case(\App\Enums\PurchaseAndSales\QuotationStatus::DRAFT->value)
                                            <span
                                                class="bg-draft text-dark rounded-pill userDatatable-content-status active ml-2 py-3 px-4">
                                                {{ __('quotation.common.status.draft') }}
                                            </span>
                                        @break


                                        @case(\App\Enums\PurchaseAndSales\QuotationStatus::PENDING->value)
                                            <span
                                                class="badge-pending rounded-pill userDatatable-content-status active ml-2 py-3 px-4">
                                                {{ __('quotation.common.status.pending') }}
                                            </span>
                                        @break


                                        @case(\App\Enums\PurchaseAndSales\QuotationStatus::ISSUED->value)
                                            <span
                                                class="bg-opacity-issued text-issued rounded-pill userDatatable-content-status active ml-2 py-3 px-4">
                                                {{ __('quotation.common.status.issued') }}
                                            </span>
                                        @break

                                       

                                        @case(\App\Enums\PurchaseAndSales\QuotationStatus::SENT->value)
                                            <span
                                                class="bg-opacity-danger color-danger rounded-pill userDatatable-content-status active ml-2 py-3 px-4">
                                                {{ __('quotation.common.status.sent') }}
                                            </span>
                                        @break

                                        @case(\App\Enums\PurchaseAndSales\QuotationStatus::CONFIRMED->value)
                                            <span
                                                class="bg-opacity-success color-success rounded-pill userDatatable-content-status active ml-2 py-3 px-4">
                                                {{ __('quotation.common.status.confirmed') }}
                                            </span>
                                        @break

                                        @case(\App\Enums\PurchaseAndSales\QuotationStatus::REFUSED->value)
                                        <span
                                            class="bg-opacity-danger color-danger rounded-pill userDatatable-content-status active ml-2 py-3 px-4">
                                            {{ __('quotation.common.status.refused') }}
                                        </span>
                                        @break

                                        @default
                                            <span
                                                class="bg-opacity-warning text-warning rounded-pill userDatatable-content-status active ml-2 py-3 px-4">
                                                {{ __('quotation.common.status.default') }}
                                            </span>
                                    @endswitch

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Tab Menu End -->

            <div class="row">
                <div class="col-md-12">
                    <!-- Confirmation Steps -->
                    <div class="checkout wizard9 global-shadow pt-2 pb-20 mb-30w-100">
                        <div class="row justify-content-center">
                            <div class="col-xl-12">
                                <div class="card checkout-shipping-form p-3 pt-2 pb-0 mt-lg-0 mt-30">
                                    <div class="row justify-content-center">
                                        <div class="col-lg-4 pr-sm-0 pr-15">
                                            <div class="checkout-progress-indicator card pur-req-details">
                                                <div class="card-body px-3">
                                                    <div class="border-bottom pb-3 mb-3">
                                                        <h4 class="mb-2 fw-400">
                                                            {{ __('quotation.common.quotation_created_at') }} </h4>



                                                        
                                                            @if ($osool_quotation->createdBy)
                                                                <span>{{ __('quotation.common.quotation_created', [
                                                                    'name' => $osool_quotation->createdBy->name,
                                                                    'date' => Helper::FormatedDatePurchaseRequest($osool_quotation->created_at),
                                                                ]) }}</span>
                                                            @else
                                                                <span>{{ __('quotation.common.quotation_created', [
                                                                    'name' =>  __('quotation.common.not_available'),
                                                                    'date' => Helper::FormatedDatePurchaseRequest($osool_quotation->created_at),
                                                                ]) }}</span>
                                                            @endif


                                                    </div>
                                                    <div class="border-bottom pb-3 mb-3">
                                                        <h4 class="mb-2 fw-400">
                                                            {{ __('quotation.common.send_to_customer') }}</h4>
                                                        <span
                                                            class="mb-2 d-block">{{ __('quotation.common.last_sent') }}:
                                                            {{ Helper::FormatedDateTimePurchaseRequest(!is_null($last_email) ? $last_email->last_attempt_at : '') }}
                                                        </span>
                                                        <div class="d-flex">

                                                        </div>
                                                    </div>
                                                   
                                                </div>
                                            </div>
                                            <!-- checkout -->
                                        </div>
                                        <div class="col-lg-8">
                                            <div class="">
                                                <div class="card">
                                                    <div class="card-body">
                                                        <div class="edit-profile__body">
                                                            <form action="" method="POST"
                                                                enctype="multipart/form-data"
                                                                id="create-quotation-form">

                                                                @csrf

                                                                <div class="company-info pb-3 border-bottom mb-3">
                                                                    <h5 class="mb-4">
                                                                        {{ __('quotation.common.quotation') }}
                                                                      

                                                                    </h5>
                                                                    <div class="d-flex align-items-center">
                                                                        <div
                                                                        class="company-logo border rounded d-flex align-items-center justify-content-center mr-3">
                                                                        @if (!empty($osool_quotation->logo))
                                                                            <img src="{{ ImagesUploadHelper::displayImage($osool_quotation->logo, \App\Enums\MediaFilesPaths::QUOTATION_LOGO->value) }}" 
                                                                                class="w-120px">
                                                                        @else
                                                                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcReEIx3BEicE-AMdsqvD8PfQKNcW9i0iP1ptQ&s"
                                                                                class="w-120px">
                                                                        @endif

                                                                    </div>
                                                                        <div>


                                                                            <h5 class="mb-2">
                                                                                {{ $company_details->name ?? __('quotation.common.not_available') }}

                                                                            </h5>
                                                                            <span class="text-dark d-block mb-2">

                                                                                {{ $company_details->address ?? __('quotation.common.not_available') }}

                                                                            </span>
                                                                            <span class="text-dark d-block mb-2">
                                                                                {{ $company_details->email ?? __('quotation.common.not_available') }}
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="company-info pb-3 border-bottom mb-3">
                                                                    <h5 class="mb-4">
                                                                        {{ __('quotation.common.bill_form') }}</h5>
                                                                    <div class="table-responsive table-items">
                                                                        <table class="table table--default">
                                                                            <thead class="userDatatable-header">
                                                                                <tr class="warehouse-table-tr">
                                                                                    <th class="width">{{ __('quotation.forms.labels.customer') }}</th>
                                                                                    <th class="width">{{ __('quotation.forms.labels.quotation_number') }}</th>
                                                                                    <th class="width">
                                                                                        {{ __('quotation.forms.labels.quotation_date') }}</th>
                                                                                    <th class="width">
                                        
                                                                                        {{ __('quotation.forms.labels.expiry_date') }}</th>
                                                                                </tr>
                                                                            </thead>
                                                                            <tbody id="items-table-body">

                                                                                <tr>
                                                                                    <td>
                                                                                        <p class="mb-0">
                                                                                            {{ !empty($aka_quotation['contact']['name']) ? $aka_quotation['contact']['name'] : __('quotation.common.not_available') }}
                                                                                        </p>
                                                                                        <p class="mb-0">
                                                                                            {{ !empty($aka_quotation['contact']['address']) ? $aka_quotation['contact']['address'] : __('quotation.common.not_available') }}
                                                                                        </p>
                                                                                    </td>
                                                                                    <td>{{ !empty($aka_quotation['document_number']) ? $aka_quotation['document_number'] : 'PRE N/A' }}
                                                                                    </td>
                                                                                    <td> {{ Helper::FormatedDatePurchaseRequest($aka_quotation['issued_at']) }}
                                                                                    </td>
                                                                                    <td>{{ Helper::FormatedDatePurchaseRequest($osool_quotation->expire_at) }}
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <div class="company-info pb-3 border-bottom mb-3">
                                                                    <h5 class="mb-4">
                                                                        {{ __('quotation.common.items') }}</h5>
                                                                    <div class="table-responsive">
                                                                        <table class="table table--default">
                                                                            <thead class="userDatatable-header">
                                                                                <tr class="warehouse-table-tr">
                                                                                    <th class="width">
                                                                                        {{ __('quotation.tabs.items_table.thead.items') }}
                                                                                    </th>
                                                                                    <th class="width">
                                                                                        {{ __('quotation.tabs.items_table.thead.description') }}
                                                                                    </th>
                                                                                    <th class="width">
                                                                                        {{ __('quotation.tabs.items_table.thead.quantity') }}
                                                                                    </th>
                                                                                    <th class="width">
                                                                                        {{ __('quotation.tabs.items_table.thead.price') }}
                                                                                    </th>
                                                                                    <th class="width">
                                                                                        {{ __('quotation.tabs.items_table.thead.tax') }}
                                                                                    </th>
                                                                                    <th class="width">
                                                                                        {{ __('quotation.tabs.items_table.thead.amount') }}
                                                                                    </th>
                                                                                </tr>
                                                                            </thead>
                                                                            @php
                                                                            $subtotal = 0;
                                                                            $taxes_array = [];
                                                                        @endphp
                                                                            <tbody id="items-table-body">
                                                                                @if (!empty($aka_quotation['items']['data']))
                                                                                    
                                                                                    @foreach ($aka_quotation['items']['data'] as $line)
                                                                                        <tr>
                                                                                            <td>
                                                                                                {{ $line['name'] ?: __('quotation.common.not_available') }}
                                                                                            </td>
    
    
                                                                                            <td>
                                                                                                {{ $line['description'] ?: __('quotation.common.not_available') }}
                                                                                            </td>
    
                                                                                            <td>
                                                                                                {{ $line['quantity'] ?: __('quotation.common.not_available') }}
                                                                                            </td>
    
                                                                                            <td>
                                                                                                {{ $line['price'] ?: __('quotation.common.not_available') }}
                                                                                            </td>
                                                                                            <td>
                                                                                                {{ $line['taxes']['data'][0]['name'] ?? __('quotation.common.not_available') }}
                                                                                                @php
                                                                                                    $taxes_array[] = $line['taxes']['data'][0]['name'] ?? null;
                                                                                                @endphp
                                                                                            </td>
    
    
    
                                                                                            <td>
                                                                                                {{ $line['total'] ?: __('quotation.common.not_available') }}
                                                                                                @php
                                                                                                    $subtotal = $subtotal + $line['total'];
                                                                                                @endphp
    
                                                                                            </td>
    
                                                                                        </tr>
                                                                                    @endforeach
                                                                                @else
                                                                                    <tr>
    
                                                                                        <td colspan="5"
                                                                                            class="text-danger">
                                                                                            {{ __('quotation.common.no_items_added') }}
                                                                                        </td>
    
                                                                                    </tr>
                                                                                @endif
    
                                                                            </tbody>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <div class="d-flex justify-content-end pb-2 border-bottom">
                                                                    <div class="d-flex">
                                                                        <table
                                                                        class="table table-borderless mb-0 table-price">
                                                                        <tr class="border-bottom">
                                                                            <th>{{ __('quotation.common.sub_total') }}
                                                                            </th>
                                                                            <td> 
                                                                                {{ $subtotal }}
                                                                                {{ $aka_quotation['currency_code'] }}
                                                                            </td>
                                                                        </tr>
                                                                        <tr class="border-bottom">
                                                                            <th>{{ __('quotation.common.tax') }}
                                                                                (
                                                                                    @foreach ($taxes_array as $elem)
                                                                                    {{$elem}}@if (!$loop->last), @endif
                                                                                    @endforeach
                                                                                )
                                                                            </th>
                                                                            <td>
                                                                                {{ $aka_quotation['amount'] - $subtotal }} 
                                                                                {{ $aka_quotation['currency_code'] }}
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <th>{{ __('quotation.common.total') }}
                                                                            </th>
                                                                            <td> {{ $aka_quotation['amount'] }}
                                                                                 {{ $aka_quotation['currency_code'] }} 
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                    </div>
                                                                </div>

                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- ends: card -->
                                        </div>
                                        <!-- ends: col -->

                                    </div>
                                </div>
                            </div>

                            <!-- ends: col -->
                        </div>
                    </div>

                    <!-- End Confirmation Steps -->
                </div>
<!-- Share Quotation Modal -->

  
            </div>
        

            <!-- ends: project tab -->
        </div>
    </div>
   </body>
   </html>