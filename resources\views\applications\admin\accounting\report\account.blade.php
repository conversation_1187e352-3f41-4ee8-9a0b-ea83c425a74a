@extends('layouts.app')
@section('styles')
    <style type="text/css">

    </style>
@endsection
@section('content')
    <div class="contents crm">
        <div class="container-fluid">
            <div class="col-lg-12">
                <div
                    class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                    @include('applications.admin.common.breadcrumb', [
                        'links' => [
                            [
                                'title' => __('accounting.report'),
                            ],
                            [
                                'title' => __('accounting.account_sum'),
                            ],
                        ],
                    ])
                    @livewire('accounting.report.account.action')
                </div>
            </div>


            <div class="table-responsive">

                @livewire('accounting.report.account.filter', ['data' => $response, 'from_date' => $fromDate, 'to_date' => $toDate])
                @livewire('accounting.report.account.account', ['data' => $response])
            </div>
            @livewire('accounting.report.account.payment', ['data' => $response])
        </div>
    </div>
    </div>
    </div>
@endsection
@section('scripts')
    <script src="/js/livewire/manage-datepicker.js"></script>
    <script src="/js/livewire/manage-select2.js"></script>
    <script src="/js/livewire/error-messages.js"></script>
    <script src="/js/livewire/manage-tooltip.js"></script>
    <script src="/js/livewire/manage-loader.js"></script>
@endsection
