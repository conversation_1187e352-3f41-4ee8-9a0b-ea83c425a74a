<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <style type="text/css">
        body {
            margin: 0;
            font-family: "Open Sans", sans-serif;
            font-weight: 400;
            line-height: 1.5;
            color: #666d92;
            text-align: left;
            background-color: #fff;
            position: relative;
            background: #f4f5f7;
            font-size: 13px;
        }

        .container-fluid,
        .container-sm,
        .container-md,
        .container-lg,
        .container-xl {
            padding-right: 15px;
            padding-left: 15px;
            margin-right: auto;
            margin-left: auto;
        }

        .mt-3 {
            margin-top: 1rem !important;
        }

        .w-50 {
            width: 50%;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        .h1,
        .h2,
        .h3,
        .h4,
        .h5,
        .h6 {
            margin-bottom: 0;
            font-family: "Open Sans", sans-serif;
            font-weight: 600;
            line-height: 1.2;
            color: #272b41;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin-top: 0;
            margin-bottom: 0;
        }

        h5,
        .h5 {
            font-size: 18px;
        }

        p {
            margin-top: 0;
            margin-bottom: 1rem;
        }

        .table-responsive {
            display: block;
            width: 100%;
            -webkit-overflow-scrolling: touch;
        }

        .table {
            width: 100%;
            margin-bottom: 1rem;
            color: #212529;
            border-bottom: none;
        }

        .th-borderless {
            border: none;
        }

        table th,
        table td {
            font-size: .8rem !important;
            line-height: 1.3rem;
        }

        table {
            border-collapse: collapse;
        }

        .userDatatable table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .userDatatable table tbody {
            margin-top: 20px;
        }

        .userDatatable table thead tr {
            border-radius: 5px;
        }

        .table.th-osool th {
            color: #152B70;
        }

        .row-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
        }

        .row-grid .full-span {
            grid-column: span 6;
            color: #212529;
            padding-left: 20px;
            padding-top: 10px;
            padding-bottom: 10px;
            padding-right: 5px;
        }

        .userDatatablecontent {
            font-size: 14px;
            line-height: 1.4285714286;
            margin-bottom: 0;
            word-break: break-all;
        }

        .userDatatable table td {
            border-top: 0;
            border-bottom: none;
            padding: 10px 20px;
            vertical-align: middle;
        }

        .userDatatable-header th {
            border-bottom: none;
            padding: 13px 20px 15px 20px;
            vertical-align: middle;
            background: #f8f9fb;
        }

        .userDatatable table thead tr th {
            color: #5a5f7d;
            background: #f8f9fb;
            border-top: 0;
            border-bottom: 0 !important;
            white-space: nowrap;
        }

        .userDatatable table thead tr th:first-child {
            border-left: 0;
            border-radius: 5px 0px 0px 5px;
        }

        .card {
            box-shadow: 0 5px 20px rgba(146, 153, 184, 0.03);
            position: relative;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
            -ms-flex-direction: column;
            flex-direction: column;
            min-width: 0;
            word-wrap: break-word;
            background-color: #fff;
            -webkit-background-clip: border-box;
            background-clip: border-box;
            border: 0;
            -webkit-border-radius: 10px;
            border-radius: 10px;
        }

        .card-body {
            -webkit-box-flex: 1;
            flex: 1 1 auto;
            min-height: 1px;
            padding: 1.56rem;
        }

        .bg-white {
            background: #ffffff;
        }

        /* width */
        .w-100 {
            width: 100% !important;
        }

        .min-w-150 {
            min-width: 150px;
        }

        .min-w-100px {
            min-width: 100px;
        }

        /* heights */
        .h-30 {
            height: 30px !important;
            line-height:30px
        }

        /* padding */
        .p-0 {
            padding: 0 !important;
        }

        .pb-0 {
            padding-bottom: 0;
        }

        .pl-0 {
            padding-left: 0 !important;
        }

        .pr-0 {
            padding-right: 0 !important
        }

        .px-0 {
            padding-left: 0 !important;
            padding-right: 0 !important;
        }

        .py-0 {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
        }

        .pl-3 {
            padding-left: 1rem !important;
        }

        .px-3 {
            padding-left: 1rem !important;
            padding-right: 1rem !important;
        }

        .pb-4 {
            padding-bottom: 1.5rem !important;
        }

        .py-4 {
            padding-bottom: 1.5rem !important;
            padding-top: 1.5rem !important;
        }
        .py-2 {
            padding-bottom: 0.5rem !important;
            padding-top: 0.5rem !important;
        }

        .p-4 {
            padding: 1.5rem !important;
        }

        .pt-4 {
            padding-top: 1.5rem !important;
        }
        .pt-2 {
            padding-top: 0.5rem !important;
        }

        .pt-0 {
            padding-top: 0;
        }


        .pr-15 {
            padding-right: 15px;
        }

        .pl-15 {
            padding-left: 15px;
        }

        /* margin */
        .mb-0 {
            margin-bottom: 0;
        }

        .mb-3 {
            margin-bottom: 1rem !important;
        }
        .mr-3 {
            margin-right: 1rem !important;
        }

        .mb-4 {
            margin-bottom: 1.5rem !important;
        }
        .mb-2 {
            margin-bottom: 0.5rem !important;
        }

        .my-3 {
            margin-bottom: 1rem !important;
            margin-top: 1rem !important;
        }
        .my-2 {
            margin-bottom: 0.5rem !important;
            margin-top: 0.5rem !important;
        }

        .mt-4 {
            margin-top: 1.5rem;
        }
        .mt-2 {
            margin-top: 0.5rem;
        }

        /* font-size-and-styles */
        h5 {
            margin-bottom: 0;
            font-family: "Open Sans", sans-serif;
            font-weight: 600;
            line-height: 1.2;
            color: #272b41;
        }

        p {
            margin-top: 0;
            margin-bottom: 1rem;
        }

        .fs-22 {
            font-size: 22px !important;
        }

        /* row-columns */
        .row {
            display: flex;
            flex-wrap: wrap;
        }

        .col-6 {
            -webkit-box-flex: 0;
            flex: 0 0 50%;
            max-width: 50%;
        }

        /* border */
        .border-0 {
            border: 0 !important;
        }

        .gap-10 {
            gap: 10px;
        }

        /* icons */
        .iconsax {
            font-family: 'iconsax' !important;
            font-style: normal;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
            line-height: 1;
            -webkit-font-smoothing: antialiased;
        }

        .page-entries {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            margin-top: 1.5rem !important;
        }
    </style>

</head>

<body>
    <div class="contents crm">
        <div class="container-fluid">

            <div class="table-responsive">

                <div id="printableArea" class="py-2">
                    <table class="w-100">
                        <tbody>
                            <tr>
                                <td class="w-50">
                                    <div class="card p-4 mr-3 h-30">
                                        <h5 class="mb-0">Report :</h5>
                                        <p class="mb-0">Account Statement Summary</p>
                                    </div>
                                </td>
                                <td class="w-50">
                                    <div class="card p-4 h-30">
                                        <h5 class="mb-0">Duration :</h5>
                                        <p class="mb-0">Jun-2025 to Jan-2025</p>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>


            <div class="card mb-2">
                <div>
                    <div class="py-2 px-3 border-0">
                        <h5>Income</h5>
                    </div>
                </div>

                <div class="card-body p-0">
                    <div class="userDatatable projectDatatable project-table bg-white w-100 border-0 userDatatable">
                        <div class="table-responsive">
                            <table class="table mb-0 radius-0 th-osool table-b-b-none">

                                <thead>
                                    <tr class="userDatatable-header">
                                        <th>
                                            Category
                                        </th>
                                        <th>
                                            Jan-Mar
                                        </th>
                                        <th>
                                            <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Apr-Jun
                                        </th>
                                        <th>
                                            <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Jul-Sep
                                        </th>
                                        <th>
                                            <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Oct-Dec
                                        </th>
                                        <th>
                                            <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Total
                                        </th>
                                    </tr>
                                </thead>

                                <tbody class="sort-table ui-sortable">
                                    <tr>
                                        <td>
                                            <span class="userDatatablecontent">Revenue :</span>
                                        </td>
                                    </tr>

                                    <tr class="ui-sortable-handle" style="opacity: 1;">
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>Test</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span> 0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>632.95K ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>32.01K ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>664.96K ﷼</span>
                                            </div>
                                        </td>
                                    </tr>

                                    <tr class="ui-sortable-handle" style="opacity: 1;">
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>Test cat</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span> 0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>284.5K ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>21.54K ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>306.04K ﷼</span>
                                            </div>
                                        </td>
                                    </tr>

                                    <tr class="ui-sortable-handle" style="opacity: 1;">
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>Test</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span> 0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>22.91K ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>27.24K ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>50.15K ﷼</span>
                                            </div>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td>
                                            <span class="userDatatablecontent">Invoice :</span>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td colspan="6">
                                            <span class="userDatatablecontent">Total Income = Revenue + Invoice</span>
                                        </td>
                                    </tr>

                                    <tr class="ui-sortable-handle" style="opacity: 1;">
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>Total Income</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span> 0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>940.35K ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>80.79K ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>1.02M ﷼</span>
                                            </div>
                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div>
                    <div class="py-2 px-3 border-0">
                        <h5>Expense</h5>
                    </div>
                </div>

                <div class="card-body px-0 pt-0 pb-0">
                    <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                        <div class="table-responsive">
                            <table class="table mb-0 radius-0 th-osool table-b-b-none">

                                <thead>
                                    <tr class="userDatatable-header">
                                        <th>
                                            Category
                                        </th>
                                        <th>
                                            <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Jan-Mar
                                        </th>
                                        <th>
                                            <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Apr-Jun
                                        </th>
                                        <th>
                                            <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Jul-Sep
                                        </th>
                                        <th>
                                            <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Oct-Dec
                                        </th>
                                        <th>
                                            <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Total
                                        </th>
                                    </tr>
                                </thead>

                                <tbody class="sort-table ui-sortable">
                                    <tr>
                                        <td>
                                            <span class="userDatatablecontent">Payment :</span>
                                        </td>
                                    </tr>

                                    <tr class="ui-sortable-handle" style="opacity: 1;">
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>Test</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span> 0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>102.5K ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>102.5K ﷼</span>
                                            </div>
                                        </td>
                                    </tr>

                                    <tr class="ui-sortable-handle" style="opacity: 1;">
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>Bill :</span>
                                            </div>
                                        </td>
                                    </tr>

                                    <tr class="ui-sortable-handle" style="opacity: 1;">
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>Employee Salary :</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span> 0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span> 0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span> 0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span> 0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span> 0.00 ﷼</span>
                                            </div>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td colspan="6">
                                            <span class="userDatatablecontent">Total Expense = Payment + Bill + Employee
                                                Salary</span>
                                        </td>
                                    </tr>

                                    <tr class="ui-sortable-handle" style="opacity: 1;">
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>Total Expenses</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span> 0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>102.5K ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>0.00 ﷼</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatablecontent mb-0">
                                                <span>102.5K ﷼</span>
                                            </div>
                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>




                <div class="userDatatable projectDatatable project-table bg-white w-100 border-0 mt-2">
                    <div class="table-responsive">
                        <table class="table mb-0 radius-0 th-osool">


                            <tbody class="sort-table ui-sortable">

                                <tr>
                                    <td colspan="6">
                                        <div class="border-0">
                                            <span class="userDatatablecontent">Net Profit = Total Income - Total
                                                Expense</span>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="min-w-150 userDatatablecontent">
                                            <span>Net Profit</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="min-w-100px userDatatablecontent">
                                            <span> 0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="min-w-100px userDatatablecontent">
                                            <span>837.85K ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="min-w-100px userDatatablecontent">
                                            <span>80.79K ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="min-w-100px userDatatablecontent">
                                            <span>0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="min-w-100px userDatatablecontent">
                                            <span>918.64K ﷼</span>
                                        </div>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>



            </div>

        </div>

    </div>

</body>

</html>