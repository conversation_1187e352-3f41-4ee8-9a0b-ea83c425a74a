<a href="javascript:void(0);" class="border-primary btn-outline-primary btn-default btn-squared btn-xs btn-add float-right  btn-svg hover-2-img d-flex align-items-center py-1 ml-2" data-toggle="modal" id="showExportModal" data-target="#export_modal">   <img src="{{ asset('img/svg/export-icon.svg') }}" class="mr-2 w-11 img-1">
<img src="{{ asset('img/svg/export-icon-white.svg') }}" class="mr-2 w-11 img-2">
<span> {{ __('work_order.forms.label.export') }} </span> </a>



<!-- export_modal_start -->
<div class="modal fade" id="export_modal" tabindex="-1" role="dialog" aria-labelledby="example_Modal_Label" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <form id="exportForm">
                    @csrf
                    <div class="form-group">
                        <label for="file-type">@lang('assets_managements.choose_file')<small class="required">*</small></label>
                        <div class="d-flex">
                            <div class="custom-radio">
                                <input class="radio" type="radio" name="file_type" value="csv" id="type_csv" checked>
                                <label for="type_csv"><span class="radio-text">@lang('assets_managements.csv')</span></label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="language">@lang('assets_managements.choose_language')<small class="required">*</small></label>
                        <div class="d-flex">
                            <div class="custom-radio">
                                <input class="radio" type="radio" name="language" value="en" id="lang_en" checked>
                                <label for="lang_en"><span class="radio-text">@lang('assets_managements.english')</span></label>
                            </div>
                            <div class="custom-radio ml-3">
                                <input class="radio" type="radio" name="language" value="ar" id="lang_ar">
                                <label for="lang_ar"><span class="radio-text">@lang('assets_managements.arabic')</span></label>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="initiateExportUrl" value="{{ route('asset-management.export_assets') }}">
                </form>
            </div>

            <div class="modal-footer d-flex justify-content-between">
                <div class="button-group">
                    <button type="button" class="btn btn-light" data-dismiss="modal">
                        {{ __('work_order.button.cancel') }}
                    </button>
                    <button type="button" class="btn btn-primary" onclick="initiateExport()">
                        <img src="{{ asset('img/svg/export-icon-white.svg') }}" class="mr-1">
                        {{ __('work_order.forms.label.export') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- export_modal_end -->




<!-- successful_modal_start -->
<div class="modal fade" id="export-confirm-modal" tabindex="-1" role="dialog" aria-labelledby="example_Modal_Label"aria-hidden="true">
<div class="modal-dialog modal-dialog-centered modal-sm" role="document">
<div class="modal-content">
<div class="modal-body">
<div class="text-center">
<img src="{{ asset('img/svg/check-circle.svg') }}" class="mb-2">
<h3 class="mb-3 fw-400">@lang('assets_managements.successful')</h3>

<p>{!! __('assets_managements.export_message', ['link' => route('reports.manage_reports')]) !!}</p>

</div>
</div>
<div class="modal-footer">
<button type="button" class="btn btn-primary" data-dismiss="modal">@lang('assets_managements.ok')</button>
</div>
</div>
</div>
</div>
<!-- successful_modal_start -->



<script>


function initiateExport() {
    let form = $('#exportForm');
    let url = $('#initiateExportUrl').val();

    // Get base form data
    let formData = form.serializeArray();

    // Additional filters
    var asset_search_by = $('#asset_search_by').val();
    var filter_text = $('#filter_text').val();
    var sort_by = $('#filter_id_section .dropdown-item.active').data('value');

    // Get selected properties
    var properties = [];
    $('input.properties_checkbox:checked').each(function () {
        properties.push($(this).val());
    });

    // Get asset categories
    var asset_categories = [];
    var allCheckboxes = $('input.asset_category_checkbox');
    var allChecked = true;

    allCheckboxes.each(function () {
        if (!$(this).is(':checked')) {
            allChecked = false;
        }
    });

    if (!allChecked) {
        allCheckboxes.each(function () {
            if ($(this).is(':checked')) {
                asset_categories.push($(this).val());
            }
        });
    }

    // Append extra fields to formData
    formData.push(
        { name: 'asset_search_by', value: asset_search_by },
        { name: 'filter_text', value: filter_text },
        { name: 'sort_by', value: sort_by }
    );

    properties.forEach((val) => {
        formData.push({ name: 'properties[]', value: val });
    });

    if (asset_categories === null || asset_categories.length === 0) {
        formData.push({ name: 'asset_categories', value: '' });
    } else {
        asset_categories.forEach((val) => {
            formData.push({ name: 'asset_categories[]', value: val });
        });
    }

    $.ajax({
        url: url,
        method: 'POST',
        data: formData,
        headers: {
            'X-CSRF-TOKEN': $('input[name="_token"]').val()
        },
        beforeSend: function () {
            $('#initiateExport').prop('disabled', true).text('Exporting...');
        },
        success: function (response) {
            $('#export_modal').modal('hide');
            $('#export-confirm-modal').modal('show');
            // $('#initiateExport').prop('disabled', false).text('Export');
            // $('#export_modal').modal('hide');
            // alert('✅ Export started successfully!');
        },
        error: function (xhr) {
            // alert('❌ Something went wrong!');
        }
    });
}




</script>