
<table border="1" cellpadding="2" cellspacing="0" style="border-collapse: collapse; text-align: left;">
    <tr>
        <th colspan="1">@lang('assets_managements.number_of_assets_exported')</th>
        <td colspan="1">{{count($data)}}</td>
    </tr>
</table>

<br>
<table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <thead>
         <tr>
            <th>@lang('assets_managements.serial')</th>
            <th>@lang('assets_managements.property_name')</th>
            <th>@lang('assets_managements.building_name')</th>
            <th>@lang('assets_managements.zone')</th>
            <th>@lang('assets_managements.unit')</th>
            <th>@lang('assets_managements.asset_name')</th>
            <th>@lang('assets_managements.service_type')</th>
            <th>@lang('assets_managements.asset_number')</th>
            <th>@lang('assets_managements.asset_tag')</th>
            <th>@lang('assets_managements.asset_qr_code')</th>
            <th>@lang('assets_managements.purchase_date')</th>
            <th>@lang('assets_managements.model_number')</th>
            <th>@lang('assets_managements.manufacturer_name')</th>
            <th>@lang('assets_managements.asset_status')</th>
            <th>@lang('assets_managements.general_information')</th>
            <th>@lang('assets_managements.work_orders_history')</th>
            <th>@lang('assets_managements.work_orders_status')</th>
            <th>@lang('assets_managements.custodian')</th>
            <th>@lang('assets_managements.history')</th>
        </tr>
    </thead>
    <tbody>
        @foreach($data as $key=>$this_data)
@php
//echo '<pre>';print_r($this_data->custodians);die;
@endphp

        <tr>
            <td>{{$key+1}}</td>
            <td>{{$this_data->property_name??''}}</td>
            <td>{{$this_data->building_name??''}}</td>
            <td>{{$this_data->floor??''}}</td>
            <td>{{$this_data->room??''}}</td>
            <td>{{$this_data->asset_name??''}}</td>
            <td>{{ $this_data->asset_category  ?? ($this_data->assetCategory ? $this_data->assetCategory->asset_category : '---')}}</td>
            <td>{{$this_data->asset_number??''}}</td>
            <td>{{$this_data->asset_tag??''}}</td>
            <td>
                @if($this_data->barcode_img_str)
                {{url('/')."/assets-managements/view-asset-details/".Crypt::encryptString($this_data->id)}}
                @endif
            </td>
            <td>{{$this_data->purchase_date??''}}</td>
            <td>{{$this_data->model_number??''}}</td>
            <td>{{$this_data->manufacturer_name??''}}</td>
            <td>{{$this_data->asset_status??''}}</td>

            <td>{{$this_data->general_information??''}}</td>

            @if($this_data->getAssetWorkOrderSummary()['totalCount'] > 0)
            <td>{{$this_data->getAssetWorkOrderSummary()['totalCount']??''}} Work Orders</td>
            <td>
                @foreach($this_data->getAssetWorkOrderSummary()['workorders'] as $this_workorders)
                {{$this_workorders['total']}} {{$this_workorders['status_label']}}, 
                @endforeach
            </td>
            @else
            <td>No work orders created</td>
            <td>None</td>
            @endif
            <td>
            @foreach($this_data->custodians as $this_custodians)
            {{$this_custodians->name??''}} - {{$this_custodians->email??''}} - {{$this_custodians->phone??''}} - {{$this_custodians->pivot->start_date??''}} - {{$this_custodians->pivot->end_date??''}} | 
            @endforeach
            </td>

            <td>
            @foreach($this_data->assetHistory as $this_assetHistory)
            {{$this_assetHistory->log_type??''}} - 
            {{$this_assetHistory->user->name??''}} - 
            {{$this_assetHistory->created_at??''}} | 
            @endforeach
            </td> 
        
        </tr>
        @endforeach
    </tbody>
</table>