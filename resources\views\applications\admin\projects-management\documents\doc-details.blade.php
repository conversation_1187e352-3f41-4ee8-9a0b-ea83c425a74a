@extends('layouts.app')
@section('styles')
<style type="text/css">
  
</style>
@endsection
@section('content')
        <div class="contents crm">
           <div class="container-fluid">
            <div class="col-lg-12">
    <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
        <div class="page-title-wrap p-0">
            <div class="page-title d-flex justify-content-between">
                <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                    <div class="user-member__title mr-sm-25 ml-0">
                        <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                            Document Details
                        </h4>
                    </div>
                </div>
            </div>
            <div>
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a>Dashboard</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>Document</a>
                         <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>Details</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="d-flex gap-10 breadcrumb_right_icons">
            <div class="d-flex gap-10 breadcrumb_right_icons">
                    <button class="btn btn-default btn-primary wh-45 no-wrap" type="button" aria-expanded="false"  data-toggle="modal" data-target="#create-revenue"><i class="iconsax mr-0" icon-name="edit-1"></i></button>
            </div>
        </div>
        <!--====End Design for Export PDF===-->
    </div>
</div>


<div class="">
        <div class="row">
            <div class="col-md-3 pr-md-0 mb-3 mb-md-0">
    <div class="card  sticky-sidebar">
        <ul class="list-group crm nav-stages">
            <li class="list-group-item active">
                <a href="#info-section" class="d-flex justify-content-between align-items-center"> Info <i class="las la-angle-right"></i> </a>
            </li>
            <li class="list-group-item">
                <a href="#attachments-section" class="d-flex justify-content-between align-items-center"> Attachements <i class="las la-angle-right"></i> </a>
            </li>
            <li class="list-group-item">
                <a href="#comments-section" class="d-flex justify-content-between align-items-center"> Comments <i class="las la-angle-right"></i> </a>
            </li>
            <li class="list-group-item">
                <a href="#note-section" class="d-flex justify-content-between align-items-center"> Notes <i class="las la-angle-right"></i> </a>
            </li>
        </ul>
    </div>
</div>


<div class="col-md-9 mb-md-0 mb-30 scroll-right">
    <div class="card info-section" id="info-section">
        <div>
        <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
            <h6>Info</h6>
        </div>
        <div class="card-body">
            <div class="row">
    <div class="col-lg-4 col-md-6">
        <div class="card">
            <div class="card-body p-3">
                <div class="row align-items-center justify-content-between">
                    <div class="col-auto mb-3 mb-sm-0">
                        <div class="d-flex align-items-center gap-10">
                            <div class="radius-xl theme-avtar bg-opacity-primary wh-45 d-center">
                                <i class="iconsax icon fs-22 mr-0 text-primary" icon-name="picture-add"></i>
                            </div>
                            <div class="ms-3">
                                <p class="mb-0 text-muted">Attachments</p>
                                <h4 class="m-0">0</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mt-md-0 mt-3">
        <div class="card">
            <div class="card-body p-3">
                <div class="row align-items-center justify-content-between">
                    <div class="col-auto mb-3 mb-sm-0">
                        <div class="d-flex align-items-center gap-10">
                            <div class="radius-xl theme-avtar bg-opacity-success wh-45 d-center">
                                <i class="iconsax icon fs-22 mr-0 text-success" icon-name="messages-1"></i>
                            </div>
                            <div class="ms-3">
                                <p class="mb-0 text-muted">Comments</p>
                                <h4 class="m-0">0</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mt-md-0 mt-3">
        <div class="card">
            <div class="card-body p-3">
                <div class="row align-items-center justify-content-between">
                    <div class="col-auto mb-3 mb-sm-0">
                        <div class="d-flex align-items-center gap-10">
                            <div class="radius-xl theme-avtar bg-opacity-danger wh-45 d-center">
                                <i class="iconsax icon fs-22 mr-0 text-danger" icon-name="clipboard-text-2"></i>
                            </div>
                            <div class="ms-3">
                                <p class="mb-0 text-muted">Notes</p>
                                <h4 class="m-0">0</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


        <div class="card mt-3 py-3">
            <table class="table table--default align-left">
                        <tbody>
                            <tr>
            <td><strong>Subject</strong></td>
            <td class="">test44</td>
            
        </tr>
        <tr>
            <td><strong>User</strong></td>
            <td class="">Kirsten Mclaughlin</td>
            
        </tr>
        <tr>
            <td><strong>Project</strong></td>
            <td class="">Test ikbel</td>
            
        </tr>
        <tr>
            <td><strong>Type</strong></td>
            <td class="">test</td>
            
        </tr>
                        </tbody>
                    </table>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6>Document Description</h6>
            </div>
            <div class="card-body">
                <textarea id="docDescription" class="form-control"></textarea>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6>Additional Description</h6>
            </div>
            <div class="card-body">
                <textarea id="docDescription" class="form-control"></textarea>
            </div>
        </div>

        </div>
    </div>
    </div>
    <div class="card mt-3" id="attachments-section">
        <div class="card-header justify-content-between d-flex align-items-center p-3">
            <h3 class="text-osool">Attachments</h3>
            <label class="btn btn-default bg-new-primary" for="imageInput">
            <i class="las la-plus fs-16"></i> Upload Files </label>
        </div>
        <div class="card-body">
            <input type="file" id="imageInput" multiple accept="image/*" class="d-none">
            <div id="imagePreview" class="preview-container file-upload-new">
                <div class="image-box d-flex justify-content-between align-items-center border radius-xl mb-2" data-name="taqnia.png" data-size="35748">
            <div class="d-flex align-items-center gap-10 pl-2">
              <img src="https://images.pexels.com/photos/27832062/pexels-photo-27832062.jpeg" download="taqnia.png">
              <span>pexele.com.png</span>
             </div>
             <div class="d-flex gap-10">
             <a class="download-btn d-center p-2 bg-new-primary text-white rounded-circle text-light cursor-pointer" href="https://images.pexels.com/photos/27832062/pexels-photo-27832062.jpeg" download="taqnia-logo 1.jpg">
                <i class="iconsax" icon-name="download-1"></i>
              </a>
              <a class="remove-btn d-center p-2 bg-loss text-white rounded-circle text-light cursor-pointer">
                <i class="iconsax" icon-name="x"></i>
              </a>
            </div>
          </div>
            </div>
        </div>
    </div>


    <div class="card comments-section mt-3" id="comments-section">
        <div>
        <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
            <h6>Comments</h6>
        </div>
        <div class="card-body d-flex flex-column align-items-end gap-10">
           <textarea class="form-control textarea" placeholder="Add Comment"></textarea>
           <button class="btn btn-sm bg-new-primary"><i class="iconsax" icon-name="add"></i> Add</button>
        </div>
        <div class="card-body pt-0">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="d-flex gap-10">
                    <img src="https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png" class="wh-45 rounded-circle">
                    <div>
                        <span class="fw-600 d-block">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor </span>
                        <span>1hour ago</span>
                    </div>
                </div>

                    <a class="remove-comment wh-30 d-center p-2 bg-loss text-white rounded-circle text-light cursor-pointer">
                <i class="iconsax" icon-name="trash"></i>
              </a>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="d-flex gap-10">
                    <img src="https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png" class="wh-45 rounded-circle">
                    <div>
                        <span class="fw-600 d-block">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor </span>
                        <span>1hour ago</span>
                    </div>
                </div>

                    <a class="remove-comment wh-30 d-center p-2 bg-loss text-white rounded-circle text-light cursor-pointer">
                <i class="iconsax" icon-name="trash"></i>
              </a>
            </div>
        </div>
    </div>
    </div>

    <div class="card comments-section mt-3" id="note-section">
        <div>
        <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
            <h6>Notes</h6>
        </div>
        <div class="card-body d-flex flex-column align-items-end gap-10">
           <textarea class="form-control textarea" placeholder="Add Comment"></textarea>
           <button class="btn btn-sm bg-new-primary"><i class="iconsax" icon-name="add"></i> Add</button>
        </div>
        <div class="card-body pt-0">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="d-flex gap-10">
                    <img src="https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png" class="wh-45 rounded-circle">
                    <div>
                        <span class="fw-600 d-block">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor </span>
                        <span>1hour ago</span>
                    </div>
                </div>

                    <a class="remove-comment wh-30 d-center p-2 bg-loss text-white rounded-circle text-light cursor-pointer">
                <i class="iconsax" icon-name="trash"></i>
              </a>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="d-flex gap-10">
                    <img src="https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png" class="wh-45 rounded-circle">
                    <div>
                        <span class="fw-600 d-block">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor </span>
                        <span>1hour ago</span>
                    </div>
                </div>

                    <a class="remove-comment wh-30 d-center p-2 bg-loss text-white rounded-circle text-light cursor-pointer">
                <i class="iconsax" icon-name="trash"></i>
              </a>
            </div>
        </div>
    </div>
    </div>

</div>


        </div>
    
</div>



</div>




<!-- Modal -->
<div class="modal fade" id="create-revenue" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Create Revenue</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
    <div class="form-group col-md-6">
        <label for="date" class="form-label">Date</label><span class="text-danger">*</span>
        <div class="form-icon-user">
            <input class="form-control datepicker" required="required" placeholder="Select Date" name="date" type="text" id="date" />
        </div>
    </div>
    <div class="form-group col-md-6">
        <label for="amount" class="form-label">Amount</label><span class="text-danger">*</span>
        <div class="form-icon-user">
            <input class="form-control" required="required" placeholder="Enter Amount" step="0.01" min="0" name="amount" type="number" id="amount" />
        </div>
    </div>
    <div class="form-group col-md-6">
        <label for="account_id" class="form-label">Account</label><span class="text-danger">*</span>
        <select class="form-control select2-new" required="required" id="account_id" name="account_id">
            <option selected="selected" value="">Select Account</option>
            <option value="27">sss cash</option>
            <option value="113">Saudi National Bank Mohammed Firdaus0</option>
        </select>
    </div>
    <div class="form-group col-md-6">
        <label for="customer_id" class="form-label">Customer</label><span class="text-danger">*</span>
        <select class="form-control select2-new" required="required" id="customer_id" name="customer_id">
            <option selected="selected" value="">Select Customer</option>
            <option value="30">Customer 1</option>
            <option value="31">Customer 2</option>
            <option value="32">Customer 3</option>
            <option value="33">Customer 4</option>
            <option value="34">Customer 5</option>
            <option value="35">Customer 6</option>
            <option value="36">Customer 7</option>
            <option value="37">Customer 8</option>
            <option value="38">Customer 9</option>
            <option value="87">Fouzan</option>
        </select>
    </div>
    <div class="form-group col-md-6">
        <label for="category_id" class="form-label">Category</label><span class="text-danger">*</span>
        <select class="form-control select2-new" required="required" id="category_id" name="category_id">
            <option selected="selected" value="">Select Category</option>
            <option value="18">Test cat</option>
        </select>
    </div>
    <div class="form-group col-md-6">
        <label for="reference" class="form-label">Reference</label><span class="text-danger">*</span>
        <div class="form-icon-user">
            <input class="form-control" placeholder="Enter Reference" required="required" name="reference" type="text" id="reference" />
        </div>
    </div>
    <div class="form-group col-md-12">
        <label for="description" class="form-label">Description</label><span class="text-danger">*</span>
        <textarea class="form-control" rows="3" required="required" name="description" cols="50" id="description"></textarea>
    </div>
    <div class="form-group col-md-12">
        <label for="add_receipt" class="form-label">Payment Receipt</label>
        <input type="file" id="imageInput" multiple accept="image/*" class="d-none">
        <label class="btn btn-default bg-new-primary" for="imageInput">
                <i class="las la-upload fs-16"></i> Upload Files </label>
        <div id="imagePreview" class="preview-container file-upload-new"></div>
    </div>
</div>

      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary">Save changes</button>
      </div>
    </div>
  </div>
</div>




<!-- Modal Bank account Details -->
<div class="modal fade" id="revenue-details" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Bank Account Details</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 th-borderless">
                        <tbody class="sort-table ui-sortable">
                            <tr>
                                 <th>
                                   Date
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                   Amount
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                   Account
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                   Customer
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Cutomer 1</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                  Category
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                   Reference
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                  Description
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center max-w-360">
                                        <span>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                   Payment Receipt
                                </th>
                                <td>
                                    <div class="d-flex align-items-center p-2 gap-10">
                                        <div class="view-img wo-img-div rounded" style="background: url('https://images.pexels.com/photos/11181151/pexels-photo-11181151.jpeg'); background-size: cover; background-position: center;">
                                            <img onclick="chatImageClick(this)" src="https://images.pexels.com/photos/11181151/pexels-photo-11181151.jpeg" alt="Maintenance Request Image" class="uploaded-image" width="100%">
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

           </div>
        </div>







@endsection

@section('scripts')
<script type="text/javascript">
    $(".datepicker").datepicker();
    $("#selectUsers,#statusList").select2();
</script>
<script>
$(document).ready(function () {
  let currentFiles = [];

  const maxFiles = 3;
  const maxSizeMB = 2;
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];

  $('#imageInput').on('change', function () {
    const newFiles = Array.from(this.files);
    let totalFiles = currentFiles.length + newFiles.length;

    if (totalFiles > maxFiles) {
      alert(`Only ${maxFiles} images allowed.`);
      this.value = ''; // reset input
      return;
    }

    newFiles.forEach((file, index) => {
      if (!allowedTypes.includes(file.type)) {
        alert(`Invalid file type: ${file.name}`);
        return;
      }

      if (file.size > maxSizeMB * 1024 * 1024) {
        alert(`File too large: ${file.name}`);
        return;
      }

      currentFiles.push(file); // track only valid files

      const reader = new FileReader();
      reader.onload = function (e) {
        const imgBox = $(`
          <div class="image-box d-flex justify-content-between align-items-center border radius-xl mb-2" data-name="${file.name}" data-size="${file.size}">
            <div class="d-flex align-items-center gap-10 pl-2">
              <img src="${e.target.result}" alt="Image Preview">
              <span>${file.name}</span>
            </div>
            <div class="d-flex gap-10">
              <a class="download-btn d-center p-2 bg-new-primary text-white rounded-circle text-light cursor-pointer"
                 href="${e.target.result}" download="${file.name}">
                <i class="iconsax" icon-name="download-1"></i>
              </a>
              <a class="remove-btn d-center p-2 bg-loss text-white rounded-circle text-light cursor-pointer">
                <i class="iconsax" icon-name="x"></i>
              </a>
            </div>
          </div>
        `);
        $('#imagePreview').append(imgBox);
      };
      reader.readAsDataURL(file);
    });

    this.value = ''; // Clear the file input to allow re-upload of same files
  });

  $('#imagePreview').on('click', '.remove-btn', function () {
    const box = $(this).closest('.image-box');
    const name = box.data('name');
    const size = box.data('size');

    // Remove file from tracking array
    currentFiles = currentFiles.filter(file => !(file.name === name && file.size === size));

    box.remove();
  });
});
</script>

<script src="{{ asset('js/admin/plugins/scroll-to-div.js')}}"></script>
<link href="{{ asset('plugins/texteditor/summernote-bs4.min.css') }}" rel="stylesheet">
<script src="{{ asset('plugins/texteditor/summernote-bs4.min.js') }}"></script>
    <script>
       $(document).ready(function () {
        $('#docDescription,#docRequirement').summernote({
          placeholder: 'Write Here...',
          tabsize: 2,
          height: 200,
          toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'italic', 'underline', 'clear']],
            ['fontsize', ['fontsize']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['insert', ['link']],
            ['view', ['codeview']]
          ]
        });
    });
</script>

@endsection