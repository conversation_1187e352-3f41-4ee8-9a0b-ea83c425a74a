@extends('layouts.app')
@section('styles')
<style type="text/css">
  
</style>
@endsection
@section('content')
        <div class="contents crm">
           <div class="container-fluid">
            <div class="col-lg-12">
    <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
        <div class="page-title-wrap p-0">
            <div class="page-title d-flex justify-content-between">
                <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                    <div class="user-member__title mr-sm-25 ml-0">
                        <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                            Manage Documents
                        </h4>
                    </div>
                </div>
            </div>
            <div>
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a>Dashboard</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>Manage Documents</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="d-flex gap-10 breadcrumb_right_icons">
            <div class="d-flex gap-10 breadcrumb_right_icons">
                    <button class="btn btn-default btn-primary w-100 no-wrap" type="button" aria-expanded="false"  data-toggle="modal" data-target="#create-document"><i class="las la-plus fs-16"></i>Create</button>
            </div>
        </div>
        <!--====End Design for Export PDF===-->
    </div>
</div>


<div class="">
        <div class="card mt-3">
        <div class="">
            <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">Manage Documents</h6>

                <div class="d-flex gap-10 table-search flex-wrap">
                    <div class="position-relative">
                    <input type="text" class="form-control" placeholder="Search">
                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
                    <div class="dropdown p-0">
                         <button class="btn btn-export text-dark"  data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button>
                          <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                            <a class="dropdown-item" href="#"><i class="las la-print"></i> Print</a>
                            <a class="dropdown-item" href="#"><i class="las la-file-csv"></i> CSV</a>
                            <a class="dropdown-item" href="#"><i class="las la-file-excel"></i> Excel</a>
                          </div>
                    </div>                    
                     <button type="button" class="btn bg-opacity-loss btn-sm text-loss wh-45 radius-md" wire:click="resetFilters">
                        <i class="iconsax mr-0 fs-18" icon-name="rotate-left"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body px-0 pt-0">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 th-osool">
                        <thead>
                            <tr class="userDatatable-header">
                                <th>
                                    Document
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Subject
                                </th>
                                <th>
                                    <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> User
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Type
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Project
                                </th>
                                <th>
                                    <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Status
                                </th>
                                <th>
                                    Action
                                </th>
                            </tr>
                        </thead>
                        <tbody class="sort-table ui-sortable">
                            <tr class="ui-sortable-handle" style="opacity: 1;">
                                <td>
                                    <a href=""><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined">DOC-1890999474</span></a>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Subject1</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Kirsten Mclaughlin</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Type1</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Project 1</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge-new rounded bg-opacity-win text-win">Accepted
                                    </span>
                                </td>
                                <td>
                                    <div class="d-inline-block">
                                        <ul class="mb-0 d-flex flex-wrap gap-10 align-items-center">
                                            <li>
                                                <a href="javascript:void(0);" title="Copy Link">
                                                    <i class="iconsax icon text-osool fs-18 mr-0" icon-name="document-copy"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);">
                                                    <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                                </a>
                                            </li>
                                            <li>
                                               <a href="javascript:void(0);" data-toggle="modal" data-target="#create-document">
                                                    <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#delete-document">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>




        <div class="card-body pt-0">
<div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
    <div class="">
        <ul class="atbd-pagination d-flex justify-content-between">
            <li>
                <div class="paging-option">
                    <div class="dataTables_length d-flex">
                        <label class="d-flex align-items-center mb-0">
                            <select aria-controls="workorder_table" wire:model.live.debounce.250ms="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                <option value="5">5</option>
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                           <span class="no-wrap"> Entries Per Page </span>
                        </label>
                    </div>
                </div>
            </li>
        </ul>
    </div>
    <div class="">
        <div class="user-pagination">
            <div class="user-pagination new-pagination">
                <div class="d-flex justify-content-sm-end justify-content-end">
                    <nav>
                        <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
    <span class="">
        <span aria-disabled="true" aria-label="&amp;laquo; Previous">
            <button class="border-0 disabled" aria-hidden="true" disabled="">
                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
            </button>
        </span>
    </span>

    <span wire:key="paginator-page-1-page1">
        <button class="border-0 current-page" disabled="">1</button>
    </span>
    <span wire:key="paginator-page-1-page2">
        <button type="button" class="border-0">2</button>
    </span>
    <span wire:key="paginator-page-1-page3">
        <button type="button" class="border-0">3</button>
    </span>
    <span wire:key="paginator-page-1-page4">
        <button type="button" class="border-0">4</button>
    </span>
    <span wire:key="paginator-page-1-page5">
        <button type="button" class="border-0">5</button>
    </span>
    <span wire:key="paginator-page-1-page6">
        <button type="button" class="border-0">6</button>
    </span>
    <span wire:key="paginator-page-1-page7">
        <button type="button" class="border-0">7</button>
    </span>
    <span wire:key="paginator-page-1-page8">
        <button type="button" class="border-0">8</button>
    </span>
    <span wire:key="paginator-page-1-page9">
        <button type="button" class="border-0"> 9 </button>
    </span>

    <span>
        <button type="button" class="border-0">
            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
        </button>
    </span>
</span>

                    </nav>
                </div>
            </div>
        </div>
    </div>
    <div>
        <p class="text-sm text-gray-700 leading-5 mb-0">
                        <span>Showing</span>
                        <span class="font-medium">1</span>
                        <span>to</span>
                        <span class="font-medium">6</span>
                        <span>of</span>
                        <span class="font-medium">52</span>
                        <span>results</span>
                    </p>
    </div>
</div>
        </div>
    </div>

    
</div>



</div>




<!-- Modal -->
<div class="modal fade" id="create-document" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Create Document</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true"><i class="iconsax" icon-name="x"></i></span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
    <div class="col-md-12 form-group">
        <label for="subject" class="col-form-label">Subject</label><span class="text-danger">*</span>
        <input class="form-control" required="required" name="subject" type="text" value="" id="subject" placeholder="Enter Subject" />
    </div>

    <div class="col-md-6 form-group">
        <label for="project_id" class="col-form-label">Project</label>
        <select class="form-control select2-new" id="project_id" name="project_id">
            <option selected="selected" value="">Select Project</option>
            <option value="6">Test ikbel</option>
            <option value="259">Converted</option>
            <option value="311">Test project2</option>
            <option value="312">Test project2</option>
        </select>
    </div>

    <div class="col-md-6">
        <div class="form-group">
            <label for="type" class="col-form-label">Type</label><span class="text-danger">*</span>
            <select class="form-control select2-new" required="required" id="type" name="type">
                <option value="6">test</option>
            </select>
        </div>
    </div>

    <div class="col-md-12 form-group">
        <label for="user_id" class="col-form-label">User</label><span class="text-danger">*</span>
        <select class="form-control select2-new" id="user_id" required="required" name="user_id">
            <option selected="selected" value="">Select User</option>
            <option value="83">Test again</option>
            <option value="84">User 4</option>
            <option value="209">Kirsten Mclaughlin</option>
            <option value="210">Khansaa Hasan44</option>
            <option value="212">Staff Test</option>
            <option value="218">Khansaa Hasan22</option>
            <option value="219">Test deal</option>
        </select>
    </div>

    <div class="col-md-12">
        <div class="form-group">
            <label for="notes" class="col-form-label">Descripation</label>
            <textarea class="form-control textarea" name="notes" id="notes"></textarea>
        </div>
    </div>
</div>

      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary">Save changes</button>
      </div>
    </div>
  </div>
</div>


<!--Delete Document Modal-->
<div class="modal fade" id="delete-document" tabindex="-1" role="dialog" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteUserModalLabel">Delete Document</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="{{ __('user_management_module.modal.close') }}">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="iconsax icon text-loss fs-60" icon-name="warning-triangle"></i>
                    <p class="mt-4">Are you sure you want to delete <br> the vendor <strong id="deleteUserName"> DOC1236789 ?</strong></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteUserForm" method="POST">
                    <button type="submit" class="btn btn-danger">Yes, Delete It</button>
                </form>
            </div>
        </div>
    </div>
</div>

           </div>
        </div>







@endsection

@section('scripts')
<script type="text/javascript">
    $(".datepicker").datepicker();
    $("#selectUsers,#statusList").select2();
</script>
<script>
$(document).ready(function () {
  let currentFiles = [];

  const maxFiles = 1;
  const maxSizeMB = 2;
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];

  $('#imageInput').on('change', function () {
    const newFiles = Array.from(this.files);
    let totalFiles = currentFiles.length + newFiles.length;

    if (totalFiles > maxFiles) {
      alert(`Only ${maxFiles} images allowed.`);
      this.value = ''; // reset input
      return;
    }

    newFiles.forEach((file, index) => {
      if (!allowedTypes.includes(file.type)) {
        alert(`Invalid file type: ${file.name}`);
        return;
      }

      if (file.size > maxSizeMB * 1024 * 1024) {
        alert(`File too large: ${file.name}`);
        return;
      }

      currentFiles.push(file); // track only valid files

      const reader = new FileReader();
      reader.onload = function (e) {
        const imgBox = $(`
          <div class="image-box d-flex justify-content-between align-items-center border radius-xl" data-name="${file.name}" data-size="${file.size}">
            <img src="${e.target.result}" alt="Image Preview">
            <button class="remove-btn d-center"><i class="iconsax" icon-name="x"></i></button>
          </div>
        `);
        $('#imagePreview').append(imgBox);
      };
      reader.readAsDataURL(file);
    });

    this.value = ''; // Clear the file input to allow re-upload of same files
  });

  $('#imagePreview').on('click', '.remove-btn', function () {
    const box = $(this).closest('.image-box');
    const name = box.data('name');
    const size = box.data('size');

    // Remove file from tracking array
    currentFiles = currentFiles.filter(file => !(file.name === name && file.size === size));

    box.remove();
  });
});


</script>
@endsection