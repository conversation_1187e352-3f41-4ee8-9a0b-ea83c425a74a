@extends('layouts.app')
@section('styles')
@endsection
@section('content')
        <div class="contents">

           <div class="container-fluid">
        <!-- <form action="{{ route('reports.testPdf')}}" method="post">
            @csrf
        <button type="submit" name="submit"> Test PDF</button>
        </form> -->

        <div class="d-flex justify-content-center">
                          <ul class="nav nav-tabs site-pills bg-white p-1 rounded mb-3 mb-sm-0" id="myTab" role="tablist">
                          <li class="nav-item" role="presentation">
                            <button class="nav-link active rounded" id="home-tab" data-toggle="tab" data-target="#home" type="button" role="tab" aria-controls="home" aria-selected="true">{{__('user_management_module.user_forms.label.reports')}}</button>
                          </li>
                          <li class="nav-item" role="presentation">
                            <button class="nav-link rounded" id="profile-tab" data-toggle="tab" data-target="#profile" type="button" role="tab" aria-controls="profile" aria-selected="false"> @lang('advance_contracts.advanced_reports')</button>
                          </li>
                        </ul>
                    </div>
                        <div class="tab-content" id="myTabContent">
                          <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">


                        <div class="breadcrumb-main user-member justify-content-sm-between mb-30">
                            <div class=" d-flex flex-wrap justify-content-center breadcrumb-main__wrapper">
                                <div class="d-flex align-items-center user-member__title justify-content-center mr-sm-25">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title page-title__left">
                                      <!-- <a href="Javascript:history.back()"><i class="las la-arrow-left"></i></a>   -->
                                      {{__('user_management_module.user_forms.label.reports')}}</h4>

                                </div>
                            </div>
                        </div>
                            <div class="">
      <div class=" checkout wizard1 wizard7 px-20 py-30 mb-30 bg-white radius-xl w-100">
         <div class="row justify-content-center">
            <div class="col-xl-8">

               <!-- checkout -->
               <div class="row justify-content-center">
                  <div class="col-lg-12">
                     <div class="">
                        <div class="card-header border-bottom-0 align-content-start pb-sm-0 pb-1 px-0 pt-0">
                           <h4 class="mb-2"><b>{{__('user_management_module.user_forms.label.generate_report')}}</b></h4>
                        </div>
                        <p>{{__('user_management_module.user_forms.label.report_long_description')}}</p>
                        <hr class="my-2">

                        <div class="card-body px-0 pb-0">
                             <div class="edit-profile__body">



                                <form method="post" id="report_create_form" action="{{ route('reports.exportCsv')}}" enctype="multipart/form-data">
                                @csrf
                                <input type="hidden" name="count" id="count" value="{{ route('reports.validateCount')}}" >

                                     <div class="action-btn row">
                                        <div class="col-md-4 col-sm-6">
                                        <div class="form-group mb-0">
                                          <label for="">
                                          {{__('user_management_module.user_forms.label.select_date')}}
                                          </label>
                                            <div class="input-container icon-left position-relative">
                                              <!-- <span class="input-icon icon-left">
                                                    <span data-feather="calendar"></span> </span> -->
                                                <!-- <input type="text" class="form-control form-cocrentrol-default date-ranger" onchange="alert();"
                                                    name="date-ranger" placeholder="May 27,2021" id="calender_filter_dashboard"> -->
                                                    <input type="hidden" id="project_start_date" value="{{ $data['project_start_date'] }}" >
                                                            <input type="text" class="form-control form-control-default"
                                                            name="date_range" placeholder="May 27,2021" id="calender_filter_dashboard" autocomplete="off">
                                                <span class="input-icon icon-right"> <span data-feather="chevron-down"></span>
                                                </span>
                                            </div>


                                        </div>
                               </div>

                           @if(Auth::user()->user_type == 'sp_admin' || Auth::user()->user_type == 'supervisor')
                           <div class="col-md-4 col-sm-6">
                           <div class="form-group mb-20 company_info">
                                      <div class="service_provider">
                                         <label for="project_ids">
                                         {{__('slider.lists.select_project')}}<small class="required">*</small>
                                         </label>

                                            <select   class="form-control" id="project_ids" name="project_ids[]" multiple="multiple">

                                            <option value=""  disabled>{{__('slider.lists.select_project')}}</option>

                                            @foreach($data['projects'] as $project)

                                            <option value="{{$project->id}}">{{$project->project_name}}</option>

                                            @endforeach
                                         </select>
                                      </div>
                                      <div id="project_ids_error"></div>

                                   </div>
                               </div>
                            @endif

                             <div class="col-md-4 col-sm-6">
                                    <div class="form-group mb-20 company_info">
                                      <div class="service_provider">
                                         <label for="service_provider">
                                         {{__('user_management_module.user_forms.label.serviceprovider')}}<small class="required">*</small>
                                         </label>

                                            @if(Auth::user()->user_type == 'sp_admin' || Auth::user()->user_type== 'supervisor')
                                                <input  type="hidden"  name="service_providers[]" value = "{{Auth::user()->service_provider}}">

                                                <select disabled class="form-control" id="service_provider" name="service_providers[]" multiple="multiple">

                                                @foreach($data['serviceproviders'] as $sp)

                                                @if(Auth::user()->service_provider == $sp->id)
                                                <option selected  value="{{$sp->id}}">{{$sp->name}}</option>

                                                @endif
                                                @endforeach

                                                </select>
                                                @else
                                                <div class="atbd-tag-mode">
                                                    <div class="atbd-select ">
                                                        <select class="form-control" id="service_provider" name="service_providers[]" multiple="multiple">

                                                            <option value=""  disabled>{{__('user_management_module.user_forms.label.serviceprovider')}}</option>

                                                            @foreach($data['serviceproviders'] as $sp)

                                                            <option value="{{$sp->id}}">{{$sp->name}}</option>

                                                            @endforeach
                                                        </select>

                                                    </div>
                                                </div>

                                            @endif

                                      </div>
                                      <div id="service_providers_error"></div>

                                   </div>
                               </div>
                               <div class="col-md-4 col-sm-6">
                                   <div class="form-group mb-25">
                                        <label for="asset_categories">{{__('configration_assets.common.asset_categories')}} <small>({{__('work_order.forms.label.optional')}})</small></label>
                                        <div class="atbd-tag-mode">
                                                <div class="atbd-select ">
                                                    <select name="asset_categories[]" id="asset_categories" class="form-control " multiple="multiple" >
                                                        <option value="" disabled>{{__('configration_assets.common.asset_categories')}}</option>

                                                    </select>
                                                </div>
                                        </div>
                                    </div>
                                    </div>
                               <div class="col-md-4 col-sm-6">
                                   <div class="form-group mb-25 ">
                                        <label for="building_ids">{{$trans_buildings}}<small class="required">*</small></label>
                                        <div class="atbd-tag-mode">
                                                <div class="atbd-select ">
                                                    <select name="building_ids[]" id="building_ids" class="form-control " multiple="multiple" >
                                                        <option value="" disabled>{{__('user_management_module.user_forms.label.building')}}</option>

                                                    </select>
                                                </div>
                                                <div id="building_id_error"></div>
                                        </div>
                                    </div>
                                </div>

                                @if(Auth::user()->user_type != 'sp_admin' && Auth::user()->user_type != 'supervisor')
                               <div class="col-md-4 col-sm-6">
                                   <div class="form-group mb-20 company_info">
                                      <div class="service_provider">
                                         <label for="building_managers">
                                         {{__('work_order.bread_crumbs.Work_orders_Raised_by')}} <small>{{__('work_order.forms.label.optional')}}</small>
                                         </label>
                                            <div class="atbd-tag-mode">
                                                <div class="atbd-select ">
                                                    <select class="form-control" id="building_managers" name="building_managers[]" multiple="multiple">
                                                    </select>
                                                </div>
                                            </div>
                                      </div>
                                      <div id="building_managers_error"></div>
                                   </div>
                               </div>
                                @else
                                    <input  type="hidden"  name="building_managers[]" value = "[]">
                                @endif


                                @if(Auth::user()->user_type != 'building_manager' && Auth::user()->user_type != 'building_manager_employee' &&  Auth::user()->user_type != 'supervisor')

                               <div class="col-md-4 col-sm-6">
                                    <div class="form-group mb-20 company_info">
                                        <div class="Supervisor">
                                            <label for="Supervisor">
                                            {{__('work_order.bread_crumbs.Assigned_Supervisor')}} <small>{{__('work_order.forms.label.optional')}}</small>
                                            </label>
                                                <div class="atbd-tag-mode">
                                                    <div class="atbd-select ">
                                                    <select class="form-control" id="Supervisor" name="Supervisor[]" multiple="multiple">
                                                    </select>
                                                    </div>
                                                </div>
                                        </div>
                                        <div id="Supervisor_error"></div>

                                    </div>
                                </div>
                                    @else
                                    <input  type="hidden"  name="Supervisor[]" value = "[]">
                                @endif

                               <div class="col-md-4 col-sm-6">
                                <div class="form-group mb-20 company_info">
                                    <div class="wo_status">
                                        <label for="wo_status">
                                        {{__('work_order.bread_crumbs.Work_Order_Status')}} <small class="required">*</small>
                                        </label>
                                        <div class="atbd-tag-mode">
                                            <div class="atbd-select ">
                                                <select class="form-control" id="wo_status" name="wo_status[]" multiple="multiple">
                                                    <option value=""  disabled> {{__('work_order.bread_crumbs.Work_Order_Status')}}</option>
                                                    <option value="All">{{__('work_order.bread_crumbs.all')}}</option>
                                                    <option value="1">{{__('work_order.bread_crumbs.open')}}</option>
                                                    <option value="2">{{__('work_order.bread_crumbs.in_progress')}}</option>
                                                    <option value="4">{{__('work_order.bread_crumbs.closed')}}</option>
                                                    <option value="3">{{__('work_order.bread_crumbs.on_hold')}}</option>
                                                    <option value="6">{{__('work_order.bread_crumbs.re_open')}}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="wo_status_error"></div>
                                </div>
                            </div>

                               <div class="col-md-4 col-sm-6 d-none">
                                   <div class="form-group mb-20 company_info">
                                      <div class="service_provider">
                                      <label for="service_provider">
                                      {{__('work_order.bread_crumbs.maintenance_type')}}<small class="required">*</small>
                                         </label>
                                         <div class="form-group mb-25 ">
                                                    <div class="checkbox-theme-default custom-checkbox ">
                                                        <input class="checkbox" type="checkbox"
                                                            id="hardService" checked value="hard" name="service_type[]">
                                                        <label for="hardService">
                                                            <span class="checkbox-text text-dark">
                                                               {{__('reports.labels.hard_service')}}
                                                            </span>
                                                        </label>
                                                        <br>
                                                        <input class="checkbox" checked type="checkbox"
                                                            id="softService" value="soft" name="service_type[]">
                                                        <label for="softService">
                                                            <span class="checkbox-text text-dark">
                                                                {{__('reports.labels.soft_service')}}
                                                            </span>
                                                        </label>
                                                    </div>

                                                </div>
                                                <div id="service_type_error"></div>


                                      </div>
                                   </div>
                               </div>

                               <div class="col-md-4 col-sm-6">
                                   <div class="form-group mb-20 company_info">
                                      <div class="service_provider">
                                      <label for="service_provider">
                                      {{__('user_management_module.user_forms.label.workorder_type')}}<small class="required">*</small>
                                         </label>

                                         <div class="form-group mb-25">
                                                    <div class="checkbox-theme-default custom-checkbox ">
                                                        <input class="checkbox" type="checkbox"
                                                            id="maintenance_type_r" value="reactive"name="maintenance_type[]">
                                                        <label for="maintenance_type_r">
                                                            <span class="checkbox-text text-dark">
                                                            {{__('work_order.forms.label.Reactive_maintenance')}}
                                                            </span>
                                                        </label>
                                                        <br>
                                                        <input class="checkbox" type="checkbox"
                                                            id="maintenance_type_p" value="preventive"name="maintenance_type[]">
                                                        <label for="maintenance_type_p">
                                                            <span class="checkbox-text text-dark">
                                                            {{__('work_order.forms.label.Preventive_maintenance')}}
                                                            </span>
                                                        </label>

                                                    </div>

                                                </div>

                                      </div>

                                      </div>
                                      <div id="maintenance_type_error"></div>
                                  </div>

                               <div class="col-md-4 col-sm-6">
                                   <div class="form-group">
                                        <label for="emp_user_status">{{__('user_management_module.user_forms.label.reports_format')}}<small class="required">*</small></label>
                                        <!-- <div class="target_response-title"> {{__('configration_assets.asset_comminucation_forms.label.email_language_desc')}} </div> -->
                                        <div class="radio-horizontal-list d-flex">
                                            <div class="radio-theme-default custom-radio ">
                                                <input class="radio" type="radio" name="type" checked value="pdf" id="active_t1">
                                                <label for="active_t1">
                                                    <span class="radio-text">PDF</span>
                                                </label>
                                            </div>
                                            <div class="radio-theme-default custom-radio ">
                                                <input class="radio" type="radio" name="type" value="csv"  id="active_t2">
                                                <label for="active_t2">
                                                    <span class="radio-text">CSV</span>
                                                </label>

                                            </div>
                                        </div>
                                        <div id="type_error"></div>

                                    </div>
                                </div>

                               <div class="col-md-4 col-sm-6">
                                   <div class="form-group">
                                        <label for="emp_user_status">{{__('configration_assets.asset_comminucation_forms.label.rert_language')}}<small class="required">*</small></label>
                                        <!-- <div class="target_response-title"> {{__('configration_assets.asset_comminucation_forms.label.email_language_desc')}} </div> -->
                                        <div class="radio-horizontal-list d-flex">
                                            <div class="radio-theme-default custom-radio ">
                                                <input class="radio" type="radio" name="language" checked value="1" id="active_lang1">
                                                <label for="active_lang1">
                                                    <span class="radio-text">{{__('user_management_module.user_forms.label.arabic')}}</span>
                                                </label>
                                            </div>
                                            <div class="radio-theme-default custom-radio ">
                                                <input class="radio" type="radio" name="language" value="0" id="active_lang2">
                                                <label for="active_lang2">
                                                    <span class="radio-text">{{__('user_management_module.user_forms.label.english')}}</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                               <!-- <div class="col-md-4 col-sm-6 d-none">
                                    <div class="form-group">
                                        <label for="emp_user_status">Design type<small class="required">*</small></label>
                                        <div class="radio-horizontal-list d-flex">
                                            <div class="radio-theme-default custom-radio ">
                                                <input class="radio" type="radio" name="design_type" checked value="old" id="active_type1" >
                                                <label for="active_type1">
                                                    <span class="radio-text">Old</span>
                                                </label>
                                            </div>
                                            <div class="radio-theme-default custom-radio ">
                                                <input class="radio" type="radio" name="design_type" value="new"  id="active_type2">
                                                <label for="active_type2">
                                                    <span class="radio-text">New</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div> -->


                                <div class="col-md-4 col-sm-6 d-none">
                                    <div class="form-group">
                                        <label for="emp_user_status">output type<small class="required">*</small></label>
                                        <!-- <div class="target_response-title"> {{__('configration_assets.asset_comminucation_forms.label.email_language_desc')}} </div> -->
                                        <div class="radio-horizontal-list d-flex">
                                            <div class="radio-theme-default custom-radio ">
                                                <input class="radio" type="radio" name="output_type" checked value="email" id="active_type1" >
                                                <label for="active_type1">
                                                    <span class="radio-text">Email</span>
                                                </label>
                                            </div>
                                            <div class="radio-theme-default custom-radio ">
                                                <input class="radio" type="radio" name="output_type" value="html"  id="active_type2">
                                                <label for="active_type2">
                                                    <span class="radio-text">HTML</span>
                                                </label>
                                            </div>

                                            <div class="radio-theme-default custom-radio ">
                                                <input class="radio" type="radio" name="output_type" value="download"  id="active_type3">
                                                <label for="active_type3">
                                                    <span class="radio-text">Download</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                           </div>


                                <div class="row justify-content-end">
                                    <div class="col-md-4 col-sm-6">
                                   <div class="button-group w-100 d-flex justify-content-end">
                                     <!-- <a href="{{ route('users.list') }}" class="btn btn-light btn-default btn-sm btn-squared fw-400 text-capitalize radius-md" style="margin:5px;">{{__('user_management_module.user_button.cancel')}}</a> -->
                                      <button type="submit" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 flex-fill">{{__('user_management_module.user_forms.label.generate')}}
                                      </button>
                                   </div>
                               </div>
                               </div>
                                </form>
                             </div>
                        </div>
                     </div>
                     <!-- ends: card -->
                  </div>
                  <!-- ends: col -->
               </div>
            </div>
            <!-- ends: col -->
         </div>
      </div>
      <!-- End: .global-shadow-->
   </div>
                          </div>
                          <div class="tab-pane fade advance-tab" id="profile" role="tabpanel" aria-labelledby="profile-tab">

                        <div class="breadcrumb-main user-member justify-content-sm-between mb-30">
                            <div class=" d-flex flex-wrap justify-content-center breadcrumb-main__wrapper">
                                <div class="d-flex align-items-center user-member__title justify-content-center mr-sm-25">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title page-title__left">
                                      <!-- <a href="Javascript:history.back()"><i class="las la-arrow-left"></i></a>   -->
                                      {{__('user_management_module.user_forms.label.advanced_reports')}}</h4>

                                </div>
                            </div>
                        </div>


<div class="checkout wizard1 wizard7 px-20 py-30 mb-30 bg-white radius-xl w-100">
   <div class="row justify-content-center">
      <div class="col-xl-8">
         <!-- checkout -->
         <div class="row justify-content-center">
            <div class="col-lg-12">
               <div class="">
                  <div class="card-header border-bottom-0 align-content-start pb-sm-0 pb-1 px-0 pt-0">
                     <h4 class="mb-2"><b>{{__('user_management_module.user_forms.label.generate_adv_reports')}}</b></h4>
                  </div>
                  <p>{{__('user_management_module.user_forms.label.report_long_description')}}</p>
                  <hr class="my-2" />
                  <form method="post" id="report_create_form_adc" action="{{ route('reports.advancedContractReport')}}" enctype="multipart/form-data">
                  @csrf
                  <div class="card-body px-0 pb-0">
                     <div class="edit-profile__body">
                        <div class="row">
                           <div class="col-md-6">
                              <div class="form-group">
                                 <label for="">
                                 {{__('user_management_module.user_forms.label.select_contract')}}<small class="required">*</small>
                                 </label>
                                 <div class="atbd-select">
                                    <select class="form-control" id="select-contracts" name="contract_ids[]"  multiple="multiple" >
                                        @foreach($data['advanceContract'] as $ac)
                                        <option value="{{$ac->id}}"  >{{$ac->contract_number}}</option>
                                        @endforeach
                                    </select>
                                 </div>
                                <div id="contract_error" class="text-danger mt-1"></div>
                              </div>
                           </div>
                           <div class="col-md-6">
                              <div class="form-group">
                                 <label for="">
                                 {{__('user_management_module.user_forms.label.select_report_type')}}<small class="required">*</small>
                                 </label>
                                 <select class="form-control select2-new" id="report-type"  name="report_type">
                                    <option value="">{{__('user_management_module.user_forms.label.select_report_type')}}</option>
                                     <option value="monthly_cost_report">@lang('advance_contracts.reports.monthly_cost_report')</option>
                                    <option value="consumed_materials_report">@lang('advance_contracts.reports.consumed_materials_report')</option>
                                    <option value="attendance_cost_report">@lang('advance_contracts.reports.attendance_cost_report')</option>
                                 </select>
                                 <div id="report_type_error"class="text-danger mt-1" ></div>
                              </div>
                           </div>
                           <div class="col-md-6">
                              <div class="form-group months-selection">
                                 <label for="exampleInputEmail1">{{__('user_management_module.user_forms.label.select_period')}} <small class="required">*</small></label>
                                 <div class="position-relative dropdown d-block">
                                    <input class="form-control" id="selectedMonthsInput"  placeholder="{{ __('advance_contracts.select_range_placeholder') }}"  name="months" autocomplete="off" aria-invalid="false"  />
                                    <div id="months_error" class="text-danger mt-1"></div>
                                    <div class="dropdown-menu rounded-xl" id="dropdownMenu">
                                       <div class="card" id="monthRangeCard">
                                          <div class="card-header d-flex justify-content-between align-items-center">
                                             <span id="prevYear" class="btn btn-light border wh-40 rotate-ar-y"><i class="las la-angle-left mr-0"></i></span>
                                             <h4 id="currentYear">2026</h4>
                                             <span id="nextYear" class="btn btn-light border wh-40 rotate-ar-y"><i class="las la-angle-right mr-0"></i></span>
                                          </div>
                                          <div class="card-body">
                                             <div class="month-picker" id="monthPicker">
                                                <div class="month selected">Jan</div>
                                                <div class="month selected">Feb</div>
                                                <div class="month selected">Mar</div>
                                                <div class="month disabled">Apr</div>
                                                <div class="month disabled">May</div>
                                                <div class="month disabled">Jun</div>
                                                <div class="month disabled">Jul</div>
                                                <div class="month disabled">Aug</div>
                                                <div class="month disabled">Sep</div>
                                                <div class="month disabled">Oct</div>
                                                <div class="month disabled">Nov</div>
                                                <div class="month disabled">Dec</div>
                                             </div>
                                             <div class="button-container mt-3 d-flex justify-content-end gap-10">
                                                <span id="clearButton" class="btn btn-light border btn-xs">
                                                    @lang('advance_contracts.clear')
                                                </span>

                                                <span id="applyButton" class="btn btn-primary btn-xs">
                                                    @lang('advance_contracts.apply')
                                                </span>

                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                    <span data-feather="calendar" class="field-icon"></span>
                                 </div>
                              </div>
                           </div>
                           <div class="col-md-3 col-sm-6">
                              <div class="form-group">
                                 <label for="emp_user_status">{{__('user_management_module.user_forms.label.reports_format')}}<small class="required">*</small></label>
                                 <div class="radio-horizontal-list d-flex">
                                    <!-- <div class="radio-theme-default custom-radio ">
                                       <input class="radio" type="radio" name="type"  value="pdf" id="format_1" disabled>
                                       <label for="format_1">
                                       <span class="radio-text">PDF</span>
                                       </label>
                                    </div> -->
                                    <div class="radio-theme-default custom-radio ">
                                       <input class="radio" type="radio" name="type" checked="" value="csv" id="format_2">
                                       <label for="format_2">
                                       <span class="radio-text">CSV</span>
                                       </label>
                                    </div>
                                 </div>
                                 <div id="type_error"></div>
                              </div>
                           </div>
                           <div class="col-md-3 col-sm-6">
                              <div class="form-group">
                                 <label for="emp_user_status">{{__('configration_assets.asset_comminucation_forms.label.rert_language')}}<small class="required">*</small></label>
                                 <div class="radio-horizontal-list d-flex">

                                    <div class="radio-theme-default custom-radio ">
                                       <input class="radio" type="radio" name="language" value="1" id="advane_lang1">
                                       <label for="advane_lang1">
                                       <span class="radio-text">{{__('user_management_module.user_forms.label.arabic')}}</span>
                                       </label>
                                    </div>
                                    <div class="radio-theme-default custom-radio ">
                                       <input class="radio" type="radio" name="language" checked=""  value="0" id="advane_lang2">
                                       <label for="advane_lang2">
                                       <span class="radio-text">{{__('user_management_module.user_forms.label.english')}}</span>
                                       </label>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <div class="row justify-content-end mt-5">
                           <div class="col-md-4 col-sm-6">
                              <div class="button-group w-100 d-flex justify-content-end">
                                 <!-- <a href="http://127.0.0.1:8000/user/users-list" class="btn btn-light btn-default btn-sm btn-squared fw-400 text-capitalize radius-md" style="margin:5px;">Cancel</a> -->
                                 <button type="submit" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 flex-fill">@lang('advance_contracts.generate')
                                 </button>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  </form>
               </div>
               <!-- ends: card -->
            </div>
            <!-- ends: col -->
         </div>
      </div>
      <!-- ends: col -->
   </div>
</div>
</div>
                        </div>
        

@endsection

{{-- Scripts Section --}}
@section('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/js-cookie/latest/js.cookie.js"></script>
<script>
  //  alert();
  // $(".fc-daygrid-day-events").click(function(){
  //  alert();
  // });
  $(".drp-buttons").hide();
  $(".applyBtn").append("Google");
  $(".applyBtn").click(function(){
   alert();
  });
</script>
<style type="text/css">
  .applyBtn{
    content: "Google" !important;
  }
</style>
<script>
$("#building_ids,#service_provider,#supervisor_id,#country_id,#city_id,#project_ids, #wo_status, #building_managers, #Supervisor,#asset_categories").select2({
    placeholder: translations.data_contract.contract_forms.place_holder.please_choose,

    language: {

            noResults: function () {
                 return translations.general_sentence.validation.No_results_found;
            }
        }
});

$(document).ready(function ()
{
document.user_id = '<?=Auth::user()->id?>';

    function checkLabelDownloadCookie(){
      const cookie =  Cookies.get('my_cookie');

      if( cookie  ){
        console.log("PDF download  found ");
        var x = eval('(' + cookie + ')');

          Cookies.remove('my_cookie'); //remove cookie
        //   window.location = '{{ url("reports/downloadfile") }}/'+x.file_name+'/'+x.rpt_no+'/';
      }

  }

  function monitorLabelDownloadCookie(){
    // check if the cookie exists every 500ms
      setInterval( checkLabelDownloadCookie, 500 );
  }

  monitorLabelDownloadCookie();



    function getPosts(date)
    {
        var cu_la = window.current_locale;
        url ='?search='+date;
        window.location.href = url;
    }

    /***************search fn *************************/

    const urlParams = new URLSearchParams(location.search);

    for (const [key, value] of urlParams)
    {
        var get = `${value}`;
    }


    // var get_start;
    // var get_end;

    if(get!=undefined)
    {
        // console.log(get);
        var getval = get.split("-");
        var start = moment(getval[0]);
        var end = moment(getval[1]);

    }else{
        var start = moment().subtract(30, 'days');
        var end = moment();
    }


    function cb(start, end)
    {
        $('#reportrange  span').html(start.format('MMMM D, YYYY') + '-' + end.format('MMMM D, YYYY'));
    }

    // var today = translations.user_management_module.user_button.delete_user;

    $('#calender_filter_dashboard').daterangepicker({
        locale: {
            format:'D/MM/YYYY',
            },
        startDate: start,
        endDate: end,
        ranges: {
          "Reset": [moment().subtract(30, 'days'), moment()],
           'Today': [moment(), moment()],
           'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
           'Last 7 Days': [moment().subtract(6, 'days'), moment()],
           'Last 30 Days': [moment().subtract(29, 'days'), moment()],
           'This Month': [moment().startOf('month'), moment().endOf('month')],
           'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        },
        maxDate:new Date(),
        minDate:new Date($("#project_start_date").val())  ,
        maxSpan: {
            "days": 365
        }
    }, cb);

    cb(start, end);

    // $("#calender_filter_dashboard").on("change", function () {
    //     var getval = $(this).val().split("-");
    //     var start = moment(getval[0]);
    //     var end = moment(getval[1]);
    //      cb(start, end);
    // });
    /************************************************/

    if(window.current_locale=='ar')
    {
        var arr = [
                translations.dashboard.bread_crumbs.reset,
                translations.dashboard.bread_crumbs.today,
                translations.dashboard.bread_crumbs.yesterday,
                translations.dashboard.bread_crumbs.last_7_days,
                translations.dashboard.bread_crumbs.last_30_day,
                translations.dashboard.bread_crumbs.this_month,
                translations.dashboard.bread_crumbs.last_month
            ];
        $('.ranges ul li').each(function(i)
        {
            // var get = $(this).attr('data-range-key'); // This is your rel value
            $(this).empty();
            $(this).append(arr[i]);
            if($(this).attr('data-range-key')=='Custom Range')
            {
                $(this).append(translations.dashboard.bread_crumbs.custom_range);
            }

        });
    }

});
var isPreAllSelected = false;
var isPreAllSelected1 = false;

var isTriggerChangeByCode = false;
var isTriggerChangeByCode1 = false;




$('#asset_categories').on('change',function() {

if(isTriggerChangeByCode1){
    isTriggerChangeByCode1 = false;
    return
    }
    var checked = document.querySelectorAll('#asset_categories :checked');
    var selected1 = [...checked].map(option1 => option1.value);
    console.log(selected1);
    if(selected1.includes("All") && !isPreAllSelected1){
    isPreAllSelected1 = true;
    var selected1 = ['All'];
    }
    else if(selected1.includes("All") && isPreAllSelected1 && selected1.length >= 2 ){
    isPreAllSelected1 = false;
    selected1 = selected1.filter(option1 => option1 != 'All')
    }
    $('#asset_categories').val(null);
    $('#asset_categories').val(selected1);
    isTriggerChangeByCode1 = true;
    $('#asset_categories').trigger('change');
});

$('#building_ids').on('change',function() {
    if(isTriggerChangeByCode){
    isTriggerChangeByCode = false;
    return
    }
    var checked = document.querySelectorAll('#building_ids :checked');
    var selected = [...checked].map(option => option.value);
    console.log(selected);
    if(selected.includes("All") && !isPreAllSelected){
    isPreAllSelected = true;
    var selected = ['All'];
    }
    else if(selected.includes("All") && isPreAllSelected && selected.length >= 2 ){
    isPreAllSelected = false;
    selected = selected.filter(option => option != 'All')
    }
    $('#building_ids').val(null);
    $('#building_ids').val(selected);
    isTriggerChangeByCode = true;
    $('#building_ids').trigger('change');

    var b_ids = $("#building_ids :selected").map((_, e) => e.value).get();

    if(b_ids.length !=0 ){
                    if ('<?=Auth::user()->user_type?>' == "sp_admin" || '<?=Auth::user()->user_type?>' == "supervisor" ) {
                    project_ids = $("#project_ids :selected").map((_, e) => e.value).get();
                     var service_provider = '';

                    }
                    else {

                        var service_provider = $("#service_provider :selected").map((_, e) => e.value).get();

                        project_ids = '';


                    }

        var url = "{{ route('reports.getBmSpsFromBuildingIds')}}";

        $.ajax({
                type: 'POST',
                dataType: "json",
                data: {'_token': $("input[name='_token']").val(), b_ids:b_ids, service_provider:service_provider},

                url: url,
                beforeSend: function( xhr ) {
                    xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
                    //$("#wait").css("display", "block");
                },
                dataType: "json",
                success: function(data) { //alert();
                    $("#Supervisor").empty();
                    $("#building_managers").empty();


                    // for supervisors field
                if(data['supervisors'].length + data['sp_admins'].length >0){

                    if(data['supervisors'].length + data['sp_admins'].length >1){
                        $("#Supervisor").append(
                            $("<option></option>")
                                .attr("value", 'All')
                                .text(translations.work_order.bread_crumbs.all)
                        );
                    }
                    if ('<?=Auth::user()->user_type?>' == "sp_admin" || '<?=Auth::user()->user_type?>' == "supervisor" ) {

                    $("#Supervisor").append(
                            $("<option></option>")
                                .attr("value", document.user_id)
                                .text(translations.reports.labels.MyWorkOrders)
                        );
                    }


                    $.each(data['sp_admins'], function (key, value) {
                        $("#Supervisor").append(
                            $("<option></option>")
                                .attr("value", value.id)
                                .text(value.name)
                        );
                    });
                    $.each(data['supervisors'], function (key, value) {
                        $("#Supervisor").append(
                            $("<option></option>")
                                .attr("value", value.id)
                                .text(value.name)
                        );
                    });

                }
                 // for BM field


                 if(data['building_managers'].length + data['building_manager_employees'].length >1){
                        $("#building_managers").append(
                            $("<option></option>")
                                .attr("value", 'All')
                                .text(translations.work_order.bread_crumbs.all)
                        );
                    }
                    if ('<?=Auth::user()->user_type?>' == "building_manager" || '<?=Auth::user()->user_type?>' == "building_manager_employee" ) {
                    $("#building_managers").append(
                            $("<option></option>")
                                .attr("value", document.user_id)
                                .text(translations.reports.labels.MyWorkOrders)
                        );
                    }

                    $.each(data['building_managers'], function (key, value) {
                        $("#building_managers").append(
                            $("<option></option>")
                                .attr("value", value.id)
                                .text(value.name)
                        );
                    });
                    $.each(data['building_manager_employees'], function (key, value) {
                        $("#building_managers").append(
                            $("<option></option>")
                                .attr("value", value.id)
                                .text(value.name)
                        );
                    });

                },
                error: function (data) {
                    var errors = $.parseJSON(data.responseText);
                    toastr.error(data, translations.general_sentence.validation.Error, {
                        timeOut: 1500,
                        positionClass: "toast-top-center",
                        progressBar: true,
                        preventDuplicates: true,
                        preventOpenDuplicates: true,
                    });
                },
            });


        }



});


$('#wo_status').on('change',function() {
    if(isTriggerChangeByCode){
    isTriggerChangeByCode = false;
    return
    }
    var checked = document.querySelectorAll('#wo_status :checked');
    var selected = [...checked].map(option => option.value);
    console.log(selected);
    if(selected.includes("All") && !isPreAllSelected){
    isPreAllSelected = true;
    var selected = ['All'];
    }
    else if(selected.includes("All") && isPreAllSelected && selected.length >= 2 ){
    isPreAllSelected = false;
    selected = selected.filter(option => option != 'All')
    }
    $('#wo_status').val(null);
    $('#wo_status').val(selected);
    isTriggerChangeByCode = true;
    $('#wo_status').trigger('change');
});

$('#building_managers').on('change',function() {
    if(isTriggerChangeByCode){
    isTriggerChangeByCode = false;
    return
    }
    var checked = document.querySelectorAll('#building_managers :checked');
    var selected = [...checked].map(option => option.value);
    console.log(selected);
    if(selected.includes("All") && !isPreAllSelected){
    isPreAllSelected = true;
    var selected = ['All'];
    }
    else if(selected.includes("All") && isPreAllSelected && selected.length >= 2 ){
    isPreAllSelected = false;
    selected = selected.filter(option => option != 'All')
    }
    $('#building_managers').val(null);
    $('#building_managers').val(selected);
    isTriggerChangeByCode = true;
    $('#building_managers').trigger('change');
});

$('#Supervisor').on('change',function() {
    if(isTriggerChangeByCode){
    isTriggerChangeByCode = false;
    return
    }
    var checked = document.querySelectorAll('#Supervisor :checked');
    var selected = [...checked].map(option => option.value);
    console.log(selected);
    if(selected.includes("All") && !isPreAllSelected){
    isPreAllSelected = true;
    var selected = ['All'];
    }
    else if(selected.includes("All") && isPreAllSelected && selected.length >= 2 ){
    isPreAllSelected = false;
    selected = selected.filter(option => option != 'All')
    }
    $('#Supervisor').val(null);
    $('#Supervisor').val(selected);
    isTriggerChangeByCode = true;
    $('#Supervisor').trigger('change');
});




$(document).on('change', '#service_provider, #project_ids', function(e) {

    $("#asset_categories").empty();

        $("#building_ids").empty();

        e.preventDefault();


        var sp_ids = $("#service_provider :selected").map((_, e) => e.value).get();


                    if(sp_ids.length !=0 ){
                        if ('<?=Auth::user()->user_type?>' == "sp_admin" || '<?=Auth::user()->user_type?>' == "supervisor" ) {
                    project_ids = $("#project_ids :selected").map((_, e) => e.value).get();

                    }
                    else {
                        project_ids = '';
                    }

        var app_url = "{{ route('reports.ajax-building-list-report')}}/"+sp_ids+"/"+project_ids+"";


        $.ajax({
                type: 'GET',
                dataType: "json",

                url: app_url ,
                beforeSend: function( xhr ) {
                    xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
                    //$("#wait").css("display", "block");
                },
                dataType: "json",
                success: function(data) { //alert();

                    if(data[0].length >1){
                        $("#building_ids").append(
                            $("<option></option>")
                                .attr("value", 'All')

                                .text(translations.work_order.bread_crumbs.all)
                        );
                    }
                    if(data[1].length >1){
                        $("#asset_categories").append(
                            $("<option></option>")
                                .attr("value", 'All')

                                .text(translations.work_order.bread_crumbs.all)
                        );
                    }

                    $.each(data[0], function (key, value) {
                        $("#building_ids").append(
                            $("<option></option>")
                                .attr("value", value.id)
                                .text(value.building_tag)
                        );
                    });
                    $.each(data[1], function (key, value) {
                        $("#asset_categories").append(
                            $("<option></option>")
                                .attr("value", value.id)
                                .text(value.asset_category)
                        );
                    });
                    console.log(data[1])
                },
                error: function (data) {
                    var errors = $.parseJSON(data.responseText);
                    toastr.error(data, translations.general_sentence.validation.Error, {
                        timeOut: 1500,
                        positionClass: "toast-top-center",
                        progressBar: true,
                        preventDuplicates: true,
                        preventOpenDuplicates: true,
                    });
                },
            });


        }
});


$.ajaxSetup({
  headers: {
    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
  } })

$("#report_create_form").validate({
    ignore: "",
    rules: {

        "wo_status[]": "required",
        "building_ids[]": "required",
        "service_providers[]": "required",
        "type[]": "required",
        "maintenance_type[]": "required",
        "service_type[]": "required",
        "project_ids[]": {
            required: function (){
            console.log($(this).serialize());
            if ('<?=Auth::user()->user_type?>' == "sp_admin") {
                return true;
            }else {
                return false;
            }
            }  },
            },
    messages: {


        "building_ids[]": {
            required: translations.general_sentence.validation.This_field_is_required,
        },
        "project_ids[]": {
            required: translations.general_sentence.validation.This_field_is_required,
        },
        "service_providers[]": {
            required: translations.general_sentence.validation.This_field_is_required,
        },
        "type[]": {
            required: translations.general_sentence.validation.This_field_is_required,
        },
        "maintenance_type[]": {
            required: translations.general_sentence.validation.This_field_is_required,
        },
        "service_type[]": {
            required: translations.general_sentence.validation.This_field_is_required,
        },
        "project_ids[]": {
            required: translations.general_sentence.validation.This_field_is_required,
        },
        "wo_status[]": {
            required: translations.general_sentence.validation.This_field_is_required,
        }




    },
    errorPlacement: function (error, element) {
        error.addClass("invalid-feedback");
        if (element.attr("name") == "building_ids[]") {
            error.appendTo($("#building_id_error"));
        }
        else if (element.attr("name") == "service_providers[]") {
            error.appendTo($("#service_providers_error"));
        }
        else if (element.attr("name") == "type[]") {
            error.appendTo($("#type_error"));
        }
        else if (element.attr("name") == "maintenance_type[]") {
            error.appendTo($("#maintenance_type_error"));
        }
        else if (element.attr("name") == "service_type[]") {
            error.appendTo($("#service_type_error"));
        }
        else if (element.attr("name") == "wo_status[]") {
            error.appendTo($("#wo_status_error"));
        }
        else if (element.attr("name") == "project_ids[]") {
            error.appendTo($("#project_ids_error"));
        }

        else
        {
          error.insertAfter(element);
        }

    },
    highlight: function (element, errorClass, validClass) {
        $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
        $(element).removeClass("is-invalid");
    },
    submitHandler: function (form,event) {
        event.preventDefault();

        $.ajax({
            url:   $('#count').val(),
            type: "POST",
            data: $(form).serialize(),
            beforeSend: function( xhr ) {
            //xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
            $("#wait").css("display", "block");
         },
            success: function(data) {
                //console.log(data);
                //return false;
                if(data > 0){
                    $("#overlayer").css("display", "block");
                    form.submit();
                }
                else{
                    swal({
                        title:translations.general_sentence.validation.there_are_no_workorders,
                        // text:translations.general_sentence.validation.there_are_no_workorders,
                        confirmButtonText:  translations.general_sentence.swal_buttons.ok,
                    });

                }
            },

            error: function (data) {
                swal('SOMETHING WENT WRONG TRY AGAIN!!')
        },

        });

    },
});
</script>

<script type="text/javascript">
    $("#select-contracts").select2({
        placeholder: "{{__('user_management_module.user_forms.label.select_contract')}}",
        allowClear: true
    });
    $(document).ready(function() {
            let currentYear = 2025;
            let startMonth = null;
            let endMonth = null;
             // const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
            const months = @json(__('advance_contracts.months'));

            function renderMonths() {
    $("#currentYear").text(currentYear);
    $("#monthPicker").empty();

    let today = new Date(); // Get the current system date
    let currentMonth = today.getMonth() + 1; // JavaScript months are 0-based
    let currentYearValue = today.getFullYear();

    months.forEach((month, index) => {
        const monthElement = $(`<div class="month">${month}</div>`);
        monthElement.data("month", index + 1);

        let currentDate = new Date(currentYear, index);
        let isSelected = false, isDisabled = false;

        let systemDate = new Date(); // current system date
        let isFuture =
            currentDate.getFullYear() > systemDate.getFullYear() ||
            (currentDate.getFullYear() === systemDate.getFullYear() && currentDate.getMonth() > systemDate.getMonth());

        if (isFuture) {
            isDisabled = true; // ❌ Mark future months as disabled
        }




        // Highlight the current month
        if (currentYear === currentYearValue && index + 1 === currentMonth) {
            monthElement.addClass("current-month"); // Add a special class for styling
        }
    if (!isFuture) {
        if (startMonth && endMonth) {
            let startDate = new Date(startMonth.year, startMonth.month - 1);
            let endDate = new Date(endMonth.year, endMonth.month - 1);

            if (currentDate >= startDate && currentDate <= endDate) {
                isSelected = true;
            } else {
                isDisabled = true;
            }
        } else if (startMonth) {
            let startDate = new Date(startMonth.year, startMonth.month - 1);
            if (currentDate.getTime() === startDate.getTime()) {
                isSelected = true;
            } else if (currentDate < startDate) {
                isDisabled = true;
            }
        }
    }

        if (isSelected) monthElement.addClass("selected");
        if (isDisabled) monthElement.addClass("disabled");

        monthElement.click(function() {
            if ($(this).hasClass("disabled")) return;

            const selectedMonth = $(this).data("month");

            // If clicked twice, unselect it
            if (startMonth && startMonth.year === currentYear && startMonth.month === selectedMonth && !endMonth) {
                startMonth = null;
                renderMonths();
                return;
            }

            if (!startMonth) {
                startMonth = { year: currentYear, month: selectedMonth };
                endMonth = null;
            } else if (!endMonth) {
                endMonth = { year: currentYear, month: selectedMonth };

                if (new Date(endMonth.year, endMonth.month - 1) < new Date(startMonth.year, startMonth.month - 1)) {
                    [startMonth, endMonth] = [endMonth, startMonth];
                }
            } else {
                startMonth = { year: currentYear, month: selectedMonth };
                endMonth = null;
            }

            renderMonths();
        });

        $("#monthPicker").append(monthElement);
    });
}


            function updateSelectedRange() {
                if (startMonth) {
                    let startText = `${months[startMonth.month - 1]} ${startMonth.year}`;
                    let endText = endMonth ? `${months[endMonth.month - 1]} ${endMonth.year}` : null;

                    let displayText = endText ? `${startText} to ${endText}` : startText;
                    $("#selectedMonthsInput").val(displayText);

                    let selectedMonths = [];
                    let date = new Date(startMonth.year, startMonth.month - 1);
                    let endDate = endMonth ? new Date(endMonth.year, endMonth.month - 1) : date;

                    // Fix: Stop infinite loop by preventing overflow across years
                    while (date <= endDate) {
                        let year = date.getFullYear();
                        let month = String(date.getMonth() + 1).padStart(2, '0');
                        selectedMonths.push(`${year}-${month}`);

                        if (year === endDate.getFullYear() && date.getMonth() === endDate.getMonth()) {
                            break;
                        }
                        date.setMonth(date.getMonth() + 1);
                    }

                    $("#selectedRangeText").text(selectedMonths.join(", "));
                } else {
                    $("#selectedRangeText").text("None");
                    $("#selectedMonthsInput").val("");
                }
            }

            $("#prevYear").click(function() {
                currentYear--;
                renderMonths();
            });

            // $("#nextYear").click(function() {
            //     currentYear++;
            //     renderMonths();
            // });
            $("#nextYear").click(function() {
                let today = new Date();
                let currentSystemYear = today.getFullYear();

                if (currentYear < currentSystemYear) {
                    currentYear++;
                    renderMonths();
                }
            });

            $("#clearButton").click(function() {
                startMonth = null;
                endMonth = null;
                renderMonths();
                updateSelectedRange();
            });

            $("#applyButton").click(function() {
                updateSelectedRange();
                event.stopPropagation();
                $("#dropdownMenu").removeClass("show");
            });

            $("#selectedMonthsInput").on("focus", function () {
                $("#dropdownMenu").addClass("show");
                $("#monthRangeCard").removeClass("d-none");
            });

            // Prevent dropdown from closing when clicking inside
            $("#dropdownMenu").on("mousedown", function (event) {
                event.stopPropagation();
            });

            // Close dropdown only when clicking outside
            $(document).on("mousedown", function (event) {
                if (!$(event.target).closest("#dropdownMenu, #selectedMonthsInput").length) {
                    $("#dropdownMenu").removeClass("show");
                }
            });

            renderMonths();
        });


$(document).ready(function () {
    $('#report_create_form_adc').on('submit', function (e) {
        let hasError = false;

        // Clear previous errors
        $('#contract_error, #report_type_error, #months_error').html('');

        // Contract validation
        
        // Report type validation
        if (!$('#report-type').val()) {
            $('#report_type_error').html(translations.general_sentence.validation.field_is_required);
            hasError = true;
        }

if ($('#select-contracts').val() == null || $('#select-contracts').val().length === 0) {
            $('#contract_error').html(translations.general_sentence.validation.field_is_required);
            hasError = true;
        }

        // Month range validation
        if (!$('#selectedMonthsInput').val()) {
            $('#months_error').html(translations.general_sentence.validation.field_is_required);
            hasError = true;
        }

        // Prevent form submit if errors exist
        if (hasError) {
            e.preventDefault();
        }
    });
});
</script>

@endsection
