@extends('layouts.app')
@section('styles')
<style>
.skip-btn {
    font-size: 12px;
}
.rating_value {
    display: none;
}
</style>
@endsection
@section('content')
<!-- Main Content Starts Here -->
<main class="main-content">
<div class="row">
    <div class="col-lg-12">
         <div class="logo-div text-xs-center mb-5 mt-5 text-center">
                        @if($data['projectImage'])                        
                        <a class="" href="{{  url('') }}"><img class="svg dark radius-xl" src="{{ImagesUploadHelper::displayImage($data['projectImage'], 'uploads/project_images')}}" alt="svg" width="150"></a>
                        @else
                        <a class="" href="{{  url('') }}"><img class="svg dark radius-xl" src="{{ asset('img/OSOOL_logo_svg.svg') }}" alt="svg" width="150"></a>
                        @endif
                        {{--<a class="" href="{{  url('') }}"><img class="svg dark" src="{{ asset('img/OSOOL_logo.png') }}" alt="svg" width="150"></a>--}}
                        </div>
        <div class="breadcrumb-main text-center">
        
            <img src="{{ asset('img/icons/completed.png') }}" class=" m-auto mb-5">
                <h4 class="text-capitalize breadcrumb-title text-center w-100 mt-4">{{ trans('data_maintanance_request.common.maintanance_request_completed', ['maintanance_id' => $data['details']->id ]); }}</h4>
                <div class="clearfix w-100">{{ __('data_maintanance_request.common.how_satisfy_are_you') }}</div>
                        
        </div>
    </div>
</div>  
<div class="form-element">
    <div class="row">
        <div class="col-lg-12">
            <div class="card card-default card-md mb-5 text-center">
                <div class="card-body pb-md-20 pt-0">
                    <div class="col-md-6 m-auto">
                    <form action="{{ route('maintenance.task.feedback') }}" method="post">
                        @csrf()
                        <input type="hidden" id="maintanance_id" name="maintanance_id" value="{{ $data['details']->id }}" >
                        <input type="hidden" id="rating" name="rating" value="" >
                        <input type="hidden" id="rating_value" name="rating_value" value="" >
                        <div class="form-row mx-n15">
                            <div class="col-12 px-15 text-center">
                                <div class="ratings mt-5">
                                <p></p>
                                <ul class="list-inline mb-4">
                                    <li><span><i class="far fa-tired"></i></span>
                                        <div class="about-smiley">
                                        {{ __('data_maintanance_request.common.worst') }} 
                                        </div>
                                        <span class="rating_value">1</span>
                                    </li>
                                    <li><span><i class="far fa-frown"></i></span>
                                        <div class="about-smiley">
                                        {{ __('data_maintanance_request.common.not_good_at_all') }} 
                                        </div>
                                        <span class="rating_value">2</span>
                                    </li>
                                    <li><span><i class="far fa-meh"></i></span>
                                        <div class="about-smiley">
                                        {{ __('data_maintanance_request.common.its_ok') }} 
                                        </div>
                                        <span class="rating_value">3</span>
                                    </li>
                                    <li><span><i class="far fa-smile"></i></span>
                                        <div class="about-smiley">
                                        {{ __('data_maintanance_request.common.very_good') }} 
                                        </div>
                                        <span class="rating_value">4</span>
                                    </li>
                                    <li><span><i class="far fa-laugh-beam"></i></span>
                                        <div class="about-smiley">
                                        {{ __('data_maintanance_request.common.excellent') }} 
                                        </div>
                                        <span class="rating_value">5</span>
                                    </li>
                                </ul>
                                <button class="btn btn-primary px-30 w-100" id="submit_ratings" type="submit"  disabled=""><b>{{ __('data_maintanance_request.buttons.send') }}</b></button>
                                    <!--div>Or</div-->
                                <a href="javascript:void(0);" class="skip-btn mt-2 d-inline-block bg-grey w-100 btn rounded text-dark" id="skip">{{ __('data_maintanance_request.buttons.skip') }}</a>

                                </div>
                            </div>                                            
                        </div>                                        
                    </form>
                </div>
                </div>
            </div>
        </div>                    
    </div>
</div>
</main>   
@endsection
@section('scripts')

<script type="text/javascript">
        $('.rating_value').hide();
        $(document).ready(function(){
            $('#submit_ratings').prop('disabled', true);
            $('.rating_value').hide();
        });
        $('.ratings ul li').click(function(){
            $(this).parent().find(".active").removeClass("active");
            $(this).addClass("active");
            var current_rating = $(this).html();
            console.log($.trim($(this).find('.about-smiley').text()));
            $('#rating').val($.trim($(this).find('.about-smiley').text()));
            $('#rating_value').val($.trim($(this).find('.rating_value').text()));
            $('#submit_ratings').prop('disabled', false);
        });

        $('#skip').click(function (){
            $.ajax({
                url: "{{ route('maintenance.skip') }}",
                method: "POST",
                data: {
                    _token: $('meta[name="csrf-token"]').attr("content"),
                    skip: "1",
                    maintanance_id: $('#maintanance_id').val(),
                },
                dataType: "json",
                beforeSend: function () {},
                success: function (data) {
                    
                    location.reload();
                    
                },
                error: function (data) {
                    var errors = $.parseJSON(data.responseText);
                    toastr.error(data, translations.general_sentence.validation.Error, {
                        timeOut: 1500,
                        positionClass: "toast-top-center",
                        progressBar: true,
                        preventDuplicates: true,
                        preventOpenDuplicates: true,
                    });
                },
            });
        });
</script>
@endsection
