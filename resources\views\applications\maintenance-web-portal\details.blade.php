@extends('layouts.app')
@section('styles')
<style type="text/css">
    a.btn-outline-primary .feather{
        color:#5f63f2 !important;
    }

a.btn-outline-primary:hover .feather{
        color:#ffffff !important;
    }
    .req-image{
        height: 80px;
        width: 80px;
    }
</style>
@endsection
@section('content')
<!-- Main Content Starts Here -->
<main class="main-content">

    <div class="breadcrumb-main">
        <div class="d-sm-flex justify-content-between flex-fill px-4 align-items-center border-bottom pb-3">
            <div class="d-flex gap-10 align-items-center">
        <div class="logo-div text-xs-center text-center">
                        @if($data['projectImage'])                        
                        <a class="" href="{{  url('') }}"><img class="svg dark radius-xl" src="{{ImagesUploadHelper::displayImage($data['projectImage'], 'uploads/project_images')}}" alt="svg" width="100"></a>
                        @else
                        <a class="" href="{{  url('') }}"><img class="svg dark radius-xl" src="{{ asset('img/OSOOL_logo_svg.svg') }}" alt="svg" width="100"></a>
                        @endif
                        {{--<a class="" href="{{  url('') }}"><img class="svg dark radius-xl" src="{{ asset('img/OSOOL_logo.png') }}" alt="svg" width="100"></a>--}}
                        </div>
        <div>
        <h4 class="text-capitalize breadcrumb-title w-100 mb-0 text-sm-left text-center mt-sm-0 mt-4">{{ __('data_maintanance_request.common.Request') }} #{{ $data['maintanance_request_id']}}</h4>
        <div class="clearfix w-100"></div>

        <p class=" w-100 grey-7 text-sm-left text-center mb-0">{{ __('data_maintanance_request.common.You can track your maintenance request details here') }}</p>
        </div>
    </div>
     <span class="badge badge-round badge-xl {{ $data['status_class'] }} pull-right">{{
                        $data['trans_status']
                    }}</span>
    </div>
    </div>

    <div class="form-element">
        <div class="row">
            <div class="col-lg-12">
                    <!-- <div class="card-header no-bg border-none">
                        <p class="fs-18 text-dark d-flex align-items-center justify-content-between mb-0 icon-heading"><span class="d-flex align-items-center justify-content-between"><span class="icon mr-2 border rounded"><span data-feather="clock"></span></span> <span>{{ __('data_maintanance_request.common.Workorder Status') }}</span></p>

                    </div> -->
                    @if($data['details']->status == 'Completed' && $data['details']->feedback == '')
                    <div class="card-header no-bg border-0 py-0 mx-2 p-0 d-center">
                        <a href="javascript:void(0);" class="btn btn-outline-primary" id="add_feedback"><b><span data-feather="star"></span> {{ __('data_maintanance_request.buttons.add_feedback') }}</b></a>
                    </div>
                    @endif
                     @if($data['details']->status == 'Rejected')
                    <div class="card-body py-0">
                        <form>
                            <div class="form-row mx-n15">
                                <div class="col-12 px-15">
                                    <p class="mb-0">
                                        {{  $data['details']->reason  }}
                                    </p>
                                </div>
                            </div>
                        </form>
                    </div>
                    @else
                    <div class="card-body py-0">
                        <form>
                            <div class="form-row mx-n15">
                                <div class="col-12 px-15">
                                    <p>
                                        <!--{{ __('data_maintanance_request.common.'.$data['status_description']) }}-->
                                    </p>
                                </div>
                            </div>
                        </form>
                    </div>
                    @endif
                <div class="border radius-15 m-sm-4 m-2 mb-sm-0 mb-3 mt-0">
                <div class="card-default">
                    <div class="card-header no-bg border-none px-3 pb-2">
                        <p class="fs-18 text-osool d-flex align-items-center mb-0 icon-heading fw-600 text-osool"><span class="mr-2 fs-26"><i class="iconsax" icon-name="location"></i></span>{{ __('data_maintanance_request.common.Workorder Location') }}</p>
                    </div>
                    <div class="card-body pb-md-20 px-3 pt-0">
                        <form>
                            <div class="form-row mx-n15">
                                <div class="col-4 mb-sm-0 mb-10 px-15">
                                        <div class="">
                                    <label for="validationDefault01" class="il-gray fs-15 align-center fw-600">{{$trans_the_building}}</label>
                                    <span class="text-new-primary">{{ $data['buildingName'] }}</span>
                                </div>
                                </div>
                                <div class="col-4 mb-sm-0 mb-10 px-15 border-right border-left border-2">
                                        <div class="">
                                    <label for="validationDefault02" class="il-gray fs-15 align-center fw-600">{{ __('data_maintanance_request.common.floor') }}</label>
                                    <span class="text-new-primary">{{                        $data['details']->floor  }}</span>
                                    </div>
                                </div>
                                <div class="col-4 mb-sm-0 mb-10 px-15">
                                        <div class="">
                                    <label for="validationDefault012" class="il-gray fs-15 align-center fw-600">{{ __('data_maintanance_request.common.space_unit') }}</label>
                                    <span class="text-new-primary">{{                        $data['details']->space_no  }}</span>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

                <div class="border radius-15 m-sm-4 m-2 mb-sm-0 mb-3 mt-0">
                <div class="card-default">
                    <div class="card-header no-bg border-none px-3 pb-2">
                        <p class="fs-18 text-osool d-flex align-items-center mb-0 icon-heading fw-600"><span class="mr-2 fs-26"><i class="iconsax" icon-name="edit-1"></i></span>{{ __('data_maintanance_request.common.Issue Details') }}</p>
                    </div>
                    <div class="card-body pb-md-20 px-3 pt-0">
                        <form>
                            <div class="form-row mx-n15">
                                <div class="col-12 px-15">
                                        <div class="bg-grey p-4 radius-xl">
                                    <label for="validationDefault01" class="il-gray fs-14 fw-100 align-center">{{ __('data_maintanance_request.common.Issue Description') }}</label>
                                    <p>
                                    {{ $data['details']->description  }}
                                    </p>
                                </div>
                                </div>

                            </div>

                        </form>
                    </div>
                </div>
            </div>

                <div class="border radius-15 m-sm-4 m-2 mb-sm-0 mb-3 mt-0">
                    <div class="card-header no-bg border-none px-3 pb-2">
                        <p class="fs-18 text-osool d-flex align-items-center mb-0 icon-heading fw-600"><span class="mr-2 fs-26"><i class="iconsax" icon-name="paperclip-square"></i></span>{{ __('data_maintanance_request.common.submitted_images') }}</p>
                    </div>
                    @php
                        $maintenanceImages = [];
                        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif'];

                        foreach (['image1', 'image2', 'image3'] as $field) {
                            $img = $data['details']->$field ?? null;
                            if ($img) {
                                $extension = strtolower(pathinfo($img, PATHINFO_EXTENSION));
                                if (in_array($extension, $imageExtensions)) {
                                    $maintenanceImages[] = $img;
                                }
                            }
                        }
                    @endphp

                    @if(count($maintenanceImages) > 0)
                    <div class="card-body pb-md-20 pt-0 px-3  image-gallery">
                                <div class="site-scrollbar req-img-section flex-fill d-flex gap-10 flex-column">
                                    @foreach($maintenanceImages as $image)
                                        @php
                                            $imageUrl = ImagesUploadHelper::displayImage($image, 'uploads/maintanance_request');
                                            $fileName = basename($image);

                                        @endphp
                                        <div class="bg-opacity-new-primary-05 p-2 radius-15 d-flex align-items-center gap-10">
                                            <div class="req-image radius-15"
                                                style="background: url('{{ $imageUrl }}'); background-size: cover; background-position: center;">
                                                <img src="{{ $imageUrl }}" alt="{{ $fileName }}">
                                            </div>
                                            <div class="mb-0 fs-14">
                                                <span class="d-block">{{ $fileName }}</span>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @else 
                        <div class="crm">
                            <div class="image-upload bg-white mb-0">
                                <label class="mb-2 mt-4">
                                    <div class="upload-bg wh-50 d-flex-center rounded-circle mx-auto position-relative">
                                        <i class="iconsax icon fs-22 fs-22 mr-0 text-dark position-relative" icon-name="emoji-sad"></i>
                                    </div>
                                    <p class="drag_drop_txt mt-3 mb-0">
                                       {{ __('data_maintanance_request.common.no_submitted_images') }}
                                    </p>
                                </label>
                            </div>
                        </div>
                    @endif
                @if(!empty($data['service_provider_image']))
                <div class="border radius-15 m-4 mt-0">
                    <div class="card-header no-bg border-none px-3 pb-2">
                        <p class="fs-18 text-osool d-flex align-items-center mb-0 icon-heading fw-600"><span class="mr-2 fs-30"><i class="iconsax" icon-name="task-list-square"></i></span>{{ __('data_maintanance_request.common.service_provider_result') }}</p>
                    </div>
                    <div class="card-body pb-md-20 pt-0 px-3">
                    <p class="fw-600">{{ __('data_maintanance_request.common.pictures_after_maintenance') }}</p>
                         <div class="card-body pb-md-20 pt-0 px-3  image-gallery">
                                <div class="site-scrollbar req-img-section flex-fill d-flex gap-10 flex-column">
                            @foreach($data['service_provider_image'] as $img)
                                @php
                                    $ext = strtolower(trim(pathinfo($img, PATHINFO_EXTENSION)));
                                    $isPdf = $ext === 'pdf';
                                    $imageUrl = $isPdf 
                                        ? asset('img/pdf.png') 
                                        : ImagesUploadHelper::displayImage($img, 'actions', 0, false, true);
                                    $fileName = basename($img);
                                    $fileSize = ''; // Optional: You can calculate file size if needed
                                    $fileUrl = url('storage/' . $img);
                                @endphp
                        
                                <div class="bg-opacity-new-primary-05 p-2 radius-15 d-flex align-items-center gap-10">
                                    <div class="req-image radius-15" 
                                        style="background: url('{{ $imageUrl }}'); background-size: cover; background-position: center;">
                                        @if($isPdf)
                                            <a target="_blank" href="{{ $fileUrl }}">
                                                <img src="{{ asset('img/pdf.png') }}" height="60px" class="mr-3 rounded" style="opacity:0;">
                                            </a>
                                        @else
                                         <img src="{{ $imageUrl }}" alt="{{ $fileName }}">
                                        @endif
                                    </div>
                                    <div class="mb-0 fs-14">
                                        <span class="d-block">{{ $fileName }}</span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                         </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</main>

<div class="lightbox">
  <button class="close bg-white wh-35 rounded-circle p-1"><i class="iconsax text-osool" icon-name="x-circle"></i></button>
  <button class="download  bg-white wh-35 rounded-circle p-1 mr-3"><i class="iconsax text-osool" icon-name="picture-download"></i></button>
  <!-- <button class="prev"><i class="iconsax" icon-name="arrow-left"></i></button> -->
  <img src="" alt="Lightbox Image">
  <!-- <button class="next"><i class="iconsax" icon-name="arrow-right"></i></button> -->
</div>

@endsection
@section('scripts')

<script>
   function previewFile(input){
      var file = $("#customFile").get(0).files[0];

      if(file){
            var reader = new FileReader();

            reader.onload = function(){
               $("#previewImg").attr("src", reader.result);
            }

            reader.readAsDataURL(file);
      }
   }
   function previewFile1(input){
      var file = $("#customFile1").get(0).files[0];

      if(file){
            var reader = new FileReader();

            reader.onload = function(){
               $("#previewImg1").attr("src", reader.result);
            }
            reader.readAsDataURL(file);
      }
   }
   function previewFile2(input){
      var file = $("#customFile2").get(0).files[0];

      if(file){
            var reader = new FileReader();

            reader.onload = function(){
               $("#previewImg2").attr("src", reader.result);
            }
            reader.readAsDataURL(file);
      }
   }
</script>
<script type="text/javascript">
   $(function() {
      $(".custom-file input:file").change(function (){
            $(this).parent().addClass("active");
      });
   });
   $(".custom-file .close").click(function(){
      $(this).parent().parent().find(".custom-file-input").val() == "";
      $(this).parent().parent().removeClass("active");
   });
</script>
<script>



    $(document).ready(function(){

        $('#add_feedback').click(function (){
            $.ajax({
                url: "{{ route('maintenance.skip') }}",
                method: "POST",
                data: {
                    _token: $('meta[name="csrf-token"]').attr("content"),
                    skip: "0",
                    maintanance_id: "{{$data['maintanance_request_id']}}",

                },
                dataType: "json",
                beforeSend: function () {},
                success: function (data) {

                    location.reload();

                },
                error: function (data) {
                    var errors = $.parseJSON(data.responseText);
                    toastr.error(data, translations.general_sentence.validation.Error, {
                        timeOut: 1500,
                        positionClass: "toast-top-center",
                        progressBar: true,
                        preventDuplicates: true,
                        preventOpenDuplicates: true,
                    });
                },
            });
        });
    });

</script>
@endsection
