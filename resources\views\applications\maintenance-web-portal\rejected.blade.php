@extends('layouts.app')
@section('styles')

@endsection
@section('content')
<!-- Main Content Starts Here -->
<main class="main-content">
<div class="row">
    <div class="col-lg-12">
                        <div class="logo-div text-xs-center mb-5 mt-5 text-center">
                        @if($data['projectImage'])                        
                        <a class="" href="{{  url('') }}"><img class="svg dark radius-xl" src="{{ImagesUploadHelper::displayImage($data['projectImage'], 'uploads/project_images')}}" alt="svg" width="150"></a>
                        @else
                        <a class="" href="{{  url('') }}"><img class="svg dark radius-xl" src="{{ asset('img/OSOOL_logo_svg.svg') }}" alt="svg" width="150"></a>
                        @endif
                        </div>
        <div class="breadcrumb-main text-center">
            <img src="{{ asset('img/icons/rejected.png') }}" class="mb-20 m-auto">
            <h4 class="text-capitalize breadcrumb-title text-center w-100 mb-20 mt-20">{{ __('data_maintanance_request.common.Sorry, Your Maintenance Request was Rejected') }}</h4>
            <div class="clearfix w-100"></div>

            <p class="text-center w-100 grey-7">{{ __('data_maintanance_request.common.Your request is rejected') }}, {{ $data['details']['reason'] }}</p>

            <p class="text-center w-100 grey-7"><a href="{{ route('maintenance.task.details',$data['details']['id']) }}">{{ __('data_maintanance_request.common.click here') }}</a> {{ __('data_maintanance_request.common.to_review_your_request_details') }}</p>
        </div>
    </div>
</div>   

            
    
</main>   
@endsection
@section('scripts')

@endsection
