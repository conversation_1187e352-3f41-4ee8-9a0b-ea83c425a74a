<?php
// phpinfo();
?>
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}"
    dir="{{ (app()->getLocale()=='ar' ? 'rtl' : 'ltr') }}">

<head>
    <meta charset="UTF-8">
    @if(App::getLocale()=='en')
    <title lang="en">Osool </title>
    @else (App::getLocale()=='ar')
    <title lang="ar">أصول </title>
    @endif
    <meta name="description" lang="en" content="Osool is your technical partner in following up on maintenance contracts and managing the operational system. Osool enables facilities and properties management teams to manage maintenance and property operations, link with operational service providers, follow up on maintenance contract work in one platform, and serve the final beneficiary of the property.">
    <meta name="description" lang="ar" content="أصول هو شريكك التقني في متابعة عقود الصيانة وإدارة المنظومة التشغيلية. يمكن أصول فرق إدارة المرافق والأملاك من إدارة عمليات الصيانة والعقار والربط مع مقدمي الخدمات التشغيلية ومتابعة أعمال عقود الصيانة في منصة واحدة وخدمة المستفيد النهائي من العقار.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta property="og:image" content="{{ asset('img/img-share.jpg') }}">
    <meta property="og:image:secure_url" content="{{ asset('img/img-share.jpg') }}">
    <meta property="og:image:type" content="image/jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <link rel="shortcut icon" href="{{ asset('home/image/favicon/favicon-32x32.png') }}" type="image">
    <!-- Bootstrap, fonts & icons  -->
   <link rel="stylesheet" href="{{ asset('new_files/new_font.css') }}?v={{ filemtime(public_path('new_files/new_font.css')) }}">


    @if(App::getLocale()=='en')
    <link rel="stylesheet" href="{{ asset('home/css/bootstrap.css') }}">
    @else (App::getLocale()=='ar')
    <link rel="stylesheet" href="{{ asset('home/css/bootstrap-rtl.css') }}">
    @endif
    @include('layouts.partials._styles')
    @if(App::getLocale()=='en')
    <link rel="stylesheet" href="{{ asset('home/css/main.css') }}">
    @else (App::getLocale()=='ar')
    <link rel="stylesheet" href="{{ asset('home/css/main-rtl.css') }}">
    @endif
    <link rel="stylesheet" href="{{ asset('home/fonts/icon-font/css/style.css') }}">
    <link rel="stylesheet" href="{{ asset('home/fonts/typography-font/typo.css') }}">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css" integrity="sha384-DyZ88mC6Up2uqS4h/KRgHuoeGwBcD4Ng9SiP4dIRy0EXTlnuz47vAwmeGwVChigm" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css2?family=Karla:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Gothic+A1:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800;900&display=swap" rel="stylesheet">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="{{ asset('home/plugins/aos/aos.min.css') }}">
    <link rel="stylesheet" href="{{ asset('home/plugins/fancybox/jquery.fancybox.min.css') }}">
    <link rel="stylesheet" href="{{ asset('home/plugins/nice-select/nice-select.min.css') }}">
    <link rel="stylesheet" href="{{ asset('home/plugins/slick/slick.min.css') }}">
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">

    <link rel="stylesheet" href="{{ asset('css/scss/new-style.css') }}">
    <style type="text/css">
        .page-wrapper {
            padding-top: 150px;
        }
        @media only screen and (max-width:576px{
            .page-wrapper {
                padding-top: 100px;
            }
        }

        .table-responsive {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .table thead {
            background-color: #F8F9FB;
            border: 1px solid #F1F2F6;
            border-radius: 5px
        }

        .table th {
            color: #9199B7;
            font-weight: 600;
            border-bottom: 1px solid #e5e7eb;
            font-size: 16px;
        }

        .table td {
            font-weight: 600;
            font-size: 16px;
            vertical-align: middle;
        }

        .status-badge {
/*            background-color: #E6F1F6;*/
            padding: 6px 12px;
            font-weight: 500;
            border-radius: 999px;
            font-size: 14px;
            display: inline-block;
        }

        @media (max-width: 768px) {
            .table thead {
                display: none;
            }

            .table tr {
                display: block;
                margin-bottom: 1rem;
                border-bottom: 2px solid #e5e7eb;
            }

            .table td {
                display: block;
                text-align: right;
                padding: 12px;
            }

            .table td::before {
                content: attr(data-label);
                float: left;
                font-weight: 500;
                color: #6b7280;
            }
        }
    </style>
</head>
@php
$lang_path=resource_path('lang/'.App::getLocale());
$translations=collect(File::allFiles($lang_path))->flatMap(function ($file)use($lang_path) {
return [
($translation = $file->getBasename('.php')) => trans($translation),
];
})->toJson();
@endphp
<script type="text/javascript">
    window.baseUrl = "{{URL::to('/')}}";
    window.current_locale = "{{App::getLocale()}}";
    window.translations = {
        !!$translations!!
    };
    //console.log(window.current_locale) ;
</script>

<body data-theme-mode-panel-active data-theme="light" class="ltr">
    <header class="site-header site-header--menu-right landing-1-menu site-header--absolute site-header--sticky bg-white shadow-sm">
        <div class="container-fluid">
            <nav class="navbar site-navbar">
                <!-- Brand Logo-->
                <div class="brand-logo">
                    <a href="{{ url('/') }}">
                        <!-- light version logo (logo must be black)-->
                        <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="light-version-logo">
                        <!-- Dark version logo (logo must be White)-->
                        <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="dark-version-logo">
                    </a>
                </div>
                <div class="menu-block-wrapper">
                    <div class="menu-overlay"></div>
                    <nav class="menu-block" id="append-menu-header">
                        <div class="mobile-menu-head">
                            <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="">
                            <div class="go-back">
                                <i class="fa fa-angle-left"></i>
                            </div>
                            <div class="current-menu-title"></div>
                            <div class="mobile-menu-close">&times;</div>
                        </div>
                        <ul class="site-menu-main">
                            <li class="nav-item">
                                <a href="#menu1" class="nav-link-item"> {{__('landing_page.menu.wahts_osool')}}</a>
                            </li>
                            <li class="nav-item">
                                <a href="#menu2" class="nav-link-item">{{__('landing_page.menu.osool_advantage')}} </a>
                            </li>
                            <li class="nav-item">
                                <a href="#menu3" class="nav-link-item"> {{__('landing_page.menu.beneficiaries_of_osool')}}</a>
                            </li>
                            <li class="nav-item">
                                <a href="#menu4" class="nav-link-item"> {{__('landing_page.menu.contact_us')}}</a>
                            </li>
                            <li class="nav-item mt-sm-0 mt-3 mt-sm-0">
                                <span class="nav-link-item no-hover pr-0">
                                    <a href="{{ route('psp-registration.index') }}" class="btn btn-border-lb btn-hover-slide position-relative overflow-hidden"><span class="rounded d-block position-relative"> Sign Up</span></a>
                                </span>
                            </li>
                            <li class="nav-item mt-sm-0 mt-3 mt-sm-0">
                                <span class="nav-link-item no-hover pr-0">
                                    <a href="javascript:void(0);" class="btn btn-border-lb btn-hover-slide position-relative overflow-hidden" data-bs-toggle="modal" data-bs-target="#osool-popup"><span class="rounded d-block position-relative"> {{__('landing_page.menu.get_started')}}</span></a>
                                </span>
                            </li>
                            <li class="d-flex align-items-center">
                                <span class="nav-link-item no-hover">
                                    <a class="btn bg-db text-white focus-reset lan-btn" href="{{ url('login') }}">
                                        {{__('landing_page.menu.login')}}
                                    </a>
                                </span>
                            </li>
                            <li class="nav-item">
                                @if (App::getLocale()=='en')
                                <a href="{{route('changeLanguage',"ar")}}" class="nav-link-item lang-btn"><span class="rounded d-block">{{__('landing_page.menu.en_ar')}}</span></a>
                                @elseif (App::getLocale()=='ar')
                                <a href="{{route('changeLanguage',"en")}}" class="nav-link-item lang-btn"><span class="rounded d-block">{{__('landing_page.menu.en_ar')}}</span></a>
                                @endif

                            </li>
                            <li class="d-flex align-items-center">
                                <div class="user-dropdown-wrapper">
                                    <div class="user-dropdown-trigger">
                                        <i class="fas fa-user-circle"></i>
                                        <span class="welcome-text">Welcome Mohammed</span>
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="user-dropdown-menu">
                                        <a href="#" class="dropdown-item">
                                            <i class="fas fa-user-cog"></i>
                                            Profile Management
                                        </a>
                                        <a href="#" class="dropdown-item">
                                            <i class="fas fa-store"></i>
                                            Register as Vendor
                                        </a>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </nav>
                </div>

                <!-- mobile menu trigger -->
                <div class="mobile-menu-trigger">
                    <span></span>
                </div>
                <!--/.Mobile Menu Hamburger Ends-->
            </nav>
        </div>
    </header>
    <div class="page-wrapper container">
        <div class="page-title-wrap">
            <div class="page-title d-flex justify-content-between">
                <div class="page-title__left">
                    <div class="d-flex align-items-center user-member__title justify-content-center mr-sm-25">
                        <h4 class="text-capitalize fw-500 breadcrumb-title">
                            <a href="javascript:history.back()"><i class="las la-arrow-left"></i></a> Profile Management
                        </h4>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
            <div class="table-responsive">
                <table class="table table-borderless mb-0">
                    <thead>
                        <tr>
                            <th><span class="no-wrap"> Application ID </span></th>
                            <th><span class="no-wrap"> Name </span></th>
                            <th><span class="no-wrap"> Date </span></th>
                            <th><span class="no-wrap"> Status </span></th>
                            <th><span class="no-wrap"> Actions </span></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td data-label="Application ID">AP00001</td>
                            <td data-label="Name">Mohammed Alotaibi</td>
                            <td data-label="Date">18/09/2024 08:23 AM</td>
                            <td data-label="Status">
                                <span class="status-badge info-badge">Submitted</span>
                            </td>
                            <td data-label="Actions">
                                <a href="#" class="btn btn-primary">View Application</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            </div>
        </div>
    </div>
    </div>

    </div>


    @include('layouts.partials._scripts')

    <script src="{{ asset('home/plugins/menu/menu.js') }}"></script>
    <!-- toggle header dropdown , general layout  -->

    <script>
        $(document).ready(function() {
            // Toggle dropdown menu
            $('.user-dropdown-trigger').on('click', function(e) {
                e.stopPropagation();
                $('.user-dropdown-menu').toggleClass('active');
            });

            // Close dropdown when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.user-dropdown-wrapper').length) {
                    $('.user-dropdown-menu').removeClass('active');
                }
            });

            // Handle mobile menu integration
            $('.mobile-menu-trigger').on('click', function() {
                if (window.innerWidth <= 768) {
                    $('.user-dropdown-menu').removeClass('active');
                }
            });
        });
    </script>


</body>


</html>