<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}"
    dir="{{ (app()->getLocale()=='ar' ? 'rtl' : 'ltr') }}">

<head>
    <meta charset="UTF-8">
    @if(App::getLocale()=='en')
    <title lang="en">Osool </title>
    @else (App::getLocale()=='ar')
    <title lang="ar">أصول </title>
    @endif
    <meta name="description" lang="en" content="Osool is your technical partner in following up on maintenance contracts and managing the operational system. Osool enables facilities and properties management teams to manage maintenance and property operations, link with operational service providers, follow up on maintenance contract work in one platform, and serve the final beneficiary of the property.">
    <meta name="description" lang="ar" content="أصول هو شريكك التقني في متابعة عقود الصيانة وإدارة المنظومة التشغيلية. يمكن أصول فرق إدارة المرافق والأملاك من إدارة عمليات الصيانة والعقار والربط مع مقدمي الخدمات التشغيلية ومتابعة أعمال عقود الصيانة في منصة واحدة وخدمة المستفيد النهائي من العقار.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta property="og:image" content="{{ asset('img/img-share.jpg') }}">
    <meta property="og:image:secure_url" content="{{ asset('img/img-share.jpg') }}">
    <meta property="og:image:type" content="image/jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <link rel="shortcut icon" href="{{ asset('home/image/favicon/favicon-32x32.png') }}" type="image">
    <!-- Bootstrap, fonts & icons  -->
   <link rel="stylesheet" href="{{ asset('new_files/new_font.css') }}?v={{ filemtime(public_path('new_files/new_font.css')) }}">


    @if(App::getLocale()=='en')
    <link rel="stylesheet" href="{{ asset('home/css/bootstrap.css') }}">
    @else (App::getLocale()=='ar')
    <link rel="stylesheet" href="{{ asset('home/css/bootstrap-rtl.css') }}">
    @endif
    @include('layouts.partials._styles')
    @if(App::getLocale()=='en')
    <link rel="stylesheet" href="{{ asset('home/css/main.css') }}">
    @else (App::getLocale()=='ar')
    <link rel="stylesheet" href="{{ asset('home/css/main-rtl.css') }}">
    @endif

    <link rel="stylesheet" href="{{ asset('home/fonts/icon-font/css/style.css') }}">
    <link rel="stylesheet" href="{{ asset('home/fonts/typography-font/typo.css') }}">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css" integrity="sha384-DyZ88mC6Up2uqS4h/KRgHuoeGwBcD4Ng9SiP4dIRy0EXTlnuz47vAwmeGwVChigm" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css2?family=Karla:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Gothic+A1:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800;900&display=swap" rel="stylesheet">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="{{ asset('home/plugins/aos/aos.min.css') }}">
    <link rel="stylesheet" href="{{ asset('home/plugins/fancybox/jquery.fancybox.min.css') }}">
    <link rel="stylesheet" href="{{ asset('home/plugins/nice-select/nice-select.min.css') }}">
    <link rel="stylesheet" href="{{ asset('home/plugins/slick/slick.min.css') }}">
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">

    <link rel="stylesheet" href="{{ asset('css/scss/new-style.css') }}">

    <style type="text/css">
        .page-wrapper {
            padding-top: 150px;
        }
        @media only screen and (max-width:576px){
            .page-wrapper {
                padding-top: 100px;
            }
        }

        .nav-link {
            color: #6c757d;
            text-decoration: none;
            padding: 10px 15px;
            transition: color 0.3s ease;
        }

        .nav-link.active p,
        .nav-link.active .nav-icon,
        .nav-link.active .chevron i {
            color: var(--primary) !important;
        }

        .nav-icon,
        .chevron i {
            color: #6c757d;
            transition: color 0.3s ease;
        }

        .account-profile #remove_pro_pic {
            position: absolute;
            color: #fff;
            border: 0px;
            bottom: 0;
            right: 0;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            box-shadow: 0px 1px 4px 0px #1A0F011F;
            background: #ffffffff;
        }

        .account-profile .pro_img_wrapper {
            position: relative;
            width: 60px;
            height: 60px;
        }

        .logout {
            background: #fff;
            border: 0
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .service-item select,
        .service-item input {
            flex: 1;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #fff !important;
            border: 1px solid #e3e6ef;
            border-radius: 4px;
            display: flex;
            align-items: center;
            padding: 10px 20px;
            color: var(--primary);
            font-weight: 500;
            padding: 8px 12px;
            border-radius: 5px;
            width: 100%;
        }

        .file-item button {
            border: none;
            background: transparent;
            color: red;
            cursor: pointer;
            font-size: 18px;
            position: absolute;
            right: 5px;
            [lang="ar"] &{
                right: auto;
                left:5px;
            }
        }

        .hidden {
            display: none;
        }

        .file-input {
            opacity: 0;
            position: absolute;
            width: 0;
            height: 0;
            display: none;
        }

        .file-name {
            flex-grow: 1;
            color: var(--primary);
        }

        .remove-file {
            border: none;
            background: transparent;
            color: red;
            cursor: pointer;
            font-size: 18px;
        }


        .secondary-btn {
            background: #8692A6;
            color: #fff;
        }

        .secondary-btn:hover {
            background: #8692A699 !important;
        }
    </style>
</head>
@php
$lang_path=resource_path('lang/'.App::getLocale());
$translations=collect(File::allFiles($lang_path))->flatMap(function ($file)use($lang_path) {
return [
($translation = $file->getBasename('.php')) => trans($translation),
];
})->toJson();
@endphp
<script type="text/javascript">
    window.baseUrl = "{{URL::to('/')}}";
    window.current_locale = "{{App::getLocale()}}";
    window.translations = {!!$translations!!};
    //console.log(window.current_locale) ;
</script>

<body data-theme-mode-panel-active data-theme="light" class="ltr">
    <header class="site-header site-header--menu-right landing-1-menu site-header--absolute site-header--sticky bg-white shadow-sm">
        <div class="container-fluid">
            <nav class="navbar site-navbar">
                <!-- Brand Logo-->
                <div class="brand-logo">
                    <a href="{{ url('/') }}">
                        <!-- light version logo (logo must be black)-->
                        <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="light-version-logo">
                        <!-- Dark version logo (logo must be White)-->
                        <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="dark-version-logo">
                    </a>
                </div>
                <div class="menu-block-wrapper">
                    <div class="menu-overlay"></div>
                    <nav class="menu-block" id="append-menu-header">
                        <div class="mobile-menu-head">
                            <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="">
                            <div class="go-back">
                                <i class="fa fa-angle-left"></i>
                            </div>
                            <div class="current-menu-title"></div>
                            <div class="mobile-menu-close">&times;</div>
                        </div>
                        <ul class="site-menu-main">
                            <li class="nav-item">
                                <a href="#menu1" class="nav-link-item"> {{__('landing_page.menu.wahts_osool')}}</a>
                            </li>
                            <li class="nav-item">
                                <a href="#menu2" class="nav-link-item">{{__('landing_page.menu.osool_advantage')}} </a>
                            </li>
                            <li class="nav-item">
                                <a href="#menu3" class="nav-link-item"> {{__('landing_page.menu.beneficiaries_of_osool')}}</a>
                            </li>
                            <li class="nav-item">
                                <a href="#menu4" class="nav-link-item"> {{__('landing_page.menu.contact_us')}}</a>
                            </li>
                            <li class="nav-item mt-sm-0 mt-3 mt-sm-0">
                                <span class="nav-link-item no-hover pr-0">
                                    <a href="{{ route('psp-registration.index') }}" class="btn btn-border-lb btn-hover-slide position-relative overflow-hidden"><span class="rounded d-block position-relative"> Sign Up</span></a>
                                </span>
                            </li>
                            <li class="nav-item mt-sm-0 mt-3 mt-sm-0">
                                <span class="nav-link-item no-hover pr-0">
                                    <a href="javascript:void(0);" class="btn btn-border-lb btn-hover-slide position-relative overflow-hidden" data-bs-toggle="modal" data-bs-target="#osool-popup"><span class="rounded d-block position-relative"> {{__('landing_page.menu.get_started')}}</span></a>
                                </span>
                            </li>
                            <li class="d-flex align-items-center">
                                <span class="nav-link-item no-hover">
                                    <a class="btn bg-db text-white focus-reset lan-btn" href="{{ url('login') }}">
                                        {{__('landing_page.menu.login')}}
                                    </a>
                                </span>
                            </li>
                            <li class="nav-item">
                                @if (App::getLocale()=='en')
                                <a href="{{route('changeLanguage',"ar")}}" class="nav-link-item lang-btn"><span class="rounded d-block">{{__('landing_page.menu.en_ar')}}</span></a>
                                @elseif (App::getLocale()=='ar')
                                <a href="{{route('changeLanguage',"en")}}" class="nav-link-item lang-btn"><span class="rounded d-block">{{__('landing_page.menu.en_ar')}}</span></a>
                                @endif

                            </li>
                            <li class="d-flex align-items-center">
                                <div class="user-dropdown-wrapper">
                                    <div class="user-dropdown-trigger">
                                        <i class="fas fa-user-circle"></i>
                                        <span class="welcome-text">Welcome Mohammed</span>
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="user-dropdown-menu">
                                        <a href="#" class="dropdown-item">
                                            <i class="fas fa-user-cog"></i>
                                            Profile Management
                                        </a>
                                        <a href="#" class="dropdown-item">
                                            <i class="fas fa-store"></i>
                                            Register as Vendor
                                        </a>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </nav>
                </div>

                <!-- mobile menu trigger -->
                <div class="mobile-menu-trigger">
                    <span></span>
                </div>
                <!--/.Mobile Menu Hamburger Ends-->
            </nav>
        </div>
    </header>
    <div class="page-wrapper container container-fluid profile-setting">
        <div class="profile-setting">
            <div class="container-fluid">
                <div class="page-title-wrap">
                    <div class="page-title d-flex justify-content-between">
                        <div class="page-title__left">
                            <div class="d-flex align-items-center user-member__title justify-content-center mr-sm-25">
                                <h4 class="text-capitalize fw-500 breadcrumb-title">
                                    <a href="javascript:history.back()"><i class="las la-arrow-left"></i></a> Profile Management
                                </h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mb-30 mt-15">
                    <div class="row">
                        <div class="col-xl-4 col-lg-4 col-sm-5">
                            <!-- Profile Account -->
                            <div class="card mb-25">
                                <div class="card-header text-center p-0">
                                    <div class="d-flex align-items-center gap-10 p-20">
                                        <img class="ap-img__main rounded-circle wh-60" src="/images/feature-card-image7.png" alt="profile" />
                                        <div>
                                            <h6>Mohammed ziyad</h6>
                                            <p class="m-0"><EMAIL></p>
                                        </div>
                                    </div>
                                    <!-- <hr class="w-80 mx-auto m-0 px-2" /> -->
                                    <!-- <div class="ap-nameAddress pb-3">
                                    <h5 class="ap-nameAddress__title">John Doe</h5>
                                </div> -->
                                </div>
                                <div class="ps-tab p-20 pb-25">
                                    <div class="nav flex-column" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                                        <a class="nav-link active d-flex align-items-center justify-content-between" id="v-pills-home-tab" data-toggle="pill" href="#v-pills-home" role="tab" aria-controls="v-pills-home" aria-selected="true">
                                            <p class="m-0 d-flex align-items-center"><span data-feather="user" class="nav-icon"></span>My Profile</p>
                                            <span class="chevron ml-auto"><i class="ri-arrow-right-s-line"></i></span>
                                        </a>
                                        <a class="nav-link d-flex align-items-center justify-content-between" id="v-pills-messages-tab" data-toggle="pill" href="#v-pills-messages" role="tab" aria-controls="v-pills-messages" aria-selected="false">
                                            <p class="m-0 d-flex align-items-center"><span data-feather="settings" class="nav-icon"></span>Password</p>
                                            <span class="chevron ml-auto"><i class="ri-arrow-right-s-line"></i></span>
                                        </a>
                                        <a class="nav-link d-flex align-items-center justify-content-between" id="v-pills-profile-tab" data-toggle="pill" href="#v-pills-profile" role="tab" aria-controls="v-pills-profile" aria-selected="false">
                                            <p class="m-0 d-flex align-items-center"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="nav-icon" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M16.7274 16.8133L15.2637 15.8556L15.2372 17.4933L16.7274 16.8133ZM21.5175 8.5172L21.9038 7.9248L21.3099 7.53742L20.9235 8.12983L21.5175 8.5172H21.5175ZM3.51789 16.715V17.8359L3.75241 17.6591C3.84199 17.5888 3.97094 17.5844 4.06614 17.6563L4.30446 17.8359V16.715H3.51789ZM4.81971 16.715H6.55657V22.0059H1.26574V16.715H3.00264V18.3522H3.00307C3.00288 18.5583 3.24396 18.6867 3.41524 18.5575L3.91118 18.1837L4.39591 18.5491C4.57844 18.6948 4.81975 18.5599 4.81975 18.3522V16.7151L4.81971 16.715ZM11.7938 7.61162C11.3172 7.13505 10.4986 7.47409 10.4986 8.14811C10.4986 8.82212 11.3172 9.16117 11.7938 8.68459C12.09 8.38834 12.09 7.90787 11.7938 7.61162ZM9.48283 10.9881C10.5346 11.7713 11.98 11.7713 13.0317 10.9881C12.5681 9.83941 11.148 9.42958 10.1448 10.1474C9.85146 10.3574 9.61989 10.6485 9.48283 10.9881ZM13.3601 6.49844C14.4926 7.63089 14.5251 9.45676 13.4334 10.6283C13.1024 9.96025 12.4783 9.47547 11.7445 9.32547C12.5785 8.97991 12.8023 7.89165 12.158 7.24731C11.6605 6.74978 10.854 6.74978 10.3565 7.24731C9.71219 7.89165 9.93597 8.97991 10.77 9.32547C10.0362 9.47547 9.41219 9.9603 9.08116 10.6283C7.98944 9.45672 8.02197 7.63089 9.15443 6.49844C10.3158 5.33706 12.1988 5.33706 13.3602 6.49844H13.3601ZM11.2573 5.1122C13.1842 5.1122 14.7464 6.67445 14.7464 8.60134C14.7464 10.5282 13.1842 12.0905 11.2573 12.0905C9.33039 12.0905 7.76814 10.5282 7.76814 8.60134C7.76814 6.67445 9.33039 5.1122 11.2573 5.1122ZM18.8389 9.99123L20.0321 8.16166L21.8478 9.34576L19.5555 12.8607L17.7398 11.6766L18.816 10.0263C18.8246 10.0153 18.8322 10.0036 18.8389 9.99123H18.8389ZM18.8698 14.8522V20.7911C18.8698 20.9334 18.7545 21.0488 18.6122 21.0488H7.07182V22.2635C7.07182 22.4057 6.95646 22.5211 6.81419 22.5211H1.00811C0.865848 22.5211 0.750488 22.4057 0.750488 22.2635V16.4574C0.750488 16.3151 0.865848 16.1998 1.00811 16.1998H3.64469V5.25883C3.64474 5.19292 3.66986 5.12697 3.72016 5.07667L7.23855 1.55828C7.28533 1.51028 7.35068 1.48047 7.423 1.48047H18.6122C18.7545 1.48047 18.8698 1.59583 18.8698 1.73809V9.00353L19.7419 7.66633C19.8191 7.54741 19.9782 7.51361 20.0972 7.59086L20.4933 7.84923L21.0197 7.04214C21.0969 6.92322 21.256 6.88942 21.3749 6.96667L22.3991 7.63459C22.518 7.71184 22.5518 7.87089 22.4746 7.98986L21.9477 8.79775L22.8551 9.38955C23.2557 9.65078 23.3701 10.19 23.1087 10.5908L21.8016 12.5952C21.7243 12.7141 21.5653 12.7479 21.4463 12.6707C21.3274 12.5934 21.2936 12.4344 21.3708 12.3154L22.678 10.311C22.7839 10.1486 22.7381 9.92631 22.5753 9.82023L22.278 9.62626L18.8698 14.8522ZM18.3856 14.6546L17.1858 16.4943L15.3701 15.3101L17.4592 12.1068L19.2749 13.2909L18.4083 14.6197C18.3998 14.6306 18.3922 14.6423 18.3856 14.6545V14.6546ZM4.52421 5.00125H7.16547V2.35998L4.52421 5.00125ZM7.68072 1.99577H18.3546V9.79366L14.7993 15.2452C14.7702 15.29 14.7569 15.3405 14.758 15.3902L14.7175 17.8853C14.7119 18.0816 14.91 18.2071 15.0807 18.1291L17.3481 17.0944C17.3979 17.0759 17.4424 17.0419 17.4737 16.9941L18.3546 15.6423V20.5335H7.07182V16.4574C7.07182 16.3151 6.95646 16.1997 6.81419 16.1997H4.15994V5.5165H7.42305C7.56532 5.5165 7.68068 5.40114 7.68068 5.25887V1.99577H7.68072ZM7.72868 17.8614H14.0898C14.2321 17.8614 14.3474 17.746 14.3474 17.6038C14.3474 17.4615 14.2321 17.3462 14.0898 17.3462H7.72868C7.58641 17.3462 7.47105 17.4615 7.47105 17.6038C7.47105 17.746 7.58641 17.8614 7.72868 17.8614ZM6.2748 15.1697H6.18311C6.04085 15.1697 5.92549 15.2851 5.92549 15.4273C5.92549 15.5696 6.04085 15.685 6.18311 15.685H6.2748C6.41707 15.685 6.53243 15.5696 6.53243 15.4273C6.53243 15.2851 6.41707 15.1697 6.2748 15.1697ZM7.72868 15.685H14.1691C14.3114 15.685 14.4267 15.5696 14.4267 15.4273C14.4267 15.2851 14.3114 15.1697 14.1691 15.1697H7.72868C7.58641 15.1697 7.47105 15.2851 7.47105 15.4273C7.47105 15.5696 7.58641 15.685 7.72868 15.685ZM6.2748 12.9933H6.18311C6.04085 12.9933 5.92549 13.1087 5.92549 13.2509C5.92549 13.3932 6.04085 13.5085 6.18311 13.5085H6.2748C6.41707 13.5085 6.53243 13.3932 6.53243 13.2509C6.53243 13.1087 6.41707 12.9933 6.2748 12.9933ZM7.72868 13.5085H15.3593C15.5015 13.5085 15.6169 13.3932 15.6169 13.2509C15.6169 13.1087 15.5015 12.9933 15.3593 12.9933H7.72868C7.58641 12.9933 7.47105 13.1087 7.47105 13.2509C7.47105 13.3932 7.58641 13.5085 7.72868 13.5085Z" fill="#696F79" stroke="#696F79" stroke-width="0.4" />
                                                </svg>
                                                Register as vendor
                                            </p>
                                            <span class="chevron ml-auto"><i class="ri-arrow-right-s-line"></i></span>
                                        </a>
                                        <button class="bg-white border-none nav-link d-flex align-items-center justify-content-between">
                                            <p class="m-0 d-flex align-items-center"><svg width="22" height="20" class="nav-icon" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M17 6L21 10M21 10L17 14M21 10H8M14 2.20404C12.7252 1.43827 11.2452 1 9.66667 1C4.8802 1 1 5.02944 1 10C1 14.9706 4.8802 19 9.66667 19C11.2452 19 12.7252 18.5617 14 17.796" stroke="#696F79" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                                </svg>
                                                Logout
                                            </p>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <!-- Profile Account End -->
                        </div>
                        <div class="col-xl-8 col-lg-8 col-sm-7">
                            <div class="mb-50">
                                <div class="tab-content" id="v-pills-tabContent">
                                    <!-- Edit Profile Tab -->
                                    <div class="tab-pane fade show active" id="v-pills-home" role="tabpanel" aria-labelledby="v-pills-home-tab">
                                        <div class="edit-profile">
                                            <div class="card">
                                                <div class="card-header px-sm-25 px-3">
                                                    <div class="edit-profile__title">
                                                        <h6>My Profile</h6>
                                                        <span class="fs-13 color-light fw-400">Set up your personal information</span>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row justify-content-center">
                                                        <div class="col-lg-8 col-sm-10">
                                                            <div class="edit-profile__body mx-lg-20">
                                                                <form id="img_upload_form" enctype="multipart/form-data" method="post">
                                                                    <div class="account-profile border-bottom mb-25 py-25 pb-0 flex-column d-flex align-items-center">
                                                                        <div class="d-flex gap-10 justify-content-between align-items-center align-self-start">
                                                                            <div class="ap-img pro_img_wrapper">
                                                                                <input id="file_upload" type="file" name="profile_img" class="d-none" accept="image/*">
                                                                                <label for="file-upload">
                                                                                    <img class="ap-img__main rounded-circle wh-60" src="/images/feature-card-image7.png" alt="profile" />
                                                                                    <span class="cross" id="remove_pro_pic" onclick="$('#file_upload').click();">
                                                                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                                            <path d="M7.99998 13.3341H14M2 13.3341H3.11636C3.44248 13.3341 3.60554 13.3341 3.75899 13.2973C3.89504 13.2646 4.0251 13.2108 4.1444 13.1377C4.27895 13.0552 4.39425 12.9399 4.62486 12.7093L13 4.33414C13.5523 3.78185 13.5523 2.88642 13 2.33413C12.4477 1.78185 11.5523 1.78185 11 2.33414L2.62484 10.7093C2.39424 10.9399 2.27894 11.0552 2.19648 11.1898C2.12338 11.3091 2.0695 11.4391 2.03684 11.5752C2 11.7286 2 11.8917 2 12.2178V13.3341Z" stroke="#5F63F2" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                                                                        </svg>

                                                                                    </span>
                                                                                </label>
                                                                                <!-- <span class="remove-img text-white btn-danger rounded-circle" data-toggle="modal" data-target="#confirmDeletePhoto">
                                                                            <span data-feather="x"></span>
                                                                        </span> -->
                                                                            </div>
                                                                            <div>
                                                                                <h6>Mohammed ziyad</h6>
                                                                                <p class="m-0"><EMAIL></p>
                                                                            </div>
                                                                        </div>
                                                                        <input type="hidden" id="profile_img_action" value="/update-profile-image">
                                                                        <input type="hidden" name="old_img" id="old_img" value="">
                                                                    </div>
                                                                </form>
                                                                <form method="post" id="user_profile_form" action="/update-profile" enctype="multipart/form-data">
                                                                    <div class="form-group mb-20">
                                                                        <label for="name">Name <small class="required">*</small></label>
                                                                        <input type="text" class="form-control" id="name" name="name" placeholder="Enter your name" value="John Doe" required>
                                                                    </div>
                                                                    <div class="form-group mb-20">
                                                                        <label for="email">Email <small class="required">*</small></label>
                                                                        <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email" value="John Doe" required>
                                                                    </div>
                                                                    <div class="form-group mb-20">
                                                                        <label for="company">Company Name/Profession <small class="required">*</small></label>
                                                                        <input type="text" class="form-control" id="company" name="company" placeholder="Enter your company" value="Company Name/Profession" required>
                                                                    </div>
                                                                    <div class="form-group mb-20">
                                                                        <label for="TypeOption">Type <small class="required">*</small></label>
                                                                        <select class="js-example-basic-single js-states form-control" id="TypeOption" name="Type_id">
                                                                            <option value="0" selected>Company</option>
                                                                            <option value="1">Saudi Arabia</option>
                                                                            <option value="2">United Arab Emirates</option>
                                                                        </select>
                                                                    </div>
                                                                    <div class="form-group mb-20 z-20 position-relative ">
                                                                        <label for="phone">Phone Number</label>
                                                                        <div class="mb-3">
                                                                            <input type="tel" id="phone" class="form-control placeholder-gray phone-input" placeholder="54xxxxxxx">
                                                                            <small class="text-danger d-none" id="error-msg">Invalid phone number</small>
                                                                        </div>
                                                                    </div>

                                                                    <div class="button-group d-flex float-right flex-wrap pt-30 mb-15">
                                                                        <button class="btn btn-primary btn-default btn-squared mr-15 text-capitalize" type="submit">Save Changes</button>
                                                                    </div>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Account Settings Tab -->
                                    <div class="tab-pane fade" id="v-pills-profile" role="tabpanel" aria-labelledby="v-pills-profile-tab">
                                        <div class="edit-profile">
                                            <div class="card">
                                                <div class="card-header px-sm-25 px-3">
                                                    <div class="edit-profile__title">
                                                        <h6>Account Settings</h6>
                                                        <span class="fs-13 color-light fw-400">Update your username and manage your account</span>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class=" w-100 p-0">

                                                        <div class="edit-profile__body mx-lg-20">
                                                            <form method="post" id="user_profile_form2" action="/update-account" enctype="multipart/form-data">
                                                                <form id="img_upload_form" enctype="multipart/form-data" method="post">
                                                                    <div class="account-profile border-bottom mb-25 py-25 pb-0 flex-column d-flex align-items-center">
                                                                        <div class="d-flex gap-10 justify-content-between align-items-center align-self-start">
                                                                            <div class="ap-img pro_img_wrapper">
                                                                                <input id="file_upload" type="file" name="profile_img" class="d-none" accept="image/*">
                                                                                <label for="file-upload">
                                                                                    <img class="ap-img__main rounded-circle wh-60" src="/images/feature-card-image7.png" alt="profile" />
                                                                                    <span class="cross" id="remove_pro_pic" onclick="$('#file_upload').click();">
                                                                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                                            <path d="M7.99998 13.3341H14M2 13.3341H3.11636C3.44248 13.3341 3.60554 13.3341 3.75899 13.2973C3.89504 13.2646 4.0251 13.2108 4.1444 13.1377C4.27895 13.0552 4.39425 12.9399 4.62486 12.7093L13 4.33414C13.5523 3.78185 13.5523 2.88642 13 2.33413C12.4477 1.78185 11.5523 1.78185 11 2.33414L2.62484 10.7093C2.39424 10.9399 2.27894 11.0552 2.19648 11.1898C2.12338 11.3091 2.0695 11.4391 2.03684 11.5752C2 11.7286 2 11.8917 2 12.2178V13.3341Z" stroke="#5F63F2" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                                                                        </svg>

                                                                                    </span>
                                                                                </label>
                                                                                <!-- <span class="remove-img text-white btn-danger rounded-circle" data-toggle="modal" data-target="#confirmDeletePhoto">
                                                                            <span data-feather="x"></span>
                                                                        </span> -->
                                                                            </div>
                                                                            <div>
                                                                                <h6>Mohammed ziyad</h6>
                                                                                <p class="m-0"><EMAIL></p>
                                                                            </div>
                                                                        </div>
                                                                        <input type="hidden" id="profile_img_action" value="/update-profile-image">
                                                                        <input type="hidden" name="old_img" id="old_img" value="">
                                                                    </div>
                                                                </form>
                                                                <div class="form-grid">
                                                                    <div class="form-group">
                                                                        <label>Business Name</label>
                                                                        <input type="text" class="form-control" value="OSOOL">
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <label>Business Address</label>
                                                                        <input type="text" class="form-control" value="Riyadh, Saudi Arabia">
                                                                    </div>
                                                                    <div class="form-group mb-20 z-20 position-relative">
                                                                        <label for="phonee">Phone Number</label>
                                                                        <div class="mb-3">
                                                                            <input type="tel" id="phonee" class="form-control placeholder-gray phone-input" placeholder="54xxxxxxx">
                                                                            <small class="text-danger d-none" id="error-msg">Invalid phone number</small>
                                                                        </div>
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <label>Number of Employees</label>
                                                                        <input type="number" class="form-control" value="183">
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <label>Business Type</label>
                                                                        <input type="text" class="form-control" value="business type">
                                                                    </div>
                                                                </div>

                                                                <div class="mb-40">
                                                                    <div class="border-bottom pb-15 mb-20">
                                                                        <h5 class="mt-4">Services</h5>
                                                                        <p>Set Up Your Services</p>
                                                                    </div>

                                                                    <div class="services-container" id="services-container">
                                                                        <div class="service-item row w-100">
                                                                            <div class="form-group col-12 col-md-6 mb-20">
                                                                                <label for="service_1">Service Category <small class="required">*</small></label>
                                                                                <select class="js-example-basic-single js-states form-control" id="service_1" name="service_1">
                                                                                    <option value="0">Select Service</option>
                                                                                    <option value="1" selected>service_1</option>
                                                                                    <option value="2">service_2</option>
                                                                                </select>
                                                                            </div>
                                                                            <div class="form-group col-12 col-md-6">
                                                                                <label>Service Description</label>
                                                                                <input type="text" class="form-control" value="service Description">
                                                                            </div>
                                                                        </div>

                                                                    </div>

                                                                    <button type="button" class="btn btn-primary mt-3 add-service">+ Add Service</button>

                                                                </div>
                                                                <div class="mb-40">
                                                                    <div class="border-bottom pb-15 mb-20">
                                                                        <h5 class="mt-4">Address</h5>
                                                                        <p>Set Up Your Address</p>
                                                                    </div>

                                                                    <div class="row">
                                                                        <div class="col-md-6">
                                                                            <label>Region</label>
                                                                            <select class="form-control">
                                                                                <option>Region</option>
                                                                            </select>
                                                                        </div>
                                                                        <div class="col-md-6">
                                                                            <label>City</label>
                                                                            <select class="form-control">
                                                                                <option>City</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="mb-40">
                                                                    <div class="border-bottom pb-15 mb-20">
                                                                        <h5 class="mt-4">Document Upload</h5>
                                                                        <p>Set Up Your Document</p>
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <label>Certifications and Qualifications</label>
                                                                        <div id="certifications-container">
                                                                            <div class="upload-container d-flex align-items-center gap-10 mt-10 position-relative">
                                                                                <div class="file-item">
                                                                                    <input type="file" id="certifications-container" class="file-input" accept=".jpg,.png,.pdf">
                                                                                    <button type="button" class="remove-file"></button>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <button type="button" class="btn btn-primary mt-2 add-file mt-3 mb-2" data-target="#certifications-container">+ Upload</button>
                                                                        <p class="text-muted">Allowed file types: jpg, png, pdf</p>
                                                                    </div>
                                                                </div>
                                                                <div class="mb-40">
                                                                    <div class="border-bottom pb-15 mb-20">
                                                                        <h5 class="mt-4">Client References</h5>
                                                                        <p>Set Up Your Client References</p>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="col-md-6">
                                                                            <label>Case Studies</label>
                                                                            <div id="case-studies-container">
                                                                                <div class="upload-container d-flex align-items-center gap-10 mt-10 position-relative">
                                                                                    <div class="file-item">
                                                                                        <input type="file" id="case-studies-container" class="file-input" accept=".jpg,.png,.pdf">
                                                                                        <button type="button" class="remove-file"></button>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <button type="button" class="btn btn-primary mt-2 add-file mt-3 mb-2" data-target="#case-studies-container">+ Upload</button>
                                                                            <p class="text-muted">Allowed file types: jpg, png, pdf</p>
                                                                        </div>
                                                                        <div class="col-md-6">
                                                                            <label>Success Stories</label>
                                                                            <div id="success-stories-container">
                                                                                <div class="upload-container d-flex align-items-center gap-10 mt-10 position-relative">
                                                                                    <div class="file-item">
                                                                                        <input type="file" id="success-stories-container" class="file-input" accept=".jpg,.png,.pdf">
                                                                                        <button type="button" class="remove-file"></button>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <button type="button" class="btn btn-primary mt-2 add-file mt-3 mb-2" data-target="#success-stories-container">+ Upload</button>
                                                                            <p class="text-muted">Allowed file types: jpg, png, pdf</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="mt-4 d-flex align-items-center justify-content-end gap-10">
                                                                    <button type="button" class="btn secondary-btn">Save & Continue Later</button>
                                                                    <button type="submit" class="btn success-btn" type="submit">Submit Application</button>
                                                                </div>
                                                            </form>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Change Password Tab -->
                                    <div class="tab-pane fade" id="v-pills-messages" role="tabpanel" aria-labelledby="v-pills-messages-tab">
                                        <div class="edit-profile">
                                            <div class="card">
                                                <div class="card-header px-sm-25 px-3">
                                                    <div class="edit-profile__title">
                                                        <h6>My Password</h6>
                                                        <span class="fs-13 color-light fw-400">Change Or Reset Your Account Password</span>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row justify-content-center">
                                                        <div class="col-lg-8">
                                                            <div class="edit-profile__body mx-lg-20">
                                                                <form method="post" id="user_profile_form3" action="/update-password" enctype="multipart/form-data">
                                                                    <div class="form-group mb-20">
                                                                        <label for="old_pw">Old Password</label>
                                                                        <div class="position-relative">
                                                                            <input type="password" class="form-control pr-50" name="old_password" id="old_password">
                                                                            <span class="fa fa-fw fa-eye-slash text-light fs-16 field-icon toggle-password-profile2"></span>
                                                                        </div>
                                                                    </div>
                                                                    <div class="form-group mb-1">
                                                                        <label for="password-field">New Password</label>
                                                                        <div class="position-relative">
                                                                            <input type="password" class="form-control pr-50" name="password" id="password">
                                                                            <span class="fa fa-fw fa-eye-slash text-light fs-16 field-icon toggle-password-profile"></span>
                                                                        </div>
                                                                        <small id="passwordHelpInline" class="text-light fs-13">Minimum 6 characters</small>
                                                                    </div>
                                                                    <div class="form-group mb-1">
                                                                        <label for="co-password">Confirm Password</label>
                                                                        <div class="position-relative">
                                                                            <input type="password" class="form-control pr-50" name="co-password" id="co-password">
                                                                            <span class="fa fa-fw fa-eye-slash text-light fs-16 field-icon toggle-password-profile"></span>
                                                                        </div>
                                                                        <small id="passwordHelpInline" class="text-light fs-13">Minimum 6 characters</small>
                                                                    </div>
                                                                    <div id="passwordError" class="text-danger mt-2" style="display: none;"></div>
                                                                    <div class="button-group d-flex flex-wrap pt-45 mb-35 float-right">
                                                                        <button class="btn btn-primary btn-default btn-squared mr-15 text-capitalize" type="submit">Update Password</button>
                                                                    </div>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirm Delete Photo Modal -->
    <div class="modal new-member profile-setting" id="confirmDeletePhoto" role="dialog" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content radius-xl bouncein-new">
                <div class="modal-body">
                    <div class="create-asset-modal">
                        <h2 class="mb-3 fs-20"><i class="fa fa-exclamation-circle mr-1 text-warning" aria-hidden="true"></i> Are you sure you want to remove this photo?</h2>
                    </div>
                    <div class="button-group d-flex justify-content-end pt-25">
                        <button type="button" class="btn btn-light btn-squared text-capitalize" data-dismiss="modal" aria-label="Close">Cancel</button>
                        <button type="button" class="btn btn-danger btn-default btn-squared text-capitalize confirm_remove_photo" data-dismiss="modal" aria-label="Close">Remove</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CONFIRM DELETE Photo MODAL ENDS -->


    @include('layouts.partials._scripts')

    <script src="{{ asset('home/plugins/menu/menu.js') }}"></script>
    <!-- toggle header dropdown , general layout  -->

    <script>
        $(document).ready(function() {
            // Toggle dropdown menu
            $('.user-dropdown-trigger').on('click', function(e) {
                e.stopPropagation();
                $('.user-dropdown-menu').toggleClass('active');
            });

            // Close dropdown when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.user-dropdown-wrapper').length) {
                    $('.user-dropdown-menu').removeClass('active');
                }
            });

            // Handle mobile menu integration
            $('.mobile-menu-trigger').on('click', function() {
                if (window.innerWidth <= 768) {
                    $('.user-dropdown-menu').removeClass('active');
                }
            });
        });
    </script>


    <!-- add service script , just for this page  -->
    <script>
        $(document).ready(function() {
            $(".add-service").click(function() {
                $("#services-container").append(`
                    <div class="service-item row w-100">
                 <div class="form-group col-12 col-md-6 mb-20">
                     <label for="service_1">Service Category <small class="required">*</small></label>
                     <select class="js-example-basic-single js-states form-control" id="service_1" name="service_1">
                         <option value="0">Select Service</option>
                         <option value="1" selected>service_1</option>
                         <option value="2">service_2</option>
                     </select>
                 </div>
                 <div class="form-group col-11 col-md-5">
                     <label>Service Description</label>
                     <input type="text" class="form-control" value="service Description">
                 </div>
                  <button type="button" class="remove-service bg-transparent border-0 cursor-pointer p-0 col-1"><svg width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.875 1.875H10.625H9.6875V0.625C9.6875 0.279846 9.40765 0 9.0625 0H3.4375C3.09235 0 2.8125 0.279846 2.8125 0.625V1.875H1.875H0.625C0.279846 1.875 0 2.15485 0 2.5C0 2.84515 0.279846 3.125 0.625 3.125H1.25V11.25C1.25 12.6285 2.37152 13.75 3.75 13.75H8.75C10.1285 13.75 11.25 12.6285 11.25 11.25V3.125H11.875C12.2202 3.125 12.5 2.84515 12.5 2.5C12.5 2.15485 12.2202 1.875 11.875 1.875ZM4.0625 1.25H8.4375V1.875H4.0625V1.25ZM10 11.25C10 11.9394 9.43939 12.5 8.75 12.5H3.75C3.06061 12.5 2.5 11.9394 2.5 11.25V3.125H3.4375H9.0625H10V11.25ZM8.4375 4.375V10.9375C8.4375 11.2827 8.15765 11.5625 7.8125 11.5625C7.46735 11.5625 7.1875 11.2827 7.1875 10.9375V4.375C7.1875 4.02985 7.46735 3.75 7.8125 3.75C8.15765 3.75 8.4375 4.02985 8.4375 4.375ZM5.3125 4.375V10.9375C5.3125 11.2827 5.03265 11.5625 4.6875 11.5625C4.34235 11.5625 4.0625 11.2827 4.0625 10.9375V4.375C4.0625 4.02985 4.34235 3.75 4.6875 3.75C5.03265 3.75 5.3125 4.02985 5.3125 4.375Z" fill="#FF2828"/>
</svg>
</button>
             </div>
            `);
            });

            $(document).on("click", ".remove-service", function() {
                $(this).closest(".service-item").remove();
            });
        });
    </script>
    <!-- add file script , just for this page  -->
    <script>
        $(document).ready(function() {
            $(".add-file").click(function() {
                let target = $(this).data("target");
                let inputId = "file-input-" + Math.random().toString(36).substring(7);

                let fileInputHtml = `
                    <div class="upload-container d-flex align-items-center gap-10 mt-10 position-relative">
                        <div class="file-item">
                            <input type="file" id="${inputId}" class="file-input" accept=".jpg,.png,.pdf">
                            <span class="file-name">No file chosen</span>
                            <button type="button" class="remove-file"><svg width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.875 1.875H10.625H9.6875V0.625C9.6875 0.279846 9.40765 0 9.0625 0H3.4375C3.09235 0 2.8125 0.279846 2.8125 0.625V1.875H1.875H0.625C0.279846 1.875 0 2.15485 0 2.5C0 2.84515 0.279846 3.125 0.625 3.125H1.25V11.25C1.25 12.6285 2.37152 13.75 3.75 13.75H8.75C10.1285 13.75 11.25 12.6285 11.25 11.25V3.125H11.875C12.2202 3.125 12.5 2.84515 12.5 2.5C12.5 2.15485 12.2202 1.875 11.875 1.875ZM4.0625 1.25H8.4375V1.875H4.0625V1.25ZM10 11.25C10 11.9394 9.43939 12.5 8.75 12.5H3.75C3.06061 12.5 2.5 11.9394 2.5 11.25V3.125H3.4375H9.0625H10V11.25ZM8.4375 4.375V10.9375C8.4375 11.2827 8.15765 11.5625 7.8125 11.5625C7.46735 11.5625 7.1875 11.2827 7.1875 10.9375V4.375C7.1875 4.02985 7.46735 3.75 7.8125 3.75C8.15765 3.75 8.4375 4.02985 8.4375 4.375ZM5.3125 4.375V10.9375C5.3125 11.2827 5.03265 11.5625 4.6875 11.5625C4.34235 11.5625 4.0625 11.2827 4.0625 10.9375V4.375C4.0625 4.02985 4.34235 3.75 4.6875 3.75C5.03265 3.75 5.3125 4.02985 5.3125 4.375Z" fill="#FF2828"/>
</svg>
</button>
</div>
                    </div>
                `;

                $(target).append(fileInputHtml);

                let fileInput = $("#" + inputId);
                let fileNameSpan = fileInput.siblings(".file-name");

                fileNameSpan.click(function() {
                    fileInput.click();
                });

                fileInput.change(function() {
                    if (this.files.length > 0) {
                        fileNameSpan.text(this.files[0].name);
                    } else {
                        fileNameSpan.text("No file chosen");
                    }
                });
            });

            $(document).on("click", ".remove-file", function() {
                $(this).closest(".upload-container").remove();
            });
        });

        document.addEventListener("DOMContentLoaded", function() {
            document.querySelectorAll(".file-item").forEach((fileItem) => {
                let input = fileItem.querySelector(".file-input");
                let fileNameSpan = document.createElement("span");
                fileNameSpan.classList.add("file-name");
                fileNameSpan.textContent = "No file chosen";
                fileItem.insertBefore(fileNameSpan, input);

                fileItem.addEventListener("click", () => input.click());

                input.addEventListener("change", function() {
                    if (this.files.length > 0) {
                        fileNameSpan.textContent = this.files[0].name;
                    }
                });

                fileItem.querySelector(".remove-file").addEventListener("click", function(event) {
                    event.stopPropagation();
                    input.value = "";
                    fileNameSpan.textContent = "No file chosen";
                });
            });
        });
    </script>

</body>


</html>
