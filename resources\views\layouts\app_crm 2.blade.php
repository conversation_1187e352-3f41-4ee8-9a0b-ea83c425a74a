<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ Session::get('locale') == 'ar' ? 'rtl' : 'ltr' }}">
<style>
    [type="search"]::-webkit-search-cancel-button,
    [type="search"]::-webkit-search-decoration {
        -webkit-appearance: none;
    }

    input[type="search" i] {
        -webkit-appearance: none !important;
    }
</style>


<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="facebook-domain-verification" content="ahabnt58zdbmzksb41doq5nz2h28er" />

    <title @if (App::getLocale() == 'en') lang = "en" @else lang = "ar" @endif>@lang('import.osool')</title>

    <meta name="description" content="@yield('page_description', $pageDescription ?? '')" />

    @livewireStyles

    <link rel="stylesheet" href="{{ asset('new_files/new_font.css') }}?v={{ filemtime(public_path('new_files/new_font.css')) }}">
    @include('layouts.partials._styles_crm')

    @yield('styles')

    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('favicon/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('favicon/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('favicon/favicon-16x16.png') }}">
    <link rel="shortcut icon" type="image/png" sizes="32x32" href="{{ asset('favicon/favicon-32x32.png') }}">
    <link rel="shortcut icon" type="image/png" sizes="16x16" href="{{ asset('favicon/favicon-16x16.png') }}">
    <link rel="manifest" href="{{ asset('favicon/site.webmanifest') }}">
    <link rel="mask-icon" href="{{ asset('favicon/safari-pinned-tab.svg') }}" color="#5bbad5">

    <script src="{{ asset('new_files/axios.min.js') }}?v={{ filemtime(public_path('new_files/axios.min.js')) }}"></script>

    @if (App::getLocale() == 'ar')
        <style>
            .toggle-icon {
                transform: rotate(180deg);
            }
        </style>
    @endif
</head>
<body class="layout-light side-menu @auth() overlayScroll @endauth">
<input type="hidden" id="app_url" value="<?= url('') ?>">
@auth()
<div class="mobile-search"></div>
<div class="mobile-author-actions"></div>
@include('layouts.partials._header')
@endauth
<!-- for reset password no sidebar -->
<main class="main-content">
@auth()
    @if (Request()->route()->getPrefix() != '/maintenance')
        @if (Route::is('workspace.home') || Route::is('workspace.admin.create'))
            <livewire:menu.aside-workspace-list />
        @else
            @if (in_array(Route::current()->getName(), [
                    'password.request',
                    'password.reset',
                    'update_reset_password',
                    'reset_password',
                ]))
            @else
                <livewire:menu.aside-nav-list />
            @endif
        @endif
    @endif
@endauth



@auth
    @php
        $url = url()->current();
        $segments = explode('/', $url);
    @endphp

    @if (auth()->user()->allow_akaunting &&
            (in_array('inventory', $segments) ||
                in_array('sales', $segments) ||
                in_array('purchases', $segments) ||
                in_array('performance-indicator', $segments)))
        @include('layouts.partials._akaunting_debug')
    @endif
@endauth

@section('content')
@show
@auth()
    @if (in_array(Route::current()->getName(), ['password.reset', 'update_reset_password', 'reset_password']))
    @elseif(Request()->route()->getPrefix() == '/maintenance')
    @else
        @include('layouts.partials._footer')
    @endif
@endauth

</main>
@auth()
    <div id="overlayer">
        <span class="loader-overlay">
            <span class="atbd-spin-dots spin-lg">
                <span class="spin-dot badge-dot dot-primary"></span>
                <span class="spin-dot badge-dot dot-primary"></span>
                <span class="spin-dot badge-dot dot-primary"></span>
                <span class="spin-dot badge-dot dot-primary"></span>
            </span>
        </span>
    </div>
@endauth
<div class="overlay-dark-sidebar"></div>
<div class="customizer-overlay"></div>
{{-- Inject:js, Global Theme JS Bundle (used by all pages) --}}
@yield('mapScript')
@yield('modal')
@include('layouts.partials._scripts_crm')
@yield('scripts')
{{-- Endinject --}}
<script type="text/javascript" src="{{ asset('js/admin/supports/create.js') }}"></script>
<script>
    window.onload = function() {
        $('.cc-select2').select2();
        $(".calendar").datepicker({
            dateFormat: 'yy-mm-dd',
        });
    }

    setTimeout(function() {
        $('#Message').fadeOut('fast');

    }, 3000);

    $(document).ready(function() {
        $("#search").keypress(function(e) {
            if (e.keyCode === 13) {
                e.preventDefault();
            }
        });
    });

    $('.akaunting-debug-exit').on('click', function() {
        $('.akaunting-debug-bar').fadeOut();
    });
   
    window.addEventListener('swal:modal', event => {
        swal({
            icon: event.detail.type,
            title: '',
            text: event.detail.text,
            showConfirmButton: true,
            timer: 3000
        });
    });
</script>
<style>
    .phpdebugbar {
        display: none;
    }
</style>
@stack('scripts')
{{-- @livewireScripts --}}
</body>

</html>
