@push('scripts')
    <script>
        let workspaceSlug = "{{ auth()->user()->workspace }}";
        let workDoUrls = ['/crm', '/sales', '/CRMProjects', '/omnichat', '/finance', '/documents']
        let isRequestInProgress = false;
        let workspaceCheckInterval;

        if (workspaceSlug == 'none') {
            $('a').each(function() {
                let href = $(this).attr('href');
                if (href && workDoUrls.some(url => href.includes(url))) {
                    $(this).css('pointer-events', 'none')
                    $(this).addClass('skeleton-text')
                }
            });

            if (workDoUrls.some(url => window.location.pathname.includes(url))) {
                // loadWaitModal();
            }

            workspaceCheckInterval = setInterval(() => {
                if (!isRequestInProgress) {
                    checkCRMSession();
                }
            }, 3000);
        }

        function checkCRMSession() {
            isRequestInProgress = true;

            $.ajax({
                type: "get",
                url: "/check-crm-workspace",
                success: function(response) {
                    if (response !== 'none') {
                        $(".skeleton-text").css('pointer-events', '').removeClass('skeleton-text');
                        clearInterval(workspaceCheckInterval);

                        if (workDoUrls.some(url => window.location.pathname.includes(url))) {
                            window.location.reload();
                        }
                    }
                },
                complete: function() {
                    isRequestInProgress = false; // Reset the flag whether the request succeeds or fails
                }
            });
        }

        function loadWaitModal() {
            let message = "{{ __('Please wait for a while...') }}";

            $('body').prepend(`<div class="modal fade" id="pleaseWaitModal" tabindex="-1" aria-hidden="true" data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog modal-dialog-centered modal-sm">
                    <div class="modal-content text-center">
                        <div class="modal-body">
                            <p class="mb-0 fs-5 skeleton-text d-block">${message}</p>
                        </div>
                    </div>
                </div>
            </div>`);
            $('#pleaseWaitModal').modal('show');
        }

        window.livewire.onError(statusCode => {
            if (statusCode === 401) {
                // loadWaitModal();
                workspaceCheckInterval = setInterval(() => {
                    if (!isRequestInProgress) {
                        checkCRMSession();
                    }
                }, 3000);
                return false
            }
        })
    </script>
@endpush
