<div>
<div class="contents crm">
    <div class="container-fluid">
        <div class="col-lg-12">
            <div class="row justify-content-sm-between align-items-center justify-content-center flex-sm-row flex-column">
                <div class="page-title-wrap">
                    <div class="page-title d-flex justify-content-between">
                        <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                            <div class="user-member__title mr-sm-25 ml-0">
                                <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                    {{ __('accounting.proposals.edit_proposal') }}
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div>
                        <ul class="atbd-breadcrumb nav">
                            <li class="atbd-breadcrumb__item">
                                <a>{{ __('accounting.navigation.dashboard') }}</a>
                                <span class="breadcrumb__seperator">
                                    <span class="la la-angle-right"></span>
                                </span>
                            </li>
                            <li class="atbd-breadcrumb__item">
                                <a>{{ __('accounting.proposals.proposals') }}</a>
                                <span class="breadcrumb__seperator">
                                    <span class="la la-angle-right"></span>
                                </span>
                            </li>
                            <li class="atbd-breadcrumb__item">
                                <a>{{ __('accounting.proposals.edit_proposal') }}</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        @if($loading)
            <div class="d-flex justify-content-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">{{ __('accounting.common.loading') }}</span>
                </div>
            </div>
        @elseif($error)
            <div class="alert alert-danger">
                {{ $error }}
                <button type="button" class="btn btn-sm btn-outline-danger ml-2" wire:click="fetchProposalData">
                    {{ __('accounting.common.retry') }}
                </button>
            </div>
        @else
            <form wire:submit.prevent="save">
                <div class="card mb-3 radius-xl">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 col-sm-6">
                                <div class="form-group">
                                    <label>{{ __('accounting.proposals.proposal_type') }} <small class="required">*</small></label>
                                    <select class="form-control" wire:model="proposal_type">
                                        <option value="">{{ __('accounting.proposals.select_customer') }}</option>
                                        @foreach($billing_types as $key => $type)
                                            <option value="{{ $key }}">{{ $type }}</option>
                                        @endforeach
                                    </select>
                                    @error('proposal_type')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="form-group">
                                    <label>{{ __('accounting.proposals.customer') }} <small class="required">*</small></label>
                                    <select class="form-control" wire:model="customer_id">
                                        <option value="">{{ __('accounting.proposals.select_customer') }}</option>
                                        @foreach($customers as $customer)
                                            <option value="{{ $customer['id'] }}">{{ $customer['name'] }}</option>
                                        @endforeach
                                    </select>
                                    @error('customer_id')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="form-group">
                                    <label>{{ __('accounting.proposals.issue_date') }} <small class="required">*</small></label>
                                    <div class="position-relative">
                                        <input type="date" class="form-control" wire:model="issue_date">
                                        <i class="iconsax field-icon" icon-name="calendar-1"></i>
                                    </div>
                                    @error('issue_date')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="form-group">
                                    <label>{{ __('accounting.proposals.proposal_number') }}</label>
                                    <div class="position-relative">
                                        <input type="text" class="form-control" value="{{ $proposal_number }}" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="form-group">
                                    <label>{{ __('accounting.proposals.category') }} <small class="required">*</small></label>
                                    <select class="form-control" wire:model="category_id">
                                        <option value="">{{ __('accounting.proposals.select_category') }}</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category['id'] }}">{{ $category['name'] }}</option>
                                        @endforeach
                                    </select>
                                    @error('category_id')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="form-group">
                                    <label>{{ __('accounting.proposals.template') }} <small class="required">*</small></label>
                                    <select class="form-control" wire:model="proposal_template">
                                        <option value="">{{ __('accounting.proposals.select_template') }}</option>
                                        @foreach($templates as $key => $template)
                                            <option value="{{ $key }}">{{ $template }}</option>
                                        @endforeach
                                    </select>
                                    @error('proposal_template')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-3 radius-xl">
                    <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                        <h6 class="text-capitalize fw-500 mb-3 mb-sm-0">{{ __('accounting.proposals.items') }}</h6>
                        <div class="">
                            <button type="button" class="btn btn-xs btn-primary" wire:click="addItem">
                                <i class="las la-plus fs-16"></i>{{ __('accounting.proposals.add_item') }}
                            </button>
                        </div>
                    </div>
                    <div class="card-body px-0 pt-0">
                        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                            <div class="table-responsive">
                                <table class="table mb-0 radius-0 table-border">
                                    <thead>
                                        <tr>
                                            <th>{{ __('accounting.proposals.item_type') }}</th>
                                            <th>{{ __('accounting.proposals.item') }}</th>
                                            <th>{{ __('accounting.proposals.quantity') }}</th>
                                            <th>{{ __('accounting.proposals.price') }}</th>
                                            <th>{{ __('accounting.proposals.discount') }}</th>
                                            <th>{{ __('accounting.proposals.tax') }}</th>
                                            <th style="line-height: 13px;">
                                                <span>{{ __('accounting.proposals.amount') }}</span>
                                                <div><small>{{ __('accounting.proposals.after_discount_tax') }}</small></div>
                                            </th>
                                            <th>{{ __('accounting.action') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($items as $index => $item)
                                            <tr>
                                                <td>
                                                    <div class="min-w-130">
                                                        <select class="form-control" wire:model="items.{{ $index }}.product_type">
                                                            <option value="">--</option>
                                                            @foreach($item_types as $key => $type)
                                                                <option value="{{ $key }}">{{ $type }}</option>
                                                            @endforeach
                                                        </select>
                                                        @error('items.' . $index . '.product_type')
                                                            <span class="text-danger">{{ $message }}</span>
                                                        @enderror
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="min-w-130">
                                                        <select class="form-control" wire:model="items.{{ $index }}.item">
                                                            <option value="">--</option>
                                                            @foreach($items_list as $itemOption)
                                                                <option value="{{ $itemOption['id'] }}">{{ $itemOption['name'] }}</option>
                                                            @endforeach
                                                        </select>
                                                        @error('items.' . $index . '.item')
                                                            <span class="text-danger">{{ $message }}</span>
                                                        @enderror
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="input-group">
                                                        <input type="number" class="form-control" min="1" wire:model.lazy="items.{{ $index }}.quantity">
                                                        @error('items.' . $index . '.quantity')
                                                            <span class="text-danger">{{ $message }}</span>
                                                        @enderror
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="input-group">
                                                        <input type="number" class="form-control" min="0" step="0.01" wire:model.lazy="items.{{ $index }}.price">
                                                        <div class="input-group-append">
                                                            <span class="input-group-text">{{ App::getLocale() == 'ar' ? 'ريال' : 'SAR' }}</span>
                                                        </div>
                                                        @error('items.' . $index . '.price')
                                                            <span class="text-danger">{{ $message }}</span>
                                                        @enderror
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="input-group">
                                                        <input type="number" class="form-control" min="0" step="0.01" wire:model.lazy="items.{{ $index }}.discount">
                                                        <div class="input-group-append">
                                                            <span class="input-group-text">{{ App::getLocale() == 'ar' ? 'ريال' : 'SAR' }}</span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge badge-primary rounded-pill userDatatable-content-status active text-white">
                                                        {{ $item['tax'] ?? 0 }}%
                                                    </span>
                                                </td>
                                                <td>
                                                    {{ number_format($this->calculateItemTotal($index), 2) }} {{ App::getLocale() == 'ar' ? 'ريال' : 'SAR' }}
                                                </td>
                                                <td>
                                                    @if(count($items) > 1)
                                                        <button type="button" class="btn btn-sm btn-danger" wire:click="removeItem({{ $index }})">
                                                            <i class="las la-trash"></i>
                                                        </button>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="8">
                                                    <div class="form-group mb-0">
                                                        <textarea class="form-control" placeholder="{{ __('accounting.proposals.description') }}" wire:model="items.{{ $index }}.description"></textarea>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                        <tr>
                                            <td colspan="8">
                                                <div class="d-flex justify-content-end pb-2 border-bottom">
                                                    <div class="d-flex">
                                                        <table class="table table-border mb-0 table-price">
                                                            <tbody>
                                                                <tr class="border-bottom">
                                                                    <th>{{ __('accounting.proposals.subtotal') }}</th>
                                                                    <td>{{ number_format($subtotal, 2) }} {{ App::getLocale() == 'ar' ? 'ريال' : 'SAR' }}</td>
                                                                </tr>
                                                                <tr class="border-bottom">
                                                                    <th>{{ __('accounting.proposals.discount') }}</th>
                                                                    <td>{{ number_format($discount_total, 2) }} {{ App::getLocale() == 'ar' ? 'ريال' : 'SAR' }}</td>
                                                                </tr>
                                                                <tr class="border-bottom">
                                                                    <th>{{ __('accounting.proposals.tax') }}</th>
                                                                    <td>{{ number_format($tax_total, 2) }} {{ App::getLocale() == 'ar' ? 'ريال' : 'SAR' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <th>{{ __('accounting.proposals.total') }}</th>
                                                                    <td class="fw-bold">{{ number_format($total, 2) }} {{ App::getLocale() == 'ar' ? 'ريال' : 'SAR' }}</td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex justify-content-end gap-2 mt-3">
                    <button type="button" class="btn btn-secondary radius-xl" wire:click="resetForm">
                        {{ __('accounting.proposals.cancel') }}
                    </button>
                    <button type="submit" class="btn btn-primary radius-xl" wire:loading.attr="disabled" wire:target="save">
                        <span wire:loading.remove wire:target="save">{{ __('accounting.proposals.update') }}</span>
                        <span wire:loading wire:target="save">{{ __('accounting.proposals.updating') }}</span>
                    </button>
                </div>
            </form>
        @endif
    </div>
</div>
</div>
