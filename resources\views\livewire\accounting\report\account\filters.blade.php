<div>
    <div class="card mb-3">
        <div class="card-body">
            <form class="fs-14">
                <div class="d-flex flex-wrap gap-10">

                    <div class="flex-fill">
                        <label for="start_Month" class="text-osool fw-600">@lang('accounting.start_date')</label>
                        <div class="position-relative">
                            <input wire:model.defer='from_date' type="text"
                                class="form-control datepicker allow-past-date" id="start_Month" type="month" />
                            <i class="iconsax field-icon" icon-name="calendar-search"></i>
                        </div>
                    </div>

                    <div class="flex-fill">
                        <label for="end_Month" class="text-osool fw-600">@lang('accounting.end_date')</label>
                        <div class="position-relative">
                            <input wire:model.defer='to_date' type="text"
                                class="form-control datepicker allow-past-date" id="end_Month" type="month" />
                            <i class="iconsax field-icon" icon-name="calendar-search"></i>
                        </div>
                    </div>

                    <div class="flex-fill">
                        <label for="select_account" class="text-osool fw-600">@lang('accounting.account')</label>
                        <select class="form-control" id="select_account" wire:model.defer="selected_account">
                            <option value="">@lang('accounting.selectAccount')</option>
                            @foreach ($accounts as $item)
                                <option value="{{ $item['id'] }}">{{ $item['holder_name'] }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="flex-fill">
                        <label for="" class="text-osool fw-600">@lang('accounting.category')</label>
                        <select class="form-control" wire:model.defer="selected_type">
                            <option value="">@lang('accounting.select_category')</option>
                            @foreach ($types as $key => $type)
                                <option value="{{ $key }}">{{ $type }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="flex-fill">
                        <label for="" class="d-md-block d-none">&nbsp;</label>
                        <div class="d-flex gap-10 justify-content-end">
                            <button type="button" wire:click.prevent='filter'
                                class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                                <i class="bg-inprogress bg-opacity-loss btn btn-sm d-flex fs-18 icon iconsax radius-md text-white wh-45"
                                    data-toggle="tooltip" data-placement="top" title="@lang('accounting.apply')"
                                    icon-name="search-normal-2"></i>
                            </button>
                            <button type="button" wire:click='resetFilter'
                                class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                                <i class="bg-secondary bg-opacity-loss btn btn-sm d-flex fs-18 icon iconsax radius-md text-white wh-45"
                                    data-toggle="tooltip" data-placement="top" title="@lang('accounting.reset')"
                                    icon-name="trash"></i>
                            </button>

                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div id="printableArea">
        <div class="row">
            <div class="col-4 pl-0">
                <div class="card p-4 mb-4 h-110">
                    <h5 class="report-text gray-text mb-0">@lang('accounting.report') :</h5>
                    <h6 class="report-text mb-0">@lang('accounting.account_sum')</h6>
                </div>
            </div>

            <div class="col-4">
                <div class="card p-4 mb-4 h-110">
                    <h5 class="report-text gray-text mb-0">@lang('accounting.duration') :</h5>
                    <h6 class="report-text mb-0">{{ \Carbon\Carbon::parse($from_date)->format('M-Y') }} to
                        {{ \Carbon\Carbon::parse($to_date)->format('M-Y') }}
                    </h6>
                </div>
            </div>

            <div class="col-4">
                <div class="card p-4 mb-4 h-110">
                    <h5 class="report-text gray-text mb-0">@lang('accounting.type') :</h5>
                    <h6 class="report-text mb-0">{{ $this->selected_type }}</h6>
                </div>
            </div>
        </div>
    </div>
</div>
