<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accounts Summary</title>

    <style type="text/css">
        body {
            margin: 0;
            font-family: "Open Sans", sans-serif;
            font-weight: 400;
            line-height: 1.5;
            color: #666d92;
            text-align: left;
            background-color: #fff;
            position: relative;
            background: #f4f5f7;
            font-size: 13px;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        .h1,
        .h2,
        .h3,
        .h4,
        .h5,
        .h6 {
            margin-bottom: 0;
            font-family: "Open Sans", sans-serif;
            font-weight: 600;
            line-height: 1.2;
            color: #272b41;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin-top: 0;
            margin-bottom: 0;
        }

        h5,
        .h5 {
            font-size: 18px;
        }

        p {
            margin-top: 0;
            margin-bottom: 1rem;
        }

        /* font-size-and-styles */
        h5 {
            margin-bottom: 0;
            font-family: "Open Sans", sans-serif;
            font-weight: 600;
            line-height: 1.2;
            color: #272b41;
        }

        /* row-columns */
        .row {
            display: flex;
            flex-wrap: wrap;
        }

        .col-6 {
            width: 50%;
            float: left;
            position: relative;
            padding-left: 15px;
            padding-right: 15px;
            box-sizing: border-box;
        }

        /* new */

        /* ===== Bootstrap-like Grid System ===== */
        .col-4 {
            width: 33.3333%;
            float: left;
            position: relative;
            padding-left: 15px;
            padding-right: 15px;
            box-sizing: border-box;
        }

        .pr-0 {
            padding-right: 0 !important;
        }

        /* ===== Card Styling ===== */
        .card {
            position: relative;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
            -ms-flex-direction: column;
            flex-direction: column;
            min-width: 0;
            word-wrap: break-word;
            background-color: #fff;
            -webkit-background-clip: border-box;
            background-clip: border-box;
            border: none !important;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            -webkit-border-radius: 10px;
            border-radius: 10px;
        }

        /* Padding and Margin Helpers */
        .p-4 {
            padding: 1.5rem !important;
        }

        .mb-4 {
            margin-bottom: 1.5rem !important;
        }

        /* ===== Custom Height ===== */
        .h-110 {
            height: 110px;
        }

        /* ===== Text Styling ===== */
        .report-text {
            font-family: Arial, Helvetica, sans-serif;
            line-height: 1.4;
        }

        .gray-text {
            color: #6c757d;
        }

        /* Heading sizes */
        h5.report-text {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        h6.report-text {
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
    </style>


</head>

<body>
    <div class="row" style="padding-top:20px">
        <div class="col-4 pl-0">
            <div class="card p-4 mb-4 h-110">
                <h6 class="report-text mb-0">@lang('accounting.report') :</h6>
                <h5 class="report-text mb-0">@lang('accounting.account_sum')</h5>
            </div>
        </div>

        <div class="col-4">
            <div class="card p-4 mb-4 h-110">
                <h6 class="report-text mb-0">@lang('accounting.duration') :</h6>
                <h5 class="report-text mb-0">{{ \Carbon\Carbon::parse($from_date)->format('M-Y') }} to
                    {{ \Carbon\Carbon::parse($to_date)->format('M-Y') }}
                </h5>
            </div>
        </div>
        <div class="col-4">
            <div class="card p-4 mb-4 h-110">
                <h6 class="report-text mb-0">@lang('accounting.type') :</h6>
                <h5 class="report-text mb-0">{{ $type }}
                </h5>
            </div>
        </div>
    </div>

    <div class="row">
        @foreach ($accounts as $account)
            <div class="col-4 pr-0">
                <div class="card p-4 mb-4 h-110">
                    <h6 class="report-text mb-0">{{ $account['holder_name'] }} -
                        {{ $account['bank_name'] }}</h6>
                    <h5 class="report-text mb-0">{!! $currency !!}
                        {{ Helper::human_readable_number($account['total']) }}</h5>
                </div>
            </div>
        @endforeach
    </div>
</body>

</html>
