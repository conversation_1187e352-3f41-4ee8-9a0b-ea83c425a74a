<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <style type="text/css">
        body {
            margin: 0;
            font-family: "Open Sans", sans-serif;
            font-weight: 400;
            line-height: 1.5;
            color: #666d92;
            text-align: left;
            background-color: #fff;
            position: relative;
            background: #f4f5f7;
            font-size: 13px;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        .h1,
        .h2,
        .h3,
        .h4,
        .h5,
        .h6 {
            margin-bottom: 0;
            font-family: "Open Sans", sans-serif;
            font-weight: 600;
            line-height: 1.2;
            color: #272b41;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin-top: 0;
            margin-bottom: 0;
        }

        h5,
        .h5 {
            font-size: 18px;
        }

        p {
            margin-top: 0;
            margin-bottom: 1rem;
        }

        /* font-size-and-styles */
        h5 {
            margin-bottom: 0;
            font-family: "Open Sans", sans-serif;
            font-weight: 600;
            line-height: 1.2;
            color: #272b41;
        }

        /* row-columns */
        .row {
            display: flex;
            flex-wrap: wrap;
        }

        .col-6 {
            width: 50%;
            float: left;
            position: relative;
            padding-left: 15px;
            padding-right: 15px;
            box-sizing: border-box;
        }

        /* new */

        /* ===== Bootstrap-like Grid System ===== */
        .col-4 {
            width: 33.3333%;
            float: left;
            position: relative;
            padding-left: 15px;
            padding-right: 15px;
            box-sizing: border-box;
        }

        .pr-0 {
            padding-right: 0 !important;
        }

        /* ===== Card Styling ===== */
        .card {
            position: relative;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
            -ms-flex-direction: column;
            flex-direction: column;
            min-width: 0;
            word-wrap: break-word;
            background-color: #fff;
            -webkit-background-clip: border-box;
            background-clip: border-box;
            border: none !important;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            -webkit-border-radius: 10px;
            border-radius: 10px;
        }

        /* Padding and Margin Helpers */
        .p-4 {
            padding: 1.5rem !important;
        }

        .mb-4 {
            margin-bottom: 1.5rem !important;
        }

        /* ===== Custom Height ===== */
        .h-110 {
            height: 110px;
        }

        /* ===== Text Styling ===== */
        .report-text {
            font-family: Arial, Helvetica, sans-serif;
            line-height: 1.4;
        }

        .gray-text {
            color: #6c757d;
        }

        /* Heading sizes */
        h5.report-text {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        h6.report-text {
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }


        .card-body {
            padding: 1.25rem;
        }

        /* Table responsive wrapper */
        .table-responsive {
            display: block;
            width: 100%;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        /* Table base */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
            background-color: #fff;
        }

        .table th,
        .table td {
            padding: 12px 15px;
            text-align: left;
            font-size: 13px;
            border-bottom: 1px solid #eaeaea;
            vertical-align: middle;
        }

        .table thead th {
            background: #f8f9fb;
            font-weight: 600;
            color: #5a5f7d;
            font-size: 13px;
            border-bottom: 2px solid #eaeaea;
        }

        .table tbody tr:nth-child(even) {
            background: #fafbfc;
        }

        /* Utilities */
        .bg-white {
            background: #ffffff !important;
        }

        .w-100 {
            width: 100% !important;
        }

        .border-0 {
            border: none !important;
        }

        .text-dark {
            color: #212529 !important;
            font-weight: 600;
        }

        .text-new-primary {
            color: #152B70 !important;
            font-weight: 600;
        }

        /* Flex utilities */
        .d-flex {
            display: flex !important;
        }

        .align-items-center {
            align-items: center !important;
        }

        .gap-10 {
            gap: 10px !important;
        }

        /* Heading adjustments */
        h6 {
            font-size: 14px;
            margin: 0;
            font-weight: 600;
            color: #272b41;
        }
    </style>


</head>

<body>

    <div class="row" style="padding-top:20px">
        <div class="col-6 pl-0">
            <div class="card p-4 mb-4 h-110">
                <h6 class="report-text mb-0">@lang('accounting.report') :</h6>
                <h5 class="report-text mb-0">@lang('accounting.income_vs_expense_sum')</h5>
            </div>
        </div>

        <div class="col-6">
            <div class="card p-4 mb-4 h-110">
                <h6 class="report-text mb-0">@lang('accounting.duration') :</h6>
                <h5 class="report-text mb-0">Jan-{{ $year }} to Dec-{{ $year }}
                </h5>
            </div>
        </div>
    </div>

    <div class="card mt-3">
        <div class="card-body">
            <div>
                <img src="{{ $chartImage }}" style="width:100%; height:auto;">
            </div>
        </div>
    </div>

    <div class="card mt-3">
        <div class="card-body">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>@lang('accounting.category')</th>
                                <th>January</th>
                                <th>February</th>
                                <th>March</th>
                                <th>April</th>
                                <th>May</th>
                                <th>June</th>
                                <th>July</th>
                                <th>August</th>
                                <th>September</th>
                                <th>October</th>
                                <th>November</th>
                                <th>December</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="13" class="text-dark"><span>@lang('accounting.income') : </span></td>
                            </tr>
                            <tr>
                                <td>@lang('accounting.revenue')</td>
                                @foreach (@$apiData['revenueIncomeTotal'] ?? [] as $item)
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            {!! $currency !!}
                                            <span
                                                class="text-new-primary">{{ Helper::human_readable_number($item) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>
                            <tr>
                                <td>@lang('accounting.invoice')</td>
                                @foreach (@$apiData['invoiceIncomeTotal'] ?? [] as $item)
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            {!! $currency !!}
                                            <span
                                                class="text-new-primary">{{ Helper::human_readable_number($item) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>
                            <tr>
                                <td colspan="13" class="text-dark"><span>@lang('accounting.expense') : </span></td>
                            </tr>
                            <tr>
                                <td>@lang('accounting.payment')</td>
                                @foreach (@$apiData['paymentExpenseTotal'] ?? [] as $item)
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            {!! $currency !!}
                                            <span
                                                class="text-new-primary">{{ Helper::human_readable_number($item) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>
                            <tr>
                                <td>@lang('accounting.bill')</td>
                                @foreach (@$apiData['billExpenseTotal'] ?? [] as $item)
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            {!! $currency !!}
                                            <span
                                                class="text-new-primary">{{ Helper::human_readable_number($item) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>
                            <tr>
                                <td>@lang('accounting.purchase')</td>
                                @foreach (@$apiData['purchaseExpenseTotal'] ?? [] as $item)
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            {!! $currency !!}
                                            <span
                                                class="text-new-primary">{{ Helper::human_readable_number($item) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>
                            <tr>
                                <td>@lang('accounting.employee_salary')</td>
                                @foreach (@$apiData['EmpSalary'] ?? [] as $item)
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            {!! $currency !!}
                                            <span
                                                class="text-new-primary">{{ Helper::human_readable_number($item) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>
                            <tr>
                                <td colspan="13" class="text-dark">
                                    <span>@lang('accounting.profit_calc')</span>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <h6>@lang('accounting.profit')</h6>
                                </td>
                                @foreach (@$apiData['profit'] ?? [] as $item)
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            {!! $currency !!}
                                            <span
                                                class="text-new-primary">{{ Helper::human_readable_number($item) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>
                        </tbody>
                    </table>

                </div>
            </div>
        </div>

    </div>


    <div class="card mt-3">
        <div class="card-body">
            <div>
                <img src="{{ $chartImage }}" style="width:100%; height:auto;">
            </div>
        </div>
    </div>
</body>

</html>
