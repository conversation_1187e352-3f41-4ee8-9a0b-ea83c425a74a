<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <style type="text/css">
        /* ====== Base Reset ====== */
        body {
            margin: 0;
            font-family: "Open Sans", Arial, Helvetica, sans-serif;
            font-weight: 400;
            line-height: 1.5;
            color: #666d92;
            text-align: left;
            background: #f4f5f7 !important;
            font-size: 13px;
            -webkit-print-color-adjust: exact;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin-top: 0;
            margin-bottom: 0;
            font-family: "Open Sans", Arial, Helvetica, sans-serif;
            font-weight: 600;
            line-height: 1.2;
            color: #272b41;
        }

        h5 {
            font-size: 18px;
        }

        h6 {
            font-size: 14px;
            font-weight: 600;
            color: #272b41;
        }

        p {
            margin-top: 0;
            margin-bottom: 1rem;
        }

        /* ====== Grid System (Bootstrap-like) ====== */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin-left: -15px;
            margin-right: -15px;
        }

        .col-6,
        .col-4 {
            position: relative;
            padding-left: 15px;
            padding-right: 15px;
            box-sizing: border-box;
        }

        .col-6 {
            width: 50%;
        }

        .col-4 {
            width: 33.3333%;
        }

        /* ====== Spacing Utilities ====== */
        .p-4 {
            padding: 1.5rem !important;
        }

        .py-4 {
            padding-top: 1.5rem !important;
            padding-bottom: 1.5rem !important;
        }

        .px-3 {
            padding-left: 1rem !important;
            padding-right: 1rem !important;
        }

        .pt-4 {
            padding-top: 1.5rem !important;
        }

        .pb-2 {
            padding-bottom: 0.5rem !important;
        }

        .pb-0 {
            padding-bottom: 0 !important;
        }

        .pt-0 {
            padding-top: 0 !important;
        }

        .mb-4 {
            margin-bottom: 1.5rem !important;
        }

        .mb-0 {
            margin-bottom: 0 !important;
        }

        .mt-4 {
            margin-top: 1.5rem !important;
        }

        .pl-0 {
            padding-left: 0 !important;
        }

        .pr-0 {
            padding-right: 0 !important;
        }

        /* ====== Flex Utilities ====== */
        .d-flex {
            display: flex !important;
        }

        .align-items-center {
            align-items: center !important;
        }

        .d-flex>*:not(:last-child) {
            margin-right: 10px;
        }

        /* gap fallback */

        /* ====== Card Styling ====== */
        .card {
            position: relative;
            display: flex;
            flex-direction: column;
            background-color: #fff;
            border: none !important;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            min-width: 0;
            word-wrap: break-word;
        }

        .card-body {
            padding: 1.25rem;
        }

        .h-110 {
            height: 110px;
        }

        /* ====== Typography Helpers ====== */
        .report-text {
            font-family: Arial, Helvetica, sans-serif;
            line-height: 1.4;
        }

        .gray-text {
            color: #6c757d;
        }

        .text-dark {
            color: #212529 !important;
            font-weight: 600;
        }

        .text-new-primary {
            color: #152B70 !important;
            font-weight: 600;
        }

        /* ====== Table Styling ====== */
        .table-responsive {
            display: block;
            width: 100%;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
            background-color: #fff;
            page-break-inside: auto;
        }

        .table th,
        .table td {
            padding: 12px 15px;
            text-align: left;
            font-size: 13px;
            border: 1px solid #ccc;
            vertical-align: middle;
        }

        .table thead th {
            background: #f8f9fb !important;
            font-weight: 600;
            color: #5a5f7d;
            font-size: 13px;
            border-bottom: 2px solid #ccc;
        }

        .table tbody tr:nth-child(even) {
            background: #fafbfc !important;
        }

        /* Repeat table header/footer when breaking pages in PDF */
        thead {
            display: table-header-group;
        }

        tfoot {
            display: table-footer-group;
        }

        tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }

        /* ====== Background & Width Utilities ====== */
        .bg-white {
            background: #ffffff !important;
        }

        .w-100 {
            width: 100% !important;
        }

        .border-0 {
            border: none !important;
        }

        /* ====== Custom Helpers ====== */
        .userDatatable-content {
            font-size: 13px;
        }

        .min-w-150 {
            min-width: 150px;
        }

        .row {
            font-size: 0;
        }

        .col-6 {
            display: inline-block;
            vertical-align: top;
            width: 50%;
            font-size: 13px;
            padding-left: 15px;
            padding-right: 15px;
            box-sizing: border-box;
        }
    </style>

</head>

<body>
    <div class="row" style="padding-top:20px">
        <div class="col-6 pl-0">
            <div class="card p-4 mb-4 h-110">
                <h6 class="report-text mb-0">@lang('accounting.report') :</h6>
                <h5 class="report-text mb-0">@lang('accounting.profit_n_loss_sum')</h5>
            </div>
        </div>

        <div class="col-6">
            <div class="card p-4 mb-4 h-110">
                <h6 class="report-text mb-0">@lang('accounting.duration') :</h6>
                <h5 class="report-text mb-0">Jan-{{ $year }} to Dec-{{ $year }}
                </h5>
            </div>
        </div>
    </div>

    <div class="card">
        <div>
            <div class="py-4 px-3 border-0">
                <h5>@lang('accounting.income')</h5>
            </div>
        </div>

        <div class="card-body px-0 pt-0 pb-0">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 th-osool">

                        <thead>
                            <tr class="userDatatable-header">
                                <th>
                                    @lang('accounting.category')
                                </th>
                                <th>
                                    Jan-Mar
                                </th>
                                <th>
                                    Apr-Jun
                                </th>
                                <th>
                                    Jul-Sep
                                </th>
                                <th>
                                    Oct-Dec
                                </th>
                                <th>
                                    @lang('accounting.total')
                                </th>
                            </tr>
                        </thead>

                        <tbody class="sort-table ui-sortable">
                            <tr>
                                <td>
                                    <span class="userDatatable-content">@lang('accounting.revenue') :</span>
                                </td>
                            </tr>

                            @foreach (@$apiData['revenueIncomeArray'] ?? [] as $item)
                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{{ $item['category'] }}</span>
                                        </div>
                                    </td>
                                    @foreach ($item['amount'] as $datum)
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{!! $currency !!}
                                                    {{ Helper::human_readable_number($datum) }}</span>
                                            </div>
                                        </td>
                                    @endforeach
                                </tr>
                            @endforeach

                            <tr>
                                <td>
                                    <span class="userDatatable-content">@lang('accounting.invoice') :</span>
                                </td>
                            </tr>

                            @foreach (@$apiData['invoiceIncomeArray'] ?? [] as $item)
                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{{ $item['category'] }}</span>
                                        </div>
                                    </td>
                                    @foreach ($item['amount'] as $datum)
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{!! $currency !!}
                                                    {{ Helper::human_readable_number($datum) }}</span>
                                            </div>
                                        </td>
                                    @endforeach
                                </tr>
                            @endforeach

                            <tr>
                                <td colspan="6">
                                    <span class="userDatatable-content">@lang('accounting.total_income_calc')</span>
                                </td>
                            </tr>

                            <tr class="ui-sortable-handle" style="opacity: 1;">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>@lang('accounting.total_income')</span>
                                    </div>
                                </td>
                                @foreach (@$apiData['totalIncome'] ?? [] as $datum)
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{!! $currency !!}
                                                {{ Helper::human_readable_number($datum) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div>
            <div class="pt-4 pb-2 px-3 border-0">
                <h5>@lang('accounting.expense')</h5>
            </div>
        </div>

        <div class="card-body px-0 pt-0 pb-0">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 th-osool">

                        <thead>
                            <tr class="userDatatable-header">
                                <th>
                                    @lang('accounting.category')
                                </th>
                                <th>
                                    Jan-Mar
                                </th>
                                <th>
                                    Apr-Jun
                                </th>
                                <th>
                                    Jul-Sep
                                </th>
                                <th>
                                    Oct-Dec
                                </th>
                                <th>
                                    Total
                                </th>
                            </tr>
                        </thead>

                        <tbody class="sort-table ui-sortable">
                            <tr>
                                <td>
                                    <span class="userDatatable-content">@lang('accounting.payment') :</span>
                                </td>
                            </tr>

                            @foreach (@$apiData['expenseArray'] ?? [] as $item)
                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{{ $item['category'] }}</span>
                                        </div>
                                    </td>
                                    @foreach ($item['amount'] as $datum)
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{!! $currency !!}
                                                    {{ Helper::human_readable_number($datum) }}</span>
                                            </div>
                                        </td>
                                    @endforeach
                                </tr>
                            @endforeach

                            <tr class="ui-sortable-handle" style="opacity: 1;">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>@lang('accounting.bill') :</span>
                                    </div>
                                </td>
                            </tr>

                            @foreach (@$apiData['billExpenseArray'] ?? [] as $item)
                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{{ $item['category'] }}</span>
                                        </div>
                                    </td>
                                    @foreach ($item['amount'] as $datum)
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{!! $currency !!}
                                                    {{ Helper::human_readable_number($datum) }}</span>
                                            </div>
                                        </td>
                                    @endforeach
                                </tr>
                            @endforeach

                            <tr class="ui-sortable-handle" style="opacity: 1;">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>@lang('accounting.employee_salary') :</span>
                                    </div>
                                </td>
                                @foreach (@$apiData['salExpenseCatAmount'] ?? [] as $datum)
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{!! $currency !!}
                                                {{ Helper::human_readable_number($datum) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>

                            <tr>
                                <td colspan="6">
                                    <span class="userDatatable-content">@lang('accounting.total_expense_calc')</span>
                                </td>
                            </tr>

                            <tr class="ui-sortable-handle" style="opacity: 1;">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>@lang('accounting.total_expenses')</span>
                                    </div>
                                </td>
                                @foreach (@$apiData['totalExpense'] ?? [] as $datum)
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{!! $currency !!}
                                                {{ Helper::human_readable_number($datum) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>




        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0 mt-4">
            <div class="table-responsive">
                <table class="table mb-0 radius-0 th-osool">


                    <tbody class="sort-table ui-sortable">

                        <tr>
                            <td colspan="6">
                                <div class="border-0">
                                    <span class="userDatatable-content">@lang('accounting.net_profit_calc')</span>
                                </div>
                            </td>
                        </tr>

                        <tr class="ui-sortable-handle" style="opacity: 1;">
                            <td>
                                <div class="mb-0 min-w-150 userDatatable-content">
                                    <span>@lang('accounting.net_profit')</span>
                                </div>
                            </td>
                            @foreach (@$apiData['netProfitArray'] ?? [] as $datum)
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>{!! $currency !!}
                                            {{ Helper::human_readable_number($datum) }}</span>
                                    </div>
                                </td>
                            @endforeach
                        </tr>

                    </tbody>
                </table>
            </div>
        </div>
    </div>

</body>

</html>
