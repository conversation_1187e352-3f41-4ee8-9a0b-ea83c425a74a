<button  class="btn px-15 btn-outline-gray mr-2" data-toggle="modal" id="showExportModal" data-target="#export_modal">
<span> @lang('advance_contracts.export')</span> </button>



<div class="modal fade" id="export_modal" tabindex="-1" role="dialog" aria-labelledby="example_Modal_Label" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sm" role="document">
        <div class="modal-content">



            <div class="modal-body">
                <form id="exportForm">
                    @csrf
                    <input type="hidden" name="contract_ids[]" value="{{$contract->id}}">
                    <input type="hidden" name="redirect_page" value="0">

<div class="row">


<div class="col-md-12">
<div class="form-group months-selection">
<label for="exampleInputEmail1">{{__('user_management_module.user_forms.label.select_period')}} <small class="required">*</small></label>
<div class="position-relative dropdown d-block">
<input class="form-control" id="selectedMonthsInput" placeholder="{{ __('advance_contracts.select_range_placeholder') }}" name="months" autocomplete="off" aria-invalid="false"  required=""/>
<div id="select_range_error"></div>
<div class="dropdown-menu rounded-xl" id="dropdownMenu">
<div class="card" id="monthRangeCard">
<div class="card-header d-flex justify-content-between align-items-center">
<span id="prevYear" class="btn btn-light border wh-40 rotate-ar-y"><i class="las la-angle-left mr-0"></i></span>
<h4 id="currentYear">2026</h4>
<span id="nextYear" class="btn btn-light border wh-40 rotate-ar-y"><i class="las la-angle-right mr-0"></i></span>
</div>
<div class="card-body">
<div class="month-picker" id="monthPicker">
<div class="month selected">Jan</div>
<div class="month selected">Feb</div>
<div class="month selected">Mar</div>
<div class="month disabled">Apr</div>
<div class="month disabled">May</div>
<div class="month disabled">Jun</div>
<div class="month disabled">Jul</div>
<div class="month disabled">Aug</div>
<div class="month disabled">Sep</div>
<div class="month disabled">Oct</div>
<div class="month disabled">Nov</div>
<div class="month disabled">Dec</div>
</div>
<div class="button-container mt-3 d-flex justify-content-end gap-10">
<span id="clearButton" class="btn btn-light border btn-xs">
@lang('advance_contracts.clear')
</span>

<span id="applyButton" class="btn btn-primary btn-xs">
@lang('advance_contracts.apply')
</span>
</div>
</div>
</div>
</div>
<span data-feather="calendar" class="field-icon"></span>
</div>
</div>
</div>

<div class="col-md-12">
  <div class="form-group">
     <label for="">
     {{__('user_management_module.user_forms.label.select_report_type')}}<small class="required">*</small>
     </label>
     <select class="form-control select2-new" id="report-type" required="" name="report_type">
        <option value="">{{__('user_management_module.user_forms.label.select_report_type')}}</option>
        <option value="monthly_cost_report">@lang('advance_contracts.reports.monthly_cost_report')</option>
        <option value="consumed_materials_report">@lang('advance_contracts.reports.consumed_materials_report')</option>
        <option value="attendance_cost_report">@lang('advance_contracts.reports.attendance_cost_report')</option>

     </select>
       <div id="report_type_error"></div>
  </div>

</div>

<div class="col-md-12">
                              <div class="form-group">
                                 <label for="emp_user_status">{{__('user_management_module.user_forms.label.reports_format')}}<small class="required">*</small></label>
                                 <div class="radio-horizontal-list d-flex">
                                    <!-- <div class="radio-theme-default custom-radio ">
                                       <input class="radio" type="radio" name="type"  value="pdf" id="format_1" disabled>
                                       <label for="format_1">
                                       <span class="radio-text">PDF</span>
                                       </label>
                                    </div> -->
                                    <div class="radio-theme-default custom-radio ">
                                       <input class="radio" type="radio" name="type" checked="" value="csv" id="format_2">
                                       <label for="format_2">
                                       <span class="radio-text">CSV</span>
                                       </label>
                                    </div>
                                 </div>
                                 <div id="type_error"></div>
                              </div>
                           </div>
<div class="col-md-12">
                              <div class="form-group">
                                 <label for="emp_user_status">{{__('configration_assets.asset_comminucation_forms.label.rert_language')}}<small class="required">*</small></label>
                                 <div class="radio-horizontal-list d-flex">
                                    <div class="radio-theme-default custom-radio ">
                                       <input class="radio" type="radio" name="language" value="1" id="advane_lang1">
                                       <label for="advane_lang1">
                                       <span class="radio-text">{{__('user_management_module.user_forms.label.arabic')}}</span>
                                       </label>
                                    </div>
                                    <div class="radio-theme-default custom-radio ">
                                       <input class="radio" type="radio" name="language" checked=""  value="0" id="advane_lang2">
                                       <label for="advane_lang2">
                                       <span class="radio-text">{{__('user_management_module.user_forms.label.english')}}</span>
                                       </label>
                                    </div>
                                 </div>
                              </div>
                           </div>


</div>

                </form>
            </div>

            <div class="modal-footer d-flex justify-content-between">
                <div class="button-group">
                    <button type="button" class="btn btn-light" data-dismiss="modal">
                        {{ __('work_order.button.cancel') }}
                    </button>
                    <button type="button" class="btn btn-primary" onclick="initiateExport()">
                        <img src="{{ asset('img/svg/export-icon-white.svg') }}" class="mr-1">
                        {{ __('work_order.forms.label.export') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>







<div class="modal fade" id="export-confirm-modal" tabindex="-1" role="dialog" aria-labelledby="example_Modal_Label"aria-hidden="true">
<div class="modal-dialog modal-dialog-centered modal-sm" role="document">
<div class="modal-content">
<div class="modal-body">
<div class="text-center">
<img src="{{ asset('img/svg/check-circle.svg') }}" class="mb-2">
<h3 class="mb-3 fw-400">@lang('advance_contracts.successful')</h3>

<p>{!! __('advance_contracts.export_message', ['link' => route('reports.manage_reports')]) !!}</p>

</div>
</div>
<div class="modal-footer">
<button type="button" class="btn btn-primary" data-dismiss="modal">@lang('advance_contracts.ok')</button>
</div>
</div>
</div>
</div>






<script>
function initiateExport() {
    let reportType = $('#report-type').val();
    $('#select_range_error').html('');
    $('#report_type_error').html('');
    let selectedMonthsInput = $('#selectedMonthsInput').val();
    if (!selectedMonthsInput) {
         $('#select_range_error').html('<span class="text-danger">' + translations.general_sentence.validation.field_is_required + '</span>');
    }
    if (!reportType) {
        $('#report_type_error').html('<span class="text-danger">' + translations.general_sentence.validation.field_is_required + '</span>');
    }
    if(!selectedMonthsInput || !reportType){
        return;
    }
    $('#overlayer').show();
    $('.loader-overlay').show();
    let form = $('#exportForm');
    let url = $('#initiateExportUrl').val();
    let formData = form.serializeArray();
    $.ajax({
        url: "{{ route('reports.advancedContractReport')}}",
        method: 'POST',
        data: formData,
        headers: {
            'X-CSRF-TOKEN': $('input[name="_token"]').val()
        },
        success: function (response) {
            $('#overlayer').hide();
            $('.loader-overlay').hide();
            $('#export_modal').modal('hide');
            $('#export-confirm-modal').modal('show');
        },
        error: function (xhr) {
            // alert('❌ Something went wrong!');
        }
    });
}
</script>
