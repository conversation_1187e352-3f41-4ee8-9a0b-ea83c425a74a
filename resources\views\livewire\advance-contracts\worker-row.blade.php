<tr class="main-row" wire:key="worker-row-{{ $assigned->id }}">
    {{-- NAME --}}
    <td class="name-cell">
        <div class="d-flex userDatatable-content mb-0 align-items-center">
            <span class="name-text">{{ $assigned->worker->name ?? '--' }}</span>
        </div>
    </td>

    {{-- COUNTRY --}}
    <td>
        <div class="d-flex userDatatable-content mb-0 align-items-center">
            <span>
                {{ app()->getLocale() === 'ar' 
                    ? $assigned->worker->country->name_ar ?? '--' 
                    : $assigned->worker->country->name_en ?? '--' 
                }}
            </span>
        </div>
    </td>

    {{-- SALARY --}}
    <td class="salary-cell">
        <div class="d-flex userDatatable-content mb-0 align-items-center">
            @if($isEditing)
                <input type="number" wire:model.lazy="salary" value="{{ $assigned->worker->salary }}" class="form-control min-w-150 salary-input">
                 @error('salary')
                <span class="text-danger small mt-1">{{ $message }}</span>
            @enderror
            @else
                <span class="{{ isset($requiredSalary) &&  $assigned->worker->salary < $requiredSalary ? 'text-danger' : '' }}">
                    @if(isset($requiredSalary) &&  $assigned->worker->salary < $requiredSalary)
                        <i class="iconsax icon fs-18 mr-1" icon-name="warning-triangle" title=" {{ __('advance_contracts.workforce_modal.value_conflict') }}"></i>
                    @endif
                     <span class="salary-text">{{  $assigned->worker->salary ?? '--' }}</span>
                </span>
            @endif
        </div>
    </td>

    {{-- ROLE --}}
    <td class="role-cell">
        <div class="d-flex userDatatable-content mb-0 align-items-center">
            @if($isEditing)
                <select wire:model.lazy="role" class="form-select form-select-sm w-75 select2-role">
                    @foreach($roles as $value => $label)
                        <option value="{{  $value }}" >{{ $label }}</option>
                    @endforeach
                </select>
                @error('role')
                    <span class="text-danger small mt-1">{{ $message }}</span>
                @enderror
            @else
                <span class="{{ $assigned->worker->role !== $selectedRole ? 'text-danger' : '' }}">
                    @if($assigned->worker->role !== $selectedRole)
                        <i class="iconsax icon fs-18 mr-1" icon-name="warning-triangle"  title=" {{ __('advance_contracts.workforce_modal.value_conflict') }}"></i>
                    @endif
                    {{ \App\Enums\Role::tryLabel($assigned->worker->role) ?? '--' }}
                </span>
            @endif
        </div>
    </td>

    {{-- PROFICIENCY --}}
    <td class="admin-cell">
        <div class="d-flex userDatatable-content mb-0 align-items-center">
            @if($isEditing)
                <select wire:model.lazy="proficiency" class="form-select form-select-sm w-75 select2-proficiency">
                    @foreach($adminLevels as $value => $label)
                        <option value="{{  $value }}">{{ $label }}</option>
                    @endforeach
                </select>
                @error('proficiency')
                    <span class="text-danger small mt-1">{{ $message }}</span>
                @enderror
            @else
                <span class="{{ $assigned->worker->admin_level !== $selectedProficiency ? 'text-danger' : '' }}">
                    @if($assigned->worker->admin_level !== $selectedProficiency)
                        <i class="iconsax icon fs-18 mr-1" icon-name="warning-triangle"  title=" {{ __('advance_contracts.workforce_modal.value_conflict') }}"></i>
                    @endif
                    {{ \App\Enums\Proficiency::tryLabel($assigned->worker->admin_level) ?? '--' }}
                </span>
            @endif
        </div>
    </td>

    {{-- ATTENDANCE --}}
    <td class="attendance-cell">
         <div class="d-flex userDatatable-content mb-0 align-items-center">
            @if($isEditing)
                <input type="number" wire:model.lazy="attendanceTarget" value="{{ $assigned->worker->attendance_target }}" class="form-control min-w-150 salary-input">
                @error('attendanceTarget')
                    <span class="text-danger small mt-1">{{ $message }}</span>
                @enderror
            @else
                <span class="{{ isset($requiredAttendanceTarget) &&  $assigned->worker->attendance_target < ($requiredAttendanceTarget * 4) ? 'text-danger' : '' }}">
                    @if(isset($requiredAttendanceTarget) &&  $assigned->worker->attendance_target < ($requiredAttendanceTarget * 4))
                        <i class="iconsax icon fs-18 mr-1" icon-name="warning-triangle"  title=" {{ __('advance_contracts.workforce_modal.value_conflict') }}"></i>
                    @endif
                     <span class="salary-text">{{  $assigned->worker->attendance_target ?? '--' }}</span>
                </span>
            @endif
        </div>
    </td>

    {{-- ACTIONS --}}
    <td class="action-cell">
        <div class="d-inline-block">
            <ul class="mb-0 d-flex gap-10">
                @if($isEditing)
                    <li>
                        <a wire:click="save" class="text-success">
                            <i class="iconsax icon fs-20" icon-name="tick-circle"></i>
                        </a>
                    </li>
                    <li>
                        <a wire:click="cancelEdit" class="text-danger">
                            <i class="iconsax icon fs-20" icon-name="x-circle"></i>
                        </a>
                    </li>
                @else
                <li>
                    <a href="javascript:void(0);" wire:click="enableEdit" class="reassign-icon">
                        <i class="iconsax icon text-osool fs-18" icon-name="reblog"></i>
                    </a>
                </li>
                @endif
            </ul>
        </div>
    </td>
</tr>
