<div wire:poll.750ms>
    <div class = "button-group d-flex pt-10 justify-content-end mb-40">
        <a href = "{{ route('bulk-import.openUploadFile') }}" class = "btn btn-light btn-default btn-squared text-capitalize radius-md shadow2 btn-sm">
            @lang('import.previous')
        </a>
        <button type = "button" class = "btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 btn-sm" id = "btn-next-map">
            @lang('import.next')
        </button>
    </div>
     <div class = "modal fade new-member" id = "progress_bar_modal" tabindex = "-1" role = "dialog" aria-labelledby = "exampleModalLabel" aria-hidden = "true" style = "background: rgba(0,0,0,.3);" data-backdrop = "static" data-keyboard = "false" wire:ignore.self>
        <div class = "modal-dialog  modal-md modal-dialog-centered" role = "document">
            <div class = "modal-content">
                <div class = "modal-header">
                    <h5 class = "modal-title text-capitalize" id = "exampleModalLabel">@lang('import.bulk_import_progress')</h5>
                    <button wire:ignore type = "button" class = "close border-0" data-dismiss = "modal" aria-label = "Close">
                        <span aria-hidden = "true">&times;</span> 
                    </button>
                </div>
                <div class = "modal-body">
                    <p class = "text-muted font-weight-bold-600">@lang('import.hey') {{ $userName }}! @lang('import.view_logs_progress')</p>
                    <div class = "mt-2 mb-4 p-3 border rounded">
                        @if(isset($usersCheck) && $usersCheck)
                            <p class = "mb-0">@lang('import.users_sheet')</p>
                            <p class = "text-muted mb-3 fs-12">@lang('import.importing') {{ count($this->initUsers()) }} @lang('import.users_small')..</p>
                        @endif

                        @if(isset($prioritiesCheck) && $prioritiesCheck)
                            <p class = "mb-0">@lang('import.priorities_sheet')</p>
                            <p class = "text-muted mb-3 fs-12">@lang('import.importing') {{ count($this->initPriorities()) }} @lang('import.priorities_levels_small')..</p>
                        @endif

                        @if(isset($servicesCheck) && $servicesCheck)
                            <p class = "mb-0">@lang('import.services_sheet')</p>
                            <p class = "text-muted mb-3 fs-12">@lang('import.importing') {{ count($this->initServices()) }} @lang('import.asset_categories_small')..</p>
                        @endif

                        @if(isset($propertiesCheck) && $propertiesCheck)
                            <p class = "mb-0">@lang('import.properties_sheet')</p>
                            <p class = "text-muted mb-3 fs-12">@lang('import.importing') {{ count($this->initProperties()) }} @lang('import.properties_small')..</p>
                        @endif

                        @if(isset($buildingsCheck) && $buildingsCheck)
                            <p class = "mb-0">@lang('import.buildngs_sheet')</p>
                            <p class = "text-muted mb-3 fs-12">@lang('import.importing') {{ count($this->initBuildings()) }} @lang('import.property_building_small')..</p>
                        @endif

                        @if(isset($assetsCheck) && $assetsCheck)
                            <p class = "mb-0">@lang('import.assets_sheet')</p>
                            <p class = "text-muted mb-3 fs-12">@lang('import.importing') {{ count($this->initAssets()) }} @lang('import.assets_small')..</p>
                        @endif
                    </div>
                    <div class = "mx-auto">
                        <div class = "progress">
                            <div class = "progress-bar progress-bar-striped progress-bar-animated" role = "progressbar" aria-valuenow = "{{ $progressPercentage }}" aria-valuemin = "0" aria-valuemax = "100" style = "width: {{ $progressPercentage }}%">
                                {{ $progressPercentage }}% @lang('import.complete')
                            </div>
                        </div>
                    </div>
                </div>
                <div class = "modal-footer">
                    <a href = "{{ route('bulk-import.openErrorsValidation', ['token' => $token]) }}" class = "btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 btn-sm {{ $progressPercentage == 0 ? 'disabled' : '' }}">
                        <span class = "spinner-border spinner-border-sm {{ $progressPercentage == 0 ? 'show' : 'hide' }}" role = "status" aria-hidden = "true"></span> 
                        @lang('import.next')
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
