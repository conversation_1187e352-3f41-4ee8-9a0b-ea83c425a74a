<div>
    <div class = "userDatatable projectDatatable project-table w-100">
        <div class = "d-flex gap-10 pb-3 mb-3 justify-content-between">
            <div class = "d-flex gap-5 align-items-center">
                <div class = "d-flex gap-5 align-items-center mx-2">
                    <i class = "iconsax text-success fs-16" icon-name = "clipboard-tick"></i> 
                    <span class = "text-success">{{ $prioritiesList->total() }} @lang('import.inserted_rows')</span>
                </div>
                <div class = "d-flex gap-5 align-items-center">
                    <i class = "iconsax text-danger fs-16" icon-name = "info-circle"></i> 
                    <span class = "text-danger">{{ $errorsList->total() }} @lang('import.system_errors')</span>
                </div>
            </div>
            <div>
                <button type = "button" class = "btn btn-danger mx-2" data-toggle = "modal" data-target = "#confirm-delete-priorities">
                    @lang('import.delete_inserted_priorities')
                </button>
            </div>
        </div>
    </div>
    <div class = "row">
        <div class = "col-sm-6 mb-3 mb-sm-0">
            <div class = "card">
                <div class = "card-body">
                    <h5 class = "card-title">@lang('import.inserted_rows')</h5>
                    <p class = "card-text">@lang('import.priorities_inserted_text')</p>
                    <div class = "table-responsive" id = "priorities-table">
                        <table class = "table mb-0">
                            <thead>
                                <tr>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.priority")</span>
                                    </th>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.service_window")</span>
                                    </th>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.service_window_type")</span>
                                    </th>
                                    <th scope = "col" class = "extra-col d-none">
                                        <span style = "font-size:11px">@lang("import.response_time")</span>
                                    </th>
                                    <th scope = "col" class = "extra-col d-none">
                                        <span style = "font-size:11px">@lang("import.response_time_type")</span>
                                    </th>
                                    <th>
                                        <span id = "show-action-btn2" class = "mx-1" style = "text-decoration: underline; cursor: pointer;">
                                            @lang('import.more')
                                        </span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(isset($prioritiesList) && $prioritiesList->count())
                                    @foreach($prioritiesList as $key => $data)
                                        <tr wire:key = "priorities-{{ $key }}"> 
                                            <td>
                                                <p style = "font-size:11px">{{ $data->priority_level ?? '-' }}</p>
                                            </td>
                                            <td>
                                                <p style = "font-size:11px">{{ $data->service_window ?? '-' }}</p>
                                            </td>
                                            <td>
                                                <p style = "font-size:11px">{{ $data->service_window_type }}</p>
                                            </td> 
                                            <td class = "extra-row d-none">
                                                <p style = "font-size:11px">{{ $data->response_time }}</p>
                                            </td>
                                            <td class = "extra-row d-none">
                                                <p style = "font-size:11px">{{ $data->response_time_type }}</p>
                                            </td>
                                        </tr>
                                    @endforeach

                                    @if ($prioritiesList->hasMorePages())
                                        <tr>
                                            <td colspan = "6">
                                                <div class = "d-flex justify-content-center gap-2">
                                                    <div class = "p-2">
                                                        <div wire:loading wire:target = "managePerPageList">
                                                            <button type = "button" class = "btn btn-primary" wire:loading.attr = "disabled">
                                                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                                @lang('import.loading3')
                                                            </button>
                                                        </div>
                                                        <button type = "button" class = "btn btn-primary text-center" wire:target = "managePerPageList" wire:click = "managePerPageList" wire:loading.class = "hide">
                                                            @lang('import.load_more')
                                                        </button>
                                                    </div>
                                                    <div class = "p-2">
                                                        <div wire:loading wire:target = "manageLoadAllList">
                                                            <button type = "button" class = "btn btn-info" wire:loading.attr = "disabled">
                                                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                                @lang('import.loading3')
                                                            </button>
                                                        </div>
                                                        <button type = "button" class = "btn btn-info" wire:target = "manageLoadAllList" wire:click = "manageLoadAllList({{ $prioritiesList->total() }})" wire:loading.class = "hide">
                                                            @lang('import.load_all')
                                                        </button>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endif
                                @else
                                    <tr class = "text-center">
                                        <td colspan = "6">@lang("import.empty_priorities")</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class = "col-sm-6 mb-3 mb-sm-0">
            <div class = "card">
                <div class = "card-body">
                    <h5 class = "card-title">@lang('import.errors_list')</h5>
                    <p class = "card-text">@lang('import.priorties_errors_text')</p>
                    <div class = "table-responsive">
                        <table class = "table mb-0">
                            <thead>
                                <tr>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.identifier")</span>
                                    </th>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.value")</span>
                                    </th>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.error")</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(isset($errorsList) && $errorsList->count())
                                    @foreach($errorsList as $key => $data)
                                        <tr wire:key = "errors-priorities-{{ $key }}"> 
                                            <td>
                                                <p style = "font-size:11px">{{ $data->identifier ?? '-' }}</p>
                                            </td>
                                            <td>
                                                <p style = "font-size:11px">{{ $data->value ?? '-' }}</p>
                                            </td>
                                            <td>
                                                <p style = "font-size:11px">
                                                    @if(isset($data->errors))
                                                        @switch($data->errors->value)
                                                            @case(\App\Enums\ValidationBukImport::PriorityNotSaved->value)
                                                                @lang('validation_bulk_import_step.priority_not_saved')
                                                            @break

                                                            @case(\App\Enums\ValidationBukImport::PriorityNotUpdated->value)
                                                                @lang('validation_bulk_import_step.priority_not_updated')
                                                            @break

                                                            @default
                                                                -
                                                            @break
                                                        @endswitch
                                                    @else
                                                        -
                                                    @endif
                                                </p>
                                            </td>
                                        </tr>
                                    @endforeach

                                    @if ($errorsList->hasMorePages())
                                        <tr>
                                            <td colspan = "3">
                                                <div class = "d-flex justify-content-center gap-2">
                                                    <div class = "p-2">
                                                        <div wire:loading wire:target = "managePerPage">
                                                            <button type = "button" class = "btn btn-primary" wire:loading.attr = "disabled">
                                                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                                @lang('import.loading3')
                                                            </button>
                                                        </div>
                                                        <button type = "button" class = "btn btn-primary text-center" wire:target = "managePerPage" wire:click = "managePerPage" wire:loading.class = "hide">
                                                            @lang('import.load_more')
                                                        </button>
                                                    </div>
                                                    <div class = "p-2">
                                                        <div wire:loading wire:target = "manageLoadAll">
                                                            <button type = "button" class = "btn btn-info" wire:loading.attr = "disabled">
                                                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                                @lang('import.loading3')
                                                            </button>
                                                        </div>
                                                        <button type = "button" class = "btn btn-info" wire:target = "manageLoadAll" wire:click = "manageLoadAll({{ $errorsList->total() }})" wire:loading.class = "hide">
                                                            @lang('import.load_all')
                                                        </button>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endif
                                @else
                                    <tr class = "text-center">
                                        <td colspan = "3">@lang("import.empty_errors")</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class = "modal fade delete" id = "confirm-delete-priorities" tabindex = "-1" role = "dialog" aria-labelledby = "deleteModalLabel" aria-hidden = "true" wire:ignore.self>
        <div class = "modal-dialog modal-sm modal-dialog-centered" role = "document">
            <div class = "modal-content radius-xl">
                <div class = "modal-body">
                    <div class = "text-center">
                        <h1 class = "text-loss mb-4">
                            <i class = "las la-exclamation-circle fs-60"></i>
                        </h1>
                        <h5 class = "mb-3">@lang('CRMProjects.common.are_you_sure')</h5>
                        <p>
                            @lang('import.question_delete_sheet')
                            @lang('CRMProjects.common.this_action_cannot_be_undone') 
                        </p>
                    </div>
                </div>
                <div class = "modal-footer justify-content-between border-0 gap-10">
                    <button type = "button" class = "btn bg-hold-light text-white flex-fill radius-xl" data-dismiss = "modal">@lang('import.cancel')</button>
                    <div wire:loading class = "text-center" wire:target = "destroyPrioritiesList">
                        <button type = "button" class = "btn bg-loss flex-fill radius-xl text-white" wire:loading.attr = "disabled">
                            <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span> 
                            @lang('work_order.common.loading')
                        </button>
                    </div>
                    <button class = "btn bg-loss flex-fill radius-xl text-white" wire:loading.class = "hide" wire:target = "destroyPrioritiesList" wire:click = "destroyPrioritiesList()">@lang('import.delete')</button>
                </div>
            </div>
        </div>
    </div>
</div>
