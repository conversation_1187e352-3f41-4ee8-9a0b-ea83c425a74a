<div>
    <div class = "userDatatable projectDatatable project-table w-100">
        <div class = "d-flex gap-10 pb-3 mb-3 justify-content-between">
            <div class = "d-flex gap-5 align-items-center">
                <div class = "d-flex gap-5 align-items-center mx-2">
                    <i class = "iconsax text-success fs-16" icon-name = "clipboard-tick"></i> 
                    <span class = "text-success">{{ $usersList->total() }} @lang('import.inserted_rows')</span>
                </div>
                <div class = "d-flex gap-5 align-items-center">
                    <i class = "iconsax text-danger fs-16" icon-name = "info-circle"></i> 
                    <span class = "text-danger">{{ $errorsList->total() }} @lang('import.system_errors')</span>
                </div>
            </div>
            <div>
                <button type = "button" class = "btn btn-danger mx-2" data-toggle = "modal" data-target = "#confirm-delete-users">
                    @lang('import.delete_inserted_users')
                </button>
            </div>
        </div>
    </div>
    <div class = "row">
        <div class = "col-sm-6 mb-3 mb-sm-0">
            <div class = "card">
                <div class = "card-body">
                    <h5 class = "card-title">@lang('import.inserted_rows')</h5>
                    <p class = "card-text">@lang('import.users_inserted_text')</p>
                    <div class = "table-responsive" id = "users-table">
                        <table class = "table mb-0">
                            <thead>
                                <tr>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.name2")</span>
                                    </th>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.email")</span>
                                    </th>
                                    <th scope = "col">
                                        <span style = "font-size:11px">
                                            @lang("import.phone")
                                        </span>
                                    </th>
                                    <th scope = "col" class = "extra-col d-none">
                                        <span style = "font-size:11px">@lang("import.type2")</span>
                                    </th>
                                    <th scope = "col" class = "extra-col d-none">
                                        <span style = "font-size:11px">@lang("import.department")</span>
                                    </th>
                                    <th scope = "col" class = "extra-col d-none">
                                        <span style = "font-size:11px">@lang("import.company")</span>
                                    </th>
                                    <th scope = "col" class = "extra-col d-none">
                                        <span style = "font-size:11px">@lang("import.admin")</span>
                                    </th>
                                    <th>
                                        <span id = "show-action-btn" class = "mx-1" style = "text-decoration: underline; cursor: pointer;">
                                            @lang('import.more')
                                        </span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(isset($usersList) && $usersList->count())
                                    @foreach($usersList as $key => $data)
                                        <tr wire:key = "users-{{ $key }}"> 
                                            <td>
                                                <p style = "font-size:11px">{{ $data->name ?? '-' }}</p>
                                            </td>
                                            <td>
                                                <p style = "font-size:11px">{{ $data->email ?? '-' }}</p>
                                            </td>
                                            <td>
                                                <p style = "font-size:11px">{{ $data->phone }}</p>
                                            </td>
                                            <td class = "extra-row d-none">
                                                <p style = "font-size:11px">
                                                    @switch ($data->user_type) 
                                                        @case ('admin')
                                                            @lang('import.poa')
                                                        @break

                                                        @case ('admin_employee')
                                                            @lang('import.poe')
                                                        @break

                                                        @case ('building_manager')
                                                            @lang('import.bma')
                                                        @break

                                                        @case ('building_manager_employee')
                                                            @lang('import.bme')
                                                        @break

                                                        @case ('sp_admin')
                                                            @lang('import.spa')
                                                        @break

                                                        @case ('supervisor')
                                                            @lang('import.sps')
                                                        @break

                                                        @case ('sp_worker')
                                                            @lang('import.spw')
                                                        @break

                                                        @case ('tenant')
                                                            @lang('import.tenant')
                                                        @break

                                                        @default
                                                            @lang('import.ud')
                                                        @break
                                                    @endswitch
                                                </p>
                                            </td>
                                            <td class = "extra-row d-none">
                                                <p style = "font-size:11px">{{ $data->emp_dept ?? '-' }}</p>
                                            </td>
                                            <td class = "extra-row d-none">
                                                <p style = "font-size:11px">
                                                    @if(in_array($data->user_type, array('sp_admin', 'supervisor', 'sp_worker')))
                                                        {{ $data->serviceProvider->name }}-{{ $data->serviceProvider->service_provider_id }}
                                                    @else
                                                        -
                                                    @endif
                                                </p>
                                            </td>
                                            <td class = "extra-row d-none">
                                                <p style = "font-size:11px">
                                                    @if(in_array($data->user_type, array('building_manager_employee', 'supervisor', 'sp_worker')))
                                                        @switch($data->user_type)
                                                            @case('building_manager_employee')
                                                                {{ $data->adminEmployee->name ?? '-' }}  
                                                            @break

                                                            @case('supervisor')
                                                                {{ $data->adminSupervisor->name ?? '-' }}
                                                            @break

                                                            @case('sp_worker')
                                                                @if(isset($data->supervisors))
                                                                    @php
                                                                        $supervisorsList = $this->explodeDataFromField($data->supervisor_id) ?? null;
                                                                    @endphp
                                                                    @foreach ($supervisorsList as $item)
                                                                        @php
                                                                            $user = $this->getUserInformationsByValues('id', $item);
                                                                        @endphp

                                                                        {{ isset($user) ? $user->name : '-' }}

                                                                        @if(count($supervisorsList) > 1)
                                                                             |
                                                                        @endif
                                                                    @endforeach
                                                                    
                                                                @else
                                                                    -
                                                                @endif
                                                            @break
                                                        @endswitch
                                                    @else
                                                        -
                                                    @endif
                                                </p>
                                            </td>
                                        </tr>
                                    @endforeach

                                    @if ($usersList->hasMorePages())
                                        <tr>
                                            <td colspan = "8">
                                                <div class = "d-flex justify-content-center gap-2">
                                                    <div class = "p-2">
                                                        <div wire:loading wire:target = "managePerPageList">
                                                            <button type = "button" class = "btn btn-primary" wire:loading.attr = "disabled">
                                                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                                @lang('import.loading3')
                                                            </button>
                                                        </div>
                                                        <button type = "button" class = "btn btn-primary text-center" wire:target = "managePerPageList" wire:click = "managePerPageList" wire:loading.class = "hide">
                                                            @lang('import.load_more')
                                                        </button>
                                                    </div>
                                                    <div class = "p-2">
                                                        <div wire:loading wire:target = "manageLoadAllList">
                                                            <button type = "button" class = "btn btn-info" wire:loading.attr = "disabled">
                                                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                                @lang('import.loading3')
                                                            </button>
                                                        </div>
                                                        <button type = "button" class = "btn btn-info" wire:target = "manageLoadAllList" wire:click = "manageLoadAllList({{ $usersList->total() }})" wire:loading.class = "hide">
                                                            @lang('import.load_all')
                                                        </button>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endif
                                @else
                                    <tr class = "text-center">
                                        <td colspan = "8">@lang("import.empty_users")</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class = "col-sm-6 mb-3 mb-sm-0">
            <div class = "card">
                <div class = "card-body">
                    <h5 class = "card-title">@lang('import.errors_list')</h5>
                    <p class = "card-text">@lang('import.users_errors_text')</p>
                    <div class = "table-responsive">
                        <table class = "table mb-0">
                            <thead>
                                <tr>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.identifier")</span>
                                    </th>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.value")</span>
                                    </th>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.error")</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(isset($errorsList) && $errorsList->count())
                                    @foreach($errorsList as $key => $data)
                                        <tr wire:key = "errors-users-{{ $key }}"> 
                                            <td>
                                                <p style = "font-size:11px">{{ $data->identifier ?? '-' }}</p>
                                            </td>
                                            <td>
                                                <p style = "font-size:11px">{{ $data->value ?? '-' }}</p>
                                            </td>
                                            <td>
                                                <p style = "font-size:11px">
                                                    @if(isset($data->errors))
                                                        @switch($data->errors->value)
                                                            @case(\App\Enums\ValidationBukImport::EmailTaken->value)
                                                                @lang('validation_bulk_import_step.email_taken')
                                                            @break

                                                            @case(\App\Enums\ValidationBukImport::UserCompanyNotSaved->value)
                                                                @lang('validation_bulk_import_step.user_company_not_saved')
                                                            @break

                                                            @case(\App\Enums\ValidationBukImport::UserNotSaved->value)
                                                                @lang('validation_bulk_import_step.user_not_saved')
                                                            @break
                                                            
                                                            @case(\App\Enums\ValidationBukImport::NoServiceProviderAdded->value)
                                                                @lang('validation_bulk_import_step.no_service_provider_added')
                                                            @break

                                                            @case(\App\Enums\ValidationBukImport::UserNotUpdated->value)
                                                                @lang('validation_bulk_import_step.no_user_updated')
                                                            @break

                                                            @case(\App\Enums\ValidationBukImport::UserNotAffected->value)
                                                                @lang('validation_bulk_import_step.affectation_error')
                                                            @break

                                                            @default
                                                                -
                                                            @break
                                                        @endswitch
                                                    @else
                                                        -
                                                    @endif
                                                </p>
                                            </td>
                                        </tr>
                                    @endforeach

                                    @if ($errorsList->hasMorePages())
                                        <tr>
                                            <td colspan = "3">
                                                <div class = "d-flex justify-content-center gap-2">
                                                    <div class = "p-2">
                                                        <div wire:loading wire:target = "managePerPage">
                                                            <button type = "button" class = "btn btn-primary" wire:loading.attr = "disabled">
                                                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                                @lang('import.loading3')
                                                            </button>
                                                        </div>
                                                        <button type = "button" class = "btn btn-primary text-center" wire:target = "managePerPage" wire:click = "managePerPage" wire:loading.class = "hide">
                                                            @lang('import.load_more')
                                                        </button>
                                                    </div>
                                                    <div class = "p-2">
                                                        <div wire:loading wire:target = "manageLoadAll">
                                                            <button type = "button" class = "btn btn-info" wire:loading.attr = "disabled">
                                                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                                @lang('import.loading3')
                                                            </button>
                                                        </div>
                                                        <button type = "button" class = "btn btn-info" wire:target = "manageLoadAll" wire:click = "manageLoadAll({{ $errorsList->total() }})" wire:loading.class = "hide">
                                                            @lang('import.load_all')
                                                        </button>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endif
                                @else
                                    <tr class = "text-center">
                                        <td colspan = "3">@lang("import.empty_errors")</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class = "modal fade delete" id = "confirm-delete-users" tabindex = "-1" role = "dialog" aria-labelledby = "deleteModalLabel" aria-hidden = "true" wire:ignore.self>
        <div class = "modal-dialog modal-sm modal-dialog-centered" role = "document">
            <div class = "modal-content radius-xl">
                <div class = "modal-body">
                    <div class = "text-center">
                        <h1 class = "text-loss mb-4">
                            <i class = "las la-exclamation-circle fs-60"></i>
                        </h1>
                        <h5 class = "mb-3">@lang('CRMProjects.common.are_you_sure')</h5>
                        <p>
                            @lang('import.question_delete_sheet')
                            @lang('CRMProjects.common.this_action_cannot_be_undone') 
                        </p>
                    </div>
                </div>
                <div class = "modal-footer justify-content-between border-0 gap-10">
                    <button type = "button" class = "btn bg-hold-light text-white flex-fill radius-xl" data-dismiss = "modal">@lang('import.cancel')</button>
                    <div wire:loading class = "text-center" wire:target = "destroyUsersList">
                        <button type = "button" class = "btn bg-loss flex-fill radius-xl text-white" wire:loading.attr = "disabled">
                            <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span> 
                            @lang('work_order.common.loading')
                        </button>
                    </div>
                    <button class = "btn bg-loss flex-fill radius-xl text-white" wire:loading.class = "hide" wire:target = "destroyUsersList" wire:click = "destroyUsersList()">@lang('import.delete')</button>
                </div>
            </div>
        </div>
    </div>
</div>