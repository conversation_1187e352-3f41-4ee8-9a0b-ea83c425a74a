<div>
    <div class="row mt-4">
        <div class="col-sm-12 col-lg-12 col-xl-12 col-md-12">
            <div class="card">
                <div class="card-body">
                    <form wire:submit.prevent='filterCalendarData'>
                        <div class="d-flex align-items-center justify-content-end">
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12 mx-2">
                                <div class="btn-box">
                                    <label for="start_date" class="form-label">@lang('CRMProjects.common.start_date')</label>
                                    <input class="form-control datepicker allow-past-date"
                                        wire:model.defer="start_date">
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12 mx-2">
                                <div class="btn-box">
                                    <label for="end_date" class="form-label">@lang('CRMProjects.common.end_date')</label>
                                    <input class="form-control datepicker allow-past-date" wire:model.defer="end_date">
                                </div>
                            </div>
                            <div class="col-auto float-end mt-4 d-flex">
                                <button type="submit" class="btn bg-new-primary radius-xl mr-1">
                                    @lang('CRMProjects.common.search')
                                </button>
                                <button wire:click="$emit('resetDate')" type="button"
                                    class="btn bg-loss text-white radius-xl">
                                    @lang('CRMProjects.common.reset')
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-8 mt-3">
            <div class="card">
                <div class="card-header">
                    <h5>@lang('CRMProjects.common.calendar')</h5>
                </div>
                <div wire:ignore class="card-body projects-calendar">
                    <div id="task_calendar"></div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mt-3">

            <div class="card">
                <div class="card-body">
                    <h4 class="mb-4">@lang('CRMProjects.calendar.upcoming_tasks')</h4>
                    <ul class="event-cards list-group list-group-flush mt-3 w-100">
                        @forelse ($data['current_month_task'] as $item)
                            <li class="list-group-item card mb-3 events" data-event-id="{{ @$item['id'] }}">
                                <div class="row align-items-center justify-content-between">
                                    <div class="col-auto mb-3 mb-sm-0">
                                        <div class="d-flex align-items-center">
                                            <div class="theme-avtar bg-primary">
                                            </div>
                                            <div class="ms-3">
                                                <h5 class="card-text text-primary mb-1 event-title">{{ $item['title'] }}
                                                </h5>
                                                <div class="card-text text-dark">@lang('CRMProjects.common.start_date') :
                                                    <span
                                                        class="event-start">{{ \Helper::formatDateForLocale($item['start_date']) }}</span>
                                                </div>
                                                <div class="card-text text-dark">@lang('CRMProjects.common.end_date') :
                                                    <span class="event-end">
                                                        {{ \Helper::formatDateForLocale($item['due_date']) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        @empty
                            <div class="text-center">
                                <h6>@lang('CRMProjects.calendar.no_task')</h6>
                            </div>
                        @endforelse
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@include('livewire.common.super-modal-v1', [
    'component' => 'c-r-m-projects.modals.update-calendar-task',
    'modalId' => 'updateCalendarTaskModal',
])
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.17/index.global.min.js'></script>
<script src="/js/livewire/error-messages.js"></script>
<script src="/js/livewire/manage-loader.js"></script>
<script src="/js/livewire/manage-modals.js"></script>
<script src="/js/livewire/manage-datepicker.js"></script>
<script src="/js/livewire/manage-tooltip.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let calendar; // declare outside so we can access it later
        let suppressDatesSet = false;

        function initCalendar(events) {

            const calendarEl = document.getElementById('task_calendar');

            // Destroy existing calendar instance if already initialized
            if (calendar) {
                calendar.destroy();
            }

            calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,dayGridDay'
                },
                views: {
                    dayGridDay: {
                        buttonText: 'Day'
                    },
                    timeGridWeek: {
                        buttonText: 'Week'
                    },
                    dayGridMonth: {
                        buttonText: 'Month'
                    },
                },
                slotDuration: '00:10:00',
                droppable: true,
                selectable: true,
                handleWindowResize: true,
                editable: true,
                eventOverlap: false,
                events: events,
                dayMaxEvents: 1,
                eventClick: function(info) {
                    const eventId = info.event.id;
                    showLoader();
                    Livewire.emit('openUpdateCalendarTaskModal', eventId);
                },
                eventDrop: function(info) {
                    const addOneDay = (date) => {
                        let newDate = new Date(date);
                        newDate.setDate(newDate.getDate() + 1);
                        return newDate;
                    };

                    const start = addOneDay(info.event.start);
                    const end = info.event.end ? addOneDay(info.event.end) : start;
                    const eventId = info.event.id;
                    const title = info.event.title;

                    // Immediately revert the drop until we confirm with Livewire
                    info.revert();

                    Livewire.emit('calendarEventUpdated', {
                        start,
                        end,
                        eventId,
                        title
                    })
                    showLoader();
                },
                datesSet: function(info) {
                    console.log('info', info);
                    console.log('info.view.type ', info.view.type);
                    if (info.view.type == 'dayGridMonth') {
                        if (suppressDatesSet) return;
                        const centerDate = calendar.getDate(); 
                        const year = centerDate.getFullYear();
                        const month = centerDate.getMonth() + 1;
                        if (month > 12 || month < 1) return;

                        // Format YYYY-MM-DD
                        const pad = (n) => n.toString().padStart(2, '0');

                        const startDate = `${year}-${pad(month)}-01`;

                        const endDateObj = new Date(year, month,
                            0);
                        const endDate = `${year}-${pad(month)}-${pad(endDateObj.getDate())}`;

                        console.log('startDate', startDate);
                        console.log('endDate', endDate);

                        showLoader();
                        window.Livewire.emit('monthChanged', startDate, endDate);
                    }
                },
                eventDidMount: function(info) {
                    info.el.setAttribute('data-toggle', 'tooltip');
                    info.el.setAttribute('title', info.event.title);
                    info.el.setAttribute('data-original-title', info.event.title);
                }
            });

            calendar.render();
        }

        initCalendar(@json($events));

        window.Livewire.on('resetDate', function() {
            showLoader();
        });

        window.addEventListener('calendar-event-update-approved', function(e) {
            const eventId = e.detail.id;
            const event = calendar.getEventById(eventId);
            const title = e.detail.title;
            const start = e.detail.start;
            const end = e.detail.end;

            if (event) {
                event.remove();
                calendar.addEvent({
                    id: eventId,
                    title: title,
                    start: new Date(start),
                    end: end ? new Date(end) : null,
                });

                let eventDiv = $(`.events[data-event-id="${eventId}"]`);
                eventDiv.find('.event-title').text(title)
                eventDiv.find('.event-start').text(start)
                eventDiv.find('.event-end').text(end)
            }
        });
        window.addEventListener('calendar-events', function(e) {
            const calendarApi = calendar; // assuming 'calendar' is already in scope
            calendarApi.removeAllEvents();
            calendarApi.addEventSource(e.detail.events);
        });
        window.addEventListener('change-calendar-month', function(e) {
            suppressDatesSet = true;
            calendar.gotoDate(e.detail.start_date);
            suppressDatesSet = false;
        });
        window.addEventListener('remove-event', function(e) {
            var event = calendar.getEventById(e.detail.id);
            if (event) {
                event.remove();
                $(`.events[data-event-id="${e.detail.id}"]`).remove();
            }
        });
        window.addEventListener('calendar-month-refresh', function () {
    if (!calendar) return;

    const currentView = calendar.view;
    const currentStart = currentView.currentStart;

    const year = currentStart.getFullYear();
    const month = currentStart.getMonth() + 1;

    const pad = (n) => n.toString().padStart(2, '0');

    const startDate = `${year}-${pad(month)}-01`;
    const endDateObj = new Date(year, month, 0);
    const endDate = `${year}-${pad(month)}-${pad(endDateObj.getDate())}`;

    console.log('[calendar-month-refresh] Emitting monthChanged', { startDate, endDate });

    Livewire.emit('monthChanged', startDate, endDate);
});

    });
</script>
