<style>
    [dir="rtl"] {
        text-align: right;
    }

    [dir="ltr"] {
        text-align: left;
    }
</style>


<div id="invoice" dir="{{ App::getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
    <div class = "card">
        <div class = "card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
            <h6 class = "text-capitalize fw-500 mb-3 mb-sm-0">@lang('finace_manage.common.proposal')</h6>
            <h6 class = "text-capitalize fw-500 mb-3 mb-sm-0">
                {{ isset($documentDetails) && $documentDetails['status'] == 'success' ? $documentDetails['data']['proposal']['proposal_number'] : '-' }}
            </h6>
        </div>
        <hr class = "mt-1 mb-0" />
      
        <div class = "card-body px-0 pt-0">
            <div class="row px-40 py-40">
          
                <div class="col-12 d-flex justify-content-end">
                    <div class="text-end">
                        <p class="text-capitalize fw-500 mb-1 text-muted">
                            @lang('finace_manage.common.issue_date'):
                        </p>
                        <p class="fw-300 mb-0 text-muted">
                            {{ isset($documentDetails) && $documentDetails['status'] == 'success' ? $documentDetails['data']['proposal']['issue_date'] : '-' }}
                        </p>
                    </div>
                </div>
            </div>

            <div class="row">

                <div class="col-9">
                    @if (!empty($documentDetails['data']['customer']))
                        <div class="row px-40 pt-0">
                            <div class="col-md-6">
                                <h6>@lang('finace_manage.common.name')</h6>
                                <span>{{ $documentDetails['data']['customer']['name'] ?? '' }}</span>
                            </div>
                            <div class="col-md-6">
                                <h6>@lang('finace_manage.common.email')</h6>
                                <span>{{ $documentDetails['data']['customer']['email'] ?? '' }}</span>
                            </div>
                        </div>

                        <div class="row mt-3 px-40 pt-0">
                            <div class="col-md-4">
                                <h6>@lang('finace_manage.common.billed_to')</h6>
                                <p class="mb-1">
                                    {{ $documentDetails['data']['customer']['billing_name'] ?? '' }}</p>
                                <p class="mb-1">
                                    {{ $documentDetails['data']['customer']['billing_address'] ?? '' }}</p>
                                <p class="mb-1">
                                    {{ $documentDetails['data']['customer']['billing_city'] ?? '' }},
                                    {{ $documentDetails['data']['customer']['billing_state'] ?? '' }},
                                    {{ $documentDetails['data']['customer']['billing_zip'] ?? '' }}
                                </p>
                                <p class="mb-1">
                                    {{ $documentDetails['data']['customer']['billing_country'] ?? '' }}</p>
                                <p class="mb-1">
                                    {{ $documentDetails['data']['customer']['billing_phone'] ?? '' }}</p>
                              {{--   <h6>@lang('finace_manage.common.tax_number')</h6>
                                <p class="mb-1">
                                    {{ $documentDetails['data']['customer']['tax_number'] ?? '' }}</p> --}}
                            </div>


                            <div class="col-md-4">
                                <h6>@lang('finace_manage.common.shipped_to')</h6>
                                <p class="mb-1">
                                    {{ $documentDetails['data']['customer']['shipping_name'] ?? '' }}</p>
                                <p class="mb-1">
                                    {{ $documentDetails['data']['customer']['shipping_address'] ?? '' }}</p>
                                <p class="mb-1">
                                    {{ $documentDetails['data']['customer']['shipping_city'] ?? '' }},
                                    {{ $documentDetails['data']['customer']['shipping_state'] ?? '' }},
                                    {{ $documentDetails['data']['customer']['shipping_zip'] ?? '' }}
                                </p>
                                <p class="mb-1">
                                    {{ $documentDetails['data']['customer']['shipping_country'] ?? '' }}</p>
                                <p class="mb-1">
                                    {{ $documentDetails['data']['customer']['shipping_phone'] ?? '' }}</p>
                                <h6>@lang('finace_manage.common.tax_number')</h6>
                                <p class="mb-1">
                                    {{ $documentDetails['data']['customer']['tax_number'] ?? '' }}</p>
                            </div>


                        </div>
                    @endif
                </div>
                <div class="col-3">
                    <img id="qr-code" src="data:image/png;base64,{{ $qrCodeBase64 }}" alt="{{ $qrCodeData }}"
                        class="img-fluid" style="max-width: 100%; height: auto;" crossorigin="anonymous"
                        title="{{ $qrCodeData }}" />
                </div>

            </div>
            <div class = "userDatatable projectDatatable project-table bg-white w-100 border-0">




                <div class = "px-3 mt-3">
                    <p class = "mb-1"> @lang('finace_manage.common.status'):</p>
                    @if (isset($documentDetails) && $documentDetails['status'] == 'success')
                        @switch($documentDetails['data']['proposal']['status'])
                            @case('Draft')
                                <span
                                    class = "bg-opacity-primary p-2 px-3 rounded-pill text-primary d-inline-block">@lang('finace_manage.common.draft')</span>
                            @break

                            @case('Open')
                                <span
                                    class = "bg-opacity-warning p-2 px-3 rounded-pill text-warning d-inline-block">@lang('finace_manage.common.open')</span>
                            @break

                            @case('Accepted')
                                <span
                                    class = "bg-opacity-success p-2 px-3 rounded-pill text-success d-inline-block">@lang('finace_manage.common.accepted')</span>
                            @break

                            @case('Declined')
                                <span
                                    class = "bg-opacity-light p-2 px-3 rounded-pill text-light d-inline-block">@lang('finace_manage.common.declined')</span>
                            @break

                            @case('Close')
                                <span
                                    class = "bg-opacity-danger p-2 px-3 rounded-pill text-danger d-inline-block">@lang('finace_manage.common.close')</span>
                            @break

                            @default
                                <span
                                    class = "bg-opacity-danger p-2 px-3 rounded-pill text-danger d-inline-block">{{ $documentDetails['data']['proposal']['status'] }}
                                </span>
                            @break
                        @endswitch
                    @else
                        -
                    @endif
                </div>
                <div class = "px-3 mt-3">
                    <p class = "text-capitalize fw-500 mb-3 mb-sm-0 text-muted">@lang('finace_manage.common.item_summary')</p>
                    <p class = "fw-300 mb-3 mb-sm-0 text-muted">@lang('finace_manage.common.items_cannot_be_deleted').</p>
                </div>
                {{-- <div class = "card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
            <h6 class = "text-capitalize fw-500 mb-3 mb-sm-0">@lang('finace_manage.common.items')</h6>
        </div> --}}
                <div class = "card-body px-0 pt-0">
                    <div class = "userDatatable projectDatatable project-table bg-white w-100 border-0">
                        <div class = "table-responsive">
                            <table class = "table mb-0 radius-0 table-border">
                                <thead>
                                    <tr>
                                        <th>
                                            #
                                        </th>
                                        <th>
                                            @lang('finace_manage.common.item_type')
                                        </th>
                                        <th>
                                            @lang('finace_manage.common.items')
                                        </th>
                                        <th>
                                            @lang('finace_manage.common.quantity')
                                        </th>
                                        <th>
                                            @lang('finace_manage.common.price')
                                        </th>
                                        <th>
                                            @lang('finace_manage.common.discount')
                                        </th>
                                        <th>
                                            @lang('finace_manage.common.tax_percent')
                                        </th>
                                        <th>
                                            @lang('finace_manage.common.description')
                                        </th>
                                        <th style = "line-height: 13px;">
                                            <span>@lang('finace_manage.common.price')</span>
                                            <div>
                                                <small>@lang('finace_manage.common.after_discount_tax')</small>
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (isset($documentDetails) && $documentDetails['status'] == 'success')
                                        @if (isset($documentDetails['data']['items']) && count($documentDetails['data']['items']) > 0)
                                            @foreach ($documentDetails['data']['items'] as $key => $value)
                                                <tr style = "opacity: 1;">
                                                    <td>{{ $key + 1 }}</td>
                                                    <td> - </td>
                                                    <td>{{ $value['project_name'] ?? '-' }}</td>
                                                    <td>{{ $value['quantity'] ?? '-' }}</td>
                                                    <td>{{ $value['price'] ?? '-' }} @lang('finace_manage.common.sar')</td>
                                                    <td>{{ $value['discount'] ?? '-' }} @lang('finace_manage.common.sar')</td>
                                                    <td>
                                                        {{ $value['tax'][$key]['amount'] ?? '-' }} @lang('finace_manage.common.sar')
                                                    </td>
                                                    <td>
                                                        <span>{{ $value['description'] ?? '-' }}</span>
                                                    </td>
                                                    <td>
                                                        @php
                                                            $priceAfterDiscountTax =
                                                                $value['price'] * $value['quantity'] -
                                                                $value['discount'] +
                                                                ($value['tax'][$key]['amount'] ?? 0);
                                                        @endphp

                                                        {{ $priceAfterDiscountTax ?? '-' }} @lang('finace_manage.common.sar')
                                                    </td>
                                                </tr>
                                            @endforeach
                                            <tr>
                                                <td colspan = "8">
                                                    <div class = "d-flex justify-content-end pb-2 border-bottom">
                                                        <div class = "d-flex">
                                                            <table class = "table table-border mb-0 table-price">
                                                                <tbody>
                                                                    <tr class="border-bottom">
                                                                        <th>@lang('finace_manage.common.sub_total')</th>
                                                                        <td>{{ $documentDetails['data']['sub_total'] ?? '-' }}
                                                                            @lang('finace_manage.common.sar')</td>
                                                                    </tr>
                                                                    <tr class = "border-bottom">
                                                                        <th>@lang('finace_manage.common.discount')</th>
                                                                        <td>{{ $documentDetails['data']['discount'] ?? '-' }}
                                                                            @lang('finace_manage.common.sar')</td>
                                                                    </tr>
                                                                    <tr class = "border-bottom">
                                                                        <th>@lang('finace_manage.common.tax')</th>
                                                                        <td>
                                                                            <ul>
                                                                                <li class = "fw-bold">
                                                                                    @php
                                                                                        $tax = 0;

                                                                                        foreach (
                                                                                            $documentDetails['data'][
                                                                                                'tax'
                                                                                            ]
                                                                                            as $data
                                                                                        ) {
                                                                                            $tax =
                                                                                                $tax + $data['amount'];
                                                                                        }
                                                                                    @endphp

                                                                                    {{ $tax }}
                                                                                    @lang('finace_manage.common.sar')
                                                                                </li>
                                                                            </ul>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th> @lang('finace_manage.common.total_amount')</th>
                                                                        <td>{{ $documentDetails['data']['total'] ?? '-' }}
                                                                            @lang('finace_manage.common.sar')</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endif
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>

<script>
    // async function downloadPDF() {
    //   const { jsPDF } = window.jspdf;
    //   const pdf = new jsPDF({
    //     unit: 'pt',
    //     format: 'a4',
    //     orientation: 'landscape', 
    //     putOnlyUsedFonts: true
    //   });

    //   const element = document.getElementById('invoice');
    //   const canvas = await html2canvas(element, { scale: 2 });
    //   const imgData = canvas.toDataURL('image/png');

    //   const imgProps = pdf.getImageProperties(imgData);
    //   const pdfWidth = pdf.internal.pageSize.getWidth();
    //   const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

    //   pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);

    //   pdf.save('proposal.pdf');
    // }




    async function downloadPDF() {
        const {
            jsPDF
        } = window.jspdf;
        const pdf = new jsPDF({
            unit: 'pt',
            format: 'a4',
            orientation: 'portrait',
            putOnlyUsedFonts: true
        });

        const element = document.getElementById('invoice');

        const canvas = await html2canvas(element, {
            scale: 2,
            useCORS: true
        });

        const imgData = canvas.toDataURL('image/png');
        const imgProps = pdf.getImageProperties(imgData);

        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();

        const imgHeight = (imgProps.height * pdfWidth) / imgProps.width;
        let heightLeft = imgHeight;
        let position = 0; // Pas de décalage, plus de texte en haut
        pdf.setFontSize(24);
        // Première page
        pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, imgHeight);
        heightLeft -= (pdfHeight - position);

        // Pages suivantes si nécessaire
        while (heightLeft > 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, imgHeight);
            heightLeft -= pdfHeight;
        }

        pdf.save('proposal.pdf');
    }
</script>
