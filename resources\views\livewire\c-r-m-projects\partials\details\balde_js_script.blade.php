<script src="{{ asset('vendor_assets/js/Chart.min.js') }}"></script>
<script src="{{ asset('js/charts_dashboard.js') }}"></script>
<script src="{{ asset('js/admin/crmProjects/projectDetails.js') }}"></script>
<script>
function projectdetailsLoader(routeName) {
    // Show loader
    document.getElementById("overlayer").style.display = "block";
    let loaderElements = document.getElementsByClassName("loader-overlay");
    if (loaderElements.length > 0) {
        loaderElements[0].style.display = "block";
    }

    // Build the URL dynamically
    const newUrl = `/${routeName}`;

    // Update browser history (without reloading)
    window.history.pushState({ path: newUrl }, '', newUrl);
}
function projectLoader(id, routeName = 'details') {
    // Show loader
    document.getElementById("overlayer").style.display = "block";
    let loaderElements = document.getElementsByClassName("loader-overlay");
    if (loaderElements.length > 0) {
        loaderElements[0].style.display = "block";
    }

    // Build the URL dynamically using route pattern
    const newUrl = `/${routeName}/${id}`;

    // Push to browser history without reload
    window.history.pushState({ id: id }, '', newUrl);
}
    let currentLanguage = @json(app()->getLocale());
    const statuses = {
        en: {
            Todo: 'Todo',
            InProgress: 'In Progress',
            Review: 'Review',
            Done: 'Done'
        },
        ar: {
            Todo: 'مايجب القيام به',
            InProgress: 'جاري التقدم',
            Review: 'مراجعة',
            Done: 'تم'
        }
    };
    let status = statuses[currentLanguage];
    var chartData = @json($chartData);
    // Default colors for the chart datasets
    const defaultColors = ["#77b6ea", "#545454", "#3cb8d9", "#37b37e"];
    var daysOfWeek = @json(__('CRMProjects.common.days'));

    function getDatasetColor(index) {
        return chartData.color && chartData.color[index] ? chartData.color[index] : defaultColors[index];
    }

    function getDatasetLabel(key) {
        return chartData.stages && chartData.stages.hasOwnProperty(key) ?
            chartData.stages[key] :
            '---';
    }


    renderChart();

    function renderChart() {
        var ctx = document.getElementById("tasksOverviewChart").getContext("2d");
        if (window.myChart) {
            window.myChart.destroy();
        }
        var datasets = [];
        var stageKeys = Object.keys(chartData.stages);
        let maxYValue = 0;
            stageKeys.forEach((stageId, index) => {
                const data = chartData[stageId] || [0, 0, 0, 0, 0, 0, 0];
                const localMax = Math.max(...data);
                if (localMax > maxYValue) {
                    maxYValue = localMax;
                }
                datasets.push({
                    label: getDatasetLabel(stageId),
                    borderColor: getDatasetColor(index),
                    backgroundColor: "transparent",
                    data: data,
                    lineTension: 0.3
                });
            });
        var dayLabels = [
            daysOfWeek.mon,
            daysOfWeek.tue,
            daysOfWeek.wed,
            daysOfWeek.thu,
            daysOfWeek.fri,
            daysOfWeek.sat,
            daysOfWeek.sun
        ] || daysOfWeek;

        window.myChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: dayLabels,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                rtl: true,
                legend: {
                    display: false
                },
                scales: {
                    xAxes: [{
                        display: true,
                        ticks: {
                            direction: 'rtl',
                            beginAtZero: true,
                        }
                    }],
                    yAxes: [{
                        display: true,
                          
                        ticks: {
                            beginAtZero: true,
                            min: 0,
                            max: Math.max(10, maxYValue),
                        }
                    }]
                },
                plugins: {
                    tooltip: {
                        rtl: true
                    }
                }
            }
        });
    }


    function initializeDatepicker(id, livewireModel) {
        var element = $('#' + id);

        if (element.length) {
            element.datepicker({
                format: "yyyy-mm-dd",
                autoclose: true,
                todayHighlight: true,
            }).on('change', function(e) {

                @this.set(livewireModel, e.target.value);
            });
        } else {
            console.log('Element with ID ' + id + ' does not exist.');
        }
    }

    function initializeSelect2(id, livewireModel, placeholder = "{{ __('CRMProjects.common.please_select') }}") {
        var element = $('#' + id);

        if (element.length) {
            element.select2({
                placeholder: placeholder,
                allowClear: true
            });

            element.on('change', function() {
                @this.set(livewireModel, $(this).val());
            });

        } else {
            console.warn(`Element with ID '${id}' does not exist.`);
        }
    }




   document.addEventListener("livewire:load", function() {

            Livewire.hook('message.processed', () => {
                console.log('Livewire Updated detailsss');
                renderChart();
                initializeSelect2('selectedUsersForInvite', 'selectedUsersForInvite',
                    "{{ __('CRMProjects.common.choose_user') }}");
                initializeSelect2('selectedvendorsForShare', 'selectedvendorsForShare',
                    "{{ __('CRMProjects.common.choose_vendor') }}");
                initializeSelect2('selectedclientsForShare', 'selectedclientsForShare',
                    "{{ __('CRMProjects.common.choose_client') }}");
                initializeSelect2('selectedBuildingsManager', 'selectedBuildingsManager',
                    "{{ __('CRMProjects.common.please_select') }}");
                console.log('Livewire Updated 33');
                /* Check  */
                initializeDatepicker('start_date', 'start_date');
                initializeDatepicker('end_date', 'end_date');

            });


        });
    document.addEventListener("DOMContentLoaded", function() {
 
     

    });
    window.addEventListener('copy-url-to-clipboard', event => {
        const {
            url,
            successMessage,
            errorMessage
        } = event.detail;

        navigator.clipboard.writeText(url).then(() => {
            window.dispatchEvent(new CustomEvent('show-toastr', {
                detail: {
                    type: 'success',
                    message: successMessage
                }
            }));
        }).catch(() => {
            window.dispatchEvent(new CustomEvent('show-toastr', {
                detail: {
                    type: 'error',
                    message: errorMessage
                }
            }));
        });
    });

    
        document.addEventListener('DOMContentLoaded', function () {
            const textarea = document.getElementById('edit_description');
            const counter = document.getElementById('counter_edit');
            const maxLength = 5000;
    
            function updateCounter() {
                let currentLength = textarea.value.length;
    
                if (currentLength > maxLength) {
                    textarea.value = textarea.value.substring(0, maxLength);
                    currentLength = maxLength;
                }
    
                counter.textContent = currentLength + '/' + maxLength;
            }
    
         
            updateCounter();
    
           
            textarea.addEventListener('input', updateCounter);
        });
    
</script>
