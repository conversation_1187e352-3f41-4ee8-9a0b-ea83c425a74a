<div class="table-responsive">
    <div class="card">
        <div class="">
            <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize fw-500 mb-3 mb-sm-0">@lang('CRMProjects.common.projects')</h6>

                <div class="d-flex gap-10 table-search">
                    {{--  <div class="position-relative">
                        <input type="text" class="form-control" placeholder="@lang('CRMProjects.common.search')" >
                        <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                    </div> --}}
                    {{--  <button class="btn bg-grey text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i>
                        @lang('CRMProjects.common.export')</button> --}}
                </div>
            </div>
        </div>
        <div class="card-body px-0 pt-0">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0">
                        <thead>
                            <tr class="userDatatable-header">
                                
                                <th wire:click="sortBy('project_type')" style="cursor: pointer;">
                                    @lang('CRMProjects.project_type')
                                    @if ($sortField == 'project_type')
                                        @if ($sortDirection == 'asc')
                                            ▲
                                        @else
                                            ▼
                                        @endif
                                    @endif
                                </th>
                                <th wire:click="sortBy('id')" style="cursor: pointer;">
                                    @lang('CRMProjects.common.name')
                                    @if ($sortField == 'id')
                                        @if ($sortDirection == 'asc')
                                            ▲
                                        @else
                                            ▼
                                        @endif
                                    @endif
                                </th>
                                <th wire:click="sortBy('priority_level')" style="cursor: pointer;">
                                    @lang('CRMProjects.priority_level')
                                    @if ($sortField == 'priority_level')
                                        @if ($sortDirection == 'asc')
                                            ▲
                                        @else
                                            ▼
                                        @endif
                                    @endif
                                </th>
                              
                                <th>
                                    @lang('CRMProjects.common.stage')
                                </th>
                                <th>
                                    @lang('CRMProjects.common.start_date')
                                </th>
                                <th>
                                    @lang('CRMProjects.common.end_date')
                                </th>
                                <th>
                                    @lang('CRMProjects.common.assign_user')
                                </th>
                                <th>
                                    @lang('CRMProjects.common.action')
                                </th>
                            </tr>
                        </thead>

                        <tbody class="sort-table ui-sortable">
                            @if (count($projects) > 0)
                                @foreach ($projects as $project)
                                    <tr class="ui-sortable-handle" style="opacity: 1;">
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>@if(!empty($project['project_type'])) @lang('CRMProjects.project_type_dropdown.' . $project['project_type']) @endif</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{{ $project['name'] . '-' . $project['id'] }} </span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>@if(!empty($project['priority_level'])) @lang('CRMProjects.priority_level_dropdown.' . $project['priority_level']) @endif</span>
                                            </div>
                                        </td>
                                        <td>
                                            @if ($project['status'] == 'Draft')
                                                <small class="py-1 px-2 bg-draft rounded text-dark">
                                                    @lang('CRMProjects.common.draft')
                                                </small>
                                            @elseif ($project['status'] == 'OnHold')
                                                <small class="py-1 px-2 bg-warning rounded text-white">
                                                    @lang('CRMProjects.common.onhold')
                                                </small>
                                            @elseif($project['status'] == 'Finished')
                                                <small class="py-1 px-2 bg-win  rounded text-white">
                                                    @lang('CRMProjects.common.finished')
                                                </small>
                                            @else
                                                <small class="py-1 px-2 bg-hold rounded text-white">
                                                    @lang('CRMProjects.common.ongoing')
                                                </small>
                                            @endif

                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{{ $project['start_date'] }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{{ $project['end_date'] }}</span>
                                            </div>
                                        </td>
                                        <td>



                                            <div class="profile-group ml-3">
                                                @php
                                                    $usersToShow = 4;
                                                    $userCount = count($project['users'] ?? []);
                                                @endphp
                                                @foreach ($project['users'] ?? [] as $index => $user)
                                                    @if ($index < $usersToShow)
                                                        <div class="profile">
                                                            <img src="{{ $user['avatar'] }}" data-toggle="tooltip"
                                                                title="{{ $user['name'] }}" alt="{{ $user['name'] }}" />
                                                        </div>
                                                    @endif
                                                @endforeach
                                                @if ($userCount > $usersToShow)
                                                    <div class="profile extra d-center">
                                                        <span class="">+{{ $userCount - $usersToShow }}</span>
                                                    </div>
                                                @endif

                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-inline-block">
                                                @if (auth()->user()->user_type != 'building_manager')
                                                    <ul class="mb-0 d-flex flex-wrap gap-10">
                                                        <li>
                                                            <a href="javascript:void(0);"
                                                                 onclick="showLoader(); @this.openProjectDetails('{{ Crypt::encrypt($project['id']) }}')">
                                                                <i class="iconsax icon text-osool fs-18"
                                                                    icon-name="eye"></i>
                                                        </a>
                                                        </li>
                                                        <li>
                                                            <a href="javascript:void(0);"
                                                                wire:click="openEditModal({{ $project['id'] }})">
                                                                <i class="iconsax icon text-new-primary fs-18"
                                                                    icon-name="edit-1"></i>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="javascript:void(0);"
                                                                wire:click="openDeleteModal({{ $project['id'] }}, '{{ $project['name'] }}')">
                                                                <i class="iconsax icon text-delete fs-18"
                                                                    icon-name="trash"></i>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="javascript:void(0);"
                                                                wire:click="openShareToVendorModal({{ $project['id'] }}, '{{ json_encode($project['vendors'] ?? []) }}')">
                                                                <i class="iconsax icon text-osool fs-18 mr-0"
                                                                    icon-name="change-shape-2"></i>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            @else
                                <tr>

                                    <td colspan="7">
                                        <div class="form-group col-12">
                                            <div class="item-inner">
                                                <div class="item-content">
                                                    <div class="image-upload border-0 radius-xl">
                                                        <label class="mb-2 mt-4">
                                                            <div class="h-100">
                                                                <div class="dplay-tbl">
                                                                    <div class="dplay-tbl-cell">
                                                                        <div
                                                                            class="upload-bg wh-50 d-flex-center rounded-circle mx-auto position-relative">
                                                                            <i class="iconsax icon fs-22 fs-22 mr-0 text-dark position-relative"
                                                                                icon-name="emoji-sad"></i>
                                                                        </div>
                                                                        <p class="drag_drop_txt mt-3">
                                                                            @lang('CRMProjects.common.no_projects_yet')</p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endif

                        </tbody>
                    </table>

                </div>
                @if (count($projects) > 0)
                    @php
                        $perPageOptions = [8, 12, 16];
                    @endphp
                    @include('livewire.c-r-m-projects.partials.paginator')
                @endif
            </div>
        </div>
    </div>


</div>
