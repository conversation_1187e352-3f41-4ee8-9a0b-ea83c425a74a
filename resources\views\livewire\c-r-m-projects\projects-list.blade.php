<div>
    <div class="contents crm">
        <div class="container-fluid">
            <div class="col-lg-12">
                <div class="row justify-content-sm-between align-items-center justify-content-center">
                    <div class="page-title-wrap">
                        <div class="page-title d-flex justify-content-between">
                            <div
                                class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                                <div class="user-member__title mr-sm-25 ml-0">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                        @lang('CRMProjects.common.manage_projects')
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div>
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a href="{{ route('admin.dashboard') }}"> @lang('CRMProjects.common.dashboard')</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>@lang('CRMProjects.common.projects')</a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <ul class="nav nav-tabs site-pills bg-white p-1 rounded mb-3 mb-sm-0" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <a class="nav-link rounded {{ $status === null ? 'active' : 'text_black' }}" href="#"
                                wire:click.prevent="filterByStatus(null)">
                                @lang('CRMProjects.common.all')
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link rounded {{ $status === 'Draft' ? 'active' : 'text_black' }}" href="#"
                                wire:click.prevent="filterByStatus('Draft')">
                                @lang('CRMProjects.common.draft')
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link rounded {{ $status === 'ongoing' ? 'active' : 'text_black' }}" href="#"
                                wire:click.prevent="filterByStatus('ongoing')">
                                @lang('CRMProjects.common.ongoing')
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link rounded {{ $status === 'finished' ? 'active' : 'text_black' }}" href="#"
                                wire:click.prevent="filterByStatus('finished')">
                                @lang('CRMProjects.common.finished')
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link rounded {{ $status === 'onhold' ? 'active' : 'text_black' }}" href="#"
                                wire:click.prevent="filterByStatus('onhold')">
                                @lang('CRMProjects.common.onhold')
                            </a>
                        </li>
                    </ul>

                    <div class="d-flex gap-10 breadcrumb_right_icons">
                        <div class="d-flex gap-10 breadcrumb_right_icons">
                            <div class="dropdown slide-dropdown ">
                                <button class="btn btn-white btn-default text-center svg-20 wh-45 dropdown-toggle"
                                    data-toggle="dropdown" aria-expanded="true">
                                    <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="sort"></i>
                                </button>



                                <div class="dropdown-menu market-dropdown filter-dropdown px-3 text-osool"
                                    aria-labelledby="dropdownMenuButton" data-auto-close="false"
                                    x-placement="bottom-start">
                                    <h5 class="fs-14 fw-500 text-osool">@lang('CRMProjects.common.start_end_date')</h5>
                                    <div class="dropdown-divider my-2"></div>
                                    <form class="fs-14" id="dateFilterForm">
                                        <div class="form-group">
                                            <label for="">@lang('CRMProjects.common.start_date')</label>
                                            <div class="position-relative">
                                                <input type="text" name="filter_start_date" id="filter_start_date"
                                                    inline-datepicker="" type="text" datepicker-autohide
                                                    class="form-control datepicker" aria-describedby="emailHelp"
                                                    placeholder="@lang('CRMProjects.common.enter_start_date')"
                                                    wire:model.defer="filter_start_date">
                                                <i class="iconsax field-icon text-light" icon-name="calendar-1"></i>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="">@lang('CRMProjects.common.end_date')</label>
                                            <div class="position-relative">
                                                <input type="text" class="form-control datepicker"
                                                    name="filter_end_date" id="filter_end_date"
                                                    placeholder="@lang('CRMProjects.common.enter_end_date')" wire:model.defer="filter_end_date">
                                                <i class="iconsax field-icon text-light" icon-name="calendar-1"></i>
                                            </div>
                                        </div>

                                        <div class="d-flex gap-10">
                                            <button type="button" class="btn bg-loss text-white radius-xl "
                                                wire:click="resetDateFilter">@lang('CRMProjects.common.reset')</button>
                                            <button type="submit"
                                                class="btn bg-new-primary text-white radius-xl flex-fill"
                                                wire:click="submitDateFilter">@lang('CRMProjects.common.submit')</button>
                                        </div>
                                    </form>


                                </div>

                            </div>



                            {{--  <button class="btn btn-white btn-default text-center svg-20 wh-45" data-toggle="tooltip"
                                title="{{ $isRefreshButton ? __('CRMProjects.common.clear_filter ') : __('CRMProjects.common.refrech') }}"
                                wire:click="handleButtonClickResetOrRefresh">
                                <i class="iconsax icon text-new-primary fs-22 colorRed mr-0"
                                    icon-name="{{ $isRefreshButton ? 'filter-x' : 'refresh' }}">
                                </i>
                            </button> --}}


                            @if ($viewMode == 'cards')
                                <button class="btn btn-white btn-default text-center svg-20 wh-45"
                                    wire:click="switchView('table')">
                                    <i class="iconsax icon text-new-primary fs-22 colorRed mr-0"
                                        icon-name="hamburger-menu"></i>
                                </button>
                            @endif

                            @if ($viewMode == 'table')
                                <button class="btn btn-white btn-default text-center svg-20 wh-45"
                                    wire:click="switchView('cards')">
                                    <i class="iconsax icon text-new-primary fs-22 colorRed mr-0"
                                        icon-name="layout-3"></i>
                                </button>
                            @endif

                            @if(auth()->user()->user_type != 'building_manager')
                            <button wire:click="openCreateModal()" class="btn btn-default btn-primary w-100 no-wrap"
                                type="button" aria-expanded="false"><i
                                    class="las la-plus fs-16"></i>@lang('CRMProjects.common.create_project')</button>
                            @endif
                        </div>
                    </div>

                </div>
            </div>


            @if ($viewMode === 'cards')
                @include('livewire.c-r-m-projects.partials.cards-view')
            @else
                @include('livewire.c-r-m-projects.partials.table-view')
            @endif

        </div>
    </div>
    {{--  @livewire('c-r-m-projects.modals.delete-confirm') --}}
    @include('livewire.c-r-m-projects.modals.delete-confirm')

    @include('livewire.c-r-m-projects.modals.create')
    @include('livewire.c-r-m-projects.modals.edit')
    @include('livewire.c-r-m-projects.modals.inviteUser')
    @include('livewire.c-r-m-projects.modals.shareClient')
    @include('livewire.c-r-m-projects.modals.shareVendors')
    @include('livewire.c-r-m-projects.modals.duplication')
    @include('livewire.c-r-m-projects.modals.saveProjectAsTemplate')



<script src="{{ asset('vendor_assets/js/Chart.min.js') }}"></script>
<script src="{{ asset('js/charts_dashboard.js') }}"></script>
<script src="{{ asset('js/admin/crmProjects/projectDetails.js') }}"></script>
    <script>
        function projectdetailsLoader(routeName) {
    // Show loader
    document.getElementById("overlayer").style.display = "block";
    let loaderElements = document.getElementsByClassName("loader-overlay");
    if (loaderElements.length > 0) {
        loaderElements[0].style.display = "block";
    }

    // Build the URL dynamically
    const newUrl = `/${routeName}`;

    // Update browser history (without reloading)
    window.history.pushState({ path: newUrl }, '', newUrl);
}
function projectLoader(id, routeName = 'details') {
    // Show loader
    document.getElementById("overlayer").style.display = "block";
    let loaderElements = document.getElementsByClassName("loader-overlay");
    if (loaderElements.length > 0) {
        loaderElements[0].style.display = "block";
    }

    // Build the URL dynamically using route pattern
    const newUrl = `/${routeName}/${id}`;

    // Push to browser history without reload
    window.history.pushState({ id: id }, '', newUrl);
}
        function initializeDatepicker(id, livewireModel) {
            var element = $('#' + id);


            if (element.length) {
                element.datepicker({
                    format: "yyyy-mm-dd",
                    autoclose: true,
                    todayHighlight: true,
                }).on('change', function(e) {

                    @this.set(livewireModel, e.target.value);
                });
            } else {
                console.log('Element with ID ' + id + ' does not exist.');
            }
        }

        function initializeSelect2(id, livewireModel, placeholder = "{{ __('CRMProjects.common.please_select') }}") {
            var element = $('#' + id);

            if (element.length) {
                element.select2({
                    placeholder: placeholder,
                    allowClear: true
                });

                element.on('change', function() {
                    @this.set(livewireModel, $(this).val());
                });

            } else {
                console.warn(`Element with ID '${id}' does not exist.`);
            }
        }





        document.addEventListener("DOMContentLoaded", function() {

            /*   if (!$('#usersListforcreateProject').hasClass('select2-hidden-accessible')) {
                  $('#usersListforcreateProject').select2({
                      placeholder: '@lang('CRMProjects.common.choose_user')'
                  });
              }

              // Sync Select2 changes with Livewire model
              $('#usersListforcreateProject').on('change', function() {
                  // Get the selected values
                  let selectedValues = $(this).val();

                  // Update Livewire's model
                  @this.set('UsersforCreateProject', selectedValues);
              }); */
            document.addEventListener("livewire:load", function() {

                Livewire.hook('message.processed', () => {
                    console.log('Livewire Updated');

                    initializeDatepicker('start_date', 'start_date');
                    initializeDatepicker('end_date', 'end_date');
                    initializeSelect2('usersListforcreateProject', 'UsersforCreateProject',
                        "{{ __('CRMProjects.common.choose_user') }}");
                    initializeSelect2('usersListforeditProject', 'UsersforEditProject',
                        "{{ __('CRMProjects.common.choose_user') }}");
                    initializeSelect2('selectedUsersForInvite', 'selectedUsersForInvite',
                        "{{ __('CRMProjects.common.choose_user') }}");
                    initializeSelect2('selectedvendorsForShare', 'selectedvendorsForShare',
                        "{{ __('CRMProjects.common.choose_vendor') }}");
                    initializeSelect2('selectedclientsForShare', 'selectedclientsForShare',
                        "{{ __('CRMProjects.common.choose_client') }}");
                    assignAvatarColors();
                    console.log('Livewire Updated');

                });

                Livewire.on("updateUrl", data => {
                    let url = new URL(window.location.href);
                    url.searchParams.set("page", data.page);
                    window.history.pushState({}, "", url);
                    Livewire.emit("currentPage", data); // Send event to Livewire

                });

            });

        });

function setdate(key,id) {
    const selectedDate = document.getElementById(id).value;
    Livewire.find('{{ $this->id }}').set(key, selectedDate);
    //console.log('>>>>>>>sdfgh>>>>>>>>>>',key, selectedDate);
}

    </script>

    <script src="{{ asset('js/admin/crmProjects/projectsList.js') }}"></script>


</div>
