deal and lead same changes if the compoent is common 
{
    "status": "success",
    "data": {
        "lead": {
            "id": 1,
            "name": "Lead-name",
            "email": "<EMAIL>",
            "subject": "lead-subject",
            "order": 0,
            "phone": "9898454556",
            "is_active": 1,
            "pipeline": "Sales",
            "pipeline_id": "1 \\ id of pipeline",
            "notes": "write notes",
            "date": "2023-10-04",
            "subtas"
            "products": [
                {
                    "id": 1,
                    "name": "product-name",
                    "image": "https:\/\/example.com\/Modules\/ProductService\/Resources\/assets\/image\/img01.jpg"
                }
            ],
            "sources": [
                {
                    "id": 1,
                    "name": "source-name"
                }
            ],
            "tasks": [
                {
                    "id": 1,
                    "name": "task 1",
                    "date": "2024-04-22",
                    "time": "14:36:00",
                    "priority": "Low",
                    "status": "On Going"
                }
            ],
            "calls": [
                {
                    "id": 1,
                    "subject": "Call subject",
                    "call_type": "outbound",
                    "duration": "00:30:42",
                    "lead_call_user": "User-name",
                    "crated_at" : "NEW",
                    "user_name" : "NEW the user who assigned to this call "
                }
            ],
            "emails": [
                {
                    "id": 1,
                    "subject": "Email-subject",
                    "to": "<EMAIL>",
                    "diff_time": "5 days ago"
                }
            ],
            "stage": "Draft",
            "users": [
                {
                    "id": 1,
                    "name": "User-name",
                    "avatar": "https:\/\/example.com\/uploads\/users-avatar\/avatar.png"
                }
            ],
            "discussion": [
                {
                    "id": 1,
                    "comment": "User-coment",
                    "user_name": "User-name",
                    "user_type": "client",
                    "user_avatar": "https:\/\/example.com\/uploads\/users-avatar\/avatar.png"
                }
            ],
            "files": [
                {
                    "id": 1,
                    "file_name": "NEW",
                    "created _at": "NEW",
                    "file_path": "https:\/\/example.com\/uploads\/leads\/filename.png"
                }
            ],
            "precentage": "17",
            "activities": 
                [
                    //array of object for activity on the lead
                ]
               
            
        },
        "stages_list":
           [ 
            // array of object for all stages with stage name and id
           ]
        ,
        "stafs_user":
           [ 
            // user with type staf (name, id)
           ]
        ,
        "clinets_user":
           [ 
            // user with type clients (name, id)
           ]
        ,
        "all_user":
            [
                //all user (name, id)
            ]
        
    }
}