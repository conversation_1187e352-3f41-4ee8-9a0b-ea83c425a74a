@if ($selectedLeadId)
<div>
 @livewire('leads.leads-details', ['leadID' => $selectedLeadId], key($selectedLeadId))
</div>
@else

<div>
    <div class="contents crm">
        <div class="container-fluid">
            <div class="col-lg-12 overflow-hidden">
                <div class="row justify-content-md-between align-items-center justify-content-center mb-3 mt-3">
                    <div class=" d-flex flex-wrap justify-content-center breadcrumb-main__wrapper">
                        <div class=" mr-sm-25">
                            <div class="d-flex align-items-center user-member__title  mr-sm-25">
                                <h4 class="text-capitalize fw-500 breadcrumb-title page-title__left"> @lang('lead.common.lead_list')
                                </h4>
                            </div>
                            <div class="">
                                <ul class="atbd-breadcrumb nav">
                                    <li class="atbd-breadcrumb__item">
                                        <a>{{ __('dashboard.bread_crumbs.dashboard') }}</a>
                                        <span class="breadcrumb__seperator">
                                            <span class="la la-angle-right"></span>
                                        </span>
                                    </li>
                                    <li class="atbd-breadcrumb__item">
                                        <a> @lang('lead.common.lead_list') </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <form action="" class="d-flex align-items-center user-member__form my-sm-0 my-2">
                            <span data-feather="search"></span>
                            <input wire:model.debounce.500ms="search"
                                class="form-control mr-sm-2 border-0 box-shadow-none min-w-200" type="search"
                                placeholder="@lang('lead.common.search_lead_name')" aria-label="Search">
                        </form>
                        <div wire:loading.delay wire:target="search">
                            <div class="ml-2 mt-1 spinner-border text-info" role="status"></div>
                        </div>
                    </div>
                    <div class="d-sm-flex gap-10">
                        <div class="d-flex gap-10">
                            <div class=" min-w-150">
                                <select id="pipelines" wire:model='selected_pipeline'>
                                    @foreach ($pipelines as $pipeline)
                                        <option value="{{ $pipeline['id'] }}">{{ $pipeline['name'] }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="action-btn  mt-sm-0 mt-2">
                            <button class="btn btn-default btn-primary w-100 no-wrap" type="button"
                                wire:click.prevent="$emit('showCreateLead', '{{ $selected_pipeline }}')"><i
                                    class="las la-plus fs-16"></i>
                                @lang('lead.forms.button.create')</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-responsive new-scrollbar crm-leads">
                <div class="d-flex gap-10 pb-4">
                    @forelse ($leadsStages as $stage)
                        @php
                            $leads = $stage['paginatedLeads'];
                        @endphp
                        <div class="col-md-3 col-sm-6 mb-md-0 mb-2 p-0">
                            <div class="card">
                                <div
                                    class="card-header header-left d-flex justify-content-between p-2 min-h-0 border-0 gap-10">
                                    <h6 class="fs-14 fw-400">{{ $stage['name'] }}</h6>
                                    <span
                                        class="p-2 d-inline-block btn-primary btn-sm wh-30 d-flex align-items-center justify-content-center rounded-xl">{{ $leads['pagination']['total'] }}</span>
                                </div>
                                <div class="card-body p-2">
                                    @if (isset($leads))
                                        <div id="stage-{{ $stage['id'] }}" class="sortable-leads"
                                            data-page="{{ $page }}" data-lastpage="{{ $original_last_page }}">

                                            @foreach (@$leads['data'] ?? [] as $lead)
                                                <div data-lead-id="{{ $lead['id'] }}"
                                                    data-stage-id="{{ $stage['id'] }}"
                                                    class="card radius-xl p-2 shadow-lg sortable-leads">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <a href="{{ route('crm.leads.details', $lead['id']) }}">{{ $lead['name'] }}
                                                            <i class="las la-angle-right rotate-ar-y"></i></a>
                                                        <div class="dropdown">
                                                            <span
                                                                class="d-block rounded border wh-20 text-center  dropdown-toggle cursor-pointer"
                                                                data-toggle="dropdown" aria-haspopup="true"
                                                                aria-expanded="false"><i
                                                                    class="las la-ellipsis-v"></i></span>
                                                            <div class="dropdown-menu"
                                                                aria-labelledby="dropdownMenuButton">
                                                                {{--                                                <a class="dropdown-item" href="#"><span data-feather="bookmark"></span> Labels</a> --}}
                                                                <a class="dropdown-item" href="#"
                                                                    wire:click.prevent="$emit('showUpdateLead', '{{ $selected_pipeline }}', '{{ $lead['id'] }}')"><span
                                                                        data-feather="edit-2"></span>
                                                                    @lang('lead.common.edit')</a>
                                                                </a>
                                                                <a wire:click="$emit('confirmDelete', {{ $lead['id'] }}, '{{ $lead['name'] }}', 'deleteLead', {{ json_encode(['stage_id' => $stage['id']]) }})"
                                                                    class="dropdown-item" href="#"><span
                                                                        data-feather="trash-2"></span>
                                                                    @lang('lead.common.delete')</a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="text-osool mt-2">
                                                        <div
                                                            class="d-flex justify-content-between align-items-center mt-2">
                                                        </div>
                                                        <div
                                                            class="d-flex justify-content-between align-items-center mt-2">
                                                            <span><span data-feather="shopping-cart"
                                                                    class="mr-1 fs-18 text-osool"></span>
                                                                {{ $lead['products_count'] }}</span>

                                                            <span><span data-feather="share-2"
                                                                    class="mr-1 fs-18 text-osool"></span>
                                                                {{ $lead['sources_count'] }}</span>
                                                        </div>
                                                        @if (isset($lead['labels']))
                                                            <div class="d-flex border-top p-2 gap-10 mx-m8 mt-2">
                                                                @foreach ($lead['labels'] as $label)
                                                                    <button
                                                                        class="btn btn-xs btn-{{ $label['color'] }}">{{ $label['name'] }}</button>
                                                                @endforeach

                                                            </div>
                                                        @endif
                                                        <div
                                                            class="d-flex justify-content-between align-items-center border-top mx-m10 px-2 pt-1">
                                                            <span>@lang('lead.forms.label.users')</span>

                                                            <div class="profile-group">
                                                                @if (isset($lead['user']))
                                                                    <div class="profile">
                                                                        <img src="{{ $lead['user']['avatar'] ?? '' }}"
                                                                            data-toggle="tooltip"
                                                                            title="{{ $lead['user']['name'] ?? '' }}"
                                                                            alt="{{ $lead['user']['name'] ?? '' }}"
                                                                            onerror="this.src='/empty-icon/no-preview.png'">
                                                                    </div>
                                                                @endif
                                                                {{-- @foreach ($lead['users'] as $index => $user)
                                                                    @if ($index < 4)
                                                                        <div class="profile">
                                                                            <img src="{{ $user['avatar'] }}"
                                                                                data-toggle="tooltip"
                                                                                title="{{ $user['name'] }}"
                                                                                alt="{{ $user['name'] }}"
                                                                                onerror="this.src='/empty-icon/no-preview.png'">
                                                                        </div>
                                                                    @endif
                                                                @endforeach

                                                                @if (count($lead['users']) > 4)
                                                                    <div
                                                                        class="extra d-flex align-items-center justify-content-center">
                                                                        +{{ count($lead['users']) - 4 }}
                                                                    </div>
                                                                @endif --}}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @empty
                        @include('livewire.common.no-data')
                    @endforelse
                </div>
            </div>
            <div id="load_more"></div>
            <div wire:loading.delay>
                <div class="spinner-border text-info" role="status"></div>
            </div>
        </div>
    </div>
    @include('livewire.common.super-modal-v1', [
    'component' => 'leads.modals.create-lead',
    'modalId' => 'createLead',
])
@include('livewire.common.super-modal-v1', [
    'component' => 'leads.modals.update-lead',
    'modalId' => 'updateLead',
])
@livewire('common.delete-confirm')
<script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.14.0/Sortable.min.js"></script>
<script src="{{ asset('js/livewire/manage-tooltip.js') }}"></script>
<script src="{{ asset('js/livewire/manage-select2.js') }}"></script>
<script src="{{ asset('js/livewire/manage-modals.js') }}"></script>
<script src="{{ asset('js/livewire/manage-loader.js') }}"></script>
<script src="{{ asset('js/livewire/error-messages.js') }}"></script>
<script src="{{ asset('js/livewire/manage-datepicker.js') }}"></script>
<script src="/js/livewire/allow-float-1.js"></script>
<script src="/js/livewire/copy-billing-to-shipping.js"></script>
<script src="/js/livewire/manage-loaders.js"></script>

<script>
    function showLoader() {
        console.log("Executing showLoader function...");
        document.getElementById("overlayer").style.display = "block";

        let loaderElements = document.getElementsByClassName("loader-overlay");
        if (loaderElements.length > 0) {
            loaderElements[0].style.display = "block";
        }
    }

    function hideLoader() {
        document.getElementById("overlayer").style.display = "none";

        let loaderElements = document.getElementsByClassName("loader-overlay");
        if (loaderElements.length > 0) {
            loaderElements[0].style.display = "none";
        }
    }

    window.addEventListener('show-loader', event => {
        showLoader();
    });

    window.addEventListener('hide-loader', event => {
        hideLoader();
    });

    document.addEventListener("livewire:load", function() {
        Livewire.hook('message.processed', (message, component) => {
            feather.replace();
            $('[data-toggle="tooltip"]').tooltip();
            initYourIcons();
        });
    });

    function initYourIcons() {
        if (typeof feather !== 'undefined') {
            feather.replace();
        }

        if (typeof Iconsax !== 'undefined') {
            Iconsax.init();
        }
    }
    document.addEventListener("DOMContentLoaded", function() {
        let currentPath = window.location.pathname;

        document.querySelectorAll(".nav-stages .list-group-item").forEach((item) => {
            let link = item.querySelector("a");

            if (link && link.href.includes(currentPath)) {
                document.querySelectorAll(".nav-stages .list-group-item").forEach(i => i.classList
                    .remove("active"));
                item.classList.add("active");
            }
        });
    });
    document.addEventListener("DOMContentLoaded", function() {
        Livewire.on('showLoader', function() {
            showLoader();
        });

        Livewire.on('hideLoader', function() {
            hideLoader();
        });
    });

    document.addEventListener('DOMContentLoaded', function() {
        @foreach ($leadsStages as $stage)
            new Sortable(document.getElementById('stage-{{ $stage['id'] }}'), {
                group: 'shared',
                animation: 150,
                draggable: '.card',
                onEnd(evt) {
                    const leadId = evt.item.getAttribute('data-lead-id');
                    const oldStageId = evt.item.getAttribute('data-stage-id');
                    const newStageId = evt.to.getAttribute('id').replace('stage-', '');
                    @this.emit('updateLeadStage', leadId, newStageId, oldStageId);
                }
            });
        @endforeach
    });

    function showLoader() {
        console.log("Executing showLoader function...");
        document.getElementById("overlayer").style.display = "block";

        let loaderElements = document.getElementsByClassName("loader-overlay");
        if (loaderElements.length > 0) {
            loaderElements[0].style.display = "block";
        }
    }

    function hideLoader() {
        document.getElementById("overlayer").style.display = "none";

        let loaderElements = document.getElementsByClassName("loader-overlay");
        if (loaderElements.length > 0) {
            loaderElements[0].style.display = "none";
        }
    }

    // checking if last element is visible then loading more items from api
    let allowLoadMore = true;

    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    function checkLastRecordVisibility() {
        const container = document.querySelector('#load_more');
        if (!container) return;

        // const thirdLastChild = children[children.length - 3];
        if (container && isElementInViewport(container)) {
            const container = document.querySelector('.sortable-leads');
            const page = parseInt(container.dataset.page);
            const lastpage = parseInt(container.dataset.lastpage);

            if (page < lastpage && allowLoadMore) {
                Livewire.emit('loadMore');
                allowLoadMore = false;
            }
        }
    }

    // Check on scroll and on page load
    window.addEventListener('scroll', checkLastRecordVisibility);
    window.addEventListener('load', checkLastRecordVisibility);
</script>
@section('scripts')
    <script>
        window.Livewire.on('showCreateLead', function() {
            showLoader();
        });

        window.Livewire.on('showUpdateLead', function() {
            showLoader();
        });

        window.Livewire.on('updateLeadStage', function() {
            showLoader();
        });

        Livewire.hook("message.sent", message => {
            message.updateQueue.forEach(update => {
                if (update.method === "$set") {
                    const variableName = update.payload.params[0];
                    if (variableName === "selected_pipeline") {
                        showLoader();
                    }
                }
            });
        });

        window.addEventListener("allowLoadMore", event => {
            if (event.detail.allow) {
                allowLoadMore = true;
            }
        });
    </script>
@endsection

</div>
@endif
