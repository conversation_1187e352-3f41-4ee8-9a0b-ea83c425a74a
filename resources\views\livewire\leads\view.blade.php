    <div class="contents crm">
        <div class="container-fluid">
            <div class="col-lg-12">
                <div class="row justify-content-between align-items-center">
                    <div class="page-title-wrap">
                        <div class="page-title d-flex justify-content-between">
                            <div class="page-title__left">
                                <div class="d-flex align-items-center user-member__title justify-content-center mr-sm-25 ml-0">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                        <!-- Javascript:history.back()-->
                                        <a href="javascript:void(0);" wire:click="$emit('backToList')">
                                            <i class="las la-arrow-left"></i>
                                        </a>
                                        {{ 
                                            ($lead['id'] ?? '') . 
                                            ' - ' . 
                                            ($lead['pipeline'] ?? '') . 
                                            ' - ' . 
                                            ($lead['name'] ?? '') 
                                        }}
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div class="">
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a>@lang('lead.lead_stage.dashboard')</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>@lang('lead.lead_stage.leads')</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>{{$lead['name']}}</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <!--====Design for Export PDF===-->
                    <div class="d-flex gap-10">
                        <div class="btn btn-white btn-default text-center svg-20 px-3 py-2" wire:click.prevent="$emit('editStage', {{  $lead['id']  }})">
                            <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="flow-chart-1"></i>
                        </div>
                        {{--                        <div class="btn btn-white btn-default text-center svg-20 px-3 py-2"> --}}
                        {{--                            <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="video-2"></i> --}}
                        {{--                        </div> --}}
                        {{--                        <div class="btn btn-white btn-default text-center svg-20 px-3 py-2"> --}}
                        {{--                            <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="tag-1"></i> --}}
                        {{--                        </div> --}}
                        <div class="btn btn-white btn-default text-center svg-20 px-3 py-2"
                            wire:click.prevent="$emitTo('leads.leads-details', 'editLead', {{ $lead['id'] }})">
                            <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="edit-1"></i>
                        </div>


                            <!-- <div class="btn btn-white btn-default text-center svg-20 px-3 py-2" wire:click.prevent="$emit('convertToDeal', {{$lead['id']}})" > -->
                            @if (@$deal_details['id'])
                                <div class="btn btn-white btn-default text-center svg-20 px-3 py-2" onclick="window.location='/crm/deals/details/{{ $deal_details['id'] }}'" style="color: #01a9f3">
                                    @lang('lead.show_deal')
                                </div>
                            @else
                                <div class="btn btn-white btn-default text-center svg-20 px-3 py-2" onclick="showLoader()"  wire:click.prevent="$emit('showConvertDeal', '{{ $leadID }}')">
                                    <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="git-arrows"></i>
                                </div>
                            @endif
                    </div>

                    <!--====End Design for Export PDF===-->
                </div>
            </div>


            <div class="checkout pt-2 pb-20 mb-30 w-100">
                <div class="row">
                    <div class="col-lg-3 pr-md-0 mb-3 mb-md-0">
                        <div class="card">
                            <ul class="list-group crm crm-sidebar nav-stages">
                                <li class="list-group-item active">
                                    <a href="#general-section"
                                        class="d-flex justify-content-between align-items-center"> @lang('lead.lead_stage.generals')
                                        <i class="las la-angle-right"></i>
                                    </a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#task-section" class="d-flex justify-content-between align-items-center">
                                        @lang('lead.lead_stage.tasks')
                                        <i class="las la-angle-right"></i>
                                    </a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#users-section" class="d-flex justify-content-between align-items-center">
                                        @lang('lead.lead_stage.user_products')
                                        <i class="las la-angle-right"></i>
                                    </a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#sources-section"
                                        class="d-flex justify-content-between align-items-center">
                                        @lang('lead.lead_stage.sources_emails')
                                        <i class="las la-angle-right"></i>
                                    </a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#notes-section" class="d-flex justify-content-between align-items-center">
                                        @lang('lead.lead_stage.discussions_notes')
                                        <i class="las la-angle-right"></i>
                                    </a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#files-section" class="d-flex justify-content-between align-items-center">
                                        @lang('lead.lead_stage.files')
                                        <i class="las la-angle-right"></i>
                                    </a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#calls-section" class="d-flex justify-content-between align-items-center">
                                        @lang('lead.lead_stage.calls')
                                        <i class="las la-angle-right"></i>
                                    </a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#activity-section"
                                        class="d-flex justify-content-between align-items-center">
                                        @lang('lead.lead_stage.activity')
                                        <i class="las la-angle-right"></i>
                                    </a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#attachments-section"
                                        class="d-flex justify-content-between align-items-center">
                                        @lang('lead.lead_stage.attachments')
                                        <i class="las la-angle-right"></i>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-9 fs-14 leads-details new-scrollbar-hidden"
                        style="max-height: 70vh;overflow-y: auto;">
                        <div class="card checkout-shipping-form pb-0 mt-lg-0 mb-3" style="margin-bottom: 100vh;">
                            <div class="card-body">
                                <div class="row" id="general-section">
                                    <div class="col-md-4 d-flex align-items-center mb-4">
                                        <span
                                            class="wh-40 radius-xl bg-new-primary  text-white lh-40 text-white d-flex-center mr-2">
                                            <i class="iconsax icon fs-22 text-white mr-0" icon-name="hierarchy-1"></i>
                                        </span>
                                        <div class="flex-fill">
                                            <p class="text-dark mb-0">@lang('lead.lead_stage.email')</p>
                                            <span class="text-new-primary">{{ $lead['email'] }}</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4 d-flex align-items-center mb-4">
                                        <span
                                            class="wh-40 radius-xl bg-new-primary  text-white lh-40 text-white d-flex-center mr-2">
                                            <i class="iconsax icon fs-22 text-white mr-0" icon-name="mobile"></i>
                                        </span>
                                        <div class="flex-fill">
                                            <p class="text-dark mb-0">@lang('lead.lead_stage.phone')</p>
                                            <span class="text-new-primary">{{ $lead['phone'] }}</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4 d-flex align-items-center mb-4">
                                        <span
                                            class="wh-40 radius-xl bg-new-primary  text-white lh-40 text-white d-flex-center mr-2">
                                            <i class="iconsax icon fs-22 text-white mr-0" icon-name="hierarchy-1"></i>
                                        </span>
                                        <div class="flex-fill">
                                            <p class="text-dark mb-0">@lang('lead.lead_stage.pipeline')</p>
                                            <span class="text-new-primary">{{ $lead['pipeline'] }}</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4 d-flex align-items-center mb-4">
                                        <span
                                            class="wh-40 radius-xl bg-new-primary text-white lh-40 d-flex-center mr-2">
                                            <i class="iconsax icon fs-22 text-white mr-0" icon-name="driver-1"></i>
                                        </span>
                                        <div class="flex-fill">
                                            <p class="text-dark mb-0">@lang('lead.lead_stage.stage')</p>
                                            <div class="d-flex align-items-center">
                                                <span class="text-new-primary mr-2">{{ $lead['stage'] }}</span>
                                                <i class="iconsax icon fs-18 text-muted cursor-pointer"
                                                    icon-name="edit" data-toggle="modal"
                                                    data-target="#editStageModal"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 d-flex align-items-center mb-4">
                                        <span
                                            class="wh-40 radius-xl bg-new-primary  text-white lh-40 text-white d-flex-center mr-2">
                                            <i class="iconsax icon fs-22 text-white mr-0" icon-name="calendar-1"></i>
                                        </span>
                                        <div class="flex-fill">
                                            <p class="text-dark mb-0">@lang('lead.lead_stage.created')</p>
                                            <span class="text-new-primary">{{ $lead['date'] }}</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4 d-flex align-items-center mb-4">
                                        <span
                                            class="wh-40 radius-xl bg-new-primary  text-white lh-40 text-white d-flex-center mr-2">
                                            <i class="iconsax icon fs-22 text-white mr-0"
                                                icon-name="dollar-square"></i>
                                        </span>
                                        <div class="flex-fill">
                                            <p class="text-dark mb-0">{{ $lead['precentage'] ?? '0%' }}</p>
                                            <div class="progress">
                                                <div class="progress-bar" role="progressbar"
                                                    style="width: {{ $lead['precentage'] ?? 0 }}"
                                                    aria-valuenow="{{ $lead['precentage'] ?? 0 }}" aria-valuemin="0"
                                                    aria-valuemax="100"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 d-flex align-items-center">
                                        <span
                                            class="wh-40 radius-xl bg-new-primary  text-white lh-40 text-white d-flex-center mr-2">
                                            <i class="iconsax icon fs-22 text-white mr-0" icon-name="calendar-1"></i>
                                        </span>
                                        <div class="flex-fill">
                                            <p class="text-dark mb-0">@lang('lead.lead_stage.followup_date')</p>
                                            <span class="text-new-primary">{{ $lead['follow_up_date'] }}</span>
                                        </div>
                                    </div>

                                    <div class="col-md-4 d-flex align-items-center">
                                        <span
                                            class="wh-40 radius-xl bg-new-primary  text-white lh-40 text-white d-flex-center mr-2">
                                            <i class="iconsax icon fs-22 text-white mr-0" icon-name="shield-time"></i>
                                        </span>
                                        <div class="flex-fill">
                                            <p class="text-dark mb-0">{{ __('lead.followup_status') }}</p>

                                            @switch($lead['followup_status'])
                                                @case('completed')
                                                    <span class="text-success fw-600">{{ __('lead.completed') }}</span>
                                                @break

                                                @case('rescheduled')
                                                    <span class="text-warning fw-600">{{ __('lead.rescheduled') }}</span>
                                                @break

                                                @default
                                                    <span class="text-loss fw-600">{{ __('lead.no_answer') }}</span>
                                                @break
                                            @endswitch
                                        </div>
                                    </div>

                                    <div class="col-md-4 d-flex align-items-center">
                                        <button class="btn btn-sm bg-new-primary flex-fill" data-toggle="modal"
                                            data-target="#update-followup"><i class="iconsax"
                                                icon-name="refresh"></i>
                                            {{ __('lead.modal.update_follow_up_status') }}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @if ($deal_details && (@$deal_details['id'] || @$deal_details['contact']['id']) || @$deal_details['account']['id'])
                            <div
                                class="card checkout-shipping-form pb-0 mt-lg-0 mb-3"
                                style="margin-bottom: 100vh"
                                >
                                <div class="card-body">
                                    <label class="pb-1 text-osool">@lang('lead.converted_to')</label>
                                    <div class="row">
                                    <div class="col-md-4 d-flex align-items-center mb-4">
                                        <span
                                        class="wh-40 radius-xl bg-new-primary text-white lh-40 text-white d-flex-center mr-2"
                                        >
                                        <i class="iconsax icon fs-22 text-white mr-0" icon-name="user-1"></i>
                                        </span>
                                        <div class="flex-fill">
                                        <p class="text-dark mb-0">@lang('lead.contact')</p>
                                        @if ($deal_details['contact']['id'])
                                            <a style="text-decoration: underline;text-decoration-color: #16b0f4;" href="/sales/contacts/{{ $deal_details['contact']['id'] }}/edit" target="_blank">
                                                <span class="text-new-primary">{{ $deal_details['contact']['name'] }}</span>
                                            </a>
                                        @else
                                            <span class="text-new-primary">{{ $deal_details['contact']['name'] }}</span>
                                        @endif
                                        </div>
                                    </div>
                                    <div class="col-md-4 d-flex align-items-center mb-4">
                                        <span
                                        class="wh-40 radius-xl bg-new-primary text-white lh-40 text-white d-flex-center mr-2"
                                        >
                                        <i class="iconsax icon fs-22 text-white mr-0" icon-name="building-2"></i>
                                        </span>
                                        <div class="flex-fill">
                                        <p class="text-dark mb-0">@lang('lead.account')</p>
                                        @if ($deal_details['account']['id'])
                                            <a style="text-decoration: underline;text-decoration-color: #16b0f4;" href="/sales/accounts/{{ $deal_details['account']['id'] }}/edit" target="_blank">
                                                <span class="text-new-primary">{{ $deal_details['account']['name'] }}</span>
                                            </a>
                                        @else
                                            <span class="text-new-primary">{{ $deal_details['account']['name'] }}</span>
                                        @endif
                                        </div>
                                    </div>
                                    <div class="col-md-4 d-flex align-items-center mb-4">
                                        <span
                                        class="wh-40 radius-xl bg-new-primary text-white lh-40 text-white d-flex-center mr-2"
                                        >
                                        <i class="iconsax icon fs-22 text-white mr-0" icon-name="calendar-3"></i>
                                        </span>
                                        <div class="flex-fill">
                                        <p class="text-dark mb-0">@lang('lead.converted_on')</p>
                                        <span class="text-new-primary">{{ Helper::formatDateForLocale($deal_details['converted_on']) }}</span>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                        <div class="row mb-3">
                            <div class="col-md-3 col-sm-6">
                                <div class="card px-3 py-4">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <p class="text-osool mb-1">@lang('lead.lead_stage.tasks')</p>
                                            <span class="text-new-primary fw-500">{{ count($lead['tasks']) }}</span>
                                        </div>
                                        <span
                                            class="wh-40 radius-xl bg-osool-new  text-white lh-40 text-white d-flex-center mr-2">
                                            <i class="iconsax icon fs-22 text-white mr-0"
                                                icon-name="task-list-square"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="card px-3 py-4">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <p class="text-osool mb-1">@lang('lead.lead_stage.products')</p>
                                            <span
                                                class="text-new-primary fw-500">
                                                @if (auth()->user()->user_type == 'admin')
                                                    {{ $unitCount }}
                                                @else
                                                     {{ count($lead['products']) }}
                                                @endif
                                            </span>
                                        </div>
                                        <span
                                            class="wh-40 radius-xl bg-osool-new  text-white lh-40 text-white d-flex-center mr-2">
                                            <i class="iconsax icon fs-22 text-white mr-0"
                                                icon-name="shopping-cart"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="card px-3 py-4">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <p class="text-osool mb-1">@lang('lead.lead_stage.source')</p>
                                            <span class="text-new-primary fw-500">{{ count($lead['sources']) }}</span>
                                        </div>
                                        <span
                                            class="wh-40 radius-xl bg-osool-new  text-white lh-40 text-white d-flex-center mr-2">
                                            <i class="iconsax icon fs-22 text-white mr-0" icon-name="share"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="card px-3 py-4">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <p class="text-osool mb-1">@lang('lead.lead_stage.files')</p>
                                            <span class="text-new-primary fw-500">{{ count($lead['files']) }}</span>
                                        </div>
                                        <span
                                            class="wh-40 radius-xl bg-osool-new  text-white lh-40 text-white d-flex-center mr-2">
                                            <i class="iconsax icon fs-22 text-white mr-0" icon-name="document-1"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <livewire:lead-task-list page-type="lead" sidebar-crm={{ true }}
                            workspace-slug="{{ auth()->user()->workspace }}" leadId="{{ $lead['id'] }}"
                            :leadTaskArray="$lead['tasks']" lazy />

                        <div class="row" id="users-section">
                            <div class="col-md-6 pr-md-2 mb-3">
                                <div class="card h-100">
                                    <div
                                        class="card-header border-bottom-0 justify-content-between d-flex align-items-center p-3">
                                        <h3 class="text-osool">@lang('lead.lead_stage.users')</h3>

                                        <button class="btn btn-default bg-new-primary dropdown-toggle" type="button"
                                            id="userModal" data-toggle="modal" aria-haspopup="true"
                                            aria-expanded="false" data-toggle="modal" data-target="#addUserModal"><i
                                                class="las la-plus fs-16"></i>@lang('lead.forms.button.create')</button>
                                    </div>
                                    <div class="card-body p-0">
                                        @if (session()->has('message'))
                                            <div class="alert alert-success">
                                                {{ session()->get('message') }}
                                            </div>
                                        @endif
                                        @foreach ($lead['users'] as $user)
                                            <div class="d-flex justify-content-between align-items-center p-3">
                                                <div class="d-flex align-items-center">
                                                    <div class="user-profile mr-2">
                                                        <img src="{{ $user['avatar'] }}" class="wh-50 rounded">
                                                    </div>
                                                    <div class="text-osool">
                                                        <p class="mb-0">{{ $user['name'] }} </p>
                                                    </div>
                                                </div>
                                                <div class="d-flex">
                                                    <a href="#"
                                                        wire:click.prevent="$emit('deleteUser', {{ $user['id'] }})"><i
                                                            class="iconsax icon fs-22 text-delete fs-22 mr-0"
                                                            icon-name="trash"></i></a>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            {{-- {{ dd($lead); }} --}}
                            {{-- {{ dd( auth()->user()->workspace,  $lead['id'],$lead ); }} --}}

                            {{-- @livewire('lead-products-livewire', ['workspaceSlug' => $workspaceSlug, 'leadId' =>'6']) --}}

                                   @if(auth()->user()->user_type == 'admin')
   <livewire:lead-units-livewire page-type="lead"
                                workspace-slug="{{ auth()->user()->workspace }}" leadId="{{ $lead['id'] }}"
                                :leadProductArray="$lead['products']" />
                                @else
    <livewire:lead-products-livewire page-type="lead"
                                workspace-slug="{{ auth()->user()->workspace }}" leadId="{{ $lead['id'] }}"
                                :leadProductArray="$lead['products']" />
                                @endif

                            <livewire:lead-sources page-type="lead" workspace-slug="{{ auth()->user()->workspace }}"
                                leadId="{{ $lead['id'] }}" :leadSourcesArray="$lead['sources']" />

                            {{--                            <div class="col-md-6 pl-md-2 mb-3"> --}}
                            {{--                                <div class="card h-100"> --}}
                            {{--                                    <div class="card-header border-bottom-0 justify-content-between d-flex align-items-center p-3"> --}}
                            {{--                                        <h3 class="text-osool">@lang('lead.lead_stage.emails')</h3> --}}

                            {{--                                        <button class="btn btn-default bg-new-primary dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="modal" data-target="#creat-leadstage" aria-haspopup="true" aria-expanded="false"><i class="las la-plus fs-16"></i>@lang('lead.forms.button.create')</button> --}}
                            {{--                                    </div> --}}
                            {{--                                    <div class="card-body p-0"> --}}
                            {{--                                        @if (isset($lead['emails'])) --}}
                            {{--                                        @foreach ($lead['emails'] as $email) --}}
                            {{--                                        <div class="d-flex justify-content-between align-items-center p-3"> --}}
                            {{--                                            <div class="d-flex align-items-center"> --}}
                            {{--                                                <div class="user-profile mr-2"> --}}
                            {{--                                                    <img src="{{ asset('img/author-nav.jpg') }}" class="wh-50 rounded"> --}}
                            {{--                                                </div> --}}
                            {{--                                                <div class="text-osool"> --}}
                            {{--                                                    <p class="mb-0">{{$email['subject']}} <span class=" ml-2 fs-12">{{$email['diff_time']}}</span></p> --}}
                            {{--                                                    <span class="fs-12">{{$email['to']}}</span> --}}
                            {{--                                                </div> --}}
                            {{--                                            </div> --}}
                            {{--                                            <div class="d-flex"> --}}
                            {{--                                                <a href="#"><i class="iconsax icon fs-22 text-delete fs-22 mr-0" icon-name="trash"></i></a> --}}
                            {{--                                            </div> --}}
                            {{--                                        </div> --}}
                            {{--                                        @endforeach --}}
                            {{--                                        @endif --}}
                            {{--                                    </div> --}}
                            {{--                                </div> --}}
                            {{--                            </div> --}}

                            <livewire:leads-discussion page-type="lead" sidebar-crm={{ false }}
                                workspace-slug="{{ auth()->user()->workspace }}" activity-id="{{ $lead['id'] }}"
                                :leadDiscussionArray="$lead['discussion']" />
                            <div class="col-md-6 mb-3" id="notes-section">
                                <div class="card h-100">
                                    <div
                                        class="card-header border-bottom-0 justify-content-between d-flex align-items-center p-3">
                                        <h3 class="text-osool">@lang('lead.lead_stage.notes')</h3>

                                        <button class="btn btn-default bg-new-primary" type="button"
                                            data-toggle="modal" data-target="#createNoteModal">
                                            <i class="las la-plus fs-16"></i> @lang('lead.forms.button.create')
                                        </button>
                                    </div>

                                    <div class="card-body p-0">
                                        <div class="d-flex justify-content-between align-items-center p-3">
                                            <div class="d-flex align-items-start">
                                                <div class="text-osool">
                                                    <span class="fs-12">
                                                        @if ($lead['notes'])
                                                           @php
                                                                echo $lead['notes'];
                                                            @endphp
                                                        @else
                                                            @lang('lead.no_notes')
                                                        @endif
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>
                        <div class="row" id="calls-section"  >
                            <livewire:lead-calls-list type="leads" sidebar-crm={{false}} workspace-slug="{{ auth()->user()->workspace}}" activity-id="{{ $lead['id'] }}"  :leadCallsArray="$lead['calls']"/>
                            </div>
                            <div class="row" id="attachments-section"  >
                            <livewire:lead-attachments-list type="leads" sidebar-crm={{false}} workspace-slug="{{ auth()->user()->workspace}}" activity-id="{{ $lead['id'] }}" :leadfilesArray="$lead['files']" />
                        </div>
                        <div class="row" id="activity-section"  >
                            <div class="col-md-6">
                            <livewire:lead-activity-list page-type="lead" sidebar-crm={{false}} workspace-slug="{{ auth()->user()->workspace}}" activity-id="{{ $lead['id'] }}" :leadActivityArray="$lead['activities']" wire:key="activity-list-{{ now()->timestamp }}"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <input type="hidden" id="users_list_url" value="{{route('crm.users.ajax-list')}}">
            <input type="hidden" id="users_img_path" value="{{asset('uploads/profile_images/')}}">


            <!-- Add User Modal -->
            <div wire:ignore.self class="modal fade" id="addUserModal" tabindex="-1" role="dialog"
                aria-labelledby="addUserModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addUserModalLabel">@lang('lead.add_user')</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            @if (session()->has('error'))
                                <div class="alert alert-danger">
                                    {{ session()->get('error') }}
                                </div>
                            @endif
                            <div>
                                <label>@lang('lead.select_user')</label>
                                <select wire:model="selectedUserId" class="form-control">
                                    <option value="">@lang('lead.choose_user')</option>
                                    @foreach ($users as $id => $name)
                                        <option value="{{ $id }}">{{ $name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary"
                                data-dismiss="modal">@lang('lead.close')</button>
                            <button type="button" class="btn btn-primary"
                                wire:click="addUser">@lang('lead.add')</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal -->
            <div wire:ignore.self class="modal fade" id="editStageModal" tabindex="-1"
                aria-labelledby="editStageModalLabel" aria-hidden="true" data-bs-backdrop="static">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="editStageModalLabel">@lang('lead.lead_stage.edit_stage')</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form wire:submit.prevent="updateLeadStage">
                                <label for="leadStage">@lang('lead.lead_stage.stage')</label>
                                <select wire:model="selectedStage" class="form-control">
                                    <option value="">Select Stage</option>
                                    @foreach ($stages as $id => $name)
                                        <option value="{{ $name }}">{{ $name }}</option>
                                    @endforeach
                                </select>
                                <div class="modal-footer">
                                    <button type="button" class="btn bg-hold-light text-white"
                                        data-dismiss="modal">@lang('lead.common.cancel')</button>
                                    <button type="submit" class="btn btn-primary">@lang('lead.common.save')</button>
                                </div>
                            </form>
                        </div>

                    </div>
                </div>
            </div>


            <div wire:ignore.self class="modal fade" id="covertToDealModal" tabindex="-1"
                aria-labelledby="covertToDealModalLabel" aria-hidden="true" data-bs-backdrop="static">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="editStageModalLabel">@lang('lead.convert_to_deal')</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">

                            @if (session()->has('error'))
                                <div class="alert alert-danger">
                                    {{ session()->get('error') }}
                                </div>
                            @endif

                            @if (session()->has('message'))
                                <div class="alert alert-success">
                                    {{ session()->get('message') }}
                                </div>
                            @endif
                            <form wire:submit.prevent="convertToDealSave">
                                <!-- Client Name Field -->
                                <div class="form-group">
                                    <label for="client_name">@lang('lead.client_name')</label>
                                    <input type="text" wire:model="client_name" class="form-control"
                                        id="client_name" placeholder="@lang('lead.client_name')">
                                    @error('client_name')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- Client Email Field -->
                                <div class="form-group">
                                    <label for="client_email">@lang('lead.client_email')</label>
                                    <input type="email" wire:model="client_email" class="form-control"
                                        id="client_email" placeholder="@lang('lead.client_email')">
                                    @error('client_email')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- Client Password Field -->
                                <div class="form-group">
                                    <label for="client_password">@lang('lead.client_password')</label>
                                    <input type="password" wire:model="client_password" class="form-control"
                                        id="client_password" placeholder="@lang('lead.client_password')">
                                    @error('client_password')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- Price Field -->
                                <div class="form-group">
                                    <label for="price">@lang('lead.price')</label>
                                    <input type="number" wire:model="price" class="form-control" id="price"
                                        placeholder="@lang('lead.price')" min="0">
                                    @error('price')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- Transfer Options -->
                                <div class="form-group">
                                    <label>@lang('lead.transfer_options')</label>

                                    <div class="form-check">
                                        <input type="checkbox" wire:model="transfer_products"
                                            class="form-check-input" id="transfer_products">
                                        <label class="form-check-label"
                                            for="transfer_products">@lang('lead.transfer_products')</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" wire:model="transfer_sources" class="form-check-input"
                                            id="transfer_sources">
                                        <label class="form-check-label"
                                            for="transfer_sources">@lang('lead.transfer_sources')</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" wire:model="transfer_notes" class="form-check-input"
                                            id="transfer_notes">
                                        <label class="form-check-label"
                                            for="transfer_notes">@lang('lead.transfer_notes')</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" wire:model="transfer_tasks" class="form-check-input"
                                            id="transfer_tasks">
                                        <label class="form-check-label"
                                            for="transfer_tasks">@lang('lead.transfer_tasks')</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" wire:model="transfer_discussions"
                                            class="form-check-input" id="transfer_discussions">
                                        <label class="form-check-label"
                                            for="transfer_discussions">@lang('lead.transfer_discussions')</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" wire:model="transfer_files" class="form-check-input"
                                            id="transfer_files">
                                        <label class="form-check-label"
                                            for="transfer_files">@lang('lead.transfer_files')</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" wire:model="transfer_calls" class="form-check-input"
                                            id="transfer_calls">
                                        <label class="form-check-label"
                                            for="transfer_calls">@lang('lead.transfer_calls')</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" wire:model="transfer_emails" class="form-check-input"
                                            id="transfer_emails">
                                        <label class="form-check-label"
                                            for="transfer_emails">@lang('lead.transfer_emails')</label>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary"
                                        data-dismiss="modal">@lang('lead.common.cancel')</button>
                                    <button type="submit" class="btn btn-primary">@lang('lead.common.save')</button>
                                </div>
                            </form>
                        </div>

                    </div>
                </div>
            </div>

            <div wire:ignore.self class="modal fade new-popup" id="creat_lead" tabindex="-1" role="dialog"
                aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
                <div class="modal-dialog radius-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h6 class="modal-title" id="exampleModalLabel">@lang('lead.common.edit_lead')</h6>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span data-feather="x"></span>
                            </button>
                        </div>
                        <div class="modal-body">
                            @if ($errors->has('form_error'))
                                <div class="alert alert-danger">
                                    {{ $errors->first('form_error') }}
                                </div>
                            @endif
                            <div class="justify-content-end mb-3 d-none">
                                <button class="btn btn-default btn-primary" type="button"><i
                                        class="iconsax icon fs-16" icon-name="emoji-happy"></i> Generate With
                                    AI</button>
                            </div>
                            <form wire:submit.prevent="updateLead">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label>@lang('lead.forms.label.users') <small class="required">*</small></label>
                                            <select wire:model.defer="user_id" class="form-control select2">
                                                <option value = "" selected>@lang('lead.forms.label.users')</option>

                                                @foreach ($users as $id => $name)
                                                    <option value="{{ $id }}">{{ $name }}</option>
                                                @endforeach
                                            </select>
                                            @error('user_id')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <!-- Name -->
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label>@lang('lead.forms.label.name') <small class="required">*</small></label>
                                            <input type="text" wire:model="name" class="form-control"
                                                placeholder="@lang('lead.forms.label.name')">
                                            @error('name')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Price -->
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label>@lang('lead.forms.label.email') <small class="required">*</small></label>
                                            <input type="text" wire:model="email" class="form-control"
                                                placeholder="@lang('lead.forms.label.email')">
                                            @error('email')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Phone Number -->
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label>@lang('lead.forms.label.phone') <small class="required">*</small></label>
                                            <input type="text" wire:model="phone" class="form-control"
                                                placeholder="@lang('lead.forms.label.phone')">
                                            @error('phone')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <!-- Phone Number -->
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label>@lang('lead.forms.label.subject') <small class="required">*</small></label>
                                            <input type="text" wire:model="subject" class="form-control"
                                                placeholder="@lang('lead.forms.label.subject')">
                                            @error('subject')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label>@lang('lead.forms.label.follow_up_date') <small class="required">*</small></label>
                                            <div class="position-relative">
                                                <input type="date" wire:model="follow_up_date"
                                                    class="form-control " placeholder="@lang('lead.forms.label.follow_up_date')">
                                                {{-- <span data-feather="calendar" class="field-icon"></span> --}}
                                                @error('follow_up_date')
                                                    <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    {{-- @if ($isEdit)
                                        <input type="hidden" wire:model="leadId" />
                                    @endif --}}
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn bg-hold-light text-white"
                                        wire:click="closeModal">
                                        @lang('lead.forms.button.cancel')
                                    </button>
                                    <button type="submit" class="btn bg-new-primary">
                                        @lang('lead.forms.button.update')
                                    </button>
                                </div>
                            </form>
                        </div>



                    </div>
                </div>
            </div>
            {{-- @livewireScripts --}}

            <!-- Create Note Modal -->
            <div wire:ignore.self class="modal fade" id="createNoteModal" tabindex="-1"
                aria-labelledby="createNoteModalLabel" aria-hidden="true" data-bs-backdrop="static">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="createNoteModalLabel">@lang('lead.lead_stage.create_note')</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            @if (session()->has('error'))
                                <div class="alert alert-danger">
                                    {{ session()->get('error') }}
                                </div>
                            @endif

                            @if (session()->has('message'))
                                <div class="alert alert-success">
                                    {{ session()->get('message') }}
                                </div>
                            @endif
                            <form wire:submit.prevent="createNote">
                                <!-- Note Content Field -->
                                <div class="form-group">
                                    <label for="note_content">@lang('lead.lead_stage.note_content')</label>
                                    <textarea wire:model="note_content" class="form-control" id="note_content" placeholder="@lang('lead.lead_stage.note_content')"
                                        rows="5"></textarea>
                                    @error('note_content')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary"
                                        data-dismiss="modal">@lang('lead.common.cancel')</button>
                                    <button type="submit" class="btn btn-primary">@lang('lead.common.save')</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>


            <!-- Modal Follow up Status -->
            <div wire:ignore.self class="modal fade" id="update-followup" tabindex="-1" role="dialog"
                aria-labelledby="exampleModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="exampleModalLabel">
                                {{ __('lead.modal.update_follow_up_status') }}</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                                wire:click.prevent="resetUpdateFollowUpModal">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            @if (session()->has('error'))
                                <div class="alert alert-danger">
                                    {{ session()->get('error') }}
                                </div>
                            @endif

                            @if (session()->has('follow_message'))
                                <div class="alert alert-success">
                                    {{ session()->get('follow_message') }}
                                </div>
                            @endif
                            <form>
                                <div class="form-group">
                                    <label class="form-label">{{ __('work_order.common.select_status') }}</label>
                                    <div class="atbd-select">
                                        <select class="form-control" id="selectStatus"
                                            wire:model="u_follow_up_status">
                                            @foreach ($follow_up_statuses as $state)
                                                <option value="{{ $state['value'] }}" @selected($u_follow_up_status == $state['value'])>
                                                    {{ __("lead.followup_statuses.{$state['value']}") }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                {{--                                @if ($u_follow_up_status === 'rescheduled') --}}
                                <div class="form-group {{ $u_follow_up_status == 'rescheduled' ? '' : 'hide' }}">
                                    <label class="form-label">{{ __('lead.forms.label.follow_up_date') }}</label>
                                    <div class="position-relative" wire:ignore>
                                        {{--                                            <input class="form-control datepicker" --}}
                                        {{--                                                   placeholder="{{ __('lead.forms.label.follow_up_date') }}"> --}}

                                        {{--                                            datepicker --}}
                                        <input name="followup_date" id="followup_date" type="text"
                                            value="{{ $current_follow_up_reschedule_date }}"
                                            class="form-control" inline-datepicker="" datepicker-autohide
                                            placeholder="{{ __('lead.forms.label.follow_up_date') }}">

                                        <input type="hidden" wire:model="u_follow_up_reschedule_date">
                                        <i class="iconsax field-icon" icon-name="calendar-2"></i>
                                    </div>
                                </div>
                                {{--                                @endif --}}
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"
                                wire:click.prevent="resetUpdateFollowUpModal">{{ __('complaints.close') }}</button>
                            <button type="button" class="btn btn-primary"
                                wire:click.prevent="updateFollowUpStatusAction"
                                wire:loading.attr="disabled">{{ __('admin.akaunting::bills.save') }}</button>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>

    {{-- <div class="contents">
         <div class="container-fluid">
             <div class="row">
                 <div class="col-lg-12">
                     <div class="breadcrumb-main user-member justify-content-sm-between ">
                         <div class=" d-flex flex-wrap justify-content-center breadcrumb-main__wrapper">
                             <div class="d-flex align-items-center user-member__title justify-content-center mr-sm-25 page-title__left">
                                 <h4 class="text-capitalize fw-500 breadcrumb-title"><a href="{{ url()->previous() }}"><i class="las la-arrow-left"></i></a> {{__('user_management_module.common.users_list')}}</h4>
                             </div>
                         </div>
                     </div>
                     <div class="row">
                         <div class="PropertyListEmpty">
                             <img src="{{asset('empty-icon/Tiredness_amico.svg')}}" class="fourth_img" alt="">
                             <h4 class="first_title">{{__('general_sentence.empty_ui.No_users_yet')}}</h4>
                             <h6 class="second_title">{{__('general_sentence.empty_ui.The_users_list_will_appear_here')}}</h6>
                             <div class="action-btn">
                                 <a href="{{ route('users.create.info') }}" class="btn px-15 btn-primary third_button">
                                     <i class="las la-plus fs-16"></i>{{__('user_management_module.user_button.add_new_user')}}</a>
                             </div>
                         </div>
                     </div>
                 </div>
             </div>
         </div>
     </div> --}}

    <div class="modal-info-delete modal fade show" id="modal-info-delete" tabindex="-1" role="dialog"
        aria-hidden="true">
        <div class="modal-dialog modal-sm modal-info" role="document">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="modal-info-body d-flex">
                        <div class="modal-info-icon warning">
                            <span data-feather="info"></span>
                        </div>
                        <div class="modal-info-text">
                            <h6>Do you Want to delete User?</h6>
                            <div class="atbd-select">
                                <select class="form-control select2 h-auto" name="worker_id" id="worker_id">
                                    <option value="">{{ __('work_order.forms.label.Choose_Worker') }} </option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger btn-outlined btn-sm"
                        data-dismiss="modal">No</button>
                    <button type="button" class="btn btn-success btn-outlined btn-sm delete_user">Confirm</button>
                </div>
            </div>
        </div>
    </div>

    <!--Create Lead Stage Modal -->
    <div class="modal fade" id="creat-leadstage" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="exampleModalLabel">Create Lead Stage</h6>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span data-feather="x"></span>
                    </button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="form-group">
                            <label>Lead Stage Name <small class="required">*</small></label>
                            <input type="text" class="form-control" placeholder="Enter Lead Stage Name"
                                name="">
                        </div>
                        <div class="form-group">
                            <label>Pipeline <small class="required">*</small></label>
                            <select class="select2" id="pipeline_list">
                                <option>Pipeline1</option>
                                <option>Pipeline2</option>
                                <option>Pipeline3</option>
                                <option>Pipeline4</option>
                                <option>Pipeline5</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default btn-light" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary">Submit</button>
                </div>
            </div>
        </div>
    </div>

    <!--Delete Lead Stage Modal -->
    <div class="modal fade delete" id="delete-leadstage" tabindex="-1" role="dialog"
        aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header d-none">
                    <h6 class="modal-title" id="exampleModalLabel">Delete Lead Stage</h6>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span data-feather="x"></span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <h1 class="text-danger mb-4"><i class="las la-exclamation-circle fs-60"></i></h1>
                        <h5 class="mb-3">Are you Sure?</h5>
                        <p>Your are about to delete <b>Open</b> Lead Stage</p>
                    </div>
                </div>
                <div class="modal-footer justify-content-between border-0">
                    <button type="button" class="btn btn-default btn-light flex-fill" data-dismiss="modal">No, Keep
                        It</button>
                    <button type="button" class="btn btn-danger flex-fill">Yes, Delete It</button>
                </div>
            </div>

        </div>
    </div>

    {{-- conversion popup --}}
    @include('livewire.common.super-modal-v1', [
        'component' => 'leads.modals.deal-conversion',
        'modalId' => 'convertDeal',
    ])

    {{-- conversion success  --}}
    @include('livewire.common.super-modal-v1', [
        'component' => 'leads.modals.deal-conversion-success',
        'modalId' => 'convertDealSuccess',
    ])

    @livewire('common.confirm-popup-v1')

    {{-- @endsection --}}
</div>
@section('scripts')
    <script type="text/javascript">
        document.addEventListener('livewire:load', function () {
            // Initialize Select2 on load
            $('#user_id').select2().on('change', function () {
                @this.
                set('user_id', $(this).val());
            });
        });

            $('#leads-stages').select2();
            </script>
        <script type="text/javascript">
            // $(".crm-sidebar a").click(function (e) {
            //     e.preventDefault();
            //     var target = $(this).attr("href");
            //     $(".leads-details").animate({
            //         scrollTop: $(target).offset().top
            //     }, 800);
            // });

                // $('#followup_date').datepicker();
            document.addEventListener('livewire:load', function () {
                initDatepicker();
            });

            Livewire.hook('message.processed', (message, component) => {
                initDatepicker();
            });

            function initDatepicker() {
                $('#followup_date').datepicker({
                    onSelect: function (dateText, inst) {
                        console.log('Selected date:', dateText);
                        @this.set('u_follow_up_reschedule_date', dateText);
                    }
                });
            }

            document.addEventListener('livewire:load', function () {
                Livewire.on('closeFollowUpModal', function () {
                    // Example: close Bootstrap modal
                    // let modal = bootstrap.Modal.getInstance(document.getElementById('update-followup'));
                    // modal?.hide();
                    // Or with jQuery:
                    setTimeout(()=>{
                        $('#update-followup').modal('hide');
                    },2000)
                    // Or do anything else...
                    console.log('Modal should close now.');
                });

            });

            document.addEventListener("DOMContentLoaded", function () {
                function initSelect2() {
                    $('#user_id').select2();
                }
            });

            // Re-init after DOM updates
            Livewire.hook('message.processed', () => {
                $('#user_id').select2().on('change', function () {
                    @this.set('user_id', $(this).val());
                });

                $('#leads-stages').select2();
            });

        // Modal open/close handlers
        window.addEventListener('open-modal-lead', () => $('#creat_lead').modal('show'));
        window.addEventListener('open-modal-convert', () => $('#covertToDealModal').modal('show'));
        window.addEventListener('open-modal-stage', () => $('#editStageModal').modal('show'));

        window.addEventListener('close-modal', () => {
            $('#creat_lead').modal('hide');
            cleanupModal();
        });

        window.addEventListener('close-modal-note', () => {
            $('#createNoteModal').modal('hide');
            cleanupModal();
        });

        window.addEventListener('close-modal-convert', () => {
            $('#covertToDealModal').modal('hide');
            cleanupModal();
        });

        window.addEventListener('close-modal-users', () => {
            $('#addUserModal').modal('hide');
            cleanupModal();
        });

        window.addEventListener('close-modal-stage', () => {
            $('#editStageModal').modal('hide');
            cleanupModal();
        });

        window.addEventListener('reload-page', () => window.location.reload());
        window.addEventListener('reload-page-stage', () => window.location.reload());

        // Sidebar scrolling
        $(document).ready(function () {
            $(".crm-sidebar a").click(function (e) {
                e.preventDefault();
                let target = $(this).attr("href");

                if (target?.startsWith("#")) {
                    let targetEl = $(target);
                    let scrollPosition = targetEl.offset().top - $(".leads-details").offset().top + $(".leads-details").scrollTop();
                    $(".leads-details").animate({ scrollTop: scrollPosition }, 500);
                }
            });
        });

        function cleanupModal() {
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        }

        $(document).ready(function () {
            $(document).ready(function() {
                // Initialize Select2 for initially visible tab
                $('.tab-pane.show.active .select2-new').select2();
                $('[data-toggle="tab"]').on('click', function(e) {
                    e.preventDefault();
                    var target = $(this).attr('href');
                    // 1. Add 'fade' class if not present (for proper animation)
                    $(target).addClass('fade');
                    // 2. Hide current active tab (with fade out)
                    $('.tab-pane.show.active').removeClass('show active');
                    // 3. Show new tab (with fade in)
                    $(target).addClass('active');
                    setTimeout(() => {
                        $(target).addClass('show');
                    }, 10);
                });
            });
            // Toggle between new/existing contact
            $('input[name="contact_type"]').on('change', function () {
                var selected = $(this).val(); // "new-contact" or "existing-contact"
                $('.contact-type.new-contact, .contact-type.existing-contact').hide();
                $('.contact-type.' + selected).fadeIn();
            });
            // Toggle between deal-yes/deal-no inside existing-contact section only
            $('input[name="new_deal"], input[name="transfer_data"]').on('change', function () {
                var selected = $(this).val(); // "deal-yes" or "deal-no"
                var wrapper = $(this).closest(".show-hide-radio");
                wrapper.find('.contact-type').hide();
                wrapper.find('.' + selected).fadeIn();
            });
            // Trigger change on load to reflect initial states
            $('input[name="contact_type"]:checked').trigger('change');
            $('input[name="new_deal"]:checked').trigger('change');
            $('input[name="transfer_data"]:checked').trigger('change');
        });
    </script>
    <script>
        $(document).on('click', '#convertDeal [data-dismiss="modal"]',function (e) {
            e.preventDefault();
           window.Livewire.emitTo(
                'common.confirm-popup-v1',
                'confirmPopup',
                'saveDraft',
                '@lang("Confirm")',
                "@lang('Would you like to save draft?')",
                [],
                "@lang('No, Just Cancel')",
                "@lang('Yes, Draft It!')"
            );
        });
        $(document).on('click', '#globalConfirmModal .ok',function (e) {
           showLoader();
        });
    </script>
@endsection
