@section('styles')
    <style type="text/css">
        .daterangepicker {
            z-index: 999999 !important;
        }
    </style>
@endsection
<div>

    <div class="row">
        <div class="col-lg-12">
            <div class="breadcrumb-main user-member justify-content-sm-between ">
                <div class=" d-flex flex-wrap justify-content-center breadcrumb-main__wrapper">
                    <div
                        class="d-block align-items-center user-member__title justify-content-center mr-sm-25 page-title__left">
                        <h4 class="text-capitalize fw-500 breadcrumb-title">
                            {{__('data_maintanance_request.common.maintanance_request_list')}}</h4>
                        <span
                            class="d-block sub-title t_maintanance_request before-none ml-0">{{$data['total_row_count']}} {{__('data_maintanance_request.common.maintanance_request')}}</span>
                    </div>

                    <!-- Search box-->
                    <div class="d-flex align-items-center user-member__form my-sm-0 my-2">
                        {{--                         style="border-radius: 10px!important;">--}}
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                             fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                             stroke-linejoin="round" class="feather feather-search">
                            <circle cx="11" cy="11" r="8"></circle>
                            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                        </svg>
                        <input autocomplete="off"
                               class="form-control mr-sm-2 border-0 box-shadow-none"
                               type="search" placeholder="{{__('work_order.button.search_by_mr_id')}}"
                               aria-label="Search" wire:model.debounce.300ms="search">
                        {{--                                    <input type="hidden" id="site_url" value="{{ url('/') }}"/>--}}
                    </div>
                    <!-- Search box-->
                </div>
                @if(( !empty( Auth::user()->isSuperAdmin() ) || !empty( Auth::user()->isOsoolAdmin() ) || !empty( Auth::user()->isProjectOwner() ) || !empty( Auth::user()->isProjectOwnerEmployee() )))
                    <div class="action-btn">
                        <a href="{{ route('property.create') }}" class="btn px-15 btn-primary">
                            <i class="las la-plus fs-16"></i>{{__('data_properties.property_button.add_new_property')}}
                        </a>
                    </div>
                @elseif(!Auth::user()->isBuildingManager() && !Auth::user()->isBuildingManagerEmployee() )
                    <div class="action-btn">
                        @php
                            $users = Auth::user();
                            $user_privileges = json_decode($users->user_privileges);
                            if(isset($user_privileges))
                            {
                            if(isset($user_privileges->property))
                            {
                                foreach($user_privileges->property as $property)
                                {
                                    if(!empty($property=='create'))
                                    { @endphp
                        <a href="{{ route('property.create') }}" class="btn px-15 btn-primary">
                            <i class="las la-plus fs-16"></i>{{__('data_properties.property_button.add_new_property')}}
                        </a>
                        @php }
                                    }
                                }
                                }
                        @endphp
                    </div>
                @endif
                <a href="{{ route('maintenance_requests.list.grouped') }}"
                   class="btn btn-default btn-primary text-white" id="dropdownMenu2">
                    <i class="lab la-buffer fs-16"></i> {{__('data_maintanance_request.common.unit_receival_requests')}}
                </a>

            </div>

        </div>
    </div>


    <div class="row">
        <div class="col-lg-12">
            <div class="project-top-wrapper project-top-progress wo-top-bar">
                <div class="d-md-flex justify-content-md-between">
                    <!-- End: .project-top-left -->
                    <div class="d-md-flex wo-left">
                        <div class="d-flex justify-content-between wo-left-one">
                            @if(!Auth::user()->isSupervisor() && !Auth::user()->isProjectOwnerEmployee() )
                                <div class="project-top-left flex-fill">
                                    <div class=""><p
                                            class="mb-0 mr-10 fs-14 color-light">{{__('work_order.forms.place_holder.choose_property')}}
                                            :</p></div>
                                    <div
                                        class="project-tap global-shadow order-lg-1 order-2 my-10 float-xl-left float-lg-left w-100 property-drop"
                                        style="min-width: 200px;">
                                        <!-- Default dropup button -->
                                        <div class="btn-group check-dropdown-toggle w-100">
                                            <label class="py-2 dropdown-toggle px-3 mb-0 property_label"
                                                   data-toggle="dropdown" aria-haspopup="true"
                                                   aria-expanded="false">
                                                <img src="{{ asset('img/svg/building.svg') }}" class="mr-1">
                                                @if(count($buildings) > 0)
                                                    {{ count($buildings) }} {{ __('work_order.forms.place_holder.selected') }}
                                                @else
                                                    {{__('work_order.forms.place_holder.property')}}
                                                @endif
                                                <span class="toggle-icon ml-10"></span>
                                            </label>
                                            <div class="dropdown-menu">
                                                <div class="check-dropdown multi-field">
                                                    {{-- @flip2@searchbar --}}
                                                    <div class="px-2 mb-3">
                                                        <input id="searchbar" class="form-control"
                                                               onkeyup="search_property(this)" type="text"
                                                               name="search"
                                                               autocomplete="off"
                                                               placeholder="{{ __('work_order.forms.place_holder.search_by_property_name') }}">
                                                    </div>
                                                    <div class="pl-3"><input type="checkbox" id="ap"
                                                                             value="All" class="mr-2"
                                                                             checked><label
                                                            for="ap">{{ __('work_order.forms.place_holder.all') }}</label>
                                                    </div>
                                                    <ul id="properties_div" class="site-scrollbar" wire:ignore>
                                                        {{--                                                        @json($data['properties'])--}}
                                                        @if (!empty($data['properties']))
                                                            @foreach ($data['properties'] as $properties)
                                                                @if ($properties['property_type'] == 'building')
                                                                    <li class="pl-3">
                                                                        <div><input type="checkbox"
                                                                                    name="buildings[]"
                                                                                    id="ap_{{ $properties['building_id'] }}"
                                                                                    value="{{ $properties['building_id'] }}"
                                                                                    class="mr-2 property"><label
                                                                                for="ap_{{ $properties['building_id'] }}"
                                                                                title="{{ $properties['building_name'] }}">{{ $properties['building_name'] }}</label>
                                                                        </div>
                                                                    </li>
                                                                @else
                                                                    <li class="pl-3 complex">
                                                                        <div><input type="checkbox" id=""
                                                                                    value="" class="mr-2" class = "" name="complex[]"
                                                                            ><label
                                                                                for="ap">{{ $properties['complex_name'] }}</label>
                                                                        </div>
                                                                        <ul id="">
                                                                            @foreach ($properties['property_buildings'] as $property_buildings)
                                                                                <li class="pl-3"><input
                                                                                        type="checkbox"
                                                                                        name="buildings[]"
                                                                                        
                                                                                        id="ap_{{ $property_buildings['id'] }}"
                                                                                        value="{{ $property_buildings['id'] }}"
                                                                                        class="mr-2"><label
                                                                                        for="ap_{{ $property_buildings['id'] }}"
                                                                                        title="{{ $property_buildings['building_name'] }}">{{ $property_buildings['building_name'] }}</label>
                                                                                </li>
                                                                            @endforeach
                                                                        </ul>
                                                                    </li>
                                                                @endif
                                                            @endforeach
                                                        @endif
                                                    </ul>
                                                    <div class = "dropdown-divider"></div>
                                                    <div class = "px-2">
                                                        <button class = "btn btn-primary w-100 mb-2" wire:click = "propertyFilterClicked" id = "property_filter_submit" onclick = "filterByPropertyBuildings()">
                                                            @lang('work_order.button.apply')
                                                        </button>
                                                        <button class = "btn btn-outline-danger w-100" wire:click = "propertyFilterReset" id = "property_filter_reset">
                                                            @lang('work_order.button.reset')
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                </div>
                            @endif
                        </div>
                    </div>
                    <div class="wo-right pl-md-3 d-sm-flex">

                        <div class="project-top-left pl-md-3 wo-left-two" wire:ignore>
                            <p class="mb-10 d-sm-none wop">&nbsp;</p>
                            <div class="input-container icon-left position-relative">
                                <span class="input-icon icon-left">
                                    <span data-feather="calendar"></span>
                                </span>
                                <input type="text" class="form-control form-control-default border-0 bg-white"
                                       name="text" placeholder="{{date('d-m-Y')}}"
                                       id="calender_filter_workorder" autocomplete="off" readonly>
                                <span class="input-icon icon-right">
                                    <span data-feather="chevron-down"></span>
                                </span>
                            </div>
                        </div>

                        <div class="project-top-right d-sm-block pl-sm-3 mt-sm-0 mt-10">
                            <div class="d-flex">

                                <!-- status filter -->
                                <div class="mb-sm-0 mb-10 pr-lg-0 flex-fill" wire:ignore>
                                    <p class="mb-10 d-sm-none wop">&nbsp;</p>

                                    <div class="dropdown action-btn w-100">

                                        <button
                                            class="btn btn-sm btn-default btn-white dropdown-toggle d-sm-ib w-100 d-flex justify-content-start overflow-hidden"
                                            type="button" id="dropdownMenu2" data-toggle="dropdown"
                                            aria-haspopup="true" aria-expanded="false">
                                            <i class="las la-sliders-h"></i> {{__('work_order.bread_crumbs.status')}}
                                            <span id="option_value"></span>
                                        </button>

                                        <div class="dropdown-menu" id="filter_id_section"
                                             aria-labelledby="dropdownMenu2">
                                            <a href="javascript:void(0)" data-check="false" data-value="Pending"
                                               class="dropdown-item">{{__('data_maintanance_request.bread_crumbs.pending')}}
                                                <i id="tick0" class="fa fa-check float-right display-none"
                                                   aria-hidden="true"></i></a>
                                            <a href="javascript:void(0)" data-check="false"
                                               data-value="In progress"
                                               class="dropdown-item">{{__('data_maintanance_request.bread_crumbs.in_progress')}}
                                                <i id="tick1" class="fa fa-check float-right display-none"
                                                   aria-hidden="true"></i></a>
                                            <a href="javascript:void(0)" data-check="false"
                                               data-value="Completed"
                                               class="dropdown-item">{{__('data_maintanance_request.bread_crumbs.completed')}}
                                                <i id="tick2" class="fa fa-check float-right display-none"
                                                   aria-hidden="true"></i></a>
                                            <a href="javascript:void(0)" data-check="false"
                                               data-value="Rejected"
                                               class="dropdown-item">{{__('data_maintanance_request.bread_crumbs.rejected')}}
                                                <i id="tick3" class="fa fa-check float-right display-none"
                                                   aria-hidden="true"></i></a>
                                        </div>

                                    </div>

                                </div>
                                <!-- end status filter -->


                                <!-- MR Workorders Export Button -->
                                <div class="mb-sm-0 mb-10 pr-lg-0 pl-3 flex-fill">
                                    <p class="mb-10 d-sm-none wop">&nbsp;</p>
                                    <button outlined="0" type="button"
                                            class="btn btn-sm btn-default btn-white d-sm-ib w-100"
                                            ant-click-animating-without-extra-node="false" data-toggle="modal"
                                            data-target="#export-mwo-modal">
                                        <i class="las la-download"></i>
                                        {{ __('work_order.forms.label.export') }}</button>
                                </div>
                                <!-- MR Workorders Export Button -->
                            </div>
                        </div>
                    </div>
                    <!-- End: .project-top-right -->
                </div>
            </div>
            <!-- End: .project-top-wrapper -->
        </div>

        <!-- End: .col -->
        <div class="col-lg-12 mb-30">
            <div class="">

                <!-- new search input box -->
                {{--                <div class="row my-3">--}}
                {{--                    <div class="col-12">--}}
                {{--                    </div>--}}
                {{--                </div>--}}
                <!-- end new search input box -->

                <div class="card">
                    <div class="card-body">
                        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                            <div class="table-responsive justify-content-center">
                                <table class="table mb-0 table-borderless">

                                    <thead>
                                    <tr class="userDatatable-header">
                                        <th>
                                            <span
                                                class="projectDatatable-title">{{__('data_maintanance_request.common.maintanance_request_id')}}</span>
                                        </th>
                                        <th>
                                            <span
                                                class="projectDatatable-title">{{__('data_maintanance_request.common.name')}}</span>
                                        </th>
                                        <th>
                                            <span
                                                class="projectDatatable-title">{{__('data_maintanance_request.common.building')}}</span>
                                        </th>
                                        <th>
                                            <span
                                                class="projectDatatable-title ">{{__('data_maintanance_request.common.floor')}}</span>
                                        </th>
                                        <th>
                                            <span
                                                class="projectDatatable-title">{{__('data_maintanance_request.common.space_unit')}}</span>
                                        </th>
                                        <th>
                                            <span
                                                class="projectDatatable-title">{{__('data_maintanance_request.common.description')}}</span>
                                        </th>
                                        <th>
                                            <span
                                                class="projectDatatable-title">{{__('data_maintanance_request.common.status')}}</span>
                                        </th>
                                        <th>
                                            <span
                                                class="projectDatatable-title">{{__('data_maintanance_request.common.submited_date')}}</span>
                                        </th>
                                        <th>
                                            <span
                                                class="projectDatatable-title">{{__('data_maintanance_request.common.submited_from')}}</span>
                                        </th>
                                        <th>
                                            <span
                                                class="projectDatatable-title">{{__('data_maintanance_request.common.is_reschedule')}}</span>
                                        </th>
                                        <th>
                                            <span
                                                class="projectDatatable-title">{{__('data_maintanance_request.common.actions')}}</span>
                                        </th>
                                        <th>
                                        </th>
                                    </tr>
                                    </thead>

                                    @forelse($maintenanceRequests as $maintenanceRequest)
                                        <tbody>
                                        <tr >
                                            <td> {{ $maintenanceRequest->id }} </td>
                                            <td>{{ \App\Http\Helpers\MaintenancePortalHelper::getMaintenanceCreatedName($maintenanceRequest->generated_from, $maintenanceRequest->name, $maintenanceRequest->phone, $maintenanceRequest->user_id) }}</td>
                                            <td>{{ \App\Http\Helpers\MaintenancePortalHelper::getBuildingNameByBuildingDetails($maintenanceRequest->building_details)  }}</td>
                                            <td>{{ $maintenanceRequest->floor ?? '-' }}</td>
                                            <td>{{ $maintenanceRequest->space_no ?? '-' }}</td>
                                            <td>{{ $maintenanceRequest->description ?? __('user_management_module.user_forms.label.no_description_added') }}</td>
                                            <td>
                                                @switch(\App\Http\Helpers\MaintenancePortalHelper::newGetMaintenanceRequestStatus($maintenanceRequest->singleWorkOrder,$maintenanceRequest->status))
                                                    @case('Rejected')
                                                        <span
                                                            class="badge-rejected rounded-pill userDatatable-content-status active">{{ __('data_maintanance_request.bread_crumbs.rejected') }}</span>
                                                        @break
                                                    @case('In Progress')
                                                        <span
                                                            class="badge-in-progress rounded-pill userDatatable-content-status active">{{ __('data_maintanance_request.bread_crumbs.in_progress') }}</span>
                                                        @break
                                                    @case('Completed')
                                                        <span
                                                            class="badge-completed rounded-pill userDatatable-content-status active">{{ __('data_maintanance_request.bread_crumbs.completed') }}</span>
                                                        @break
                                                    @case('Finished')
                                                        <span
                                                            class="bg-opacity-success color-pending rounded-pill userDatatable-content-status active">{{ __('data_maintanance_request.bread_crumbs.finished') }}</span>
                                                        @break
                                                    @default
                                                        <span
                                                            class="badge-pending rounded-pill userDatatable-content-status active">{{ __('data_maintanance_request.bread_crumbs.pending') }}</span>
                                                @endswitch
                                            </td>
                                            <td>{{ date('d/m/Y h:i A', strtotime($maintenanceRequest->created_at)) }}</td>
                                            <td>
                                                @if($maintenanceRequest->generated_from == 'app' && $maintenanceRequest->app_type != 'tenant')
                                                        {{ __('data_maintanance_request.common.worker_app') }}
                                                @elseif($maintenanceRequest->generated_from == 'app' && $maintenanceRequest->app_type == 'tenant')
                                                        {{ __('data_maintanance_request.common.tenant_app') }}
                                                @else
                                                        {{ __('data_maintanance_request.common.maintenance_portal') }}
                                                @endif
                                            </td>
                                            <td>
                                                @if($maintenanceRequest->reschedule->count() > 0)
                                                        {{ __('data_maintanance_request.common.yes') }}
                                                         <a class="view"
                                                            wire:click="$emit('openRescheduleModal', {{ $maintenanceRequest->id }})"
                                                            href="javascript:void(0)" title="View">
                                                            <i class="la la-eye"></i>
                                                         </a>
                                                @else
                                                        {{ __('data_maintanance_request.common.no') }}
                                                @endif
                                            </td>
                                            <td>

                                                <ul class="orderDatatable_actions mb-0"
                                                    style="min-width:0px; max-width:50px;">
                                                    <li>
                                                        <a class = "view showdetails" data-id = "{{ $maintenanceRequest->id }}" title = "@lang('import.view')" href = "javascript:void(0)" wire:click = "setSelectedMaintenanceRequest({{ $maintenanceRequest->id }})">
                                                            <i class = "la la-eye"></i>
                                                        </a>
                                                    </li>
                                                </ul>

                                            </td>
                                        </tr>
                                        </tbody>
                                    @empty
                                        <tbody>
                                        <tr class="odd">
                                            <td valign="top" colspan="10" class="dataTables_empty">
                                                <div class="row">
                                                    <div class="PropertyListEmpty"><img
                                                            src="https://staging.osool.cloud/empty-icon/To_do_list_rafiki.svg"
                                                            class="fourth_img" alt=""><h4
                                                            class="first_title">{{ __('data_maintanance_request.common.No_results_found') }}</h4>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    @endforelse


                                </table><!-- End: .table -->

                                {{--                                <table id="loading-table" wire:loading class="table mb-0 table-borderless">--}}
                                {{--                                    <thead>--}}
                                {{--                                    <tr class="userDatatable-header">--}}
                                {{--                                        <th>--}}
                                {{--                                            <span--}}
                                {{--                                                class="projectDatatable-title">{{__('data_maintanance_request.common.maintanance_request_id')}}</span>--}}
                                {{--                                        </th>--}}
                                {{--                                        <th>--}}
                                {{--                                            <span--}}
                                {{--                                                class="projectDatatable-title">{{__('data_maintanance_request.common.name')}}</span>--}}
                                {{--                                        </th>--}}
                                {{--                                        <th>--}}
                                {{--                                            <span--}}
                                {{--                                                class="projectDatatable-title">{{__('data_maintanance_request.common.building')}}</span>--}}
                                {{--                                        </th>--}}
                                {{--                                        <th>--}}
                                {{--                                            <span--}}
                                {{--                                                class="projectDatatable-title ">{{__('data_maintanance_request.common.floor')}}</span>--}}
                                {{--                                        </th>--}}
                                {{--                                        <th>--}}
                                {{--                                            <span--}}
                                {{--                                                class="projectDatatable-title">{{__('data_maintanance_request.common.space_unit')}}</span>--}}
                                {{--                                        </th>--}}
                                {{--                                        <th>--}}
                                {{--                                            <span--}}
                                {{--                                                class="projectDatatable-title">{{__('data_maintanance_request.common.description')}}</span>--}}
                                {{--                                        </th>--}}
                                {{--                                        <th>--}}
                                {{--                                            <span--}}
                                {{--                                                class="projectDatatable-title">{{__('data_maintanance_request.common.status')}}</span>--}}
                                {{--                                        </th>--}}
                                {{--                                        <th>--}}
                                {{--                                            <span--}}
                                {{--                                                class="projectDatatable-title">{{__('data_maintanance_request.common.submited_date')}}</span>--}}
                                {{--                                        </th>--}}
                                {{--                                        <th>--}}
                                {{--                                            <span--}}
                                {{--                                                class="projectDatatable-title">{{__('data_maintanance_request.common.submited_from')}}</span>--}}
                                {{--                                        </th>--}}
                                {{--                                        <th>--}}
                                {{--                                            <span--}}
                                {{--                                                class="projectDatatable-title">{{__('data_maintanance_request.common.actions')}}</span>--}}
                                {{--                                        </th>--}}
                                {{--                                        <th>--}}
                                {{--                                        </th>--}}
                                {{--                                    </tr>--}}
                                {{--                                    </thead>--}}
                                {{--                                    <tbody>--}}
                                {{--                                    <tr>--}}
                                {{--                                        --}}{{--                                        <td class="td-1"><span></span></td>--}}
                                {{--                                        <td class="td-2"><span></span></td>--}}
                                {{--                                        <td class="td-3"><span></span></td>--}}
                                {{--                                        <td class="td-3"><span></span></td>--}}
                                {{--                                        <td class="td-3"><span></span></td>--}}
                                {{--                                        <td class="td-3"><span></span></td>--}}
                                {{--                                        <td class="td-3"><span></span></td>--}}
                                {{--                                        <td class="td-3"><span></span></td>--}}
                                {{--                                        <td class="td-3"><span></span></td>--}}
                                {{--                                        <td class="td-3"><span></span></td>--}}
                                {{--                                        <td class="td-4"></td>--}}
                                {{--                                        <td class="td-5"><span></span></td>--}}
                                {{--                                    </tr>--}}
                                {{--                                    </tbody>--}}
                                {{--                                </table>--}}
                                @if(count($maintenanceRequests) > 0)
                                    <div class="row justify-content-between">
                                        <div>
                                            <ul class="atbd-pagination d-flex justify-content-between">
                                                <li>
                                                    <div class="paging-option">
                                                        <div class="dataTables_length d-flex">
                                                            <label class="d-flex align-items-center">
                                                                @lang('general_sentence.show')
                                                                <select aria-controls="workorder_table"
                                                                        wire:model="perPage"
                                                                        class="custom-select custom-select-sm form-control form-control-sm mx-2"
                                                                        style="min-height: 35px;">
                                                                    <option value="5">5</option>
                                                                    <option value="10">10</option>
                                                                    <option value="25">25</option>
                                                                    <option value="50">50</option>
                                                                    <option value="100">100</option>
                                                                </select>
                                                                @lang('general_sentence.results')
                                                            </label>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="user-pagination">
                                            <div class="d-flex justify-content-sm-end justify-content-end mt-1 mb-30">
                                                {!!$maintenanceRequests->links("vendor.pagination.livewire-pagination-2")!!}
                                            </div>
                                        </div>

                                    </div>
                                @endif
                            </div>
                        </div><!-- End: .userDatatable -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <input type="hidden" id="data_url" value="{{route('maintenance_requests.list')}}">
    <input type="hidden" id="data_url_ajax" value="{{route('maintenance_requests.list_post')}}">
    <input type="hidden" id="data_detail_url_ajax" value="{{route('maintenance_requests.list_details')}}">


    <div class="image-zoom align-items-center justify-content-center">
        <div class="close-img"><span data-feather="x" class=""></span></div>
        <div class="d-flex image-zoom-btn justify-content-center rounded-pill">
            <span class="bg-white border-right  download-image" title="Download"><span
                    data-feather="download"></span></span>
            <span class="bg-white rotate-img" title="Rotate"><span data-feather="rotate-cw"></span></span>
        </div>
    </div>

    <div class="modal-basic modal fade show" id="modal-basic-details" tabindex="-1" role="dialog" aria-hidden="true" wire:ignore>
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content modal-bg-white">
                <div class="modal-header">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="page-title-wrap pt-0 pb-0">
                                <ul class="atbd-breadcrumb nav">
                                    <li class="atbd-breadcrumb__item">
                                        <a href="javascript:void(0)"> {{__('data_maintanance_request.bread_crumbs.maintanance_request')}}</a>
                                        <span class="breadcrumb__seperator">
                                        #<span class="maintanance_id">66</span>
                                    </span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <h6 class="modal-title"></h6>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span data-feather="x"></span></button>
                </div>
                <div class="modal-body">
                    <form action="{{ route('maintenance_requests.details') }}" method="post"
                          id="maintanance_request_form"
                          enctype="multipart/form-data">
                        <input type="hidden" id="maintanance_request_id" name="maintanance_request_id" value="">
                        @csrf
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="name1">{{ __('data_maintanance_request.common.building')  }}</label>
                                    <div class="building">457</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="name1">{{__('data_maintanance_request.common.floor')}}</label>
                                    <div class="floor">457</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="name1">{{__('data_maintanance_request.common.space_unit')}}</label>
                                    <div class="space_unit">457</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="name1">{{__('data_maintanance_request.common.name')}}</label>
                                    <div class="name">Anup kr. bhakta</div>
                                </div>
                                <div class="form-group">
                                    <label for="name1">{{__('data_maintanance_request.common.description')}}</label>
                                    <div class="description">
                                        Hi i am testing
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="name1">{{__('data_maintanance_request.common.phone')}}</label>
                                    <div class="phone">4646788999</div>
                                </div>
                                <div class="form-group asset_category_div">
                                    <label
                                        for="name1">{{__('configration_checklist.checklist_forms.label.asset_category')}}</label>
                                    <div class="asset_category">
                                        Hi i am testing
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group workorderid_div">
                                    <label for="name1"
                                           class="email_label">{{__('data_maintanance_request.common.email')}}</label>
                                    <div class="email"><EMAIL></div>
                                </div>
                                <div class="form-group feedback1">
                                    <label for="name1">{{__('data_maintanance_request.common.feedback')}}</label>
                                    <div class="feedback">
                                        Hi i am testing
                                    </div>
                                </div>

                                <!-- <div class="form-group warranty_status_div d-none">
                                <label for="name1">{{__('user_management_module.user_forms.label.warranty_status')}}</label>
                                <div class="warranty_status_text">
                                    Hi i am testing
                                </div>
                            </div> -->

                                <!-- <div class="form-group rejection_reason_div d-none">
                                <label for="name1">{{__('data_maintanance_request.common.reason_of_rejection')}}</label>
                                <div class="rejection_reason">
                                    Hi i am testing
                                </div>
                            </div> -->

                                <div class="form-group asset_name_reason">
                                    <label
                                        for="name1">{{__('data_maintanance_request.property_forms.label.Asset_Name')}}</label>
                                    <div class="asset_name">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group rejection_reason_div d-none">
                                    <label
                                        for="name1">{{__('data_maintanance_request.common.reason_of_rejection')}}</label>
                                    <div class="rejection_reason">
                                        Hi i am testing
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group asset_name_div hide">
                                    <label
                                        for="name1">{{__('data_maintanance_request.property_forms.label.Asset_Name')}}</label>
                                    <div class="asset_name">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group rejection_reason_div_bottom d-none">
                                    <label
                                        for="name1">{{__('data_maintanance_request.common.reason_of_rejection')}}</label>
                                    <div class="rejection_reason">
                                        Hi i am testing
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="row">
                        <div class="col-md-12">
                            <div class="form-group rejection_reason_div_bottom d-none">
                                <label for="name1">{{__('data_maintanance_request.common.reason_of_rejection')}}</label>
                                <div class="rejection_reason">
                                    Hi i am testing
                                </div>
                            </div>
                        </div>
                    </div> -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label
                                        for="name1">{{__('data_maintanance_request.common.uploaded_pictures')}}</label>
                                    <div class="no_pictures_uploaded_class hide">
                                        {{__('data_maintanance_request.common.no_pictures_uploaded')}}
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="maint-req d-flex">
                            <div class="image1-block view-img">
                                <img class="image1 image rounded mr-2" src="">
                            </div>
                            <div class="image2-block view-img">
                                <img class="image2 image rounded mr-2" src="">
                            </div>
                            <div class="image3-block view-img">
                                <img class="image3 image rounded mr-2" src="">
                            </div>
                        </div>
                        <div
                            class="d-flex pt-40 justify-content-md-end justify-content-center button-group d-flex pt-25 justify-content-end">
                            @if(Auth::user()->isBuildingManager() || Auth::user()->isBuildingManagerEmployee())
                                @if(Helper::checkLoggedinUserPrivilegesWithoutRedirect('no_view','workorder')['success'])
                                @elseif((Helper::checkLoggedinUserPrivilegesWithoutRedirect('create','workorder')['success']) || (Helper::checkLoggedinUserPrivilegesWithoutRedirect('edit','workorder')['success']))
                                    <button onClick="rejectDetails()" data-toggle="modal"
                                            data-target="#modal-reject-details" type="reset" data-dismiss="modal"
                                            class="btn btn-light btn-default btn-squared fw-400 text-capitalize radius-md"
                                            id="resetCategory">{{__('data_maintanance_request.buttons.reject')}}
                                    </button>
                                    <button type="submit"
                                            class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 createCategory1SRK"
                                            id="createCategory1SRK">{{__('data_maintanance_request.buttons.submit')}}
                                    </button>
                                @else

                                @endif
                            @else
                                <button onClick="rejectDetails()" data-toggle="modal"
                                        data-target="#modal-reject-details" type="reset" data-dismiss="modal"
                                        class="btn btn-light btn-default btn-squared fw-400 text-capitalize radius-md"
                                        id="resetCategory">{{__('data_maintanance_request.buttons.reject')}}
                                </button>
                                <button type="submit"
                                        class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 createCategory1SRK"
                                        id="createCategory1SRK">{{__('data_maintanance_request.buttons.submit')}}
                                </button>
                            @endif

                            @if(Auth::user()->isBuildingManager() || Auth::user()->isBuildingManagerEmployee())
                                @if(Helper::checkLoggedinUserPrivilegesWithoutRedirect('no_view','workorder')['success'])
                                @else
                                    <a href="{{route('workorder.show')}}"
                                       class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2"
                                       id="view_work_order">{{__('work_order.button.view_work_order')}}</a>
                                @endif
                            @else
                                <a href="{{route('workorder.show')}}"
                                   class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2"
                                   id="view_work_order">{{__('work_order.button.view_work_order')}}</a>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>


    <div class="modal-basic modal fade show" id="modal-reject-details" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content modal-bg-white ">
                <div class="modal-header">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="page-title-wrap pt-0 pb-0">
                                <ul class="atbd-breadcrumb nav">
                                    <li class="atbd-breadcrumb__item">
                                        <a href="{{route('configuration.service_assets.category')}}"> {{__('data_maintanance_request.bread_crumbs.maintanance_request')}}</a>
                                        <span class="breadcrumb__seperator">
                                        <span class="maintanance_id">#66</span>
                                    </span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <h6 class="modal-title"></h6>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            data-feather="x"></span></button>
                </div>
                <div class="modal-body">
                    <form action="{{ route('maintenance_requests.reject') }}" method="post" id="maintanance_reject_form"
                          enctype="multipart/form-data">
                        <input type="hidden" id="reject_maintanance_request_id" name="maintanance_request_id" value="">
                        @csrf
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label
                                        for="name1">{{__('data_maintanance_request.common.reject_description')}}</label>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="name1">{{__('data_maintanance_request.common.description')}}<span
                                            class="required">*</span></label>
                                    <div class="textarea">
                                        <textarea rows="8" id="reason_description" name="reason_description"
                                                  class="form-control"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            class="d-flex pt-40 justify-content-md-end justify-content-center button-group d-flex pt-25 justify-content-end">
                            <button type="reset" data-dismiss="modal"
                                    class="btn btn-light btn-default btn-squared fw-400 text-capitalize radius-md"
                                    id="resetCategory">{{__('data_maintanance_request.buttons.cancel')}}
                            </button>
                            <button type="submit"
                                    class="btn btn-danger  btn-default btn-squared text-capitalize radius-md shadow2 createCategory1SRK"
                                    id="createCategory1SRK">{{__('data_maintanance_request.buttons.submit_rejection')}}
                            </button>
                            <input type="hidden" name="initiateMRExportUrl" id="initiateMRExportUrl"
                                   value="{{ route('export.initiateExportMr') }}">
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @include('applications.admin/workorder/export-modals')
    <!-- Include the Reschedule Livewire component only once -->
    @livewire('maintenance-request.reschedule')


</div>
@section('scripts')
    <script src="{{ asset('js/admin/workorder/property_filter.js') }}"></script>
    <script type="text/javascript" charset="utf8"
            src="{{ asset('js/admin/maintanance_request/table_list.js') }}"></script>
    <script>

        document.addEventListener('DOMContentLoaded', () => {
            this.livewire.hook('message.sent', (message, component) => {
                if (component.name === 'maintenance-request.table-list') {
                    // console.log("message.sent")
                    $(".loader").css("display", "block");
                    $("#overlayer").css("display", "block");
                    $(".loader-overlay").css("display", "block");
                }
            })
            this.livewire.hook('message.processed', (message, component) => {
                if (component.name === 'maintenance-request.table-list') {
                    $(".loader").css("display", "none");
                    $("#overlayer").css("display", "none");
                    $(".loader-overlay").css("display", "none");
                }
            })

            if ($('#ap').is(':checked')) {
                initProperty();
            }  
        });

        

        var building_svg = '{{ asset('img/svg/building.svg') }}';

        $('#ap').click(function () {
            console.log($(this).val())
            if ($(this).is(':checked') || $(this).val() === '' || $(this).val() === 'All') {
                // alert("Hello")
              //  @this.set("buildings", []);
                initProperty();
            }
        }); 
        
        $('#property_filter_reset').click(function () {
            initProperty();
        }); 

        $("[name='complex[]']").click(function () {
           managePropertyInputs();
        });

        $("[name='buildings[]']").click(function () {
            managePropertyInputs();
        });

        //for exporting modal
        $('#list_div').empty();
        object = @json($data['building_list']);
        all_mrs = @json($data['all_mrs']);

        all_mrs.forEach(function (item, index) {
            $('#list_div').append(`<li class="pl-3"><div><input type="checkbox" name="requests[]"onclick="checkBox()" id="${item}" value="${item}" class="mr-2" /><label for="ap" title="${item}"> ${item}</label></div></li>`);
        });
        if (Object.keys(object).length > 0) {
            $('#buildingsList').append(`<option value="All">${translations.general_sentence.All}</option>`);
        }
        Object.keys(object).forEach(key => {
            $('#buildingsList').append(`<option value="${object[key].id}">${object[key].building_name}</option>`);
        })

        $(".view-img img").click(function () {
            $(".close-img").show();
            $(".image-zoom").fadeIn().addClass("d-flex");
            var $img = $(this).clone();
            $('.image-zoom').append($img);
            $('.image-zoom').find(".view-img").removeClass("view-img");
            $('.image-zoom img').removeAttr("onclick");

        });

        $(document).on('click', '.check-dropdown-toggle .dropdown-menu', function (e) {
            e.stopPropagation();
        });

        $(document).ready(function () {
            var url = '{{route('workorder.show', Crypt::encryptString($data['work_order_id']))}}';
            //alert(url);
            @if(isset($data['mr_details']) && isset($data['mr_details']->status))

            showDetails('{{$data['mr_details']->status}}', '{{$data['mr_details']->id}}', '{{$data['mr_details']->buildingName}}', '{{$data['mr_details']->floor}}', '{{$data['mr_details']->space_no}}', '{{$data['mr_details']->name}}', '{{$data['mr_details']->phone}}', '{{$data['mr_details']->email}}', '{{$data['mr_details']->description}}', '{{$data['mr_details']->feedback_value}}', '{{$data['mr_details']->image1}}', '{{$data['mr_details']->image2}}', '{{$data['mr_details']->image3}}', '{{$data['mr_details']->apartment_no}}', '{{$data['mr_details']->generated_from}}', '{{$data['work_order_id']}}', '{{$data['mr_details']->asset_category}}', url, '{{$data['mr_details']->reason}}', '{{$data['mr_details']->user_id}}');
            $('#modal-basic-details').modal('show');
            @endif
        });

        let new_status_filter = [];
        let new_status_filter_text = [];

        $(document).on('click', '#filter_id_section a', function (event) {
            event.preventDefault();
            $(this).toggleClass('dropdown-item-checked');
            if ($(this).hasClass("dropdown-item-checked")) {
                StatusVal = $(this).data('value');
                new_status_filter.push($(this).data('value'));

                new_status_filter_text.push($(this).text());
            } else {
                new_status_filter.splice($.inArray($(this).data('value'), new_status_filter), 1);
                new_status_filter_text.splice($.inArray($(this).text(), new_status_filter_text), 1);
            }
            new_status_filter = new_status_filter.filter(function (itm, i, new_status_filter) {
                return i == new_status_filter.indexOf(itm);
            });
            var len = new_status_filter.length;

            if (len > 1) {
                $('.update_status_text').html('<i class="las la-sliders-h"></i><i class="las la-ellipsis-h" style="font-size:38px;"></i>');
            } else {
                $('.update_status_text').html('<i class="las la-sliders-h"></i>' + $(this).text());
            }
            var textVal = new_status_filter_text[0];
            if (textVal != undefined) {
                var myTextArr = textVal.split("(");
            }

            if (len > 0 && len < 2) {
                $('#option_value').html('(' + myTextArr[0] + ')');
            } else if (len >= 2) {
                $('#option_value').html('(' + myTextArr[0] + ',....)');
            } else {
                $('#option_value').html('');
            }
        @this.set('filter.status', new_status_filter)
        });


        /**Calender part section */

        $("#calender_filter_workorder").on("change", function () {
            wDateRange = [];
            var startDate = $(this).data('daterangepicker').startDate._d;
            var endDate = $(this).data('daterangepicker').endDate._d;
            startDate = moment(startDate).format('YYYY-MM-DD');
            endDate = moment(endDate).format('YYYY-MM-DD');
            wDateRange.push(startDate, endDate);
            //send date range data to livewire here
            console.log("wDateRange", wDateRange)
            @this.set('filter.date_range', wDateRange)
        });

        if (window.current_locale == "ar") {
            var arr = [
                translations.dashboard.bread_crumbs.reset,
                translations.dashboard.bread_crumbs.today,
                translations.dashboard.bread_crumbs.yesterday,
                translations.dashboard.bread_crumbs.last_7_days,
                translations.dashboard.bread_crumbs.last_30_day,
                translations.dashboard.bread_crumbs.this_month,
                translations.dashboard.bread_crumbs.last_month
            ];
            $(".ranges ul li").each(function (i) {
                // var get = $(this).attr('data-range-key'); // This is your rel value
                $(this).empty();
                if ($(this).attr("data-range-key") == "Custom Range") {
                    $(this).append(
                        translations.dashboard.bread_crumbs.custom_range
                    );
                } else if ($(this).attr("data-range-key") == "Today") {
                    $(this).append(
                        translations.dashboard.bread_crumbs.today
                    );
                } else if ($(this).attr("data-range-key") == "Yesterday") {
                    $(this).append(
                        translations.dashboard.bread_crumbs.yesterday
                    );
                } else if ($(this).attr("data-range-key") == "Last 7 Days") {
                    $(this).append(
                        translations.dashboard.bread_crumbs.last_7_days
                    );
                } else if ($(this).attr("data-range-key") == "Last 30 Days") {
                    $(this).append(
                        translations.dashboard.bread_crumbs.last_30_day
                    );
                } else if ($(this).attr("data-range-key") == "This Month") {
                    $(this).append(
                        translations.dashboard.bread_crumbs.this_month
                    );
                } else if ($(this).attr("data-range-key") == "Last Month") {
                    $(this).append(
                        translations.dashboard.bread_crumbs.last_month
                    );
                }
            });
        }
        window.addEventListener('show-toastr', event => {
                toastr.options = {
                    "closeButton": true,
                    "progressBar": true,
                    "positionClass": "toast-top-right",
                    "timeOut": "3000"
                };

                if (event.detail.type === 'success') {
                    toastr.success(event.detail.message);
                } else if (event.detail.type === 'error') {
                    toastr.error(event.detail.message);
                }
            });

        function filterByPropertyBuildings() {
            var countInputs = $("[name='buildings[]']").length;
            var countChecked = $("[name='buildings[]']:checked").length;
            var checkedInputs = $("[name='buildings[]']:checked");
            var selectedValues = [];

            if(countChecked == countInputs){
                $('#ap').prop('checked', true);
            }

            else if(countChecked == 0){
                $('#ap').prop('checked', true);
            }

            else if(countChecked < countInputs){
                $('#ap').prop('checked', false);
            }    

            checkedInputs.each(function() {
                selectedValues.push($(this).val());
            });

            Livewire.emit('setPropertyBuildings', selectedValues);
        }

        function initProperty() {
            $("[name='buildings[]']").prop('checked', true);
            $("[name='complex[]']").prop('checked', true);
        }

        function managePropertyInputs() {
            var countInputs = $("[name='buildings[]']").length;
            var countChecked = $("[name='buildings[]']:checked").length;
            var checkedInputs = $("[name='buildings[]']:checked");

            if(countChecked == countInputs){
                $('#ap').prop('checked', true);
            }

            else if(countChecked == 0){
                $('#ap').prop('checked', true);
            }

            else if(countChecked < countInputs){
                $('#ap').prop('checked', false);
            }
        }
    </script>
@endsection
