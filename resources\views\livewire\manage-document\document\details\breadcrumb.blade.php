<div>
    <div class="col-lg-12">
        <div
            class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
            @include('applications.admin.common.breadcrumb', [
                'links' => [
                    [
                        'title' => __('document_module.document'),
                    ],
                    [
                        'title' => __('document_module.details'),
                    ],
                ],
            ])
            <div class="d-flex gap-10 breadcrumb_right_icons">
                <div class="d-flex gap-10 breadcrumb_right_icons">
                    <button onclick="showLoader()" class="btn btn-default btn-primary wh-45 no-wrap"
                        wire:click="$emit('showDuplicateDocument', '{{ $itemId }}')" href="javascript:void(0);"
                        data-toggle="tooltip" title="@lang('document_module.duplicate')">
                        <i class="iconsax icon fs-18 mr-0" icon-name="document-copy"></i>
                    </button>
                </div>
                <div class="d-flex gap-10 breadcrumb_right_icons">
                    <a data-toggle="tooltip" title="@lang('document_module.download')" class="btn btn-default btn-primary wh-45 no-wrap"
                        href="/documents/document/download-pdf/{{ $itemId }}" target="_blank">
                        <i class="iconsax mr-0" icon-name="download-1"></i>
                    </a>
                </div>
                <div class="d-flex gap-10 breadcrumb_right_icons">
                    <a class="btn btn-default btn-primary wh-45 no-wrap" data-toggle="tooltip" title="@lang('document_module.preview')"
                        href="/documents/document/preview/{{ $itemId }}" target="_blank">
                        <i class="iconsax icon fs-18 mr-0" icon-name="eye"></i>
                    </a>
                </div>
                <div class="d-flex gap-10 breadcrumb_right_icons">
                    <div wire:loading class="text-center mt-10">
                        <div class="spinner-border text-info" role="status">
                            <span class="sr-only">@lang('Processing...')</span>
                        </div>
                    </div>

                    <select wire:loading.class="disabled-upload" wire:loading.class.remove="enabled-upload"
                        class="form-control" wire:model.defer='status'>
                        <option></option>
                        @foreach ($statuses as $key => $value)
                            <option value="{{ $value }}">{{ $value }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <!--====End Design for Export PDF===-->
        </div>
    </div>
</div>
@include('livewire.common.super-modal-v1', [
    'component' => 'manage-document.document.modals.duplicate',
    'modalId' => 'duplicateDocument',
])
