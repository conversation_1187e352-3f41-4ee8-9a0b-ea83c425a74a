<div>
    <div class="card comments-section mt-3" id="note-section">
        <div>
            <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
                <h6>@lang('document_module.notes')</h6>
            </div>
            <form wire:submit.prevent='save'>
                <div class="card-body d-flex flex-column align-items-end gap-10">
                    <textarea wire:model.defer='note' class="form-control textarea" placeholder="@lang('document_module.add_note')"></textarea>
                    @error('note')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                    <div wire:loading wire:target="save" class="mt-2 text-center">
                        <div class="spinner-border text-info" role="status" style="width: 1rem; height: 1rem;">
                            <span class="sr-only">@lang('Submitting...')</span>
                        </div>
                        <div class="mt-1">@lang('Submitting, please wait...')</div>
                    </div>
                    <button type="submit" class="btn btn-sm bg-new-primary"><i class="iconsax" icon-name="add"></i>
                        @lang('document_module.add')</button>
                </div>
            </form>

            <div class="card-body pt-0">
                @foreach ($details as $item)
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex gap-10">
                            <img src="https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png"
                                class="wh-45 rounded-circle">
                            <div>
                                <span class="fw-600 d-block">{!! $item['note'] !!} </span>
                                <span>{{ $item['time_ago'] }}</span>
                            </div>
                        </div>
                        <div class="d-flex">
                            @if ($deletingId == $item['id'])
                                <div class="text-center mr-10">
                                    <div class="spinner-border text-danger" role="status"
                                        style="width: 1.5rem;; height: 1.5rem;;">
                                        <span class="sr-only">@lang('Deleting...')</span>
                                    </div>
                                </div>
                            @endif
                            <a wire:click="confirmNotetDeletion({{ $item['id'] }})" data-toggle="tooltip"
                                title="@lang('document_module.delete')"
                                class="remove-comment wh-30 d-center p-2 bg-loss text-white rounded-circle text-light cursor-pointer">
                                <i class="iconsax" icon-name="trash"></i>
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>
