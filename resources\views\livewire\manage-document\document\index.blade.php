<div>
    <div class="contents crm">
        <div class="container-fluid">
            <div class="col-lg-12">
                <div
                    class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                    @include('applications.admin.common.breadcrumb', [
                        'links' => [
                            [
                                'title' => __('document_module.manage_documents'),
                            ],
                        ],
                    ])
                    <div class="d-flex gap-10 breadcrumb_right_icons">
                        <div class="d-flex gap-10 breadcrumb_right_icons">
                            <button onclick="showLoader()" class="btn btn-default btn-primary w-100 no-wrap" type="button"
                                wire:click="$emit('showCreateDocument')"><i
                                    class="las la-plus fs-16"></i>@lang('document_module.create')</button>
                        </div>
                    </div>
                    <!--====End Design for Export PDF===-->
                </div>
            </div>


            <div class="">
                <div class="card mt-3">
                    <div class="">
                        <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                            <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">@lang('document_module.manage_documents')</h6>

                            <div class="d-flex gap-10 table-search flex-wrap">
                                <div class="position-relative d-flex">
                                    <div wire:loading class="mr-10 mt-1 text-center">
                                        <div class="spinner-border text-info" role="status">
                                            <span class="sr-only">@lang('Processing...')</span>
                                        </div>
                                    </div>
                                    <input type="text" class="form-control" wire:model.debounce.500ms='search'
                                        placeholder="@lang('Search')">
                                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                                </div>
                                <div class="dropdown p-0">
                                    <button class="btn btn-export text-dark" data-toggle="dropdown" aria-haspopup="true"
                                        aria-expanded="false"><i class="iconsax icon fs-22 mr-0"
                                            icon-name="upload-1"></i>
                                        @lang('document_module.export')</button>
                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                        <a wire:click.prevent='printPage' class="dropdown-item" href="#"><i
                                                class="las la-print"></i>
                                            @lang('document_module.print')</a>
                                        <a wire:click.prevent='download("csv")' class="dropdown-item" href="#"><i
                                                class="las la-file-csv"></i>
                                            @lang('document_module.csv')</a>
                                        <a wire:click.prevent='download("excel")' class="dropdown-item"
                                            href="#"><i class="las la-file-excel"></i>
                                            @lang('document_module.excel')</a>
                                    </div>
                                </div>
                                <button data-toggle="tooltip" title="@lang('document_module.reset')" type="button"
                                    class="btn bg-opacity-loss btn-sm text-loss wh-45 radius-md"
                                    wire:click="resetFilters">
                                    <i class="iconsax mr-0 fs-18" icon-name="rotate-left"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body px-0 pt-0" id="printableArea">
                        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                            <div class="table-responsive">
                                <table class="table mb-0 radius-0 th-osool">
                                    <thead>
                                        <tr class="userDatatable-header">
                                            <th wire:click="sort('document_id')">
                                                <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                                @lang('document_module.document')
                                            </th>
                                            <th wire:click="sort('subject')">
                                                <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                                @lang('document_module.subject')
                                            </th>
                                            <th wire:click="sort('user_name')">
                                                <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                                @lang('document_module.user')
                                            </th>
                                            <th wire:click="sort('type_name')">
                                                <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                                @lang('document_module.type')
                                            </th>
                                            <th wire:click="sort('project_name')">
                                                <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                                @lang('document_module.project')
                                            </th>
                                            <th wire:click="sort('status')">
                                                <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                                @lang('document_module.status')
                                            </th>
                                            <th>
                                                @lang('document_module.action')
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="sort-table ui-sortable">
                                        @forelse ($items as $document)
                                            <tr class="ui-sortable-handle" style="opacity: 1;">
                                                <td>
                                                    <a
                                                        href="{{ route('documents.document.detail', ['id' => $document['id']]) }}"><span
                                                            class="fw-400 badge badge-round badge-primary badge-lg badge-outlined">{{ $document['document_id'] }}</span></a>
                                                </td>
                                                <td>
                                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                        <span>{{ $document['subject'] }}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                        <span>{{ $document['user_name'] ?? '-' }}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                        <span>{{ $document['type_name'] }}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                        <span>{{ $document['project_name'] ?? '-' }}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    @php
                                                        $statusColors = [
                                                            'accept' => 'bg-opacity-win text-win',
                                                            'decline' => 'bg-opacity-danger text-danger',
                                                            'closed' => 'bg-opacity-gray text-close"',
                                                            'pending' => 'bg-opacity-warning text-warning',
                                                        ];
                                                    @endphp
                                                    <span
                                                        class="badge-new rounded text-capitalize {{ $statusColors[$document['status']] }}">{{ $document['status'] }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="d-inline-block">
                                                        <ul class="mb-0 d-flex flex-wrap gap-10 align-items-center">
                                                            <li>
                                                                <a onclick="showLoader()"
                                                                    wire:click="$emit('showDuplicateDocument', '{{ $document['id'] }}')"
                                                                    href="javascript:void(0);" data-toggle="tooltip"
                                                                    title="@lang('document_module.duplicate')">
                                                                    <i class="iconsax icon text-osool fs-18 mr-0"
                                                                        icon-name="document-copy"></i>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="{{ route('documents.document.detail', ['id' => $document['id']]) }}"
                                                                    data-toggle="tooltip" title="@lang('document_module.view')"
                                                                    href="javascript:void(0);">
                                                                    <i class="iconsax icon text-osool fs-18"
                                                                        icon-name="eye"></i>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a onclick="showLoader()"
                                                                    wire:click="$emit('showEditDocument', '{{ $document['id'] }}')"
                                                                    data-toggle="tooltip" title="@lang('document_module.edit')"
                                                                    href="javascript:void(0);">
                                                                    <i class="iconsax icon text-new-primary fs-18"
                                                                        icon-name="edit-1"></i>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a wire:click="$emit('confirmDelete', {{ $document['id'] }}, '{{ $document['document_id'] }}')"
                                                                    data-toggle="tooltip" title="@lang('document_module.delete')"
                                                                    href="javascript:void(0);">
                                                                    <i class="iconsax icon text-delete fs-18"
                                                                        icon-name="trash"></i>
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="7">
                                                    @include('livewire.sales.common.no-data-tr')
                                                </td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    @livewire('common.paginator-v1', [
                        'currentPage' => $page,
                        'functionName' => 'fetchData',
                        'totalRecords' => $total,
                        'perPage' => $perPage,
                    ])
                </div>
            </div>
        </div>
    </div>
</div>
@include('livewire.common.super-modal-v1', [
    'component' => 'manage-document.document.modals.create',
    'modalId' => 'createDocument',
])
@include('livewire.common.super-modal-v1', [
    'component' => 'manage-document.document.modals.edit',
    'modalId' => 'editDocument',
])
@include('livewire.common.super-modal-v1', [
    'component' => 'manage-document.document.modals.duplicate',
    'modalId' => 'duplicateDocument',
])
@livewire('common.delete-confirm-v1')
<script>
    document.addEventListener("DOMContentLoaded", function() {
        Livewire.on('openPrintWindow', content => {
            const printWindow = window.open('', '_blank');
            printWindow.document.write(content);
            printWindow.document.close();

            // Wait for all styles and DOM to load before printing
            printWindow.onload = function() {
                printWindow.focus();
                printWindow.print();

                // Optional: auto-close after printing
                printWindow.close();
            };
        });
    });
</script>
