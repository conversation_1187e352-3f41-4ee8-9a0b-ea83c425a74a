<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ Session::get('locale') == 'ar' ? 'rtl' : 'ltr' }}">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="facebook-domain-verification" content="ahabnt58zdbmzksb41doq5nz2h28er" />

    <title @if (App::getLocale() == 'en') lang = "en" @else lang = "ar" @endif>@lang('import.osool')</title>

    <meta name="description" content="@yield('page_description', $pageDescription ?? '')" />

    @livewireStyles

    <link rel="stylesheet" href="{{ asset('new_theme/css/new_font.css') }}">

    @include('layouts.partials._styles')

    @yield('styles')

    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('favicon/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('favicon/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('favicon/favicon-16x16.png') }}">
    <link rel="shortcut icon" type="image/png" sizes="32x32" href="{{ asset('favicon/favicon-32x32.png') }}">
    <link rel="shortcut icon" type="image/png" sizes="16x16" href="{{ asset('favicon/favicon-16x16.png') }}">
    <link rel="manifest" href="{{ asset('favicon/site.webmanifest') }}">
    <link rel="mask-icon" href="{{ asset('favicon/safari-pinned-tab.svg') }}" color="#5bbad5">

    <script src="{{ asset('new_theme/js/axios.min.js') }}"
        integrity="sha512-0qU9M9jfqPw6FKkPafM3gy2CBAvUWnYVOfNPDYKVuRTel1PrciTj+a9P3loJB+j0QmN2Y0JYQmkBBS8W+mbezg=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    @if (App::getLocale() == 'ar')
        <style>
            .toggle-icon {
                transform: rotate(180deg);
            }
        </style>
    @endif
</head>

<body class="layout-light side-menu">
    <div class="contents crm expanded">
        <div class="container-fluid">
            <div class="">
                <div class="card mt-3">
                    <div class="card-body px-0 pt-0" id="printableArea">
                        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                            <div class="table-responsive">
                                <table class="table mb-0 radius-0 th-osool">
                                    <thead>
                                        <tr class="userDatatable-header">
                                            <th wire:click="sort('document_id')">
                                                @lang('document_module.document')
                                            </th>
                                            <th wire:click="sort('subject')">
                                                @lang('document_module.subject')
                                            </th>
                                            <th wire:click="sort('user_name')">
                                                @lang('document_module.user')
                                            </th>
                                            <th wire:click="sort('type_name')">
                                                @lang('document_module.type')
                                            </th>
                                            <th wire:click="sort('project_name')">
                                                @lang('document_module.project')
                                            </th>
                                            <th wire:click="sort('status')">
                                                @lang('document_module.status')
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="sort-table ui-sortable">
                                        @forelse ($items as $document)
                                            <tr class="ui-sortable-handle" style="opacity: 1;">
                                                <td>
                                                    <a href=""><span
                                                            class="fw-400 badge badge-round badge-primary badge-lg badge-outlined">{{ $document['document_id'] }}</span></a>
                                                </td>
                                                <td>
                                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                        <span>{{ $document['subject'] }}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                        <span>{{ $document['user_name'] }}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                        <span>{{ $document['type_name'] }}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                        <span>{{ $document['project_name'] }}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    @php
                                                        $statusColors = [
                                                            'accept' => 'bg-opacity-win text-win',
                                                            'decline' => 'bg-opacity-danger text-danger',
                                                            'closed' => 'bg-opacity-gray text-close"',
                                                            'pending' => 'bg-opacity-warning text-warning',
                                                        ];
                                                    @endphp
                                                    <span
                                                        class="badge-new rounded {{ $statusColors[$document['status']] }}">{{ $document['status'] }}
                                                    </span>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="7">
                                                    @include('livewire.sales.common.no-data-tr')
                                                </td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
