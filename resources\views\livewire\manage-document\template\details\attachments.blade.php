<div>
    <div class="card mt-3" id="attachments-section">
        <div class="card-header justify-content-between d-flex align-items-center p-3">
            <h3 class="text-osool">@lang('document_module.attachments')</h3>
            <div wire:loading wire:target="attachment" class="text-center my-3">
                <div class="spinner-border text-info" role="status">
                    <span class="sr-only">@lang('Uploading...')</span>
                </div>
                <div class="mt-2">@lang('Uploading file, please wait...')</div>
            </div>
            <label wire:loading.class="disabled-upload" wire:loading.class.remove="enabled-upload"
                wire:target="attachment" class="btn btn-default bg-new-primary" for="imageInput">
                <i class="las la-plus fs-16"></i> @lang('document_module.upload_file') </label>
        </div>
        <div class="card-body">
            <input type="file" id="imageInput" accept="image/*" class="d-none" wire:model='attachment'>
            @foreach ($attachments as $item)
                <div id="imagePreview" class="preview-container file-upload-new" wire:key="{{ $item['id'] }}">
                    <div class="image-box d-flex justify-content-between align-items-center border radius-xl mb-2"
                        data-name="taqnia.png" data-size="35748">
                        <div class="d-flex align-items-center gap-10 pl-2">
                            <img onerror="this.style.display='none'" src="{{ $item['file'] }}">
                            <span>{{ basename($item['file']) }}</span>
                        </div>
                        <div class="d-flex gap-10">
                            <a data-toggle="tooltip" title="@lang('document_module.download')"
                                class="download-btn d-center p-2 bg-new-primary text-white rounded-circle text-light cursor-pointer"
                                href="{{ $item['file'] }}" download target="_blank">
                                <i class="iconsax" icon-name="download-1"></i>
                            </a>
                            <div class="d-flex">
                                @if ($deletingId == $item['id'])
                                    <div class="text-center mr-10">
                                        <div class="spinner-border text-danger" role="status"
                                            style="width: 1.5rem;; height: 1.5rem;;">
                                            <span class="sr-only">@lang('Deleting...')</span>
                                        </div>
                                    </div>
                                @endif
                                <a wire:click="confirmAttachmentDeletion({{ $item['id'] }})" data-toggle="tooltip"
                                    title="@lang('document_module.delete')"
                                    class="remove-comment wh-30 d-center p-2 bg-loss text-white rounded-circle text-light cursor-pointer">
                                    <i class="iconsax" icon-name="trash"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
    <script>
        window.addEventListener('resetFileInput', () => {
            document.getElementById('imageInput').value = null;
        });
    </script>
</div>
