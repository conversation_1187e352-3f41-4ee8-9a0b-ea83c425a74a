<div>
    <div class="col-lg-12">
        <div
            class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
            @include('applications.admin.common.breadcrumb', [
                'links' => [
                    [
                        'title' => __('document_module.template'),
                    ],
                    [
                        'title' => __('document_module.details'),
                    ],
                ],
            ])
            <div class="d-flex gap-10 breadcrumb_right_icons">
                <div class="d-flex gap-10 breadcrumb_right_icons">
                    <button onclick="showLoader()" class="btn btn-default btn-primary wh-45 no-wrap"
                        wire:click="$emit('showDuplicateDocument', '{{ $itemId }}')" href="javascript:void(0);"
                        data-toggle="tooltip" title="@lang('document_module.duplicate')">
                        <i class="iconsax icon fs-18 mr-0" icon-name="document-copy"></i>
                    </button>
                </div>
                <div class="d-flex gap-10 breadcrumb_right_icons">
                    <a data-toggle="tooltip" title="@lang('document_module.download')" class="btn btn-default btn-primary wh-45 no-wrap"
                        href="/documents/template/download-pdf/{{ $itemId }}" target="_blank">
                        <i class="iconsax mr-0" icon-name="download-1"></i>
                    </a>
                </div>
                <div class="d-flex gap-10 breadcrumb_right_icons">
                    <a class="btn btn-default btn-primary wh-45 no-wrap" data-toggle="tooltip" title="@lang('document_module.preview')"
                        href="/documents/template/preview/{{ $itemId }}" target="_blank">
                        <i class="iconsax icon fs-18 mr-0" icon-name="eye"></i>
                    </a>
                </div>
            </div>

            <!--====End Design for Export PDF===-->
        </div>
    </div>
</div>
@include('livewire.common.super-modal-v1', [
    'component' => 'manage-document.template.modals.duplicate',
    'modalId' => 'duplicateDocument',
])
