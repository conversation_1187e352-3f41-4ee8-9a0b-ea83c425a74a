<div>
    <div class="card info-section" id="info-section">
        <div>
            <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
                <h6>@lang('document_module.info')</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-4 col-md-6">
                        <div class="card">
                            <div class="card-body p-3">
                                <div class="row align-items-center justify-content-between">
                                    <div class="col-auto mb-3 mb-sm-0">
                                        <div class="d-flex align-items-center gap-10">
                                            <div class="radius-xl theme-avtar bg-opacity-primary wh-45 d-center">
                                                <i class="iconsax icon fs-22 mr-0 text-primary"
                                                    icon-name="picture-add"></i>
                                            </div>
                                            <div class="ms-3">
                                                <p class="mb-0 text-muted">@lang('document_module.attachments')</p>
                                                <h4 class="m-0">{{ $attachments_count }}</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mt-md-0 mt-3">
                        <div class="card">
                            <div class="card-body p-3">
                                <div class="row align-items-center justify-content-between">
                                    <div class="col-auto mb-3 mb-sm-0">
                                        <div class="d-flex align-items-center gap-10">
                                            <div class="radius-xl theme-avtar bg-opacity-success wh-45 d-center">
                                                <i class="iconsax icon fs-22 mr-0 text-success"
                                                    icon-name="messages-1"></i>
                                            </div>
                                            <div class="ms-3">
                                                <p class="mb-0 text-muted">@lang('document_module.comments')</p>
                                                <h4 class="m-0">{{ $comments_count }}</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mt-md-0 mt-3">
                        <div class="card">
                            <div class="card-body p-3">
                                <div class="row align-items-center justify-content-between">
                                    <div class="col-auto mb-3 mb-sm-0">
                                        <div class="d-flex align-items-center gap-10">
                                            <div class="radius-xl theme-avtar bg-opacity-danger wh-45 d-center">
                                                <i class="iconsax icon fs-22 mr-0 text-danger"
                                                    icon-name="clipboard-text-2"></i>
                                            </div>
                                            <div class="ms-3">
                                                <p class="mb-0 text-muted">@lang('document_module.notes')</p>
                                                <h4 class="m-0">{{ $notes_count }}</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="card mt-3 py-3">
                    <table class="table table--default align-left">
                        <tbody>
                            <tr>
                                <td><strong>@lang('document_module.subject')</strong></td>
                                <td class="">{{ $details['subject'] }}</td>

                            </tr>
                            <tr>
                                <td><strong>@lang('document_module.type')</strong></td>
                                <td class="">{{ $details['type'] }}</td>

                            </tr>
                        </tbody>
                    </table>
                </div>

                <form wire:submit.prevent='save'>
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6>@lang('document_module.document_description')</h6>
                        </div>
                        <div class="card-body">
                            <textarea class="form-control" wire:model='description'></textarea>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h6>@lang('document_module.additional_description')</h6>
                        </div>
                        <div class="card-body">
                            <textarea class="form-control" wire:model="additional_description"></textarea>
                        </div>
                    </div>
                    <div class="d-flex justify-content-end mt-15">
                        <!-- Spinner while submitting -->
                        <div wire:loading wire:target="save" class="mt-2 text-center">
                            <div class="spinner-border text-info" role="status" style="width: 1rem; height: 1rem;">
                                <span class="sr-only">@lang('Submitting...')</span>
                            </div>
                            <div class="mt-1">@lang('Submitting, please wait...')</div>
                        </div>
                        <button type="submit" class="btn btn-default bg-new-primary">@lang('document_module.add')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
