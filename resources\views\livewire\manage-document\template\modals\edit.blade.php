    <div>
        <div class="modal-dialog" role="document">
            <form wire:submit.prevent="save">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">@lang('document_module.edit_document')</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true"><i class="iconsax" icon-name="x"></i></span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-12 form-group">
                                <label for="subject" class="col-form-label">@lang('document_module.subject')</label><span
                                    class="text-danger">*</span>
                                <input class="form-control" wire:model='subject' type="text" value=""
                                    placeholder="@lang('document_module.subject')" />
                                @error('subject')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="type" class="col-form-label">@lang('document_module.type')</label><span
                                        class="text-danger">*</span>
                                    <select class="form-control" wire:model='type'>
                                        <option></option>
                                        @foreach ($types as $item)
                                            <option value="{{ $item['id'] }}">{{ $item['name'] }}</option>
                                        @endforeach
                                    </select>
                                    @error('type')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="notes" class="col-form-label">@lang('document_module.description')</label>
                                    <textarea class="form-control textarea" wire:model='description'></textarea>
                                    @error('description')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">@lang('document_module.close')</button>
                        <button type="submit" class="btn btn-primary">@lang('document_module.save_changes')</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
