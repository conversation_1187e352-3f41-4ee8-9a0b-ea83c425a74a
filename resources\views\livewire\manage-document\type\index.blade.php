<div>
    <div class="contents crm">
        <div class="container-fluid">
            <div class="col-lg-12">
                <div
                    class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                    @include('applications.admin.common.breadcrumb', [
                        'links' => [
                            [
                                'title' => __('document_module.document_type'),
                            ],
                        ],
                    ])
                    <div class="d-flex gap-10 breadcrumb_right_icons">
                        <div class="d-flex gap-10 breadcrumb_right_icons">
                            <button onclick="showLoader()" class="btn btn-default btn-primary w-100 no-wrap" type="button"
                                wire:click="$emit('showCreateDocumentType')"><i
                                    class="las la-plus fs-16"></i>@lang('document_module.create')</button>
                        </div>
                    </div>
                    <!--====End Design for Export PDF===-->
                </div>
            </div>


            <div class="">
                <div class="card mt-3">
                    <div class="">
                        <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                            <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">@lang('document_module.manage_document_types')</h6>
                            <div wire:loading class="mr-10 mt-1 text-center">
                                <div class="spinner-border text-info" role="status">
                                    <span class="sr-only">@lang('Processing...')</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body px-0 pt-0" id="printableArea">
                        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                            <div class="table-responsive">
                                <table class="table mb-0 radius-0 th-osool">
                                    <thead>
                                        <tr class="userDatatable-header">
                                            <th>
                                                @lang('document_module.document_type')
                                            </th>
                                            <th>
                                                @lang('document_module.action')
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="sort-table ui-sortable">
                                        @forelse ($items as $type)
                                            <tr class="ui-sortable-handle" style="opacity: 1;">
                                                <td>
                                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                        <span>{{ $type['name'] }}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-inline-block">
                                                        <ul class="mb-0 d-flex flex-wrap gap-10 align-items-center">
                                                            <li>
                                                                <a onclick="showLoader()"
                                                                    wire:click="$emit('showEditDocumentType', '{{ $type['id'] }}', '{{ $type['name'] }}')"
                                                                    data-toggle="tooltip" title="@lang('document_module.edit')"
                                                                    href="javascript:void(0);">
                                                                    <i class="iconsax icon text-new-primary fs-18"
                                                                        icon-name="edit-1"></i>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a wire:click="$emit('confirmDelete', {{ $type['id'] }}, '{{ $type['name'] }}')"
                                                                    data-toggle="tooltip" title="@lang('document_module.delete')"
                                                                    href="javascript:void(0);">
                                                                    <i class="iconsax icon text-delete fs-18"
                                                                        icon-name="trash"></i>
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="2">
                                                    @include('livewire.sales.common.no-data-tr')
                                                </td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    @livewire('common.paginator-v1', [
                        'currentPage' => $page,
                        'functionName' => 'fetchData',
                        'totalRecords' => $total,
                        'perPage' => $perPage,
                    ])
                </div>
            </div>
        </div>
    </div>
</div>
@include('livewire.common.super-modal-v1', [
    'component' => 'manage-document.type.modals.create',
    'modalId' => 'createDocumentType',
])
@include('livewire.common.super-modal-v1', [
    'component' => 'manage-document.type.modals.edit',
    'modalId' => 'editDocumentType',
])
@livewire('common.delete-confirm-v1')
