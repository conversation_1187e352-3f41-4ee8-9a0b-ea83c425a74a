<div>

    {{--   @include('livewire.common.loader') --}}

    <div class="card mb-3">
        <div class="card-body">

            <form wire:submit.prevent="applyDateFilters" class="fs-14">
                <div class="d-flex flex-wrap gap-10">

                    <div class="flex-fill">
                        <label for="">@lang('Assign User')</label>
                        <div class="position-relative">
                            <select class="form-control" id="user" wire:model.defer="assign_to">
                                <option value="" selected>@lang('Select')</option>
                                @foreach ($users as $user)
                                    <option value="{{ $user['name'] }}">
                                        {{ $user['name'] }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>


                    <div class="flex-fill">
                        <label for="">@lang('CRMProjects.common.startDate')</label>
                        <div class="position-relative">
                            <input type="date" wire:model="start_date" class="form-control" id="start_date"
                                aria-describedby="emailHelp" placeholder="@lang('CRMProjects.common.enterStartDate')">
                            {{-- <i class="iconsax field-icon text-light" icon-name="calendar-1"></i> --}}
                        </div>
                    </div>


                    <div class="flex-fill">
                        <label for="">@lang('CRMProjects.due-date')</label>
                        <div class="d-flex gap-10">
                            <div class="position-relative flex-fill">
                                <input type="date" wire:model="end_date" class="form-control" id="end_date"
                                    placeholder="@lang('CRMProjects.due-date')">
                                {{--  <i class="iconsax field-icon text-light" icon-name="calendar-1"></i> --}}
                            </div>
                            <button type="submit" class="btn bg-new-primary btn-sm text-white radius-md wh-45"> <i
                                    class="iconsax mr-0 fs-18" icon-name="search-normal-2"></i>
                                <!-- @lang('CRMProjects.common.submit') -->
                            </button>
                            <button type="button" wire:click="resetFilters"
                                class="btn bg-loss btn-sm text-white wh-45 radius-md"><i class="iconsax mr-0 fs-18"
                                    icon-name="trash"></i>
                                <!-- @lang('CRMProjects.common.reset') -->
                            </button>
                        </div>
                    </div>


                </div>
            </form>
        </div>
    </div>


    @if (count($selectedTasks) > 0)
        <div class="card mb-3">
            <div class="card-body">
                <p>{{ count($selectedTasks) }} task(s) selected.</p>
                <div class="d-flex flex-wrap gap-10">
                    <button wire:click="openModalbulkAssigUserToTask" class="btn bg-new-primary btn-sm text-white ">
                        @lang('Assign User')
                    </button>
                    <button wire:click="openModalBulksUpdateStatus" class="btn bg-success btn-sm text-white ">
                        @lang('CRMProjects.common.update_status')
                    </button>

                </div>

            </div>
        </div>
    @endif


    <div class="table-responsive mt-2">
        <div class="card">
            <div class="card-body px-0 pt-0">
                <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                    <div class="table-responsive">
                        <table class="table mb-0 radius-0">
                            <thead>
                                <tr class="userDatatable-header">
                                    <th></th>
                                    <th wire:click="sortBy('title')">
                                        @lang('Title')
                                        @if ($sortField == 'title')
                                            @if ($sortDirection == 'asc')
                                                ▲
                                            @else
                                                ▼
                                            @endif
                                        @endif
                                    </th>
                                    <th wire:click="sortBy('milestone')">
                                        @lang('CRMProjects.milestone')
                                        @if ($sortField == 'milestone')
                                            @if ($sortDirection == 'asc')
                                                ▲
                                            @else
                                                ▼
                                            @endif
                                        @endif
                                    </th>
                                    <th wire:click="sortBy('priority')">
                                        @lang('Priority')
                                        @if ($sortField == 'priority')
                                            @if ($sortDirection == 'asc')
                                                ▲
                                            @else
                                                ▼
                                            @endif
                                        @endif
                                    </th>
                                    <th wire:click="sortBy('stage')">
                                        @lang('Stage')
                                        @if ($sortField == 'stage')
                                            @if ($sortDirection == 'asc')
                                                ▲
                                            @else
                                                ▼
                                            @endif
                                        @endif
                                    </th>
                                    <th>
                                        @lang('Assign User')
                                    </th>
                                    <th>
                                        @lang('Action')
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="sort-table ui-sortable">
                                
                                @forelse ($records['items'] ?? [] as $task)
                                    <tr class="ui-sortable-handle" style="opacity: 1;">
                                        <td>
                                            @if ($task['task_type'] == 'tangible')
                                                <input type="checkbox" disabled checked title="{{ $task['task_type'] ?? '' }}"
                                                data-toggle="tooltip">
                                            @else
                                                <input type="checkbox" wire:model="selectedTasks"
                                                    value="{{ $task['id'] }}">
                                            @endif

                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span >{{ $task['title'] }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{{ $task['milestone_name'] }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            @php
                                                $color = 'hold';
                                                if ($task['priority'] == 'Medium') {
                                                    $color = 'warning';
                                                }
                                                if ($task['priority'] == 'High') {
                                                    $color = 'danger';
                                                }
                                            @endphp
                                            <small class="py-1 px-2 bg-{{ $color }} rounded text-white">
                                                {{ $task['priority'] }}
                                            </small>
                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{{ $task['stage_name'] }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="profile-group ml-3">
                                                @foreach ($task['assign_to'] as $index => $user)
                                                    @if ($index < 4)
                                                        <div class="profile">
                                                            <img data-toggle="tooltip" src="{{ $user['avatar'] }}"
                                                                title="{{ $user['name'] }}" alt="{{ $user['name'] }}">
                                                        </div>
                                                    @endif
                                                @endforeach
                                                @if (count($task['assign_to']) > 4)
                                                    <div class="extra d-flex align-items-center justify-content-center">
                                                        +{{ count($task['assign_to']) - 4 }}</div>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-inline-block">
                                                <ul class="mb-0 d-flex flex-wrap gap-10">
                                                    <li>
                                                      

                                                        @php
                                                            $taskData=[
                                                                'id_task'=> data_get($task,'id','---'),
                                                                 'title' => data_get( $task, 'title', '---' ),
                                                                 'priority' => data_get( $task, 'priority', '---' ),
                                                                 'assign_to' => data_get( $task, 'assign_to', [] ),
                                                                 'milestone' =>  data_get( $task, 'milestone_name',  [] ),
                                                                  'milestoneID' =>  data_get( $task, 'milestone_id','' ),
                                                                 'description' => data_get( $task, 'description', '---' ),
                                                                 'start_date' => data_get( $task, 'start_date', '---' ),
                                                                 'due_date' => data_get( $task, 'due_date', '---' ),
                                                                 'comments' => data_get( $task, 'comments',  [] ),
                                                                 'users' =>  data_get( $task, 'assign_to',  [] ),
                                                                'workorder_id' =>  data_get( $task, 'workorder_id',  null ),
                                                                 'workorder_type' =>  data_get( $task, 'workorder_type',  null ),
                                                                 'property_name' =>  data_get( $task, 'property_name',  null ),

                                                        
                                                            ]
                                                        @endphp
                                                        <a href="javascript:void(0)"
                                                            wire:click='showUntangibleTask(@json($taskData))'>
                                                            <i class="iconsax icon text-osool fs-18"
                                                                icon-name="eye"></i>
                                                        </a>

                                                    </li>

                                                    <li>
                                                          @if ($task['task_type'] == 'tangible')
                                                        <a href="javascript:void(0)"
                                                            wire:click=' editTangibleTask(@json($taskData))'>
                                                            <i class="iconsax icon text-new-primary fs-18"
                                                                icon-name="edit-1"></i>
                                                        </a>
                                                        @else
                                                      <a href="javascript:void(0)"
                                                            wire:click='editUntangibleTask(@json($taskData))'>
                                                            <i class="iconsax icon text-new-primary fs-18"
                                                                icon-name="edit-1"></i>
                                                        </a>


                                                        @endif
                                                    </li>
                                                    <li>
                                                        <a wire:click.prevent="$emit('confirmDelete', {{ @$task['id'] }}, '{{ @$task['title'] }}', 'deleteTask')"
                                                            href="#"><i class="iconsax icon text-delete fs-18"
                                                                icon-name="trash"></i>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7">
                                            @include('livewire.sales.common.no-data-tr')
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                        @if (@$records)
                            @livewire('common.paginator', ['totalPages' => ceil(@$records['total'] / @$records['per_page']), 'currentPage' => \Helper::getCurrentPageFromUrl(@$records['next_page_url']), 'totalRecords' => @$records['total']])
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        window.addEventListener('openUntangibleTaskModal', () => {
            $('#show-untangible-task-modal').modal('show');
        });
    </script>


   
    @include('livewire.project.task-board.modals.show-untangible-task')
    @include('livewire.project.task-board.modals.edit-untangible-task')
        @include('livewire.project.task-board.modals.edit-tangible-task')
    @include('livewire.project.task-board.modals.Bulk-Actions.assignUserToTaskBulk')
    @include('livewire.project.task-board.modals.Bulk-Actions.updateTaskBulk')
    <script>
        window.addEventListener('open-modal-ById', event => {
            const modalId = event.detail.modalId;
            if (modalId) {
                $('#' + modalId).modal('show');
            }
        });

        window.addEventListener('close-modal-ById', event => {
            const modalId = event.detail.modalId;
            if (modalId) {
                $('#' + modalId).modal('hide');
            }
        });
    </script>
</div>
@livewire('common.delete-confirm')
<script src="/js/livewire/manage-loader.js"></script>
<script src="/js/livewire/manage-tooltip.js"></script>
