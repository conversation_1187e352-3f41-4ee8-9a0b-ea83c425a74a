<div>
    <div class = "contents wo-management">
        <div class = "container-fluid">
            <div class = "row">
                <div class = "col-lg-12">
                    <div class = "mt-20">
                        <div class = "project-progree-breadcrumb">
                            <div class = "breadcrumb-main user-member justify-content-sm-between mb-xs-none">
                                <div class = "d-flex flex-wrap justify-content-center breadcrumb-main__wrapper">
                                    <div class = "page-title-wraps">
                                        <div class = "page-title bm-page-title justify-content-between">
                                            <div class = "d-flex align-items-center user-member__title justify-content-center mr-sm-25 page-title__left ml-0">
                                                <h4 class = "text-capitalize fw-500 breadcrumb-title">@lang('work_order.button.manage_pm_work_order')</h4>
                                                <span class = "sub-title ml-sm-25 pl-sm-25 work_order_count">
                                                    {{ isset($list) ? $list->total() : 0 }}
                                                    @lang('work_order.bread_crumbs.work_orders_preventive')
                                                </span>
                                            </div>
                                        </div>
                                        @if(in_array($user->user_type, array('admin', 'admin_employee')))
                                            <div>
                                                <ul class = "atbd-breadcrumb nav">
                                                    <li class = "atbd-breadcrumb__item">
                                                        <a>@lang('calender.bread_crumbs.Service_Provider')</a>
                                                        <span class = "breadcrumb__seperator">
                                                            <span class = "la la-angle-right"></span>
                                                        </span>
                                                    </li>
                                                    <li class = "atbd-breadcrumb__item">
                                                        <a>{{ $serviceProviderName ?? '-' }}</a>
                                                    </li>
                                                </ul>
                                            </div>
                                        @endif
                                    </div>
                                    <form action = "javascript:void(0)" class = "d-flex align-items-center user-member__form my-sm-0 my-2">
                                        <span data-feather = "search"></span>
                                        <input type = "search" id = "search_by_preventive_title" class = "form-control mr-sm-2 border-0 box-shadow-none" placeholder = "@lang('work_order.forms.label.Preventive Maintenance Title')" aria-label = "Search" wire:model.live.debounce.250ms = "search">
                                    </form>
                                </div>
                                @if(in_array($user->user_type,['super_admin','osool_admin']))
                                    <div class="action-btn d-sm-flex">
{{--                                        data-toggle="modal" data-target="#ppmImport"--}}
{{--                                        wire:click.prevent="openUploadModal"--}}
                                        <a href = "javascript:void(0)" onclick = "event.preventDefault(); Livewire.emit('openUploadModal','ppmImport')" class = "btn btn-xs btn-squared btn-primary ml-sm-10 mb-sm-0 mb-3 no-wrap" >
                                            @lang('ppm.importing.import_ppm_btn')
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class = "row mt-20">
                <div class = "col-lg-12">
                    <div class = "project-top-wrapper project-top-progress wo-top-bar">
                        <div class = "row">
                            <div class = "col-lg-7 col-md-12 col-sm-12">
                                <div class = "d-flex justify-content-left">
                                    <div class = "d-md-flex wo-left" id = "filterElements">
                                        <div class = "project-top-left wo-left-two">
                                            <div>
                                                <p class = "mb-0 mr-10 fs-14 color-light">
                                                    @lang('work_order.forms.place_holder.choose_property'):
                                                </p>
                                            </div>
                                            <div class = "project-tap global-shadow order-lg-1 order-2 my-10 float-xl-left float-lg-left w-100 property-drop">
                                                <div class = "btn-group check-dropdown-toggle w-100">
                                                    <label class = "py-2 dropdown-toggle pl-3 pr-5 mb-0 property_label" data-toggle = "dropdown" aria-haspopup = "true" aria-expanded = "false">
                                                        <img src = "{{ asset('img/svg/building.svg') }}" class = "mr-1">
                                                        
                                                        @php
                                                            $selectedCount = is_array(session('selected_property_ids')) ? count(session('selected_property_ids')) : 0;
                                                            $selectedPropertyText = $selectedCount > 0
                                                                ? $selectedCount .' '.__('work_order.forms.place_holder.selected')
                                                                : __('work_order.forms.place_holder.property');
                                                        @endphp

                                                        <span id = "selectedPropertyText">{{ $selectedPropertyText }}</span>
                                                        <span class = "toggle-icon ml-10"></span>
                                                    </label>
                                                    <div class = "dropdown-menu">
                                                        <div class = "check-dropdown multi-field">
                                                            <div class = "px-2 mb-3">
                                                                <input type = "text" id = "searchbar" class = "form-control"  name = "search" placeholder = "@lang('work_order.forms.place_holder.search_by_property_name')">
                                                            </div>
                                                            <div class = "pl-3">
                                                                <input type = "checkbox" id = "all_properties" value = "1" class = "mr-2"  {{ $selectedCount == 0 || $selectedCount == $propertiesCount ? 'checked' : '' }}>
                                                                <label for = "all_properties" class="cursor-pointer">@lang('work_order.forms.place_holder.all')</label>
                                                            </div>
                                                            <ul id = "properties_div" class = "site-scrollbar">
                                                                @if(isset($propertiesList) && count($propertiesList) > 0)
                                                                    @foreach($propertiesList as $data)
                                                                        @if($data['property_type'] == 'building')
                                                                            <li class = "pl-3 property-filter" data-name = "{{ $data['building_name'] }}">
                                                                                <div>
                                                                                    @php
                                                                                        $checkedProperty = false;

                                                                                        if($selectedCount == 0){
                                                                                            $checkedProperty = true;
                                                                                        }

                                                                                        else{
                                                                                            if(in_array($data['building_id'], $this->getDataSession('selected_property_ids'))){
                                                                                                $checkedProperty = true;
                                                                                            }
                                                                                        }
                                                                                    @endphp
                                                                                    <input type = "checkbox" name = "buildings[]" id = "{{ $data['building_id'] }}" value = "{{ $data['building_id'] }}" class = "mr-2 prop_field" {{ $checkedProperty == true ? 'checked' : '' }}>
                                                                                    <label for = "{{ $data['building_id'] }}" title = "{{ $data['building_name'] }}" class="cursor-pointer">{{ $data['building_name'] }}</label>
                                                                                </div>
                                                                            </li>
                                                                        @else
                                                                            <li class = "pl-3 complex property-filter" data-name = "{{ $data['complex_name'] }}">
                                                                                <div>
                                                                                    <input type = "checkbox" value = "" class = "mr-2 prop_field" name = "complex[]"  {{ $selectedCount == 0 || $selectedCount == $propertiesCount ? 'checked' : '' }}>
                                                                                    <label for = "ap">{{ $data['complex_name'] }}</label>
                                                                                </div>
                                                                                <ul>
                                                                                    @foreach ($data['property_buildings'] as $row)
                                                                                        @php
                                                                                            $checkedComplex = false;

                                                                                            if($selectedCount == 0){
                                                                                                $checkedComplex = true;
                                                                                            }

                                                                                            else{
                                                                                                if(in_array($row['id'], $this->getDataSession('selected_property_ids'))){
                                                                                                    $checkedComplex = true;
                                                                                                }
                                                                                            }
                                                                                        @endphp
                                                                                        <li class="pl-3">
                                                                                            <input type="checkbox" name="buildings[]" id="{{ $row['id'] }}" value="{{ $row['id'] }}" class="mr-2 property" {{ $checkedComplex == true ? 'checked' : '' }}>
                                                                                            <label for="{{ $row['id'] }}" title="{{ $row['building_name'] }}" class=" cursor-pointer">{{ $row['building_name'] }}</label>
                                                                                        </li>
                                                                                    @endforeach
                                                                                </ul>
                                                                            </li>
                                                                        @endif
                                                                    @endforeach
                                                                @endif
                                                            </ul>
                                                            <div class = "dropdown-divider"></div>
                                                            <div class = "px-2">
                                                                <a class = "btn btn-primary w-100 mb-2" id = "property_filter_submit" href = "javascript:void(0)">@lang('work_order.button.apply')</a>
                                                                <a class = "btn btn-outline-danger w-100" id = "property_filter_reset" href = "javascript:void(0)">@lang('work_order.button.reset')</a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class = " ml-3">
                                            <div>
                                                <p class = "mb-0 mr-10 fs-14 color-light">
                                                    @lang('work_order.forms.place_holder.choose_service'):
                                                </p>
                                            </div>
                                            <div class = "project-tap global-shadow order-lg-1 order-2 my-10 float-xl-left float-lg-left w-100 property-drop">
                                                <div class = "btn-group check-dropdown-toggle w-100">
                                                    <label class = "py-2 dropdown-toggle pl-3 pr-5 mb-0 property_label" data-toggle = "dropdown" aria-haspopup = "true" aria-expanded = "false">
                                                        <i class = "las la-tools text-light"></i>
                                                        @php
                                                            $selectedCount = is_array(session('selected_service_ids')) ? count(session('selected_service_ids')) : 0;
                                                            $selectedServiceText = $selectedCount > 0
                                                                ? $selectedCount .' '.__('work_order.forms.place_holder.s_selected')
                                                                : __('work_order.forms.place_holder.service');
                                                        @endphp

                                                        <span id="selectedServiceText">{{ $selectedServiceText }}</span>

                                                        <span class = "toggle-icon ml-10"></span>
                                                    </label>
                                                    <div class = "dropdown-menu">
                                                        <div class = "check-dropdown multi-field">
                                                            <div class = "px-2 mb-3">
                                                                <input type = "text" id = "searchbarservice" class = "form-control"  name = "search" placeholder = "@lang('work_order.forms.place_holder.search_by_service_name')">
                                                            </div>
                                                            <ul id = "properties_div" class = "site-scrollbar">
                                                                @if(isset($servicesList) && count($servicesList) > 0)
                                                                    <div class = "pl-3">
                                                                        <input type = "checkbox" id = "service_all" value = "All" class = "mr-2" {{ $selectedCount == 0 || $selectedCount == count($servicesList) ? 'checked' : '' }} />
                                                                        <label for = "service_all" class="cursor-pointer">@lang('work_order.bread_crumbs.all')</label>
                                                                    </div>
                                                                    <ul id = "service_types_div" class = "site-scrollbar">
                                                                        @foreach($servicesList as $row)
                                                                            @php
                                                                                $checkedService = false;

                                                                                if($selectedCount == 0){
                                                                                    $checkedService = true;
                                                                                }

                                                                                else{
                                                                                    if(in_array($row['id'], $this->getDataSession('selected_service_ids'))){
                                                                                        $checkedService = true;
                                                                                    }
                                                                                }
                                                                            @endphp
                                                                            <li class = "pl-3 service-filter" data-name = "{{ $row['asset_category'] }}">
                                                                                <div>
                                                                                    <input type = "checkbox" name = "service_types[]" id = "{{ $row['id'] }}" value = "{{ $row['id'] }}" class = "mr-2 prop_field_service" {{ $checkedService == true ? 'checked' : '' }}/>
                                                                                    <label for = "{{ $row['id'] }}" title = "{{ $row['asset_category'] }}" class="cursor-pointer">
                                                                                        {{ $row['asset_category'] }}
                                                                                    </label>
                                                                                </div>
                                                                            </li>
                                                                        @endforeach
                                                                    </ul>
                                                                @else
                                                                    <p class = "py-2 text-italic text-center">
                                                                        <i class = "las la-exclamation-circle mr-1"></i>
                                                                        @lang('work_order.forms.label.no_results_found')
                                                                    </p>
                                                                @endif
                                                            </ul>
                                                            <div class = "dropdown-divider"></div>
                                                            <div class = "px-2">
                                                                <a class = "btn btn-primary w-100 mb-2" id = "service_filter_submit" href = "javascript:void(0)">@lang('work_order.button.apply')</a>
                                                                <a class = "btn btn-outline-danger w-100" id = "service_filter_reset" href = "javascript:void(0)">@lang('work_order.button.reset')</a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class = "col-lg-5 col-md-12 col-sm-12">
                                <div class = "project-top-right w-100 d-block">
                                    <div class = "row">
                                        <div class = "col-lg-7 col-md-6 col-sm-6 col-8 mb-sm-0 mb-10">
                                            <script src = "{{asset('new_theme/js/jquery.min.js')}}"></script>
                                            <script>
                                                $(document).ready(function() {
                                                    initCalendar(moment().subtract(1, 'year'));
                                                    filterByCalendar();
                                                });
                                            </script>
                                            <p class = "mb-10 d-lg-block d-md-none d-none">&nbsp;</p>
                                            <div class = "input-container icon-left position-relative">
                                                <span class = "input-icon icon-left">
                                                    <span data-feather = "calendar"></span>
                                                </span>
                                                <input type = "text" class = "form-control form-control-default" name = "text" placeholder = "{{ $currentDate }}" id = "calender_filter_workorder" autocomplete = "off">
                                                <span class = "input-icon icon-right">
                                                    <span data-feather = "chevron-down"></span>
                                                </span>
                                            </div>
                                        </div>
                                        <div class = "col-md-6 col-lg-5 col-sm-6 col-4 pl-sm-15 pl-0">
                                            <p class = "mb-10 d-lg-block d-md-none d-none">&nbsp;</p>
                                            <div class = "dropdown action-btn w-100 pb-lg-0 pb-xs-2">
                                                <button class = "btn btn-sm btn-default btn-white dropdown-toggle h-100 w-100" type = "button" id = "dropdownMenu2" data-toggle = "dropdown" aria-haspopup = "true" aria-expanded = "false">
                                                    <i class = "las la-sliders-h"></i>
                                                    @lang('work_order.forms.label.frequency')
                                                </button>
                                                <div class = "dropdown-menu" id = "filter_id_section" aria-labelledby = "dropdownMenu2" wire:ignore>
                                                    <a href = "javascript:void(0)" data-check = "true" data-value = "{{ \App\Enums\Frequency::All->value }}" class = "dropdown-item">
                                                        @lang('work_order.bread_crumbs.all')
                                                        <i id = "tick0" class = "fa fa-check float-right display-none" aria-hidden = "true"></i>
                                                    </a>
                                                    <a href = "javascript:void(0)" data-check = "false" data-value = "{{ \App\Enums\Frequency::Daily->value }}" class = "dropdown-item">
                                                        @lang('work_order.list.Daily')
                                                        <i id = "tick1" class = "fa fa-check float-right display-none" aria-hidden = "true"></i>
                                                    </a>
                                                    <a href = "javascript:void(0)" data-check = "false" data-value = "{{ \App\Enums\Frequency::Weekly->value }}" class = "dropdown-item">
                                                        @lang('work_order.list.Weekly')
                                                        <i id = "tick1" class = "fa fa-check float-right display-none" aria-hidden = "true"></i>
                                                    </a>
                                                    <a href = "javascript:void(0)" data-check = "false" data-value = "{{ \App\Enums\Frequency::Monthly->value }}" class = "dropdown-item">
                                                        @lang('work_order.list.Monthly')
                                                        <i id = "tick3" class = "fa fa-check float-right display-none" aria-hidden = "true"></i>
                                                    </a>
                                                    <a href = "javascript:void(0)" data-check = "false" data-value = "{{ \App\Enums\Frequency::Custom->value }}" class = "dropdown-item">
                                                        @lang('work_order.forms.label.Custom')
                                                        <i id = "tick6" class = "fa fa-check float-right display-none" aria-hidden = "true"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class = "projects-tab-content" id = "workorder_table">
            <div class = "container-fluid">
                <div class = "tab-content" id = "ap-tabContent">
                    <div class = "tab-pane fade show active" id = "ap-overview" role = "tabpanel" aria-labelledby = "ap-overview-tab">
                        <div>
                            <div class = "userDatatable projectDatatable project-table global-shadow border p-30 bg-white radius-xl w-100 position-relative">
                                <div class = "table-responsive" id = "workorder_table">
                                    <table class = "table mb-0">
                                        <thead>
                                            <tr class = "userDatatable-header">
                                                <th>
                                                    <span class = "projectDatatable-title">@lang('work_order.forms.label.Preventive Maintenance Title')</span>
                                                </th>
                                                @if(!in_array($user->user_type, array('building_manager', 'building_manager_employee')))
                                                    <th>
                                                        <span class = "projectDatatable-title">@lang('work_order.table.project_name')</span>
                                                    </th>
                                                @endif
                                                <th>
                                                    <span class = "projectDatatable-title">@lang('work_order.forms.label.property_name')</span>
                                                </th>
                                                <th>
                                                    <span class = "projectDatatable-title">@lang('work_order.forms.label.frequency')</span>
                                                </th>
                                                <th>
                                                    <span class = "projectDatatable-title">@lang('work_order.forms.label.asset_category')</span>
                                                </th>
                                                <th>
                                                    <span class = "projectDatatable-title">@lang('work_order.forms.label.start_date')</span>
                                                </th>
                                                <th>
                                                    <span class = "projectDatatable-title">@lang('work_order.forms.label.end_date')</span>
                                                </th>
                                                <th>
                                                    <span class = "projectDatatable-title">@lang('configration_assets.asset_categories_table.action')</span>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @if(isset($list) && $list->count())
                                                @foreach($list as $data)
                                                    <tr wire:key="row-{{ $data->id }}">
                                                        <td>
                                                            <div class = "userDatatable-content">
                                                                <a @if($viewPm) href = "{{ route('workorder.sp_preventive_details', [$this->encryptDecryptedString($data['id'])])}}" @endif>
                                                                    <span class = "userDatatable-content">
                                                                        {{ $data->pm_title ?? '-' }}
                                                                        <span class = "badge badge-round badge-light badge-lg badge-outlined">
                                                                            {{ $data['wo_total'] ?? '-' }}
                                                                        </span>
                                                                    </span>
                                                                </a>
                                                            </div>
                                                        </td>
                                                        @if(!in_array($user->user_type, array('building_manager', 'building_manager_employee')))
                                                            <td>
                                                                <div class = "userDatatable-content">
                                                                    <div class = "d-flex">
                                                                        <div class = "userDatatable-inline-title">
                                                                            <span class = "fw-400 badge badge-round badge-grey badge-lg badge">
                                                                                {{ $selectedLanguage == 'en' ? $project->project_name : $project->project_name_ar }}
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        @endif
                                                        <td>
                                                            <div class = "userDatatable-content">
                                                                <div class = "d-flex">
                                                                    <div class = "userDatatable-inline-title">
                                                                        <span class = "fw-400 badge badge-round badge-grey badge-lg badge">
                                                                            {{ isset($data->complex_name) ? $data->complex_name.'-' : '' }}
                                                                            {{ isset($data->building_name) ? $data->building_name : '' }}
                                                                            {{ isset($data->building_deleted_at) ? __('general_sentence.modal.deleted') : '' }}
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class = "userDatatable-content no-wrap" id = "dynamic_frequency">
                                                                {{ $selectedLanguage == 'en' ? $data->frequencyMaster->title : $data->frequencyMaster->title_ar }}
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class = "userDatatable-content">
                                                                <span class = "max-td">
                                                                    {{ $data->asset_category ?? '-' }}
                                                                    {{ $data->asc_deleted_at <> '' && $data->asc_is_deleted <> 'no' ? __('general_sentence.modal.deleted') : '' }}
                                                                </span>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class = "userDatatable-content">
                                                                <span class = "max-td">
                                                                    {{ $this->changeDateFormat('d-m-Y', $data->start_date) ?? '-' }}
                                                                </span>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class = "userDatatable-content">
                                                                <span class = "max-td">
                                                                    {{ $this->changeDateFormat('d-m-Y', $data->pmEndDate) ?? '-' }}
                                                                </span>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <ul class = "orderDatatable_actions mb-0 d-flex flex-wrap">
                                                                @if(in_array($user->user_type, array('sp_admin', 'supervisor', 'admin', 'admin_employee')))
                                                                    <li>
                                                                        <a class = "view" title = "@lang('work_order.user_previleges.view')" href = "{{ route('workorder.sp_preventive_details', [$this->encryptDecryptedString($data['id'])]) }}">
                                                                            <i class = "la la-eye"></i></i>
                                                                        </a>
                                                                    </li>
                                                                @endif

                                                                @if(in_array($user->user_type, array('building_manager_employee', 'building_manager', 'osool_admin')))
                                                                    @if($viewPm)
                                                                        <li>
                                                                            <a class = "view" title = "@lang('work_order.user_previleges.view')" href = "{{ route('workorder.sp_preventive_details', [$this->encryptDecryptedString($data['id'])]) }}">
                                                                                <i class = "la la-eye"></i></i>
                                                                            </a>
                                                                        </li>
                                                                    @endif

                                                                    @if($editPm)
                                                                        <li>
                                                                            <a class = "edit action_icon" title = "@lang('work_order.user_previleges.edit')" href = "{{ route('workorder.edit.preventive.info', [$this->encryptDecryptedString($data['id'])]) }}">
                                                                                <i class = "la la-edit"></i></i>
                                                                            </a>
                                                                        </li>
                                                                    @endif

                                                                    @if($deletePm)
                                                                        <li>
                                                                            <a class = "remove action_icon delete_workorderp" title = "@lang('work_order.user_previleges.remove')" href = "javascript:void(0)" data-unique_id = "{{ $this->encryptDecryptedString($data['unique_id']) }}">
                                                                                <i class = "la la-trash-alt"></i></i>
                                                                            </a>
                                                                        </li>
                                                                    @endif
                                                                @endif
                                                            </ul>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td colspan = "7">
                                                        <div class = "row">
                                                            <div class = "PropertyListEmpty">
                                                                <img src = "{{ asset('empty-icon/To_do_list_rafiki.svg' )}}" class = "fourth_img" width = "50%">
                                                                <h4 class = "first_title">@lang('work_order.common.no_preventive_maintenance_found')</h4>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    @if(!$this->valueIsRequired($list))
                        <div class = "row">
                            <div class = "col-12">
                                <div class = "user-pagination">
                                    <div class = "user-pagination">
                                        <div class = "d-flex justify-content-sm-end justify-content-end mt-1 mb-30">
                                            {!!$list->links("vendor.pagination.livewire-pagination")!!}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<livewire:p-p-m-importer />

<script>
// $(document).ready(function () {
//     function updateSelectedCount() {
//         let count = $("input[name='service_types[]']:checked").length;
//         let textElement = $("#selectedServiceText");

//         if (count > 0) {
//             textElement.text(count + " Selected");
//         } else {
//             textElement.text("@lang('work_order.forms.place_holder.service')"); // Default text if none selected
//         }
//     }

//     // Event listener for checkbox changes
//     $(document).on("change", "input[name='service_types[]']", function () {
//         updateSelectedCount();
//     });

//     // Re-run count after applying filter
//     $(document).on("click", "#service_filter_submit", function () {
//         updateSelectedCount();
//     });

//     $(document).on("click", ".property_label", function () {
//         updateSelectedCount();
//     });

//     // Run on page load to check for pre-selected checkboxes
//     updateSelectedCount();
// });

$(document).on('change', 'input[name="service_types[]"]', function () {
    const count = $('input[name="service_types[]"]:checked').length;
    const displayText = count === 0
        ? "@lang('work_order.forms.place_holder.service')"
        : count + " @lang('work_order.forms.place_holder.s_selected')";
    $('#selectedServiceText').text(displayText);
});

$(document).on('change', 'input[name="buildings[]"]', function () {
    const count = $('input[name="buildings[]"]:checked').length;
    const displayText = count === 0
        ? "@lang('work_order.forms.place_holder.property')"
        : count + " @lang('work_order.forms.place_holder.selected')";
    $('#selectedPropertyText').text(displayText);
});

//Excel File Upload for PPM Import


$('#file-upload').on('change', function(e) {
    const file = e.target.files[0];
    if (file) {
      $('#file-name').text(file.name);
      $('#file-size').text((file.size / 1024 / 1024).toFixed(2) + ' MB');
      $('.progress-wrapper').show();
      $('#import-btn').prop('disabled', true);

      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += 5;
        $('.progress-fill').css('width', progress + '%');
        if (progress >= 100) {
          clearInterval(interval);
          $('#import-btn').prop('disabled', false);
        }
      }, 200);
    }
  });

  $('.cancel-btn').on('click', function() {
    $('#file-upload').val('');
    $('.progress-wrapper').hide();
    $('.progress-fill').css('width', '0');
    $('#import-btn').prop('disabled', true);
  });
$('.dropdown-menu').on('click', function (e) {
    e.stopPropagation();
});
</script>
