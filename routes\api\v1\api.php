<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PageController;
use App\Http\Controllers\Api\V1\Tenant\ApiHelper;
use App\Http\Controllers\Api\V1\InventoryController;
use App\Http\Controllers\Api\V1\WorkOrderController;
use App\Http\Controllers\Api\ConfigurationController;
use App\Http\Controllers\Api\V1\Tenant\BidController;
use App\Http\Controllers\Api\V1\Tenant\UserController;
use App\Http\Controllers\Api\V1\User\ProfileController;
use App\Http\Controllers\Api\V1\Tenant\TenantController;
use App\Http\Controllers\Api\V1\Tenant\ServiceController;
use App\Http\Controllers\Api\V1\Auth\CustomerAuthController;
use App\Http\Controllers\Api\V1\SPVendor\SPVendorController;
use App\Http\Controllers\Api\V1\Worker\TeamLeaderController;
use App\Http\Controllers\Api\V1\Auth\PasswordResetController;
use App\Http\Controllers\Api\V1\Worker\OfflineModeController;
use App\Http\Controllers\Api\V1\Worker\RatingWorkerController;
use App\Http\Controllers\Api\V1\Worker\WorkerSummaryController;
use App\Http\Controllers\Api\V1\Auth\DeliveryManLoginController;
use App\Http\Controllers\Api\V1\Tenant\ServiceRequestController;
use App\Http\Controllers\Api\V1\Auth\Tenant\TenantAuthController;
use App\Http\Controllers\Api\V1\Tenant\EvaluateServiceController;
use App\Http\Controllers\Api\V1\Worker\WorkerWorkOrderController;
use App\Http\Controllers\Api\V1\Tenant\GetNotificationsController;
use App\Http\Controllers\Api\V1\Appointment\TimeSlotGeneratorController;
use App\Http\Controllers\Api\V1\Tenant\WorkOrder\ListReactiveWorkOrdersController;
use App\Http\Controllers\Api\V1\Tenant\WorkOrder\ListPreventiveWorkOrdersController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register api routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "api" middleware group. Now create something great!|
 */

Route::group(['namespace' => 'Api\V1'], function () {
    App::setLocale('ar');
    Route::post('Test-Notif', [TenantAuthController::class, 'Notification_Verification']);

    // Handling akaunting stuff
    Route::group(['prefix' => 'akaunting', 'namespace' => 'Akaunting'], function(){
       Route::get('test', [\App\Http\Controllers\Api\V1\Akaunting\TestController::class, 'index']);
    });
    //Handling the auth
    Route::group(['prefix' => 'auth', 'namespace' => 'Auth'], function () {
        Route::post('register', [CustomerAuthController::class, 'register']);
        Route::post('login', [CustomerAuthController::class, 'login']);
        Route::get('sendinblue', [CustomerAuthController::class, 'sendinblue']);
        Route::post('verify-phone', [CustomerAuthController::class, 'verify_phone']);
        Route::post('verify-otp', [CustomerAuthController::class, 'verify_otp']);
        Route::post('check-email', [CustomerAuthController::class, 'check_email']);
        Route::post('verify-email', [CustomerAuthController::class, 'verify_email']);
        Route::post('forgot-password', [PasswordResetController::class, 'reset_password_request']);
        Route::post('verify-token', [PasswordResetController::class, 'verify_token']);
        Route::put('reset-password', [PasswordResetController::class, 'reset_password_submit']);
        Route::get('app-versions', [CustomerAuthController::class, 'appVersions']);
        Route::get('generateFujibarcode', [CustomerAuthController::class, 'generateFujibarcode']);
        Route::group(['prefix' => 'delivery-man'], function () {
            Route::post('login', [DeliveryManLoginController::class, 'login']);
        });
        //Handling the auth of Tenant App
        Route::group(['prefix' => 'tenant', 'namespace' => 'Tenant'], function () {
            Route::get('get-onboarding', [TenantAuthController::class, 'get_onboarding']);
            Route::post('login', [TenantAuthController::class, 'login']);
            Route::post('verify-otp', [TenantAuthController::class, 'verify_otp']);
            Route::post('check-email', [TenantAuthController::class, 'check_email']);
            Route::post('verify-email', [TenantAuthController::class, 'verify_email']);
            Route::post('forgot-password', [TenantAuthController::class, 'reset_password_request']);
            Route::post('verify-token', [TenantAuthController::class, 'verify_token']);
            Route::put('reset-password', [TenantAuthController::class, 'reset_password_submit']);
            Route::get('app-versions', [TenantAuthController::class, 'appVersions']);
            Route::get('send-notifications', [TenantAuthController::class, 'sendNotifications']);
            Route::get('privacy-policy-tenant/{lang?}', [PageController::class, 'privacyPolicyTenant'])->name('home.privacy_policy_tenant');
            Route::get('generate-passport-token/{userId}', [TenantAuthController::class, 'generatePassportToken']);
        });
    });

    //Tenant app Routes
    Route::group(['middleware'=>'auth:api', 'prefix' => 'tenant','namespace' => 'Tenant'], function () {

        Route::group(['prefix' => 'orders', 'namespace' => 'WorkOrder'], function () {
            Route::get('/preventive/{id}/history', 'GetPreventiveWorkOrdersHistoryController');
            Route::get('/preventive/{id}/details', 'GetPreventiveWorkOrdersDetailsController');
            Route::get('/preventive/{building_id}', 'ListPreventiveWorkOrdersController');
        });

        Route::get('logout', [TenantController::class, 'logout']);
        Route::get('get-profile', [TenantController::class, 'get_profile']);
        Route::post('update-profile', [TenantController::class, 'update_profile']);
        Route::get('get-home', [TenantController::class, 'get_home']);
        Route::get('get-all-workorders/{building_id}/{work_order_type?}/{status?}', [TenantController::class, 'get_all_workorders']);
        Route::get('get-workorders/{work_order_type}', [TenantController::class, 'get_workorders']);
        Route::post('get-workorder-detail', [TenantController::class, 'get_workorder_detail']);
        Route::get('get-maintenance-requests', [TenantController::class, 'get_maintenance_requests']);
        Route::get('get-projects', [TenantController::class, 'get_projects']);
        Route::get('get-buildings/{project_id?}', [TenantController::class, 'get_buildings']);
        Route::get('get-building-detail/{id}', [TenantController::class, 'get_building_detail']);
        Route::post('get-buildingfloors', [TenantController::class, 'get_buildingfloors']);
        Route::post('get-buildingrooms', [TenantController::class, 'get_buildingrooms']);
        Route::get('get-recevialplaces', [TenantController::class, 'get_recevial_places']);
        Route::get('asset-categories', [TenantController::class, 'assetCategories']);
        Route::get('get-documents/{building_id}', [TenantController::class, 'get_documents']);
        Route::post('post-documents', [TenantController::class, 'post_documents']);
        Route::post('delete-documents', [TenantController::class, 'delete_documents']);
        Route::post('get-maintenancerequests', [TenantController::class, 'get_maintenancerequests']);
        Route::post('post-maintenancerequest', [TenantController::class, 'save_maintenancerequest']);
        Route::post('get-maintenancerequest-detail', [TenantController::class, 'get_maintenancerequest_detail']);
        Route::get('get-latestchat', [TenantController::class, 'get_latestchat']);
        Route::get('get-chatroom/{building_id}/{keyword?}', [TenantController::class, 'get_chatroom']);
        Route::post('post-chat', [TenantController::class, 'post_chat']);
        Route::post('post-rating', [TenantController::class, 'post_rating']);
        Route::post('post-comment', [TenantController::class, 'post_comment']);
        Route::post('post-comment-rating', [TenantController::class, 'post_comment_rating']);
        Route::post('get-post-comment', [TenantController::class, 'get_post_comment']);
        Route::post('get-post-answer', [TenantController::class, 'get_post_answer']);
        Route::post('delete-chat', [TenantController::class, 'delete_chat']);
        Route::post('delete-post-comment', [TenantController::class, 'delete_post_comment']);
        Route::get('get-contracts', [TenantController::class, 'get_contracts']);
        Route::post('get-contract-detail', [TenantController::class, 'get_contract_detail']);
        Route::get('get-other-tenants', [TenantController::class, 'get_other_tenants']);
        Route::get('get-notifications/{building_id}','GetNotificationsController');
        Route::get('read-notifications/{building_id}/{notification_id?}', [TenantController::class, 'read_notifications']);
        Route::get('delete-account', [TenantController::class, 'deleteAccount']);
        Route::get('community-status', [TenantController::class, 'communityStatus']);
        // Get the tenant enabled services
        Route::get('tenant-form-enabled-services', [TenantController::class, 'tenantFormEnabledServices']);
        // Create maintenance request
        Route::post('create-maintenance-requests', [TenantController::class, 'createMaintenanceRequests']);
        // Grouped Maintenance request
        Route::get('maintenance-requests/grouped', [TenantController::class, 'getGroupedMaintenanceRequests']);
        // Grouped Maintenance request using groupId
        Route::get('maintenance-requests/grouped/{groupId}', [TenantController::class, 'getMaintenanceRequestsByGroup']);
        // Get Maintenance request using maintenanceRequestId
        Route::get('maintenance-requests/{maintenanceRequestId}', [TenantController::class, 'getMaintenanceRequestById']);
        //Reschedule the maintenance
        Route::post('reschedule-maintenance', [TenantController::class, 'rescheduleMaintenance']);
        // Save later date api
        Route::post('save-later-date', [TenantController::class, 'saveLaterDate']);
        // Update the unit receival later date
        Route::put('/user/update-unit-receival-later', [UserController::class, 'updateUnitReceivalLater']);
        //Add Rating for MR API
        Route::post('give-rating', [TenantController::class, 'addMaintenanceRequestRating']);

        Route::post('appointments/cancel', [TenantController::class, 'cancelAppointment']);

     
    });

    //Worker App Routes
    Route::group(['middleware'=>'auth:api', 'prefix' => 'workorders'], function () {
        Route::get('logout', [WorkOrderController::class, 'logout']);
        Route::get('get_current_upcoming_orders', [WorkOrderController::class, 'get_current_upcoming_orders']);
        Route::get('details', [WorkOrderController::class, 'get_details']);
        Route::post('update_start_job', [WorkOrderController::class, 'update_start_job']);
        Route::post('update_job_on_hold', [WorkOrderController::class, 'update_job_on_hold']);
        Route::post('upload_no_checklist_actions', [WorkOrderController::class, 'upload_no_checklist_actions']);
        Route::post('complete_job', [WorkOrderController::class, 'complete_job']);
        Route::get('history', [WorkOrderController::class, 'history']);
        Route::get('overdue_orders', [WorkOrderController::class, 'overdue_orders']);
        Route::get('on_hold_orders', [WorkOrderController::class, 'on_hold_orders']);
        Route::get('re_opened_orders', [WorkOrderController::class, 're_opened_orders']);
        Route::get('rejected_orders', [WorkOrderController::class, 'rejected_orders']);
        Route::get('work_order_checklists', [WorkOrderController::class, 'getWorkorderChecklists']);
        Route::get('checklist_completed_task_details', [WorkOrderController::class, 'checklist_completed_task_details']);
        Route::get('pending_orders', [WorkOrderController::class, 'pending_orders']);
        Route::get('all_pending_orders', [WorkOrderController::class, 'all_pending_orders']);
        Route::get('profile', [WorkOrderController::class, 'profile']);
        Route::get('get_privileges', [WorkOrderController::class, 'getPrivileges']);
        Route::get('notifications', [WorkOrderController::class, 'notifications']);
        Route::get('read_notifications', [WorkOrderController::class, 'readNotifications']);
        Route::get('delete-account', [WorkOrderController::class, 'deleteAccount']);
        Route::get('get-projects', [WorkOrderController::class, 'get_projects']);
        Route::get('get-buildings/{project_id?}', [WorkOrderController::class, 'get_buildings']);
        Route::get('get-building-detail/{id}', [WorkOrderController::class, 'get_building_detail']);
        Route::get('get-building-floors/{building_id?}', [WorkOrderController::class, 'get_building_floors']);
        Route::get('get-building-rooms/{building_id?}/{floor?}', [WorkOrderController::class, 'get_building_rooms']);
        Route::get('asset-categories', [WorkOrderController::class, 'assetCategories']);
        Route::post('save-maintenance-request', [WorkOrderController::class, 'save_maintenance_request']);
        Route::get('get-maintenancerequest-detail/{id}', [WorkOrderController::class, 'get_maintenancerequest_detail']);
        Route::get('get-maintenance-requests', [WorkOrderController::class, 'get_maintenance_requests']);
        Route::get('get-subtask-action-list', [WorkOrderController::class, 'get_subtask_action_list']);
        Route::post('save_app_language', [WorkOrderController::class, 'save_app_language']);
        Route::post('change-profile-image', [WorkOrderController::class, 'changeWorkerProfilepicture']);
        Route::post('change-password', [WorkOrderController::class, 'changePassword']);
        Route::get('check-passwordchange-attempt', [WorkOrderController::class, 'checkPasswordchangeAttempt']);
        Route::post('resend-otp', [WorkOrderController::class, 'resendOtp']);
        Route::post('verify-otp', [WorkOrderController::class, 'verifyOtp']);
        Route::get('check-phonenumberchange-attempt', [WorkOrderController::class, 'checkPhonenumberChangeAttempt']);
        Route::post('change-phonenumber', [WorkOrderController::class, 'changePhonenumber']);
        Route::get('workorder-filterdata', [WorkOrderController::class, 'workorderFilterdata']);
        Route::get('asset-names/{asset_category_id?}', [WorkOrderController::class, 'assetNames']);
        Route::post('change-availability-status', [WorkOrderController::class, 'changeAvailabilityStatus']);
        Route::get('availability-request-reason-type', [WorkOrderController::class, 'availabilityRequestReasonType']);
        Route::get('availability-request-logout', [WorkOrderController::class, 'availabilityRequestLogout']);
        Route::get('check-active-leave-request', [WorkOrderController::class, 'checkActiveLeaveRequestStatus']);
        Route::get('clock-in-timer', [WorkOrderController::class, 'ClockInTimer']);
        Route::get('attendance-history', [WorkOrderController::class, 'fetchAttendanceHistory']);
        Route::get('count-workorders', [WorkOrderController::class, 'getWorkordersCount']);
        Route::get('workorders-by-status', [WorkOrderController::class, 'getFetchWorkorderbyStatus']);

        Route::group(['prefix' => 'inventory'], function () {
            Route::get('start-examine/{workOrderId}', [InventoryController::class, 'startExamine']);
            Route::get('items/{workOrderId}', [InventoryController::class, 'fetchInventoryItems']);
            Route::post('store-item/{workOrderId}', [InventoryController::class, 'storeWorkOrderItems']);
        });
        Route::get('get-qr-code/{project_id}/{building_id}', [WorkOrderController::class, 'getQrCode']);
        //Start geofence API
        Route::get('get-qr-code', [WorkOrderController::class, 'getQrCode']);
        Route::post('scan-qr', [WorkOrderController::class, 'scanQR']);
        Route::post('complete-work', [WorkOrderController::class, 'completeWork']);
        Route::post('update-location', [WorkOrderController::class, 'updateLocation']);
        Route::post('popup-location-match', [WorkOrderController::class, 'popupLocationMatch']);
        // end geofence API
        Route::get('asset-details/{asset_category_id?}', [WorkOrderController::class, 'assetDetails']);
        Route::get('qr-scan-details/{asset_barcode_id?}', [WorkOrderController::class, 'qrScanDetails']);
        Route::get('project-settings', [WorkOrderController::class, 'projectSettings']);
        Route::get('get-leave-requests', [WorkOrderController::class, 'getFetchLeaveRequests']);
        Route::get('get-leave-request-details', [WorkOrderController::class, 'getLeaveRequestDetails']);
        //Offline mode API
        Route::get('sync-work-orders', [OfflineModeController::class, 'syncWorkOrders']);
        Route::post("sync-single-work-order/{workOrderID}", [OfflineModeController::class, 'syncSingleWorkOrder']);
        //end Offline mode API
    });


    /*
    |--------------------------------------------------------------------------
    | SMS API
    |--------------------------------------------------------------------------
    |
    | This route is to just theck the api response.
    |
    */
    Route::group(['prefix' => 'tenant','namespace' => 'Tenant'], function () {
        Route::get('check_sms_api', [TenantController::class, 'check_sms_api']);
        Route::get('get_qr_code', [WorkOrderController::class, 'get_current_upcoming_orders']);
    });

    Route::post('send-notification-to-worker', function (Request $request) {
        $userToken = collect($request->input('token'))->toArray();
        $body = $request->input('body');
        $title = $request->input('title');
        $workerID = $request->input('worker_id');
        $message = [
            'title'=>$title,
            'body'=>$body,
        ];
        $building_id = '';
        $notification_type = '';
        $res= ApiHelper::send_custom_notification_worker_FCM($workerID,$userToken,$message,$building_id,'test_notification','$notification_type');
        return response()->json($res);
    });

    Route::group(['middleware' => 'api', 'prefix' => 'configurations'], function($router){
        Route::get('/configurations-list',[ConfigurationController::class,'showAllConfigurationsList']);
        Route::get('/configuration/{code}',[ConfigurationController::class,'showConfigurationByCode']);
        Route::get('/configurations-list-auth-user',[ConfigurationController::class,'showAllConfigurationListAuthUser']);
        Route::get('/configurations-auth-user/{code}',[ConfigurationController::class,'showByCodeConfigurationAuthUser']);


    });

    Route::group(['middleware' => 'auth:api', 'prefix' => 'appointments'], function () {
        Route::put('/later', 'Appointment\LaterAppointmentController');
        Route::get('/settings', 'Appointment\AppointmentSettingsController');
        Route::get('/time_slots', 'Appointment\TimeSlotGeneratorController');
        Route::put('/appointments-cancel/{id}', 'Appointment\CancelAppointmentController');
    });

    Route::group(['middleware' => 'auth:api'], function () {
        Route::resource('appointments', 'Appointment\AppointmentController', ['only' => ['index', 'show', 'store']]);
    });
    Route::group(['prefix' => 'workers'], function ($router) {
        Route::get('/get-worker-rating/{id}',[RatingWorkerController::class,'getRatingWorker']);
    });
    // Worker-related APIs
    Route::prefix('worker')->group(function () {
        // Work order summary (status counts)
        Route::get('/{worker}/work-orders-list', [WorkerSummaryController::class, 'getWorkerWorkOrderSummary']);
        // Paginated work orders by status with search functionality
        Route::get('/{worker_id}/work-orders', [WorkerWorkOrderController::class, 'getWorkOrdersByStatus']);
    });

      // Worker-related APIs
    Route::prefix('user')->group(function () {
        // View profile
        Route::get('/{id}/profile', [ProfileController::class, 'show']);
        // Update profile
        Route::post('/{id}/update-profile', [ProfileController::class, 'update']);

        Route::post('/phone-number/request-otp', [ProfileController::class, 'requestPhoneOtp']);
        Route::post('/phone-number/verify-otp', [ProfileController::class, 'verifyPhoneOtp']);
        Route::post('/phone-number/resend-otp', [ProfileController::class, 'resendOtp']);
    });

    // Team lead-related APIs
   // Route::prefix('team-leader')->group(function () {
   Route::group(['middleware' => 'auth:api', 'prefix' => 'team-leader'], function () {
        // Team lead dashboard (assuming you still want this in TeamLeaderController)
        Route::get('/get-dashboard', [TeamLeaderController::class, 'getDashboard']);

        // Get workers assigned to team lead
        Route::get('/get-workers', [TeamLeaderController::class, 'listWorkers']);

        // GEt work order Count by status
        Route::get('/count-workorders', [TeamLeaderController::class, 'getCountWorkorders']);
        // Get Workorder list by status
        Route::get('/workorder-list-by-status', [TeamLeaderController::class, 'getWorkorderlistbyStatus']);

        // Get filter data for work orders
        Route::get('/workorder-filter-data', [TeamLeaderController::class, 'geteWorkorderFilterdata']);

        // Assign work order to tl / worker
        Route::post('/assign-workorder', [TeamLeaderController::class, 'assignWorkorder']);


        // Upload chat attchment file
        Route::post('/upload-file', [TeamLeaderController::class, 'uploadFile']);


        
        // Send  notification for chat to worker and tl
        Route::post('/chat-notifiy', [TeamLeaderController::class, 'sendChatNotification']);
    });

    Route::middleware('auth:api')->group(function () {

        Route::group(['prefix' => 'vendors'], function () {
            Route::get('/list', [SPVendorController::class, 'getVendorsList']);
            Route::get('/details/{vendorId}', [SPVendorController::class, 'getVendorDetails']);
            Route::group(['prefix' => 'evaluate-services'], function () {
                Route::post('rate', [EvaluateServiceController::class, 'rate']);
            });
        });


        Route::get('service-list', [ServiceController::class, 'getServiceList']);
        
        Route::post('service-requests', [ServiceRequestController::class, 'createServiceRequest']);
        Route::post('get-service-requests-list', [ServiceRequestController::class, 'getServiceRequestList']);
        Route::post('get-service-requests-detail', [ServiceRequestController::class, 'getServiceRequestDetail']);

        Route::post('get-work-order', [ServiceRequestController::class, 'getWorkOrder']);
        Route::post('work-order-rating', [ServiceRequestController::class, 'workOrderRating']);

        Route::post('work-order-accept', [ServiceRequestController::class, 'workOrderAccept']);

        Route::post('get-service-requests-quotations', [ServiceRequestController::class, 'getServiceRequestQuotations']);



        Route::post('get-service-requests-tracking', [ServiceRequestController::class, 'getServiceRequestTracking']);

        Route::post('accept-bid-offer', [BidController::class, 'acceptBidOffer']);
        Route::post('revise-bid-offer', [BidController::class, 'reviseBidOffer']);
        Route::post('get-bid-comment', [BidController::class, 'getBidComment']);

        Route::get('get-reject-reasons', [BidController::class, 'getRejectResons']);

        Route::post('reject-bid-offer', [BidController::class, 'rejectBidOffer']);

        Route::post('cancel-service-requests', [ServiceRequestController::class, 'cancelServiceRequest']);
        
        Route::post('place-bid', [BidController::class, 'placeBid']);

        //Route::post('get-vendor-list', [ServiceRequestController::class, 'getVendorList']);

         Route::post('set_app_language', [TenantController::class, 'set_app_language']);
    }); 

});
