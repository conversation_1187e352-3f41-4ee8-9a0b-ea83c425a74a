{"__meta": {"id": "X2c7b6183a2a8b8a0e29a159e7d036f78", "datetime": "2025-07-29 12:47:35", "utime": **********.711522, "method": "POST", "uri": "/livewire/message/accounting.customers", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[12:47:35] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.638688, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.277923, "end": **********.711537, "duration": 0.*****************, "duration_str": "434ms", "measures": [{"label": "Booting", "start": **********.277923, "relative_start": 0, "end": **********.621512, "relative_end": **********.621512, "duration": 0.*****************, "duration_str": "344ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.62152, "relative_start": 0.3435969352722168, "end": **********.711538, "relative_end": 1.1920928955078125e-06, "duration": 0.09001803398132324, "duration_str": "90.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 37663544, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00316, "accumulated_duration_str": "3.16ms", "statements": [{"sql": "select * from `users` where `id` = 7368 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 23}], "duration": 0.00316, "duration_str": "3.16ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_dev_db2", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": 1}, "count": 1}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/_debugbar/open?id=Xac050a3537657dc5e3fea4cfb84826ef&op=get\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7368", "plain_user_password": "123456", "locale": "en", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/livewire/message/accounting.customers", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">ulKVgM41LsERWxYjjvGA</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">accounting.customers</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"17 characters\">finance/customers</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>l584382499-0</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Iil7DKM5BIl2zAfSogoD</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>l584382499-1</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">D6wi8qyADxJ66QyFAwrF</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">bf89f91a</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:35</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>viewMode</span>\" => \"<span class=sf-dump-str title=\"4 characters\">list</span>\"\n      \"<span class=sf-dump-key>search</span>\" => \"\"\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n      \"<span class=sf-dump-key>customers</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>564</span>\n          \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00050</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Fouzan</span>\"\n          \"<span class=sf-dump-key>mobile_no</span>\" => \"<span class=sf-dump-str title=\"14 characters\">+91-9999999999</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"15 characters\"><EMAIL></span>\"\n          \"<span class=sf-dump-key>balance</span>\" => <span class=sf-dump-num>-628</span>\n          \"<span class=sf-dump-key>initials</span>\" => \"<span class=sf-dump-str>F</span>\"\n          \"<span class=sf-dump-key>avatar_color</span>\" => \"<span class=sf-dump-str title=\"10 characters\">bg-warning</span>\"\n          \"<span class=sf-dump-key>salesperson</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>activities</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>display_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00050</span>\"\n          \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">+91-9999999999</span>\"\n          \"<span class=sf-dump-key>total_sales</span>\" => <span class=sf-dump-num>0</span>\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>500</span>\n          \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00009</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Customer 9</span>\"\n          \"<span class=sf-dump-key>mobile_no</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+919876543209</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"\n          \"<span class=sf-dump-key>balance</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>initials</span>\" => \"<span class=sf-dump-str title=\"2 characters\">C9</span>\"\n          \"<span class=sf-dump-key>avatar_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">bg-loss</span>\"\n          \"<span class=sf-dump-key>salesperson</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>activities</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>display_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00009</span>\"\n          \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+919876543209</span>\"\n          \"<span class=sf-dump-key>total_sales</span>\" => <span class=sf-dump-num>0</span>\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>499</span>\n          \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00008</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Customer 8</span>\"\n          \"<span class=sf-dump-key>mobile_no</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+447800123408</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"\n          \"<span class=sf-dump-key>balance</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>initials</span>\" => \"<span class=sf-dump-str title=\"2 characters\">C8</span>\"\n          \"<span class=sf-dump-key>avatar_color</span>\" => \"<span class=sf-dump-str title=\"14 characters\">bg-new-primary</span>\"\n          \"<span class=sf-dump-key>salesperson</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>activities</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>display_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00008</span>\"\n          \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+447800123408</span>\"\n          \"<span class=sf-dump-key>total_sales</span>\" => <span class=sf-dump-num>0</span>\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>498</span>\n          \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00007</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Customer 7</span>\"\n          \"<span class=sf-dump-key>mobile_no</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+447700123407</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"\n          \"<span class=sf-dump-key>balance</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>initials</span>\" => \"<span class=sf-dump-str title=\"2 characters\">C7</span>\"\n          \"<span class=sf-dump-key>avatar_color</span>\" => \"<span class=sf-dump-str title=\"10 characters\">bg-warning</span>\"\n          \"<span class=sf-dump-key>salesperson</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>activities</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>display_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00007</span>\"\n          \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+447700123407</span>\"\n          \"<span class=sf-dump-key>total_sales</span>\" => <span class=sf-dump-num>0</span>\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>497</span>\n          \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00006</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Customer 6</span>\"\n          \"<span class=sf-dump-key>mobile_no</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+14041234506</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"\n          \"<span class=sf-dump-key>balance</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>initials</span>\" => \"<span class=sf-dump-str title=\"2 characters\">C6</span>\"\n          \"<span class=sf-dump-key>avatar_color</span>\" => \"<span class=sf-dump-str title=\"6 characters\">bg-win</span>\"\n          \"<span class=sf-dump-key>salesperson</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>activities</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>display_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00006</span>\"\n          \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+14041234506</span>\"\n          \"<span class=sf-dump-key>total_sales</span>\" => <span class=sf-dump-num>0</span>\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>496</span>\n          \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00005</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Customer 5</span>\"\n          \"<span class=sf-dump-key>mobile_no</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+15551234505</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"\n          \"<span class=sf-dump-key>balance</span>\" => <span class=sf-dump-num>-8970</span>\n          \"<span class=sf-dump-key>initials</span>\" => \"<span class=sf-dump-str title=\"2 characters\">C5</span>\"\n          \"<span class=sf-dump-key>avatar_color</span>\" => \"<span class=sf-dump-str title=\"10 characters\">bg-primary</span>\"\n          \"<span class=sf-dump-key>salesperson</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>activities</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>display_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00005</span>\"\n          \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+15551234505</span>\"\n          \"<span class=sf-dump-key>total_sales</span>\" => <span class=sf-dump-num>0</span>\n        </samp>]\n        <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>540</span>\n          \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00049</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 49</span>\"\n          \"<span class=sf-dump-key>mobile_no</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+15551234549</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n          \"<span class=sf-dump-key>balance</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>initials</span>\" => \"<span class=sf-dump-str title=\"2 characters\">C4</span>\"\n          \"<span class=sf-dump-key>avatar_color</span>\" => \"<span class=sf-dump-str title=\"10 characters\">bg-warning</span>\"\n          \"<span class=sf-dump-key>salesperson</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>activities</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>display_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00049</span>\"\n          \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+15551234549</span>\"\n          \"<span class=sf-dump-key>total_sales</span>\" => <span class=sf-dump-num>0</span>\n        </samp>]\n        <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>539</span>\n          \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00048</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 48</span>\"\n          \"<span class=sf-dump-key>mobile_no</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+971551234548</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n          \"<span class=sf-dump-key>balance</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>initials</span>\" => \"<span class=sf-dump-str title=\"2 characters\">C4</span>\"\n          \"<span class=sf-dump-key>avatar_color</span>\" => \"<span class=sf-dump-str title=\"6 characters\">bg-win</span>\"\n          \"<span class=sf-dump-key>salesperson</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>activities</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>display_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00048</span>\"\n          \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+971551234548</span>\"\n          \"<span class=sf-dump-key>total_sales</span>\" => <span class=sf-dump-num>0</span>\n        </samp>]\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>538</span>\n          \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00047</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 47</span>\"\n          \"<span class=sf-dump-key>mobile_no</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+971501234547</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n          \"<span class=sf-dump-key>balance</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>initials</span>\" => \"<span class=sf-dump-str title=\"2 characters\">C4</span>\"\n          \"<span class=sf-dump-key>avatar_color</span>\" => \"<span class=sf-dump-str title=\"10 characters\">bg-primary</span>\"\n          \"<span class=sf-dump-key>salesperson</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>activities</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>display_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00047</span>\"\n          \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+971501234547</span>\"\n          \"<span class=sf-dump-key>total_sales</span>\" => <span class=sf-dump-num>0</span>\n        </samp>]\n        <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>537</span>\n          \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00046</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 46</span>\"\n          \"<span class=sf-dump-key>mobile_no</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+966561234546</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n          \"<span class=sf-dump-key>balance</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>initials</span>\" => \"<span class=sf-dump-str title=\"2 characters\">C4</span>\"\n          \"<span class=sf-dump-key>avatar_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">bg-hold</span>\"\n          \"<span class=sf-dump-key>salesperson</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>activities</span>\" => \"<span class=sf-dump-str>-</span>\"\n          \"<span class=sf-dump-key>display_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00046</span>\"\n          \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+966561234546</span>\"\n          \"<span class=sf-dump-key>total_sales</span>\" => <span class=sf-dump-num>0</span>\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>50</span>\n      \"<span class=sf-dump-key>currentPage</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>lastPage</span>\" => <span class=sf-dump-num>5</span>\n      \"<span class=sf-dump-key>loading</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>error</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>customerId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>contact</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>tax_number</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>billing_name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>billing_phone</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>billing_address</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>billing_city</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>billing_state</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>billing_country</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>billing_zip</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>shipping_name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>shipping_phone</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>shipping_address</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>shipping_city</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>shipping_state</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>shipping_country</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>shipping_zip</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>electronic_address</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>electronic_address_scheme</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>password</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">65dc9b50fc1d2baf1377bf1838b8403f61df591066e5caa2b2107e616affe73e</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">c8bq</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">goToView</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"200 characters\">eyJpdiI6IlJsak1SYUljWTM4OWJmV3NYa2pqdWc9PSIsInZhbHVlIjoiRzl4UERCclAyR2VPZWNKV1QzdUpLQT09IiwibWFjIjoiMjk4NjZjYmZkNGQyODg4OWNkM2Q0ZjcyYzRmZWQ0MWViYjFkNmVkNTA1ZmVjYmRlYTkxZWZjMzdlZWU3Y2VmZSIsInRhZyI6IiJ9</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1156144659 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">4102</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://osool-b2g.test/finance/customers</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"467 characters\">laravel_session=GOxW6BteBlci4BUCkQHjCtca8ErcwVAkVGuhKO5W; XSRF-TOKEN=eyJpdiI6InJGZXc5SGlzVTVLeTA4c0R6M3VFVVE9PSIsInZhbHVlIjoiQzNjVkxvT1V4WXpEcnFsQU9mQ1JoQzV6RVI5bERyTXQvWTZ1c0lta0lhRlNQWDRJUmN1Tlp1MHRHKzBxdW5qZFVPS2VYYkZnQm0wa2k0YkcvQk9RMWthM0FKeXVOWThjeXh5QStaRFVFMEhMejh3YkxOekZ2YUxFUzJBZjBlNkgiLCJtYWMiOiI5YTIzMjYwOTY5NWFkOGM0YTVjZTYzMTdkNTI5YWVhZTg1ZTc0ZTZjZTk1YzkzZTE2MzZjZDU2Y2FlYTk3ZDY4IiwidGFnIjoiIn0%3D; osool_session=ukgxiCLrWh2Ph1pUUnYGYr2xGpNV0HmbCgiKY1PL</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1156144659\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:43</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">4102</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://osool-b2g.test/finance/customers</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"467 characters\">laravel_session=GOxW6BteBlci4BUCkQHjCtca8ErcwVAkVGuhKO5W; XSRF-TOKEN=eyJpdiI6InJGZXc5SGlzVTVLeTA4c0R6M3VFVVE9PSIsInZhbHVlIjoiQzNjVkxvT1V4WXpEcnFsQU9mQ1JoQzV6RVI5bERyTXQvWTZ1c0lta0lhRlNQWDRJUmN1Tlp1MHRHKzBxdW5qZFVPS2VYYkZnQm0wa2k0YkcvQk9RMWthM0FKeXVOWThjeXh5QStaRFVFMEhMejh3YkxOekZ2YUxFUzJBZjBlNkgiLCJtYWMiOiI5YTIzMjYwOTY5NWFkOGM0YTVjZTYzMTdkNTI5YWVhZTg1ZTc0ZTZjZTk1YzkzZTE2MzZjZDU2Y2FlYTk3ZDY4IiwidGFnIjoiIn0%3D; osool_session=ukgxiCLrWh2Ph1pUUnYGYr2xGpNV0HmbCgiKY1PL</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60982</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"38 characters\">/livewire/message/accounting.customers</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"38 characters\">/livewire/message/accounting.customers</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.2779</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-707523120 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 09:47:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InJ6cG9EOHN1L2todkhiR3hGY3dmbXc9PSIsInZhbHVlIjoiNytxeWwwT2VEZHlOV2tMZ2F5STU4WHFnazdKWEJSWXN6OXdmN1R6U3BBTEEvam9XSDRyaVlSaGJHZXkyNFdxSTdDRCszL0pnYWEwRDQ5ODE5c1E1aHpRNjJ4NDZONWhpTDBnNE00UjJUenJGb1gvWFhVZEFvaG1lWTlPdnozaUYiLCJtYWMiOiJmN2M3MzY3YzI3ZDI5YTM3M2ZhZDNjNWEwOWVlMzI1OTkyNGMyNDc0YzgyNDU4ODgxZTE0YmRmNTJjMzhhYzViIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 11:47:35 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6ImdPdTBVN085UTRQTFdTMzVnMmxsWUE9PSIsInZhbHVlIjoidzl1VFc2L1ZPazJyQmU4aHdyTnpiM1FTRXM0RWZjMnVKNUUvaUJneWtvTmY5L1l1QkhRTVdvV1B3TkRoVkVRNWpvOGhaRThuUWd2YVI0TXRMd0pONnFJV29UTW9pY04xcXRNRnFSUU5jQjNjTGxlME8zT2NOQ3IrenNMUzVJbXIiLCJtYWMiOiI3ZTMwNzI0Mjc5Njc1Mjc0NTJmOTNlOTFkNTc2ZWQ0NjcwMGIzZDU3NzhkNjc1YWQzZGNmYmIwZjMzZDBhOWM0IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 11:47:35 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InJ6cG9EOHN1L2todkhiR3hGY3dmbXc9PSIsInZhbHVlIjoiNytxeWwwT2VEZHlOV2tMZ2F5STU4WHFnazdKWEJSWXN6OXdmN1R6U3BBTEEvam9XSDRyaVlSaGJHZXkyNFdxSTdDRCszL0pnYWEwRDQ5ODE5c1E1aHpRNjJ4NDZONWhpTDBnNE00UjJUenJGb1gvWFhVZEFvaG1lWTlPdnozaUYiLCJtYWMiOiJmN2M3MzY3YzI3ZDI5YTM3M2ZhZDNjNWEwOWVlMzI1OTkyNGMyNDc0YzgyNDU4ODgxZTE0YmRmNTJjMzhhYzViIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 11:47:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6ImdPdTBVN085UTRQTFdTMzVnMmxsWUE9PSIsInZhbHVlIjoidzl1VFc2L1ZPazJyQmU4aHdyTnpiM1FTRXM0RWZjMnVKNUUvaUJneWtvTmY5L1l1QkhRTVdvV1B3TkRoVkVRNWpvOGhaRThuUWd2YVI0TXRMd0pONnFJV29UTW9pY04xcXRNRnFSUU5jQjNjTGxlME8zT2NOQ3IrenNMUzVJbXIiLCJtYWMiOiI3ZTMwNzI0Mjc5Njc1Mjc0NTJmOTNlOTFkNTc2ZWQ0NjcwMGIzZDU3NzhkNjc1YWQzZGNmYmIwZjMzZDBhOWM0IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 11:47:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-707523120\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-341130877 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"80 characters\">http://osool-b2g.test/_debugbar/open?id=Xac050a3537657dc5e3fea4cfb84826ef&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7368</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-341130877\", {\"maxDepth\":0})</script>\n"}}