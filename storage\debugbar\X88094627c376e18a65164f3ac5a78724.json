{"__meta": {"id": "X88094627c376e18a65164f3ac5a78724", "datetime": "2025-07-29 13:16:15", "utime": **********.924028, "method": "POST", "uri": "/livewire/message/notifications.new-notifications-list-top-nav", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 6, "messages": [{"message": "[13:16:14] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.514616, "xdebug_link": null, "collector": "log"}, {"message": "[13:16:14] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.598908, "xdebug_link": null, "collector": "log"}, {"message": "[13:16:14] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.599036, "xdebug_link": null, "collector": "log"}, {"message": "[13:16:14] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.599137, "xdebug_link": null, "collector": "log"}, {"message": "[13:16:14] LOG.warning: Optional parameter $filters declared before required parameter $userServiceProvider is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\WorkOrdersTrait.php on line 3418", "message_html": null, "is_string": false, "label": "warning", "time": **********.609335, "xdebug_link": null, "collector": "log"}, {"message": "[13:16:14] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.615152, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.04255, "end": **********.924054, "duration": 1.8815038204193115, "duration_str": "1.88s", "measures": [{"label": "Booting", "start": **********.04255, "relative_start": 0, "end": **********.492914, "relative_end": **********.492914, "duration": 0.4503638744354248, "duration_str": "450ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.49293, "relative_start": 0.4503798484802246, "end": **********.924055, "relative_end": 1.1920928955078125e-06, "duration": 1.4311251640319824, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 40231496, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "livewire.notifications.new-notifications-list-top-nav (\\resources\\views\\livewire\\notifications\\new-notifications-list-top-nav.blade.php)", "param_count": 28, "params": ["list", "totalUnreadNotifications", "errors", "_instance", "user", "perPage", "assignedAsset", "contractsIds", "accessBuildingsIds", "currentDate", "currentDateTime", "readyToLoad", "configOciLink", "ociLink", "selectedLanguage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 12, "nb_failed_statements": 0, "accumulated_duration": 1.2618999999999998, "accumulated_duration_str": "1.26s", "statements": [{"sql": "select * from `users` where `id` = 7368 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 23}], "duration": 0.00364, "duration_str": "3.64ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_dev_db2", "start_percent": 0, "width_percent": 0.288}, {"sql": "select `asset_id` from `user_assets_mapping` where `user_id` = 7368", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Traits\\UserAssetMappingTrait.php", "line": 11}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 62}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 202}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.0035, "duration_str": "3.5ms", "stmt_id": "\\app\\Http\\Traits\\UserAssetMappingTrait.php:11", "connection": "osool_dev_db2", "start_percent": 0.288, "width_percent": 0.277}, {"sql": "select `contracts`.`id` from `contracts` where `contracts`.`is_deleted` = 'no' and `contracts`.`deleted_at` is null and `contracts`.`user_id` = 7368 and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["no", "7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Traits\\ContractsTrait.php", "line": 160}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 74}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 203}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\app\\Http\\Traits\\ContractsTrait.php:160", "connection": "osool_dev_db2", "start_percent": 0.566, "width_percent": 0.084}, {"sql": "select `property_buildings`.`id` from `properties` left join `property_buildings` on `property_buildings`.`property_id` = `properties`.`id` where `properties`.`user_id` = 7368 and `properties`.`is_deleted` = 'no' and `properties`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7368", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Traits\\PropertyTrait.php", "line": 317}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 103}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 204}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00277, "duration_str": "2.77ms", "stmt_id": "\\app\\Http\\Traits\\PropertyTrait.php:317", "connection": "osool_dev_db2", "start_percent": 0.65, "width_percent": 0.22}, {"sql": "select `id_configuration`, `name`, `code`, `value`, `description`, `active`, `platform` from `configurations` where `code` = 3 and `configurations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ConfigurationTrait.php", "line": 49}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 167}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 205}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\app\\Http\\Traits\\ConfigurationTrait.php:49", "connection": "osool_dev_db2", "start_percent": 0.869, "width_percent": 0.048}, {"sql": "select `asset_id` from `user_assets_mapping` where `user_id` = 7368", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Traits\\UserAssetMappingTrait.php", "line": 11}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 62}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 149}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 207}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.0016699999999999998, "duration_str": "1.67ms", "stmt_id": "\\app\\Http\\Traits\\UserAssetMappingTrait.php:11", "connection": "osool_dev_db2", "start_percent": 0.917, "width_percent": 0.132}, {"sql": "select `contracts`.`id` from `contracts` where `contracts`.`is_deleted` = 'no' and `contracts`.`deleted_at` is null and `contracts`.`user_id` = 7368 and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["no", "7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Traits\\ContractsTrait.php", "line": 160}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 74}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 207}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\app\\Http\\Traits\\ContractsTrait.php:160", "connection": "osool_dev_db2", "start_percent": 1.049, "width_percent": 0.048}, {"sql": "select `property_buildings`.`id` from `properties` left join `property_buildings` on `property_buildings`.`property_id` = `properties`.`id` where `properties`.`user_id` = 7368 and `properties`.`is_deleted` = 'no' and `properties`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7368", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Traits\\PropertyTrait.php", "line": 317}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 103}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 207}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Traits\\PropertyTrait.php:317", "connection": "osool_dev_db2", "start_percent": 1.097, "width_percent": 0.063}, {"sql": "select `id_configuration`, `name`, `code`, `value`, `description`, `active`, `platform` from `configurations` where `code` = 3 and `configurations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ConfigurationTrait.php", "line": 49}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 167}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 154}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 207}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Http\\Traits\\ConfigurationTrait.php:49", "connection": "osool_dev_db2", "start_percent": 1.159, "width_percent": 0.074}, {"sql": "select `notifications`.*, `work_orders`.`work_order_id`, `work_orders`.`work_order_type`, `work_orders`.`assigned_to`, `work_orders`.`supervisor_id` from `notifications` left join `work_orders` on `work_orders`.`id` = `notifications`.`section_id` left join `user_assets_mapping` on `user_assets_mapping`.`contract_id` = `work_orders`.`contract_id` where (`notifications`.`is_timeline` = 'no' or `notifications`.`is_timeline` is null) and (NOT find_in_set(7368, notifications.user_id)) and (0 = 1 and 0 = 1 and `notifications`.`notification_sub_type` in ('new_work_order_created', 'bm_has_approved_and_evaluated_wo', 'wo_completed_wo', 'bm_work_order_rejected', 'bm_has_did_not_agreed_on_workorder', 'bm_has_reopend_wo', 'wo_started_wo', 'sent_to_project_owner', 'pause_workorder') and `notifications`.`section_type` = 'work_order' and `notifications`.`section_type` != 'new_chat_message' and `notifications`.`created_at` between '2025-06-29 13:16:14' and '2025-07-29 13:16:14') or (`section_type` in ('commercial_contracts') and 0 = 1 and `notifications`.`created_at` between '2025-06-29 13:16:14' and '2025-07-29 13:16:14') or (`section_type` in ('bookings', 'milestones') and `notifications`.`user_id` in (7368) and `notifications`.`created_at` between '2025-06-29 13:16:14' and '2025-07-29 13:16:14') or (`notifications`.`section_type` = 'report' and `notifications`.`user_id` = 7368 and `notifications`.`created_at` between '2025-06-29 13:16:14' and '2025-07-29 13:16:14') or (`notifications`.`section_type` = 'contracts' and FIND_IN_SET(7368, notifications.user_id) and `notifications`.`created_at` between '2025-06-29 13:16:14' and '2025-07-29 13:16:14') or (`notifications`.`section_type` = 'variation_order' and FIND_IN_SET( 7368, notifications.user_id) and `notifications`.`created_at` between '2025-06-29 13:16:14' and '2025-07-29 13:16:14') or (`notifications`.`section_type` = 'advance_contracts' and FIND_IN_SET(7368, notifications.user_id) and `notifications`.`created_at` between '2025-06-29 13:16:14' and '2025-07-29 13:16:14') or (`notifications`.`section_type` = 'complaints' and FIND_IN_SET(7368, notifications.user_id) and `notifications`.`created_at` between '2025-06-29 13:16:14' and '2025-07-29 13:16:14') group by `notifications`.`id` order by `notifications`.`created_at` desc limit 5", "type": "query", "params": [], "bindings": ["no", "new_work_order_created", "bm_has_approved_and_evaluated_wo", "wo_completed_wo", "bm_work_order_rejected", "bm_has_did_not_agreed_on_workorder", "bm_has_reopend_wo", "wo_started_wo", "sent_to_project_owner", "pause_workorder", "work_order", "new_chat_message", "2025-06-29 13:16:14", "2025-07-29 13:16:14", "commercial_contracts", "2025-06-29 13:16:14", "2025-07-29 13:16:14", "bookings", "milestones", "7368", "2025-06-29 13:16:14", "2025-07-29 13:16:14", "report", "7368", "2025-06-29 13:16:14", "2025-07-29 13:16:14", "contracts", "2025-06-29 13:16:14", "2025-07-29 13:16:14", "variation_order", "2025-06-29 13:16:14", "2025-07-29 13:16:14", "advance_contracts", "2025-06-29 13:16:14", "2025-07-29 13:16:14", "complaints", "2025-06-29 13:16:14", "2025-07-29 13:16:14"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Traits\\NotificationTrait.php", "line": 286}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 217}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 30}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.68296, "duration_str": "683ms", "stmt_id": "\\app\\Http\\Traits\\NotificationTrait.php:286", "connection": "osool_dev_db2", "start_percent": 1.234, "width_percent": 54.122}, {"sql": "select `notifications`.*, `work_orders`.`work_order_id`, `work_orders`.`work_order_type`, `work_orders`.`assigned_to`, `work_orders`.`supervisor_id` from `notifications` left join `work_orders` on `work_orders`.`id` = `notifications`.`section_id` left join `user_assets_mapping` on `user_assets_mapping`.`contract_id` = `work_orders`.`contract_id` where (`notifications`.`is_timeline` = 'no' or `notifications`.`is_timeline` is null) and (NOT find_in_set(7368, notifications.user_id)) and (0 = 1 and 0 = 1 and `notifications`.`notification_sub_type` in ('new_work_order_created', 'bm_has_approved_and_evaluated_wo', 'wo_completed_wo', 'bm_work_order_rejected', 'bm_has_did_not_agreed_on_workorder', 'bm_has_reopend_wo', 'wo_started_wo', 'sent_to_project_owner', 'pause_workorder') and `notifications`.`section_type` = 'work_order' and `notifications`.`section_type` != 'new_chat_message' and `notifications`.`created_at` between '2025-06-29 13:16:15' and '2025-07-29 13:16:14') or (`section_type` in ('commercial_contracts') and 0 = 1 and `notifications`.`created_at` between '2025-06-29 13:16:15' and '2025-07-29 13:16:14') or (`section_type` in ('bookings', 'milestones') and `notifications`.`user_id` in (7368) and `notifications`.`created_at` between '2025-06-29 13:16:15' and '2025-07-29 13:16:14') or (`notifications`.`section_type` = 'report' and `notifications`.`user_id` = 7368 and `notifications`.`created_at` between '2025-06-29 13:16:15' and '2025-07-29 13:16:14') or (`notifications`.`section_type` = 'contracts' and FIND_IN_SET(7368, notifications.user_id) and `notifications`.`created_at` between '2025-06-29 13:16:15' and '2025-07-29 13:16:14') or (`notifications`.`section_type` = 'variation_order' and FIND_IN_SET( 7368, notifications.user_id) and `notifications`.`created_at` between '2025-06-29 13:16:15' and '2025-07-29 13:16:14') or (`notifications`.`section_type` = 'advance_contracts' and FIND_IN_SET(7368, notifications.user_id) and `notifications`.`created_at` between '2025-06-29 13:16:15' and '2025-07-29 13:16:14') or (`notifications`.`section_type` = 'complaints' and FIND_IN_SET(7368, notifications.user_id) and `notifications`.`created_at` between '2025-06-29 13:16:15' and '2025-07-29 13:16:14') group by `notifications`.`id` order by `notifications`.`created_at` desc", "type": "query", "params": [], "bindings": ["no", "new_work_order_created", "bm_has_approved_and_evaluated_wo", "wo_completed_wo", "bm_work_order_rejected", "bm_has_did_not_agreed_on_workorder", "bm_has_reopend_wo", "wo_started_wo", "sent_to_project_owner", "pause_workorder", "work_order", "new_chat_message", "2025-06-29 13:16:15", "2025-07-29 13:16:14", "commercial_contracts", "2025-06-29 13:16:15", "2025-07-29 13:16:14", "bookings", "milestones", "7368", "2025-06-29 13:16:15", "2025-07-29 13:16:14", "report", "7368", "2025-06-29 13:16:15", "2025-07-29 13:16:14", "contracts", "2025-06-29 13:16:15", "2025-07-29 13:16:14", "variation_order", "2025-06-29 13:16:15", "2025-07-29 13:16:14", "advance_contracts", "2025-06-29 13:16:15", "2025-07-29 13:16:14", "complaints", "2025-06-29 13:16:15", "2025-07-29 13:16:14"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Traits\\NotificationTrait.php", "line": 276}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\NotificationTrait.php", "line": 297}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 227}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 31}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.56277, "duration_str": "563ms", "stmt_id": "\\app\\Http\\Traits\\NotificationTrait.php:276", "connection": "osool_dev_db2", "start_percent": 55.355, "width_percent": 44.597}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 201 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_dev_db2", "start_percent": 99.952, "width_percent": 0.048}]}, "models": {"data": {"App\\Models\\ProjectsDetails": 1, "App\\Models\\Configuration": 2, "App\\Models\\User": 1}, "count": 4}, "livewire": {"data": {"notifications.new-notifications-list-top-nav #8ZJ2EOuxecahXq9TMkL1": "array:5 [\n  \"data\" => array:11 [\n    \"user\" => App\\Models\\User {#3272\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:69 [\n        \"id\" => 7368\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$Nrgb2fubI18xCSRqaKjUjOzVJbptwn80b0U.ID9srqgW/v2G.7cpy\"\n        \"name\" => \"Crm Test 02\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => \"99090\"\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => null\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => null\n        \"role_cities\" => null\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => null\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 21\n        \"project_id\" => 201\n        \"project_user_id\" => 7368\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-02-26 20:35:15\"\n        \"modified_at\" => \"2025-07-29 12:45:45\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khansaa-test34444\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNzgyMzQ1LCJleHAiOjE3NTM3ODU5NDUsIm5iZiI6MTc1Mzc4MjM0NSwianRpIjoidTIzV0hGQTdWTmdtUXZtTiIsInN1YiI6IjY1IiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.EmUhl2LGOZHlyMhYAPKT_bYbQnG0gsMSUXzTViUo4Hw\"\n      ]\n      #original: array:69 [\n        \"id\" => 7368\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$Nrgb2fubI18xCSRqaKjUjOzVJbptwn80b0U.ID9srqgW/v2G.7cpy\"\n        \"name\" => \"Crm Test 02\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => \"99090\"\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => null\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => null\n        \"role_cities\" => null\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => null\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 21\n        \"project_id\" => 201\n        \"project_user_id\" => 7368\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-02-26 20:35:15\"\n        \"modified_at\" => \"2025-07-29 12:45:45\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khansaa-test34444\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNzgyMzQ1LCJleHAiOjE3NTM3ODU5NDUsIm5iZiI6MTc1Mzc4MjM0NSwianRpIjoidTIzV0hGQTdWTmdtUXZtTiIsInN1YiI6IjY1IiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.EmUhl2LGOZHlyMhYAPKT_bYbQnG0gsMSUXzTViUo4Hw\"\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:1 [\n        \"projectDetails\" => App\\Models\\ProjectsDetails {#4451\n          #connection: \"mysql\"\n          #table: \"projects_details\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:22 [\n            \"id\" => 201\n            \"user_id\" => 0\n            \"project_name\" => \"Khadeer CRM Test 02\"\n            \"project_name_ar\" => \"Khadeer CRM Test 02\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-26 17:34:01\"\n            \"updated_at\" => \"2025-04-23 13:56:58\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:22 [\n            \"id\" => 201\n            \"user_id\" => 0\n            \"project_name\" => \"Khadeer CRM Test 02\"\n            \"project_name_ar\" => \"Khadeer CRM Test 02\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-26 17:34:01\"\n            \"updated_at\" => \"2025-04-23 13:56:58\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:16 [\n            0 => \"user_id\"\n            1 => \"project_name\"\n            2 => \"project_name_ar\"\n            3 => \"project_image\"\n            4 => \"industry_type\"\n            5 => \"created_by\"\n            6 => \"is_deleted\"\n            7 => \"use_erp_module\"\n            8 => \"use_tenant_module\"\n            9 => \"tenant_status\"\n            10 => \"use_beneficiary_module\"\n            11 => \"benificiary_status\"\n            12 => \"community_status\"\n            13 => \"contract_status\"\n            14 => \"share_post\"\n            15 => \"use_crm_module\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n          #excludedAttributes: []\n          +auditEvent: null\n          +auditCustomOld: null\n          +auditCustomNew: null\n          +isCustomEvent: false\n          +preloadedResolverData: []\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:62 [\n        0 => \"allow_akaunting\"\n        1 => \"email\"\n        2 => \"password\"\n        3 => \"name\"\n        4 => \"first_name\"\n        5 => \"last_name\"\n        6 => \"apartment\"\n        7 => \"unit_receival_date\"\n        8 => \"later_booking_alert\"\n        9 => \"phone\"\n        10 => \"profile_img\"\n        11 => \"address\"\n        12 => \"country_id\"\n        13 => \"city_id\"\n        14 => \"role_regions\"\n        15 => \"role_cities\"\n        16 => \"asset_categories\"\n        17 => \"properties\"\n        18 => \"contracts\"\n        19 => \"beneficiary\"\n        20 => \"service_provider\"\n        21 => \"user_type\"\n        22 => \"project_id\"\n        23 => \"project_user_id\"\n        24 => \"created_by\"\n        25 => \"status\"\n        26 => \"user_privileges\"\n        27 => \"approved_max_amount\"\n        28 => \"emp_id\"\n        29 => \"profession_id\"\n        30 => \"emp_dept\"\n        31 => \"building_ids\"\n        32 => \"contract_ids\"\n        33 => \"supervisor_id\"\n        34 => \"sp_admin_id\"\n        35 => \"langForSms\"\n        36 => \"deleted_at\"\n        37 => \"otp\"\n        38 => \"temp_password\"\n        39 => \"otp_for_password\"\n        40 => \"otp_for_password_verified\"\n        41 => \"temp_phone_number\"\n        42 => \"favorite_language\"\n        43 => \"is_subcontractors_worker\"\n        44 => \"keeper_warehouses\"\n        45 => \"save_later_date\"\n        46 => \"first_login\"\n        47 => \"is_unit_link\"\n        48 => \"akaunting_vendor_id\"\n        49 => \"akaunting_customer_id\"\n        50 => \"crm_api_token\"\n        51 => \"workspace_slug\"\n        52 => \"is_bma_area_manager\"\n        53 => \"assigned_workers\"\n        54 => \"sleep_mode\"\n        55 => \"offline_mode\"\n        56 => \"attendance_mandatory\"\n        57 => \"admin_level\"\n        58 => \"role\"\n        59 => \"attendance_target\"\n        60 => \"salary\"\n        61 => \"show_extra_info\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #rememberTokenName: \"remember_token\"\n      #accessToken: null\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n    }\n    \"perPage\" => 5\n    \"assignedAsset\" => []\n    \"contractsIds\" => []\n    \"accessBuildingsIds\" => []\n    \"currentDate\" => \"2025-07-29\"\n    \"currentDateTime\" => \"2025-07-29 13:16:14\"\n    \"readyToLoad\" => true\n    \"configOciLink\" => App\\Models\\Configuration {#4474\n      #connection: \"mysql\"\n      #table: \"configurations\"\n      #primaryKey: \"id_configuration\"\n      #keyType: \"int\"\n      +incrementing: false\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id_configuration\" => 3\n        \"name\" => \"OciLink\"\n        \"code\" => 3\n        \"value\" => \"https://axpem4hcpiq4.compat.objectstorage.me-jeddah-1.oraclecloud.com\"\n        \"description\" => \"The OCI storage link\"\n        \"active\" => 1\n        \"platform\" => \"1\"\n      ]\n      #original: array:7 [\n        \"id_configuration\" => 3\n        \"name\" => \"OciLink\"\n        \"code\" => 3\n        \"value\" => \"https://axpem4hcpiq4.compat.objectstorage.me-jeddah-1.oraclecloud.com\"\n        \"description\" => \"The OCI storage link\"\n        \"active\" => 1\n        \"platform\" => \"1\"\n      ]\n      #changes: []\n      #casts: array:3 [\n        \"platform\" => \"App\\Enums\\Platform\"\n        \"active\" => \"App\\Enums\\Status\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:13 [\n        0 => \"id_configuration\"\n        1 => \"platform\"\n        2 => \"name\"\n        3 => \"code\"\n        4 => \"value\"\n        5 => \"description\"\n        6 => \"active\"\n        7 => \"created_by\"\n        8 => \"deleted_by\"\n        9 => \"updated_by\"\n        10 => \"deleted_at\"\n        11 => \"created_at\"\n        12 => \"updated_at\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"ociLink\" => \"https://axpem4hcpiq4.compat.objectstorage.me-jeddah-1.oraclecloud.com\"\n    \"selectedLanguage\" => \"en\"\n  ]\n  \"name\" => \"notifications.new-notifications-list-top-nav\"\n  \"view\" => \"livewire.notifications.new-notifications-list-top-nav\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav\"\n  \"id\" => \"8ZJ2EOuxecahXq9TMkL1\"\n]"}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/finance/proposal/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7368", "plain_user_password": "123456", "locale": "en", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/livewire/message/notifications.new-notifications-list-top-nav", "status_code": "<pre class=sf-dump id=sf-dump-1002918580 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1002918580\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-231623058 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-231623058\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2030803145 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">8ZJ2EOuxecahXq9TMkL1</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"44 characters\">notifications.new-notifications-list-top-nav</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"23 characters\">finance/proposal/create</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">47a312c0</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>assignedAsset</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>contractsIds</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>accessBuildingsIds</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>currentDate</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>currentDateTime</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>readyToLoad</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>configOciLink</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>ociLink</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>selectedLanguage</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">f293e2ec212107b726011ddd0438137862f4f27fd30c27b079e674e0e7154553</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">awtc</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">loadData</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2030803145\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1723210428 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">640</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://osool-b2g.test/finance/proposal/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"467 characters\">laravel_session=GOxW6BteBlci4BUCkQHjCtca8ErcwVAkVGuhKO5W; XSRF-TOKEN=eyJpdiI6IlZXcjM3ZzMzYmN1KzFBakphbHJPc0E9PSIsInZhbHVlIjoiZ3ZRd1lPbmtqeklCeXhJeEVra1BDQkdZUkZHb2R0TjRSNWhFa3FuZjB3bVlnWGFUWWdDOE1ES0hHdnRFcDdoR01pNmxGVWJEck13MEJwbFVmL2c2eTAvSk9FTTBYbE1UeFVkek1zY0kwWFVFZG9VdWltSGlrWXl0NjVEM1RMQW4iLCJtYWMiOiJhNTE5YTc3MDA4YzhjMDNmNmI1YjVkOTU3MDU0MGMzNzhhNDkzY2U1OGFhYzFkNmNlNjM5NjgwMjgzOTlhMTRlIiwidGFnIjoiIn0%3D; osool_session=h4KnTICfkewM1CCT8BCVpbNwn1SBvVI4ftCRrPJ0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723210428\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-792968694 data-indent-pad=\"  \"><span class=sf-dump-note>array:43</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">640</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://osool-b2g.test/finance/proposal/create</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"467 characters\">laravel_session=GOxW6BteBlci4BUCkQHjCtca8ErcwVAkVGuhKO5W; XSRF-TOKEN=eyJpdiI6IlZXcjM3ZzMzYmN1KzFBakphbHJPc0E9PSIsInZhbHVlIjoiZ3ZRd1lPbmtqeklCeXhJeEVra1BDQkdZUkZHb2R0TjRSNWhFa3FuZjB3bVlnWGFUWWdDOE1ES0hHdnRFcDdoR01pNmxGVWJEck13MEJwbFVmL2c2eTAvSk9FTTBYbE1UeFVkek1zY0kwWFVFZG9VdWltSGlrWXl0NjVEM1RMQW4iLCJtYWMiOiJhNTE5YTc3MDA4YzhjMDNmNmI1YjVkOTU3MDU0MGMzNzhhNDkzY2U1OGFhYzFkNmNlNjM5NjgwMjgzOTlhMTRlIiwidGFnIjoiIn0%3D; osool_session=h4KnTICfkewM1CCT8BCVpbNwn1SBvVI4ftCRrPJ0</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">62710</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"62 characters\">/livewire/message/notifications.new-notifications-list-top-nav</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"62 characters\">/livewire/message/notifications.new-notifications-list-top-nav</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.0426</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-792968694\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2078542830 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2078542830\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-420102140 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 10:16:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkFyM2gvZ0lWOVBRcmtlbjg1NHJyMVE9PSIsInZhbHVlIjoiQUhZUWFDMjNHY0ZHejFFSXl2cU5NYjRIaUppaWt5cktQNFp5Mmc2S0IwNHFPYUYyTmE3M3BKWU1rcSs5VUxQREw0c3VTaGd1cGVQOFFVUENKNkxvVmFhWXZJYVlvWGIzdSswQmZJL0hHU2FPamFiZzN2S0ZIaG5RRWVkRTNZVmgiLCJtYWMiOiJjMjdmMWRjZWY1ZjBlZGZhMTQ1MGUyYmM1ZTM2NjQ5NWNjMzIzYjVlMTg1Y2RhOGM1MzBmOTg2NzY1OTlmOWNiIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 12:16:15 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IklFMlA2QXV5SVBQYWo0TWk5dEFiZHc9PSIsInZhbHVlIjoiM2lhRkJvSUJGK000aWlWTFh1U2xhUUxrNWFIQVU3Mk1yQWJta2ozODBGdU9TYVEzU1h4T2VOZnMyN3RCQXlXUkhvL1FVWUo4UG1lbzZsRnZWalJ3NEtxZHFCNzdGR1RQWE94cnBGNEtQMHkzZWg1Y2YydmFPdWZFWnZtZGxZUUoiLCJtYWMiOiI2MGRkYjg2ZjljMGQwMGI0NzM2ZmQ2ZmM3OTFhMzkyNWNkZWRmOTY5NTUyNTg4Y2M5ODM5N2RiNTdmYmU1ZDgxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 12:16:15 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkFyM2gvZ0lWOVBRcmtlbjg1NHJyMVE9PSIsInZhbHVlIjoiQUhZUWFDMjNHY0ZHejFFSXl2cU5NYjRIaUppaWt5cktQNFp5Mmc2S0IwNHFPYUYyTmE3M3BKWU1rcSs5VUxQREw0c3VTaGd1cGVQOFFVUENKNkxvVmFhWXZJYVlvWGIzdSswQmZJL0hHU2FPamFiZzN2S0ZIaG5RRWVkRTNZVmgiLCJtYWMiOiJjMjdmMWRjZWY1ZjBlZGZhMTQ1MGUyYmM1ZTM2NjQ5NWNjMzIzYjVlMTg1Y2RhOGM1MzBmOTg2NzY1OTlmOWNiIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 12:16:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IklFMlA2QXV5SVBQYWo0TWk5dEFiZHc9PSIsInZhbHVlIjoiM2lhRkJvSUJGK000aWlWTFh1U2xhUUxrNWFIQVU3Mk1yQWJta2ozODBGdU9TYVEzU1h4T2VOZnMyN3RCQXlXUkhvL1FVWUo4UG1lbzZsRnZWalJ3NEtxZHFCNzdGR1RQWE94cnBGNEtQMHkzZWg1Y2YydmFPdWZFWnZtZGxZUUoiLCJtYWMiOiI2MGRkYjg2ZjljMGQwMGI0NzM2ZmQ2ZmM3OTFhMzkyNWNkZWRmOTY5NTUyNTg4Y2M5ODM5N2RiNTdmYmU1ZDgxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 12:16:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-420102140\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1984931761 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://osool-b2g.test/finance/proposal/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7368</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1984931761\", {\"maxDepth\":0})</script>\n"}}