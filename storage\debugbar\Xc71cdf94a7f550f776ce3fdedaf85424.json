{"__meta": {"id": "Xc71cdf94a7f550f776ce3fdedaf85424", "datetime": "2025-07-29 14:18:04", "utime": **********.771139, "method": "POST", "uri": "/livewire/message/accounting.customer-view", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 4, "messages": [{"message": "[14:18:02] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.159309, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:02] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.236964, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:02] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.237006, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:02] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.237044, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.794256, "end": **********.771166, "duration": 2.97691011428833, "duration_str": "2.98s", "measures": [{"label": "Booting", "start": **********.794256, "relative_start": 0, "end": **********.139494, "relative_end": **********.139494, "duration": 0.34523797035217285, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.139502, "relative_start": 0.****************, "end": **********.771168, "relative_end": 1.9073486328125e-06, "duration": 2.****************, "duration_str": "2.63s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.accounting.customers.view (\\resources\\views\\livewire\\accounting\\customers\\view.blade.php)", "param_count": 19, "params": ["errors", "_instance", "listdata", "accounts", "customerId", "activeTab", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.accounting.customers.include.proposal (\\resources\\views\\livewire\\accounting\\customers\\include\\proposal.blade.php)", "param_count": 32, "params": ["errors", "_instance", "customerId", "search", "perPage", "sortField", "sortDirection", "showFullView", "proposals", "total", "currentPage", "lastPage", "loading", "error", "editId", "deleteId", "deleteTitle", "page", "paginators", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00698, "accumulated_duration_str": "6.98ms", "statements": [{"sql": "select * from `users` where `id` = 7368 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 23}], "duration": 0.00639, "duration_str": "6.39ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_dev_db2", "start_percent": 0, "width_percent": 91.547}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 201 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.00058**********9999, "duration_str": "590μs", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_dev_db2", "start_percent": 91.547, "width_percent": 8.453}]}, "models": {"data": {"App\\Models\\ProjectsDetails": 1, "App\\Models\\User": 1}, "count": 2}, "livewire": {"data": {"accounting.customers.customer-proposal #U0v1Xu0cr7aK6JLVxrlt": "array:5 [\n  \"data\" => array:17 [\n    \"customerId\" => 564\n    \"search\" => \"\"\n    \"perPage\" => 10\n    \"sortField\" => \"issue_date\"\n    \"sortDirection\" => \"desc\"\n    \"showFullView\" => false\n    \"proposals\" => array:3 [\n      0 => array:6 [\n        \"id\" => 221\n        \"proposal_number\" => \"#PROP000004\"\n        \"issue_date\" => \"2025-07-29\"\n        \"amount\" => 150\n        \"status\" => \"Draft\"\n        \"status_color\" => \"primary\"\n      ]\n      1 => array:6 [\n        \"id\" => 222\n        \"proposal_number\" => \"#PROP000005\"\n        \"issue_date\" => \"2025-07-29\"\n        \"amount\" => 100\n        \"status\" => \"Draft\"\n        \"status_color\" => \"primary\"\n      ]\n      2 => array:6 [\n        \"id\" => 195\n        \"proposal_number\" => \"#PROP000001\"\n        \"issue_date\" => \"2025-06-18\"\n        \"amount\" => 440\n        \"status\" => \"Open\"\n        \"status_color\" => \"warning\"\n      ]\n    ]\n    \"total\" => 3\n    \"currentPage\" => 1\n    \"lastPage\" => 1\n    \"loading\" => false\n    \"error\" => null\n    \"editId\" => null\n    \"deleteId\" => null\n    \"deleteTitle\" => \"\"\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"accounting.customers.customer-proposal\"\n  \"view\" => \"livewire.accounting.customers.include.proposal\"\n  \"component\" => \"App\\Http\\Livewire\\Accounting\\Customers\\CustomerProposal\"\n  \"id\" => \"U0v1Xu0cr7aK6JLVxrlt\"\n]", "accounting.customer-view #HhPHATf7qRo7pBY9oKSa": "array:5 [\n  \"data\" => array:4 [\n    \"listdata\" => array:25 [\n      \"name\" => \"Fouzan\"\n      \"email\" => \"<EMAIL>\"\n      \"contact\" => \"+91-**********\"\n      \"tax_number\" => \"TAX1234\"\n      \"billing_name\" => \"Fouzan\"\n      \"billing_country\" => \"India\"\n      \"billing_state\" => \"Andhra Pradesh\"\n      \"billing_city\" => \"Hyderabad\"\n      \"billing_phone\" => \"*********\"\n      \"billing_zip\" => \"500008\"\n      \"billing_address\" => \"Hyderabad\"\n      \"shipping_name\" => \"Fouzan\"\n      \"shipping_country\" => \"India\"\n      \"shipping_state\" => \"Andhra Pradesh\"\n      \"shipping_city\" => \"Hyderabad\"\n      \"shipping_phone\" => \"*********\"\n      \"shipping_zip\" => \"500008\"\n      \"shipping_address\" => \"Hyderabad\"\n      \"customer_id\" => \"#CUST00050\"\n      \"created_at\" => \"2025-06-18T08:37:27.000000Z\"\n      \"balance\" => -628\n      \"overdue\" => 0\n      \"total_sum_of_invoices\" => 2178\n      \"invoice_quantity\" => 3\n      \"avg_sales\" => 726\n    ]\n    \"accounts\" => []\n    \"customerId\" => 564\n    \"activeTab\" => \"proposals\"\n  ]\n  \"name\" => \"accounting.customer-view\"\n  \"view\" => \"livewire.accounting.customers.view\"\n  \"component\" => \"App\\Http\\Livewire\\Accounting\\CustomerView\"\n  \"id\" => \"HhPHATf7qRo7pBY9oKSa\"\n]"}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/finance/customers/view/eyJpdiI6IlJsak1SYUljWTM4OWJmV3NYa2pqdWc9PSIsInZhbHVlIjoiRzl4UERCclAyR2VPZWNKV1QzdUpLQT09IiwibWFjIjoiMjk4NjZjYmZkNGQyODg4OWNkM2Q0ZjcyYzRmZWQ0MWViYjFkNmVkNTA1ZmVjYmRlYTkxZWZjMzdlZWU3Y2VmZSIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7368", "plain_user_password": "123456", "locale": "en", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/livewire/message/accounting.customer-view", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">HhPHATf7qRo7pBY9oKSa</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">accounting.customer-view</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"223 characters\">finance/customers/view/eyJpdiI6IlJsak1SYUljWTM4OWJmV3NYa2pqdWc9PSIsInZhbHVlIjoiRzl4UERCclAyR2VPZWNKV1QzdUpLQT09IiwibWFjIjoiMjk4NjZjYmZkNGQyODg4OWNkM2Q0ZjcyYzRmZWQ0MWViYjFkNmVkNTA1ZmVjYmRlYTkxZWZjMzdlZWU3Y2VmZSIsInRhZyI6IiJ9</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">7f76a123</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>listdata</span>\" => <span class=sf-dump-note>array:25</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Fouzan</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"15 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>contact</span>\" => \"<span class=sf-dump-str title=\"14 characters\">+91-**********</span>\"\n        \"<span class=sf-dump-key>tax_number</span>\" => \"<span class=sf-dump-str title=\"7 characters\">TAX1234</span>\"\n        \"<span class=sf-dump-key>billing_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Fouzan</span>\"\n        \"<span class=sf-dump-key>billing_country</span>\" => \"<span class=sf-dump-str title=\"5 characters\">India</span>\"\n        \"<span class=sf-dump-key>billing_state</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Andhra Pradesh</span>\"\n        \"<span class=sf-dump-key>billing_city</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Hyderabad</span>\"\n        \"<span class=sf-dump-key>billing_phone</span>\" => \"<span class=sf-dump-str title=\"9 characters\">*********</span>\"\n        \"<span class=sf-dump-key>billing_zip</span>\" => \"<span class=sf-dump-str title=\"6 characters\">500008</span>\"\n        \"<span class=sf-dump-key>billing_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Hyderabad</span>\"\n        \"<span class=sf-dump-key>shipping_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Fouzan</span>\"\n        \"<span class=sf-dump-key>shipping_country</span>\" => \"<span class=sf-dump-str title=\"5 characters\">India</span>\"\n        \"<span class=sf-dump-key>shipping_state</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Andhra Pradesh</span>\"\n        \"<span class=sf-dump-key>shipping_city</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Hyderabad</span>\"\n        \"<span class=sf-dump-key>shipping_phone</span>\" => \"<span class=sf-dump-str title=\"9 characters\">*********</span>\"\n        \"<span class=sf-dump-key>shipping_zip</span>\" => \"<span class=sf-dump-str title=\"6 characters\">500008</span>\"\n        \"<span class=sf-dump-key>shipping_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Hyderabad</span>\"\n        \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">#CUST00050</span>\"\n        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-06-18T08:37:27.000000Z</span>\"\n        \"<span class=sf-dump-key>balance</span>\" => <span class=sf-dump-num>-628</span>\n        \"<span class=sf-dump-key>overdue</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>total_sum_of_invoices</span>\" => <span class=sf-dump-num>2178</span>\n        \"<span class=sf-dump-key>invoice_quantity</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>avg_sales</span>\" => <span class=sf-dump-num>726</span>\n      </samp>]\n      \"<span class=sf-dump-key>accounts</span>\" => []\n      \"<span class=sf-dump-key>customerId</span>\" => <span class=sf-dump-num>564</span>\n      \"<span class=sf-dump-key>activeTab</span>\" => \"<span class=sf-dump-str title=\"7 characters\">details</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">7f464fe05a9ceb4948714b26be5060178492dfd55965b77be53792b95d4c2243</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">onxa</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">activeTab</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">proposals</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-344004935 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1334</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"245 characters\">http://osool-b2g.test/finance/customers/view/eyJpdiI6IlJsak1SYUljWTM4OWJmV3NYa2pqdWc9PSIsInZhbHVlIjoiRzl4UERCclAyR2VPZWNKV1QzdUpLQT09IiwibWFjIjoiMjk4NjZjYmZkNGQyODg4OWNkM2Q0ZjcyYzRmZWQ0MWViYjFkNmVkNTA1ZmVjYmRlYTkxZWZjMzdlZWU3Y2VmZSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"467 characters\">laravel_session=GOxW6BteBlci4BUCkQHjCtca8ErcwVAkVGuhKO5W; XSRF-TOKEN=eyJpdiI6Iit1RGxrREFHZ2hVM2dLVWhWZXRVeHc9PSIsInZhbHVlIjoiNzZJWjhsd2tsYW8yT2ZaaFE5dHh3ZE5OTWpVcmZqOFg1anU1S0N2QTlyR2pvVjJ2N2lIV25YT1FyK1Jwa0VyWTJtNE5OM004WWhvWk5FYlc2YmMyU2U0VUg4bjFWMnBNdGJPSDZuTVpwV0Q0bzFHYStwYm1WN1lwNytQbnE5QS8iLCJtYWMiOiIzMTM2NjY2NjQxYmFmOTIwNjA3NTVkNmMwYjk2ZjYyOTYzMTU4ZmJjMTY0ZDkzOWU1NmQxNWZmNzVhMjIxMWQ2IiwidGFnIjoiIn0%3D; osool_session=XbAoQ6pdzQfZxyaksDHyK2YBfDSKB2Q3mYYb1zXm</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-344004935\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:43</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1334</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"245 characters\">http://osool-b2g.test/finance/customers/view/eyJpdiI6IlJsak1SYUljWTM4OWJmV3NYa2pqdWc9PSIsInZhbHVlIjoiRzl4UERCclAyR2VPZWNKV1QzdUpLQT09IiwibWFjIjoiMjk4NjZjYmZkNGQyODg4OWNkM2Q0ZjcyYzRmZWQ0MWViYjFkNmVkNTA1ZmVjYmRlYTkxZWZjMzdlZWU3Y2VmZSIsInRhZyI6IiJ9</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"467 characters\">laravel_session=GOxW6BteBlci4BUCkQHjCtca8ErcwVAkVGuhKO5W; XSRF-TOKEN=eyJpdiI6Iit1RGxrREFHZ2hVM2dLVWhWZXRVeHc9PSIsInZhbHVlIjoiNzZJWjhsd2tsYW8yT2ZaaFE5dHh3ZE5OTWpVcmZqOFg1anU1S0N2QTlyR2pvVjJ2N2lIV25YT1FyK1Jwa0VyWTJtNE5OM004WWhvWk5FYlc2YmMyU2U0VUg4bjFWMnBNdGJPSDZuTVpwV0Q0bzFHYStwYm1WN1lwNytQbnE5QS8iLCJtYWMiOiIzMTM2NjY2NjQxYmFmOTIwNjA3NTVkNmMwYjk2ZjYyOTYzMTU4ZmJjMTY0ZDkzOWU1NmQxNWZmNzVhMjIxMWQ2IiwidGFnIjoiIn0%3D; osool_session=XbAoQ6pdzQfZxyaksDHyK2YBfDSKB2Q3mYYb1zXm</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">50050</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"42 characters\">/livewire/message/accounting.customer-view</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"42 characters\">/livewire/message/accounting.customer-view</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.7943</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1445268565 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 11:18:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik9UbjN5NmZ0czNkVkFZWU5rdE5abHc9PSIsInZhbHVlIjoiZkkwcjdJVEpUd2R5MmRjODkrYzBFeXlHZ0lScHltK0RKVzc4Y0JuU00xOWdJOSsrTjhHK3lCQml0alJGWkJnb2k4SVhUTGpNV2c2Q2J0YVJXSG1XSHkxd0l5SFNQcXlrd2NTNlBwaWg2di9iVWJjL2V5MEtNVTBzZWZTVjhONDciLCJtYWMiOiIwZjEyZWIzZmRmYmVmNTBkNGNmM2EyODRiY2M3NDM5ZTUzYTlhMDc5YmJjYmRkY2MyNjk1MjAyMzc5OGU0M2FjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 13:18:04 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IlN1R01TWjdqby9waVVtTit5dkdyd2c9PSIsInZhbHVlIjoidHVOMEhYSWhQV3hvakNRTkZaYlFMMmVidzZMcS9tS05lRW9CTGtPdjhoMmpwcUljU3YxSVFHdFExRUJwTmpmaUcrWFhrVFJJWXhMSUYzY3JkSnJhU1hSQUpIRWlVYXlqd1Y3OW5TNU9VU3BOZmN6OGVOdGRObFpHZmdnNStBcU8iLCJtYWMiOiI2NWJhYTA1MDlhMWEwMTkyMTgxM2ZiMGU0NjZkMWY3OGRiMWI4YmQyMTI2YTZiNjU0NGNmOTNmMTVlYmVmYTJjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 13:18:04 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik9UbjN5NmZ0czNkVkFZWU5rdE5abHc9PSIsInZhbHVlIjoiZkkwcjdJVEpUd2R5MmRjODkrYzBFeXlHZ0lScHltK0RKVzc4Y0JuU00xOWdJOSsrTjhHK3lCQml0alJGWkJnb2k4SVhUTGpNV2c2Q2J0YVJXSG1XSHkxd0l5SFNQcXlrd2NTNlBwaWg2di9iVWJjL2V5MEtNVTBzZWZTVjhONDciLCJtYWMiOiIwZjEyZWIzZmRmYmVmNTBkNGNmM2EyODRiY2M3NDM5ZTUzYTlhMDc5YmJjYmRkY2MyNjk1MjAyMzc5OGU0M2FjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 13:18:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IlN1R01TWjdqby9waVVtTit5dkdyd2c9PSIsInZhbHVlIjoidHVOMEhYSWhQV3hvakNRTkZaYlFMMmVidzZMcS9tS05lRW9CTGtPdjhoMmpwcUljU3YxSVFHdFExRUJwTmpmaUcrWFhrVFJJWXhMSUYzY3JkSnJhU1hSQUpIRWlVYXlqd1Y3OW5TNU9VU3BOZmN6OGVOdGRObFpHZmdnNStBcU8iLCJtYWMiOiI2NWJhYTA1MDlhMWEwMTkyMTgxM2ZiMGU0NjZkMWY3OGRiMWI4YmQyMTI2YTZiNjU0NGNmOTNmMTVlYmVmYTJjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 13:18:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1445268565\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1896530968 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"245 characters\">http://osool-b2g.test/finance/customers/view/eyJpdiI6IlJsak1SYUljWTM4OWJmV3NYa2pqdWc9PSIsInZhbHVlIjoiRzl4UERCclAyR2VPZWNKV1QzdUpLQT09IiwibWFjIjoiMjk4NjZjYmZkNGQyODg4OWNkM2Q0ZjcyYzRmZWQ0MWViYjFkNmVkNTA1ZmVjYmRlYTkxZWZjMzdlZWU3Y2VmZSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7368</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1896530968\", {\"maxDepth\":0})</script>\n"}}