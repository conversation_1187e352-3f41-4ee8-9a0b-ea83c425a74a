{"__meta": {"id": "Xe7aa4b5902d5afb0492ee9b984b234d5", "datetime": "2025-07-29 13:14:43", "utime": **********.447803, "method": "POST", "uri": "/livewire/message/accounting.proposal.proposal-create", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[13:14:43] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.36924, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.021249, "end": **********.447825, "duration": 0.****************, "duration_str": "427ms", "measures": [{"label": "Booting", "start": **********.021249, "relative_start": 0, "end": **********.351925, "relative_end": **********.351925, "duration": 0.****************, "duration_str": "331ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.351935, "relative_start": 0.****************, "end": **********.447826, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "95.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "livewire.accounting.proposal.proposal-create (\\resources\\views\\livewire\\accounting\\proposal\\proposal-create.blade.php)", "param_count": 35, "params": ["errors", "_instance", "customers", "billing_types", "categories", "templates", "items_list", "item_types", "proposal_type", "customer_id", "issue_date", "category_id", "proposal_template", "proposal_number", "items", "subtotal", "discount_total", "tax_total", "total", "loading", "saving", "error", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0038299999999999996, "accumulated_duration_str": "3.83ms", "statements": [{"sql": "select * from `users` where `id` = 7368 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 23}], "duration": 0.0030499999999999998, "duration_str": "3.05ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_dev_db2", "start_percent": 0, "width_percent": 79.634}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 201 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_dev_db2", "start_percent": 79.634, "width_percent": 20.366}]}, "models": {"data": {"App\\Models\\ProjectsDetails": 1, "App\\Models\\User": 1}, "count": 2}, "livewire": {"data": {"accounting.proposal.proposal-create #vTUY9R15pJvLTlaQA99j": "array:5 [\n  \"data\" => array:20 [\n    \"customers\" => array:57 [\n      0 => array:2 [\n        \"name\" => \"Test deal\"\n        \"id\" => 219\n      ]\n      1 => array:2 [\n        \"name\" => \"Test Client\"\n        \"id\" => 318\n      ]\n      2 => array:2 [\n        \"name\" => \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n        \"id\" => 323\n      ]\n      3 => array:2 [\n        \"name\" => \"Customer 1\"\n        \"id\" => 492\n      ]\n      4 => array:2 [\n        \"name\" => \"Customer 2\"\n        \"id\" => 493\n      ]\n      5 => array:2 [\n        \"name\" => \"Customer 3\"\n        \"id\" => 494\n      ]\n      6 => array:2 [\n        \"name\" => \"Customer 4\"\n        \"id\" => 495\n      ]\n      7 => array:2 [\n        \"name\" => \"Customer 5\"\n        \"id\" => 496\n      ]\n      8 => array:2 [\n        \"name\" => \"Customer 6\"\n        \"id\" => 497\n      ]\n      9 => array:2 [\n        \"name\" => \"Customer 7\"\n        \"id\" => 498\n      ]\n      10 => array:2 [\n        \"name\" => \"Customer 8\"\n        \"id\" => 499\n      ]\n      11 => array:2 [\n        \"name\" => \"Customer 9\"\n        \"id\" => 500\n      ]\n      12 => array:2 [\n        \"name\" => \"Customer 10\"\n        \"id\" => 501\n      ]\n      13 => array:2 [\n        \"name\" => \"Customer 11\"\n        \"id\" => 502\n      ]\n      14 => array:2 [\n        \"name\" => \"Customer 12\"\n        \"id\" => 503\n      ]\n      15 => array:2 [\n        \"name\" => \"Customer 13\"\n        \"id\" => 504\n      ]\n      16 => array:2 [\n        \"name\" => \"Customer 14\"\n        \"id\" => 505\n      ]\n      17 => array:2 [\n        \"name\" => \"Customer 15\"\n        \"id\" => 506\n      ]\n      18 => array:2 [\n        \"name\" => \"Customer 16\"\n        \"id\" => 507\n      ]\n      19 => array:2 [\n        \"name\" => \"Customer 17\"\n        \"id\" => 508\n      ]\n      20 => array:2 [\n        \"name\" => \"Customer 18\"\n        \"id\" => 509\n      ]\n      21 => array:2 [\n        \"name\" => \"Customer 19\"\n        \"id\" => 510\n      ]\n      22 => array:2 [\n        \"name\" => \"Customer 20\"\n        \"id\" => 511\n      ]\n      23 => array:2 [\n        \"name\" => \"Customer 21\"\n        \"id\" => 512\n      ]\n      24 => array:2 [\n        \"name\" => \"Customer 22\"\n        \"id\" => 513\n      ]\n      25 => array:2 [\n        \"name\" => \"Customer 23\"\n        \"id\" => 514\n      ]\n      26 => array:2 [\n        \"name\" => \"Customer 24\"\n        \"id\" => 515\n      ]\n      27 => array:2 [\n        \"name\" => \"Customer 25\"\n        \"id\" => 516\n      ]\n      28 => array:2 [\n        \"name\" => \"Customer 26\"\n        \"id\" => 517\n      ]\n      29 => array:2 [\n        \"name\" => \"Customer 27\"\n        \"id\" => 518\n      ]\n      30 => array:2 [\n        \"name\" => \"Customer 28\"\n        \"id\" => 519\n      ]\n      31 => array:2 [\n        \"name\" => \"Customer 29\"\n        \"id\" => 520\n      ]\n      32 => array:2 [\n        \"name\" => \"Customer 30\"\n        \"id\" => 521\n      ]\n      33 => array:2 [\n        \"name\" => \"Customer 31\"\n        \"id\" => 522\n      ]\n      34 => array:2 [\n        \"name\" => \"Customer 32\"\n        \"id\" => 523\n      ]\n      35 => array:2 [\n        \"name\" => \"Customer 33\"\n        \"id\" => 524\n      ]\n      36 => array:2 [\n        \"name\" => \"Customer 34\"\n        \"id\" => 525\n      ]\n      37 => array:2 [\n        \"name\" => \"Customer 35\"\n        \"id\" => 526\n      ]\n      38 => array:2 [\n        \"name\" => \"Customer 36\"\n        \"id\" => 527\n      ]\n      39 => array:2 [\n        \"name\" => \"Customer 37\"\n        \"id\" => 528\n      ]\n      40 => array:2 [\n        \"name\" => \"Customer 38\"\n        \"id\" => 529\n      ]\n      41 => array:2 [\n        \"name\" => \"Customer 39\"\n        \"id\" => 530\n      ]\n      42 => array:2 [\n        \"name\" => \"Customer 40\"\n        \"id\" => 531\n      ]\n      43 => array:2 [\n        \"name\" => \"Customer 41\"\n        \"id\" => 532\n      ]\n      44 => array:2 [\n        \"name\" => \"Customer 42\"\n        \"id\" => 533\n      ]\n      45 => array:2 [\n        \"name\" => \"Customer 43\"\n        \"id\" => 534\n      ]\n      46 => array:2 [\n        \"name\" => \"Customer 44\"\n        \"id\" => 535\n      ]\n      47 => array:2 [\n        \"name\" => \"Customer 45\"\n        \"id\" => 536\n      ]\n      48 => array:2 [\n        \"name\" => \"Customer 46\"\n        \"id\" => 537\n      ]\n      49 => array:2 [\n        \"name\" => \"Customer 47\"\n        \"id\" => 538\n      ]\n      50 => array:2 [\n        \"name\" => \"Customer 48\"\n        \"id\" => 539\n      ]\n      51 => array:2 [\n        \"name\" => \"Customer 49\"\n        \"id\" => 540\n      ]\n      52 => array:2 [\n        \"name\" => \"Customer 50\"\n        \"id\" => 541\n      ]\n      53 => array:2 [\n        \"name\" => \"Khansaa 18.06\"\n        \"id\" => 563\n      ]\n      54 => array:2 [\n        \"name\" => \"Fouzan\"\n        \"id\" => 564\n      ]\n      55 => array:2 [\n        \"name\" => \"Testing\"\n        \"id\" => 631\n      ]\n      56 => array:2 [\n        \"name\" => \"New Tenant Dev Server\"\n        \"id\" => 652\n      ]\n    ]\n    \"billing_types\" => array:3 [\n      \"product\" => \"Item Wise\"\n      \"project\" => \"Project Wise\"\n      \"parts\" => \"Parts Wise\"\n    ]\n    \"categories\" => array:1 [\n      0 => array:2 [\n        \"name\" => \"Test cat\"\n        \"id\" => 18\n      ]\n    ]\n    \"templates\" => array:10 [\n      \"template1\" => \"New York\"\n      \"template2\" => \"Toronto\"\n      \"template3\" => \"Rio\"\n      \"template4\" => \"London\"\n      \"template5\" => \"Istanbul\"\n      \"template6\" => \"Mumbai\"\n      \"template7\" => \"Hong Kong\"\n      \"template8\" => \"Tokyo\"\n      \"template9\" => \"Sydney\"\n      \"template10\" => \"Paris\"\n    ]\n    \"items_list\" => array:4 [\n      0 => array:2 [\n        \"id\" => 11\n        \"name\" => \"Test\"\n      ]\n      1 => array:2 [\n        \"id\" => 12\n        \"name\" => \"test 3\"\n      ]\n      2 => array:2 [\n        \"id\" => 18\n        \"name\" => \"bbb\"\n      ]\n      3 => array:2 [\n        \"id\" => 22\n        \"name\" => \"Service - Programing new feature\"\n      ]\n    ]\n    \"item_types\" => array:3 [\n      \"product\" => \"Products\"\n      \"service\" => \"Services\"\n      \"parts\" => \"Parts\"\n    ]\n    \"proposal_type\" => \"product\"\n    \"customer_id\" => \"564\"\n    \"issue_date\" => \"202025-07-29\"\n    \"category_id\" => null\n    \"proposal_template\" => null\n    \"proposal_number\" => \"#PROP000004\"\n    \"items\" => array:1 [\n      0 => array:7 [\n        \"product_type\" => \"\"\n        \"item\" => \"\"\n        \"quantity\" => 1\n        \"price\" => 0\n        \"tax\" => 0\n        \"discount\" => 0\n        \"description\" => \"\"\n      ]\n    ]\n    \"subtotal\" => 0\n    \"discount_total\" => 0\n    \"tax_total\" => 0\n    \"total\" => 0\n    \"loading\" => false\n    \"saving\" => false\n    \"error\" => null\n  ]\n  \"name\" => \"accounting.proposal.proposal-create\"\n  \"view\" => \"livewire.accounting.proposal.proposal-create\"\n  \"component\" => \"App\\Http\\Livewire\\Accounting\\Proposal\\ProposalCreate\"\n  \"id\" => \"vTUY9R15pJvLTlaQA99j\"\n]"}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/_debugbar/open?id=X84c9146dec4c2250a2b841abbdcbaad8&op=get\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7368", "plain_user_password": "123456", "locale": "en", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/livewire/message/accounting.proposal.proposal-create", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">vTUY9R15pJvLTlaQA99j</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"35 characters\">accounting.proposal.proposal-create</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"23 characters\">finance/proposal/create</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">6fff26b7</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:20</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>customers</span>\" => <span class=sf-dump-note>array:57</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Test deal</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>219</span>\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Test Client</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>318</span>\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Alhayajneh Mohammad</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>323</span>\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Customer 1</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>492</span>\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Customer 2</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>493</span>\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Customer 3</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>494</span>\n        </samp>]\n        <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Customer 4</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>495</span>\n        </samp>]\n        <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Customer 5</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>496</span>\n        </samp>]\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Customer 6</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>497</span>\n        </samp>]\n        <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Customer 7</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>498</span>\n        </samp>]\n        <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Customer 8</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>499</span>\n        </samp>]\n        <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Customer 9</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>500</span>\n        </samp>]\n        <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 10</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>501</span>\n        </samp>]\n        <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 11</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>502</span>\n        </samp>]\n        <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 12</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>503</span>\n        </samp>]\n        <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 13</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>504</span>\n        </samp>]\n        <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 14</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>505</span>\n        </samp>]\n        <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 15</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>506</span>\n        </samp>]\n        <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 16</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>507</span>\n        </samp>]\n        <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 17</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>508</span>\n        </samp>]\n        <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 18</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>509</span>\n        </samp>]\n        <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 19</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>510</span>\n        </samp>]\n        <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 20</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>511</span>\n        </samp>]\n        <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 21</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>512</span>\n        </samp>]\n        <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 22</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>513</span>\n        </samp>]\n        <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 23</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>514</span>\n        </samp>]\n        <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 24</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>515</span>\n        </samp>]\n        <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 25</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>516</span>\n        </samp>]\n        <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 26</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>517</span>\n        </samp>]\n        <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 27</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>518</span>\n        </samp>]\n        <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 28</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>519</span>\n        </samp>]\n        <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 29</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>520</span>\n        </samp>]\n        <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 30</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>521</span>\n        </samp>]\n        <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 31</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>522</span>\n        </samp>]\n        <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 32</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>523</span>\n        </samp>]\n        <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 33</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>524</span>\n        </samp>]\n        <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 34</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>525</span>\n        </samp>]\n        <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 35</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>526</span>\n        </samp>]\n        <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 36</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>527</span>\n        </samp>]\n        <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 37</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>528</span>\n        </samp>]\n        <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 38</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>529</span>\n        </samp>]\n        <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 39</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>530</span>\n        </samp>]\n        <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 40</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>531</span>\n        </samp>]\n        <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 41</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>532</span>\n        </samp>]\n        <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 42</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>533</span>\n        </samp>]\n        <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 43</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>534</span>\n        </samp>]\n        <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 44</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>535</span>\n        </samp>]\n        <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 45</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>536</span>\n        </samp>]\n        <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 46</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>537</span>\n        </samp>]\n        <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 47</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>538</span>\n        </samp>]\n        <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 48</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>539</span>\n        </samp>]\n        <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 49</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>540</span>\n        </samp>]\n        <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Customer 50</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>541</span>\n        </samp>]\n        <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa 18.06</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>563</span>\n        </samp>]\n        <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Fouzan</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>564</span>\n        </samp>]\n        <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Testing</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>631</span>\n        </samp>]\n        <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">New Tenant Dev Server</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>652</span>\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>billing_types</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>product</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Item Wise</span>\"\n        \"<span class=sf-dump-key>project</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Project Wise</span>\"\n        \"<span class=sf-dump-key>parts</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Parts Wise</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Test cat</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>18</span>\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>templates</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>template1</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New York</span>\"\n        \"<span class=sf-dump-key>template2</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Toronto</span>\"\n        \"<span class=sf-dump-key>template3</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Rio</span>\"\n        \"<span class=sf-dump-key>template4</span>\" => \"<span class=sf-dump-str title=\"6 characters\">London</span>\"\n        \"<span class=sf-dump-key>template5</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Istanbul</span>\"\n        \"<span class=sf-dump-key>template6</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Mumbai</span>\"\n        \"<span class=sf-dump-key>template7</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Hong Kong</span>\"\n        \"<span class=sf-dump-key>template8</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Tokyo</span>\"\n        \"<span class=sf-dump-key>template9</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Sydney</span>\"\n        \"<span class=sf-dump-key>template10</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Paris</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>items_list</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Test</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>12</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">test 3</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>18</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">bbb</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>22</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Service - Programing new feature</span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>item_types</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>product</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Products</span>\"\n        \"<span class=sf-dump-key>service</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Services</span>\"\n        \"<span class=sf-dump-key>parts</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Parts</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>proposal_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n      \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">564</span>\"\n      \"<span class=sf-dump-key>issue_date</span>\" => \"<span class=sf-dump-str title=\"12 characters\">220202-07-29</span>\"\n      \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>proposal_template</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>proposal_number</span>\" => \"<span class=sf-dump-str title=\"11 characters\">#PROP000004</span>\"\n      \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>product_type</span>\" => \"\"\n          \"<span class=sf-dump-key>item</span>\" => \"\"\n          \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>discount</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>description</span>\" => \"\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>discount_total</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>tax_total</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>loading</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>saving</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>error</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">e8217d66f4ba9a0833d63a49709e69bd7a05ebdc7d26faa4d362c5076d024999</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">syncInput</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">rtem</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">issue_date</span>\"\n        \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">202025-07-29</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1700997876 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3185</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://osool-b2g.test/finance/proposal/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"467 characters\">laravel_session=GOxW6BteBlci4BUCkQHjCtca8ErcwVAkVGuhKO5W; XSRF-TOKEN=eyJpdiI6Im9iRTMzZnpzRTFTV1RaMUk3VFRWQXc9PSIsInZhbHVlIjoiakFpdDlYcW1MTWErcmRHVVVIYXRzOU1DRXBGV2RvMXNjMTRja3AvOVgwaW1YcGdiU1lLTmlwcFU1U0JWUlB6ZnFkWnhKVEpSanhINXRqTjVWS2cwSk9OMXRjaEw4WSs2OGtUTGM1L2g4aU1yVlViaTN6NFl5T1ozOUZCNWdXL00iLCJtYWMiOiJhYzMyNTJjYjZiYjYxMjIwNTIxNDkxZTZhZGQ3YmY3OGEyNjNmZGQwMDFmNGU0ZjgwZGRlYjZkYTJkMTdkZmExIiwidGFnIjoiIn0%3D; osool_session=OBiQBVhfEhzB2ZcjiDvE6xT6kGT4ivLAinbHUAaR</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1700997876\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:43</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3185</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://osool-b2g.test/finance/proposal/create</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"467 characters\">laravel_session=GOxW6BteBlci4BUCkQHjCtca8ErcwVAkVGuhKO5W; XSRF-TOKEN=eyJpdiI6Im9iRTMzZnpzRTFTV1RaMUk3VFRWQXc9PSIsInZhbHVlIjoiakFpdDlYcW1MTWErcmRHVVVIYXRzOU1DRXBGV2RvMXNjMTRja3AvOVgwaW1YcGdiU1lLTmlwcFU1U0JWUlB6ZnFkWnhKVEpSanhINXRqTjVWS2cwSk9OMXRjaEw4WSs2OGtUTGM1L2g4aU1yVlViaTN6NFl5T1ozOUZCNWdXL00iLCJtYWMiOiJhYzMyNTJjYjZiYjYxMjIwNTIxNDkxZTZhZGQ3YmY3OGEyNjNmZGQwMDFmNGU0ZjgwZGRlYjZkYTJkMTdkZmExIiwidGFnIjoiIn0%3D; osool_session=OBiQBVhfEhzB2ZcjiDvE6xT6kGT4ivLAinbHUAaR</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">62553</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"53 characters\">/livewire/message/accounting.proposal.proposal-create</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"53 characters\">/livewire/message/accounting.proposal.proposal-create</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.0212</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1516774978 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 10:14:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InNIdmUxMVo2Q2VaZEJ3MmRLbnJuSFE9PSIsInZhbHVlIjoiNGxiZEQ3OURRL09WYXBVWTZMSnIrV01CM0k2U2lUbHRIVHUxY3diWnBScFdsT2g0azcweU9rQ3dLTlhZcE16c1ByZWNrZEY1TUtEQjgwMlBmL0R5Q3o4bmRJK29yc2o3aEtMdHVFeWdpb0wxSndaMUkxMUR4bGlHYkxzSTF2cFMiLCJtYWMiOiI1YmI2YTEwNWM0ZjkxZjU3N2MzMTQ0NmQxMWQ0OGVlMzc1NDVjOTNkMzUzMWFlMTRiNzkwMTk3MDMwM2RjMDIzIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 12:14:43 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IjRzcS9Pd1hiWk4vMzN1Zk5KTEJKRXc9PSIsInZhbHVlIjoiQm9HYkthQTFqVzNyUkpubi9nUHpEUkVwMkl1SWhaUGU0amVIdFBLaE5CbVVnRHUrOFJuN3ZxaEx2Q0tWbG1Yd0g2R0FxQTg0R2ovWEVkSU9FcHV1d1gxSERnV3lrbTVrQkpxZkxOeVgxZ0Y4WlpSSjdoL2FPUDBhdmlGemNPN3QiLCJtYWMiOiJiMTA0NTJiMzY2NDk4NGU2OWQ4NTcyZWZkMjc5OWU3ZGUwN2JlMzViYzBhNGU2ZDNhNzUxNWI0YTdlMTU1NzJjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 12:14:43 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InNIdmUxMVo2Q2VaZEJ3MmRLbnJuSFE9PSIsInZhbHVlIjoiNGxiZEQ3OURRL09WYXBVWTZMSnIrV01CM0k2U2lUbHRIVHUxY3diWnBScFdsT2g0azcweU9rQ3dLTlhZcE16c1ByZWNrZEY1TUtEQjgwMlBmL0R5Q3o4bmRJK29yc2o3aEtMdHVFeWdpb0wxSndaMUkxMUR4bGlHYkxzSTF2cFMiLCJtYWMiOiI1YmI2YTEwNWM0ZjkxZjU3N2MzMTQ0NmQxMWQ0OGVlMzc1NDVjOTNkMzUzMWFlMTRiNzkwMTk3MDMwM2RjMDIzIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 12:14:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IjRzcS9Pd1hiWk4vMzN1Zk5KTEJKRXc9PSIsInZhbHVlIjoiQm9HYkthQTFqVzNyUkpubi9nUHpEUkVwMkl1SWhaUGU0amVIdFBLaE5CbVVnRHUrOFJuN3ZxaEx2Q0tWbG1Yd0g2R0FxQTg0R2ovWEVkSU9FcHV1d1gxSERnV3lrbTVrQkpxZkxOeVgxZ0Y4WlpSSjdoL2FPUDBhdmlGemNPN3QiLCJtYWMiOiJiMTA0NTJiMzY2NDk4NGU2OWQ4NTcyZWZkMjc5OWU3ZGUwN2JlMzViYzBhNGU2ZDNhNzUxNWI0YTdlMTU1NzJjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 12:14:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1516774978\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-434986988 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"80 characters\">http://osool-b2g.test/_debugbar/open?id=X84c9146dec4c2250a2b841abbdcbaad8&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7368</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-434986988\", {\"maxDepth\":0})</script>\n"}}